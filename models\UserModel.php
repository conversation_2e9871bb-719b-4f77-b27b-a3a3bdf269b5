<?php
/**
 * User Model
 * 
 * This model handles all database operations related to users.
 */
class UserModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all users
     * 
     * @param string $role Optional role filter
     * @return array
     */
    public function getUsers($role = null) {
        $sql = 'SELECT id, name, email, role, profile_image, phone, address, city, state, zip, created_at, last_login, status FROM users';
        
        if ($role) {
            $sql .= ' WHERE role = :role';
            $this->db->query($sql);
            $this->db->bind(':role', $role);
        } else {
            $this->db->query($sql);
        }
        
        return $this->db->resultSet();
    }

    /**
     * Get paginated users with search and filtering
     *
     * @param int $page Page number (1-based)
     * @param int $perPage Number of users per page
     * @param string $search Search term for name/email
     * @param string $role Role filter
     * @param string $status Status filter
     * @param string $orderBy Order by field (name, email, role, status, created_at, last_login)
     * @param string $orderDir Order direction (ASC, DESC)
     * @return array Array with users, pagination info
     */
    public function getPaginatedUsers($page = 1, $perPage = 20, $search = '', $role = '', $status = '', $orderBy = 'created_at', $orderDir = 'DESC') {
        $startTime = microtime(true);

        // Validate inputs
        $page = max(1, (int)$page);
        $perPage = max(1, min(100, (int)$perPage)); // Limit to 100 per page max
        $offset = ($page - 1) * $perPage;

        // Validate order by field
        $allowedOrderFields = ['name', 'email', 'role', 'status', 'created_at', 'last_login'];
        if (!in_array($orderBy, $allowedOrderFields)) {
            $orderBy = 'created_at';
        }

        // Validate order direction
        $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

        // Build WHERE conditions and parameters
        $whereConditions = [];
        $bindParams = [];

        if (!empty($search)) {
            $whereConditions[] = '(name LIKE :search_name OR email LIKE :search_email)';
            $bindParams['search_name'] = '%' . $search . '%';
            $bindParams['search_email'] = '%' . $search . '%';
        }

        if (!empty($role)) {
            $whereConditions[] = 'role = :role';
            $bindParams['role'] = $role;
        }

        if (!empty($status)) {
            $whereConditions[] = 'status = :status';
            $bindParams['status'] = $status;
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM users $whereClause";
        $this->db->query($countSql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $countResult = $this->db->single();
        $totalUsers = $countResult ? $countResult->total : 0;
        $totalPages = ceil($totalUsers / $perPage);

        // Get users for current page
        $sql = "SELECT id, name, email, role, profile_image, status, created_at, last_login
                FROM users
                $whereClause
                ORDER BY $orderBy $orderDir
                LIMIT :limit OFFSET :offset";

        $this->db->query($sql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
        $this->db->bind(':offset', $offset, PDO::PARAM_INT);

        $users = $this->db->resultSet();

        // Performance monitoring
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds

        // Log performance metrics
        error_log("UserModel::getPaginatedUsers - Performance Metrics:");
        error_log("  - Execution time: {$executionTime}ms");
        error_log("  - Total users found: {$totalUsers}");
        error_log("  - Page: {$page}/{$totalPages}");
        error_log("  - Per page: {$perPage}");
        error_log("  - Search: '" . ($search ?: 'none') . "'");
        error_log("  - Filters: role='" . ($role ?: 'all') . "', status='" . ($status ?: 'all') . "'");
        error_log("  - Order: {$orderBy} {$orderDir}");

        // Log warning if query is slow
        if ($executionTime > 100) { // More than 100ms
            error_log("WARNING: Slow user query detected! Execution time: {$executionTime}ms");
        }

        return [
            'users' => $users,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_users' => $totalUsers,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'start_record' => $totalUsers > 0 ? $offset + 1 : 0,
                'end_record' => min($offset + $perPage, $totalUsers)
            ],
            'filters' => [
                'search' => $search,
                'role' => $role,
                'status' => $status,
                'order_by' => $orderBy,
                'order_dir' => $orderDir
            ],
            'performance' => [
                'execution_time_ms' => $executionTime,
                'query_count' => 2 // Count query + data query
            ]
        ];
    }

    /**
     * Get user by ID
     *
     * @param int $id User ID
     * @return object|bool User object or false if not found
     */
    public function getUserById($id) {
        $this->db->query('SELECT id, name, email, role, profile_image, phone, address, city, state, zip, created_at, last_login, status, facebook_id 
                          FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get user by email
     * 
     * @param string $email User email
     * @return object|bool User object or false if not found
     */
    public function getUserByEmail($email) {
        $this->db->query('SELECT id, name, email, password, role, profile_image, phone, address, city, state, zip, created_at, last_login, status, facebook_id 
                          FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        
        return $this->db->single();
    }
    
    /**
     * Create a new user
     * 
     * @param array $data User data
     * @return bool|int False on failure, user ID on success
     */
    public function createUser($data) {
        $this->db->query('INSERT INTO users (name, email, password, role, phone, address, city, state, zip, created_at) 
                          VALUES (:name, :email, :password, :role, :phone, :address, :city, :state, :zip, NOW())');
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', password_hash($data['password'], PASSWORD_BCRYPT, ['cost' => 12]));
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':city', $data['city'] ?? null);
        $this->db->bind(':state', $data['state'] ?? null);
        $this->db->bind(':zip', $data['zip'] ?? null);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update a user
     * 
     * @param array $data User data
     * @return bool
     */
    public function updateUser($data) {
        // If password is provided, update it too
        if (!empty($data['password'])) {
            $this->db->query('UPDATE users SET name = :name, email = :email, password = :password, 
                              role = :role, status = :status, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip WHERE id = :id');
            $this->db->bind(':password', password_hash($data['password'], PASSWORD_BCRYPT, ['cost' => 12]));
        } else {
            $this->db->query('UPDATE users SET name = :name, email = :email, 
                              role = :role, status = :status, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip WHERE id = :id');
        }
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':status', $data['status']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':city', $data['city'] ?? null);
        $this->db->bind(':state', $data['state'] ?? null);
        $this->db->bind(':zip', $data['zip'] ?? null);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Update user profile
     * 
     * @param array $data Profile data
     * @return bool
     */
    public function updateProfile($data) {
        // If password is provided, update it too
        if (!empty($data['password'])) {
            $this->db->query('UPDATE users SET name = :name, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip, timezone = :timezone, password = :password 
                              WHERE id = :id');
            $this->db->bind(':password', password_hash($data['password'], PASSWORD_BCRYPT, ['cost' => 12]));
        } else {
            $this->db->query('UPDATE users SET name = :name, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip, timezone = :timezone 
                              WHERE id = :id');
        }
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':city', $data['city'] ?? null);
        $this->db->bind(':state', $data['state'] ?? null);
        $this->db->bind(':zip', $data['zip'] ?? null);
        $this->db->bind(':timezone', $data['timezone'] ?? 'America/New_York');
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Update user profile image
     * 
     * @param int $id User ID
     * @param string $imagePath Path to profile image
     * @return bool
     */
    public function updateProfileImage($id, $imagePath) {
        $this->db->query('UPDATE users SET profile_image = :profile_image WHERE id = :id');
        $this->db->bind(':profile_image', $imagePath);
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Delete a user
     * 
     * @param int $id User ID
     * @return bool
     */
    public function deleteUser($id) {
        $this->db->query('DELETE FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get judges
     * 
     * @return array
     */
    public function getJudges() {
        $this->db->query('SELECT id, name, email, profile_image FROM users WHERE role = :role AND status = :status');
        $this->db->bind(':role', 'judge');
        $this->db->bind(':status', 'active');
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if email exists
     * 
     * @param string $email Email to check
     * @param int $excludeId Optional user ID to exclude from check
     * @return bool
     */
    public function emailExists($email, $excludeId = null) {
        if ($excludeId) {
            $this->db->query('SELECT id FROM users WHERE email = :email AND id != :id');
            $this->db->bind(':id', $excludeId);
        } else {
            $this->db->query('SELECT id FROM users WHERE email = :email');
        }
        
        $this->db->bind(':email', $email);
        $this->db->single();
        
        return $this->db->rowCount() > 0;
    }
    
    /**
     * Get users by role (legacy method - loads all users)
     *
     * @param string $role Role to filter by
     * @return array Array of users with the specified role
     */
    public function getUsersByRole($role) {
        // Get users with the specified role
        $this->db->query('SELECT id, name, email, role, profile_image, phone, address, city, state, zip, created_at, last_login, status
                          FROM users WHERE role = :role ORDER BY name');
        $this->db->bind(':role', $role);

        return $this->db->resultSet();
    }

    /**
     * Get paginated users by role with search functionality
     *
     * @param string $role Role to filter by
     * @param int $page Page number (1-based)
     * @param int $perPage Number of users per page
     * @param string $search Search term for name/email
     * @param string $status Status filter
     * @param string $orderBy Order by field (name, email, status, created_at, last_login)
     * @param string $orderDir Order direction (ASC, DESC)
     * @return array Array with users, pagination info
     */
    public function getPaginatedUsersByRole($role, $page = 1, $perPage = 20, $search = '', $status = '', $orderBy = 'name', $orderDir = 'ASC') {
        $startTime = microtime(true);

        // Validate inputs
        $page = max(1, (int)$page);
        $perPage = max(1, min(50, (int)$perPage)); // Limit to 50 per page max for role view
        $offset = ($page - 1) * $perPage;

        // Validate order by field
        $allowedOrderFields = ['name', 'email', 'status', 'created_at', 'last_login'];
        if (!in_array($orderBy, $allowedOrderFields)) {
            $orderBy = 'name';
        }

        // Validate order direction
        $orderDir = strtoupper($orderDir) === 'DESC' ? 'DESC' : 'ASC';

        // Build WHERE conditions and parameters
        $whereConditions = ['role = :role'];
        $bindParams = ['role' => $role];

        if (!empty($search)) {
            $whereConditions[] = '(name LIKE :search_name OR email LIKE :search_email)';
            $bindParams['search_name'] = '%' . $search . '%';
            $bindParams['search_email'] = '%' . $search . '%';
        }

        if (!empty($status)) {
            $whereConditions[] = 'status = :status';
            $bindParams['status'] = $status;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM users $whereClause";
        $this->db->query($countSql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $countResult = $this->db->single();
        $totalUsers = $countResult ? $countResult->total : 0;
        $totalPages = ceil($totalUsers / $perPage);

        // Get users for current page
        $sql = "SELECT id, name, email, role, profile_image, status, created_at, last_login
                FROM users
                $whereClause
                ORDER BY $orderBy $orderDir
                LIMIT :limit OFFSET :offset";

        $this->db->query($sql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
        $this->db->bind(':offset', $offset, PDO::PARAM_INT);

        $users = $this->db->resultSet();

        // Performance monitoring
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);

        error_log("UserModel::getPaginatedUsersByRole - Role: {$role}, Page: {$page}, Users: {$totalUsers}, Time: {$executionTime}ms");

        if ($executionTime > 100) {
            error_log("WARNING: Slow role users query detected! Role: {$role}, Time: {$executionTime}ms");
        }

        return [
            'users' => $users,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_users' => $totalUsers,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'start_record' => $totalUsers > 0 ? $offset + 1 : 0,
                'end_record' => min($offset + $perPage, $totalUsers)
            ],
            'filters' => [
                'role' => $role,
                'search' => $search,
                'status' => $status,
                'order_by' => $orderBy,
                'order_dir' => $orderDir
            ],
            'performance' => [
                'execution_time_ms' => $executionTime
            ]
        ];
    }
    
    /**
     * Check if a user has a specific role
     * 
     * @param int $userId User ID
     * @param string $role Role name
     * @return bool True if the user has the role, false otherwise
     */
    public function hasRole($userId, $role) {
        // Check if we're using the new role system
        $this->db->query("SHOW TABLES LIKE 'roles'");
        $hasRolesTable = $this->db->rowCount() > 0;
        
        if ($hasRolesTable) {
            // New role system with roles table
            $this->db->query('SELECT COUNT(*) as count 
                              FROM user_roles ur 
                              JOIN roles r ON ur.role_id = r.id 
                              WHERE ur.user_id = :user_id AND r.name = :role');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            
            $result = $this->db->single();
            
            return $result && $result->count > 0;
        } else {
            // Old role system with role column in users table
            $this->db->query('SELECT COUNT(*) as count FROM users WHERE id = :user_id AND role = :role');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            
            $result = $this->db->single();
            
            return $result && $result->count > 0;
        }
    }
    
    /**
     * Get total user count
     * 
     * @return int Total number of users
     */
    public function getUserCount() {
        $this->db->query('SELECT COUNT(*) as count FROM users');
        $result = $this->db->single();
        return $result ? $result->count : 0;
    }

    /**
     * Get users by multiple roles
     * 
     * @param array $roles Array of roles to filter by
     * @return array Array of users with any of the specified roles
     */
    public function getUsersByRoles($roles) {
        if (empty($roles)) {
            return [];
        }
        
        // Create named placeholders for the IN clause
        $placeholders = [];
        $params = [];
        
        foreach ($roles as $index => $role) {
            $param = ":role{$index}";
            $placeholders[] = $param;
            $params[$param] = $role;
        }
        
        $placeholderString = implode(',', $placeholders);
        
        $this->db->query("SELECT id, name, email, role, profile_image, created_at, last_login, status 
                          FROM users WHERE role IN ($placeholderString) ORDER BY name");
        
        // Bind each role value
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Get count of users by role
     * 
     * @return array Array of role counts
     */
    public function getRoleCounts() {
        $startTime = microtime(true);

        $this->db->query('SELECT role, COUNT(*) as count FROM users GROUP BY role');
        $results = $this->db->resultSet();

        $counts = [];
        foreach ($results as $result) {
            $counts[$result->role] = $result->count;
        }

        // Performance monitoring
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);
        error_log("UserModel::getRoleCounts - Execution time: {$executionTime}ms");

        if ($executionTime > 50) {
            error_log("WARNING: Slow role counts query detected! Execution time: {$executionTime}ms");
        }

        return $counts;
    }
    
    /**
     * Update user role
     *
     * @param int $userId User ID
     * @param string $role New role
     * @return bool True on success, false on failure
     */
    public function updateUserRole($userId, $role) {
        $this->db->query('UPDATE users SET role = :role WHERE id = :id');
        $this->db->bind(':role', $role);
        $this->db->bind(':id', $userId);

        return $this->db->execute();
    }

    /**
     * Update user status
     *
     * @param int $userId User ID
     * @param string $status New status
     * @return bool True on success, false on failure
     */
    public function updateUserStatus($userId, $status) {
        $this->db->query('UPDATE users SET status = :status WHERE id = :id');
        $this->db->bind(':status', $status);
        $this->db->bind(':id', $userId);

        return $this->db->execute();
    }

    /**
     * Soft delete user (set status to inactive)
     *
     * @param int $userId User ID
     * @return bool True on success, false on failure
     */
    public function softDeleteUser($userId) {
        // For safety, we'll set status to inactive instead of hard delete
        // This preserves data integrity for shows, registrations, etc.
        $this->db->query('UPDATE users SET status = :status WHERE id = :id');
        $this->db->bind(':status', 'inactive');
        $this->db->bind(':id', $userId);

        return $this->db->execute();
    }
    
    /**
     * Get all users except the specified user
     * 
     * @param int $excludeUserId User ID to exclude
     * @return array Array of users
     */
    public function getUsersExcept($excludeUserId) {
        $this->db->query('SELECT id, name, email, role, profile_image, created_at, last_login, status 
                          FROM users WHERE id != :exclude_id ORDER BY role, name');
        $this->db->bind(':exclude_id', $excludeUserId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Search for users by name, email, or phone
     * 
     * @param string $search Search term
     * @return array Array of matching users
     */
    public function searchUsers($search) {
        // Use the advanced search method for better results
        return $this->advancedUserSearch($search);
    }
    
    /**
     * Advanced search for users with better matching and debugging
     * 
     * @param string $search Search term
     * @return array Array of matching users
     */
    public function advancedUserSearch($search) {
        try {
            // Sanitize the search term
            $search = trim($search);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('UserModel::advancedUserSearch - Search term: ' . $search);
            }
            
            // First try an exact match for email (since that worked in testing)
            if (strpos($search, '@') !== false) {
                $this->db->query('SELECT id, name, email, phone, role, status 
                                  FROM users 
                                  WHERE email = :exact_email
                                  LIMIT 10');
                $this->db->bind(':exact_email', $search);
                $exactResults = $this->db->resultSet();
                
                if (is_array($exactResults) && count($exactResults) > 0) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('UserModel::advancedUserSearch - Found exact email match: ' . json_encode($exactResults[0]));
                    }
                    return $exactResults;
                }
            }
            
            // Special handling for numeric-only searches (likely phone numbers)
            if (preg_match('/^[0-9]+$/', $search)) {
                $this->db->query('SELECT id, name, email, phone, role, status 
                                  FROM users 
                                  WHERE phone LIKE :phone_search
                                  OR REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(phone, "-", ""), " ", ""), ".", ""), "(", ""), ")", "") LIKE :phone_clean_search
                                  LIMIT 10');
                                  
                $this->db->bind(':phone_search', '%' . $search . '%');
                $this->db->bind(':phone_clean_search', '%' . $search . '%');
                
                $phoneResults = $this->db->resultSet();
                
                if (is_array($phoneResults) && count($phoneResults) > 0) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('UserModel::advancedUserSearch - Found phone match: ' . json_encode($phoneResults[0]));
                        error_log('UserModel::advancedUserSearch - Total phone matches: ' . count($phoneResults));
                    }
                    return $phoneResults;
                }
            }
            
            // Get total user count for debugging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $this->db->query('SELECT COUNT(*) as count FROM users');
                $countResult = $this->db->single();
                $userCount = $countResult ? $countResult->count : 0;
                error_log('UserModel::advancedUserSearch - Total users in database: ' . $userCount);
                
                // Get a sample of users for debugging
                $this->db->query('SELECT id, name, email, phone FROM users LIMIT 3');
                $sampleUsers = $this->db->resultSet();
                error_log('UserModel::advancedUserSearch - Sample users: ' . json_encode($sampleUsers));
                
                // Check phone number formats in the database
                $this->db->query('SELECT DISTINCT phone FROM users WHERE phone IS NOT NULL AND phone != "" LIMIT 5');
                $phoneFormats = $this->db->resultSet();
                error_log('UserModel::advancedUserSearch - Phone number formats: ' . json_encode($phoneFormats));
            }
            
            // Prepare a more flexible search query with enhanced phone search
            $sql = 'SELECT id, name, email, phone, role, status 
                    FROM users 
                    WHERE name LIKE :search 
                    OR email LIKE :search 
                    OR phone LIKE :search 
                    OR LOWER(name) LIKE LOWER(:search)
                    OR LOWER(email) LIKE LOWER(:search)
                    OR REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(phone, "-", ""), " ", ""), ".", ""), "(", ""), ")", "") LIKE :search_clean
                    OR phone LIKE :phone_partial
                    OR REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(phone, "-", ""), " ", ""), ".", ""), "(", ""), ")", "") LIKE :phone_partial_clean
                    ORDER BY 
                      CASE 
                        WHEN email = :exact_match THEN 1
                        WHEN name = :exact_match THEN 2
                        WHEN phone = :exact_match THEN 3
                        WHEN email LIKE :starts_with THEN 4
                        WHEN name LIKE :starts_with THEN 5
                        WHEN phone LIKE :phone_partial THEN 6
                        ELSE 7
                      END,
                      name
                    LIMIT 10';
                    
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('UserModel::advancedUserSearch - SQL: ' . $sql);
            }
            
            $this->db->query($sql);
            
            // Bind the search parameters
            $searchParam = '%' . $search . '%';
            $this->db->bind(':search', $searchParam);
            $this->db->bind(':exact_match', $search);
            $this->db->bind(':starts_with', $search . '%');
            
            // Bind the clean search parameter (no special characters)
            $searchClean = '%' . preg_replace('/[^a-zA-Z0-9]/', '', $search) . '%';
            $this->db->bind(':search_clean', $searchClean);
            
            // Bind phone-specific search parameters
            // This handles cases where the phone number is stored with formatting
            // but the user enters just the digits (e.g., "704" should match "(*************")
            $this->db->bind(':phone_partial', '%' . $search . '%');
            $this->db->bind(':phone_partial_clean', '%' . preg_replace('/[^0-9]/', '', $search) . '%');
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('UserModel::advancedUserSearch - Search parameter: ' . $searchParam);
                error_log('UserModel::advancedUserSearch - Clean search parameter: ' . $searchClean);
            }
            
            // Execute and return results
            $results = $this->db->resultSet();
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('UserModel::advancedUserSearch - Result count: ' . (is_array($results) ? count($results) : 'not an array'));
                if (is_array($results) && count($results) > 0) {
                    error_log('UserModel::advancedUserSearch - First result: ' . json_encode($results[0]));
                } else {
                    // If still no results, try a direct query with LOWER() for case insensitivity
                    $this->db->query('SELECT id, name, email, phone, role, status 
                                     FROM users 
                                     WHERE LOWER(name) LIKE LOWER(:name_search)
                                     OR LOWER(email) LIKE LOWER(:email_search)
                                     LIMIT 10');
                    $this->db->bind(':name_search', '%' . strtolower($search) . '%');
                    $this->db->bind(':email_search', '%' . strtolower($search) . '%');
                    
                    $lowerResults = $this->db->resultSet();
                    error_log('UserModel::advancedUserSearch - Case-insensitive search results: ' . count($lowerResults));
                    
                    if (count($lowerResults) > 0) {
                        error_log('UserModel::advancedUserSearch - Found with case-insensitive search: ' . json_encode($lowerResults[0]));
                        return $lowerResults;
                    }
                }
            }
            
            // If no results, return empty array instead of false
            return is_array($results) ? $results : [];
            
        } catch (Exception $e) {
            // Log the error if in debug mode
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error in UserModel::advancedUserSearch: ' . $e->getMessage());
                error_log('Error trace: ' . $e->getTraceAsString());
            }
            
            // Return empty array on error
            return [];
        }
    }
    
    /**
     * Update user phone number
     * 
     * @param int $userId User ID
     * @param string $phone Phone number
     * @return bool Success status
     */
    public function updateUserPhone($userId, $phone) {
        try {
            $this->db->query('UPDATE users SET phone = :phone WHERE id = :id');
            $this->db->bind(':phone', $phone);
            $this->db->bind(':id', $userId);
            
            return $this->db->execute();
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error in UserModel::updateUserPhone: ' . $e->getMessage());
            }
            return false;
        }
    }
}