<?php
/**
 * Demo Data Generator Web Interface
 * 
 * Professional web interface for generating comprehensive demo data
 * for showcasing the Events and Shows Management System to potential customers.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once 'generate_demo_data.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Data Generator - Events and Shows Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .btn-generate {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 12px 30px;
            font-weight: bold;
        }
        .btn-cleanup {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            border: none;
            padding: 12px 30px;
            font-weight: bold;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        .demo-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-database me-3"></i>
                        Demo Data Generator
                    </h1>
                    <p class="lead mb-4">
                        Create comprehensive, professional demo data for showcasing the Events and Shows Management System to potential customers.
                    </p>
                    <div class="d-flex flex-wrap gap-3">
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-trophy me-2"></i>10 Professional Car Shows
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-users me-2"></i>20+ Demo Users
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-car me-2"></i>20 Stunning Vehicles
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-star me-2"></i>Complete Judging System
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-images me-2"></i>Professional Images
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-magic fa-8x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-5">
        <!-- Current Status -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Current Database Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $db = new Database();
                            
                            // Check current data
                            $stats = [];
                            
                            $tables = ['users', 'shows', 'vehicles', 'registrations', 'payments', 'judging_scores', 'fan_votes', 'images'];
                            foreach ($tables as $table) {
                                try {
                                    $db->query("SELECT COUNT(*) as count FROM {$table}");
                                    $result = $db->single();
                                    $stats[$table] = $result->count ?? 0;
                                } catch (Exception $e) {
                                    $stats[$table] = 'N/A';
                                }
                            }
                            
                            // Check for demo data
                            $db->query("SELECT COUNT(*) as count FROM users WHERE email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com'");
                            $demoResult = $db->single();
                            $demoUsers = $demoResult->count ?? 0;
                            
                            echo "<div class='row'>";
                            foreach ($stats as $table => $count) {
                                $icon = [
                                    'users' => 'fas fa-users',
                                    'shows' => 'fas fa-trophy',
                                    'vehicles' => 'fas fa-car',
                                    'registrations' => 'fas fa-clipboard-list',
                                    'payments' => 'fas fa-credit-card',
                                    'judging_scores' => 'fas fa-star',
                                    'fan_votes' => 'fas fa-thumbs-up',
                                    'images' => 'fas fa-images'
                                ][$table] ?? 'fas fa-table';
                                
                                echo "<div class='col-md-3 col-sm-6 mb-3'>";
                                echo "<div class='text-center'>";
                                echo "<i class='{$icon} fa-2x text-primary mb-2'></i>";
                                echo "<h4 class='mb-1'>{$count}</h4>";
                                echo "<small class='text-muted'>" . ucfirst(str_replace('_', ' ', $table)) . "</small>";
                                echo "</div>";
                                echo "</div>";
                            }
                            echo "</div>";
                            
                            if ($demoUsers > 0) {
                                echo "<div class='alert alert-info mt-3'>";
                                echo "<i class='fas fa-info-circle me-2'></i>";
                                echo "<strong>Demo Data Detected:</strong> Found {$demoUsers} demo users in the system.";
                                echo "</div>";
                            }
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                            echo "Error checking database status: " . $e->getMessage();
                            echo "</div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">What Will Be Generated</h2>
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                                <h5>Professional Car Shows</h5>
                                <p class="text-muted">10 realistic car shows with detailed descriptions, locations, and dates</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x text-primary mb-3"></i>
                                <h5>Complete User Base</h5>
                                <p class="text-muted">Coordinators, judges, and participants with realistic profiles</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-car fa-3x text-success mb-3"></i>
                                <h5>Stunning Vehicles</h5>
                                <p class="text-muted">20 detailed vehicle profiles with professional descriptions</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-3x text-info mb-3"></i>
                                <h5>Complete Judging</h5>
                                <p class="text-muted">Professional scores, comments, and voting data</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-images fa-3x text-danger mb-3"></i>
                                <h5>Professional Images</h5>
                                <p class="text-muted">High-quality vehicle and show images from free sources</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row">
            <div class="col-12 text-center">
                <h3 class="mb-4">Generate Demo Data</h3>
                <p class="text-muted mb-4">
                    Click the button below to generate a complete, professional demo dataset.
                    This process may take a few minutes to complete. Use "Comprehensive Cleanup" to remove all demo data including shows, registrations, payments, images, and related records.
                </p>
                
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="?action=generate" class="btn btn-generate btn-lg text-white">
                        <i class="fas fa-magic me-2"></i>
                        Generate Complete Demo Data
                    </a>
                    
                    <a href="?action=cleanup" class="btn btn-cleanup btn-lg text-white">
                        <i class="fas fa-broom me-2"></i>
                        Comprehensive Cleanup
                    </a>
                </div>
                
                <div class="progress-container">
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <p class="mt-2 text-muted">Generating demo data...</p>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <?php if (isset($_GET['action'])): ?>
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            Generation Results
                        </h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
<?php
try {
    $generator = new DemoDataGenerator();
    
    if ($_GET['action'] === 'generate') {
        echo "🚀 Starting Demo Data Generation...\n";
        echo "=====================================\n\n";
        
        $result = $generator->generateCompleteDemo(true);
        
        if ($result) {
            echo "\n🎉 SUCCESS! Your demo site is ready to showcase!\n";
            echo "You can now demonstrate all features to potential customers.\n";
        } else {
            echo "\n❌ Generation failed. Please check the error messages above.\n";
        }
        
    } elseif ($_GET['action'] === 'cleanup') {
        echo "🧹 Starting Demo Data Cleanup...\n";
        echo "=================================\n\n";
        
        $deleted = $generator->cleanupDemoData(true);
        
        echo "\n✅ Cleanup completed! Deleted {$deleted} demo users and all related data.\n";
        echo "Your database is now clean and ready for new demo data.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString();
}
?>
                        </pre>
                        <div class="mt-3">
                            <a href="?" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Generator
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
