-- Calendar Performance Indexes Migration
-- Add indexes to calendar-related tables for improved performance with large datasets
-- These indexes will significantly improve search, filtering, and sorting operations

-- Check existing indexes first
SELECT 'Checking existing indexes on calendar tables...' as status;

-- ===== CALENDAR_CLUBS TABLE INDEXES =====
SELECT 'Adding indexes to calendar_clubs table...' as status;

-- Index on name for search operations
CREATE INDEX IF NOT EXISTS idx_clubs_name ON calendar_clubs(name);

-- Index on email for contact search
CREATE INDEX IF NOT EXISTS idx_clubs_email ON calendar_clubs(email);

-- Index on owner_id for filtering by ownership
CREATE INDEX IF NOT EXISTS idx_clubs_owner_id ON calendar_clubs(owner_id);

-- Index on verification status for filtering
CREATE INDEX IF NOT EXISTS idx_clubs_verification_status ON calendar_clubs(verification_status);

-- Index on is_verified for filtering
CREATE INDEX IF NOT EXISTS idx_clubs_is_verified ON calendar_clubs(is_verified);

-- Index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_clubs_created_at ON calendar_clubs(created_at);

-- Index on updated_at for sorting
CREATE INDEX IF NOT EXISTS idx_clubs_updated_at ON calendar_clubs(updated_at);

-- Composite index for name and description searches
CREATE INDEX IF NOT EXISTS idx_clubs_name_desc ON calendar_clubs(name, description(100));

-- Composite index for owner and verification status
CREATE INDEX IF NOT EXISTS idx_clubs_owner_verified ON calendar_clubs(owner_id, is_verified);

-- ===== CALENDAR_VENUES TABLE INDEXES =====
SELECT 'Adding indexes to calendar_venues table...' as status;

-- Index on name for search operations
CREATE INDEX IF NOT EXISTS idx_venues_name ON calendar_venues(name);

-- Index on city for location filtering
CREATE INDEX IF NOT EXISTS idx_venues_city ON calendar_venues(city);

-- Index on state for location filtering
CREATE INDEX IF NOT EXISTS idx_venues_state ON calendar_venues(state);

-- Index on zipcode for location search
CREATE INDEX IF NOT EXISTS idx_venues_zipcode ON calendar_venues(zipcode);

-- Index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_venues_created_at ON calendar_venues(created_at);

-- Index on updated_at for sorting
CREATE INDEX IF NOT EXISTS idx_venues_updated_at ON calendar_venues(updated_at);

-- Composite index for location searches (city, state)
CREATE INDEX IF NOT EXISTS idx_venues_location ON calendar_venues(city, state);

-- Composite index for name and location
CREATE INDEX IF NOT EXISTS idx_venues_name_location ON calendar_venues(name, city, state);

-- Spatial index for lat/lng if columns exist
-- Note: This will only work if lat/lng columns exist in the venues table
-- CREATE SPATIAL INDEX IF NOT EXISTS idx_venues_coordinates ON calendar_venues(lat, lng);

-- ===== CALENDARS TABLE INDEXES =====
SELECT 'Adding indexes to calendars table...' as status;

-- Index on name for search operations
CREATE INDEX IF NOT EXISTS idx_calendars_name ON calendars(name);

-- Index on owner_id for user's calendars
CREATE INDEX IF NOT EXISTS idx_calendars_owner_id ON calendars(owner_id);

-- Index on is_visible for filtering
CREATE INDEX IF NOT EXISTS idx_calendars_is_visible ON calendars(is_visible);

-- Index on is_public for filtering
CREATE INDEX IF NOT EXISTS idx_calendars_is_public ON calendars(is_public);

-- Index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_calendars_created_at ON calendars(created_at);

-- Index on updated_at for sorting
CREATE INDEX IF NOT EXISTS idx_calendars_updated_at ON calendars(updated_at);

-- Composite index for owner and visibility
CREATE INDEX IF NOT EXISTS idx_calendars_owner_visible ON calendars(owner_id, is_visible);

-- Composite index for public and visible calendars
CREATE INDEX IF NOT EXISTS idx_calendars_public_visible ON calendars(is_public, is_visible);

-- ===== CALENDAR_CLUB_MEMBERS TABLE INDEXES =====
SELECT 'Adding indexes to calendar_club_members table...' as status;

-- Index on club_id for club member queries
CREATE INDEX IF NOT EXISTS idx_club_members_club_id ON calendar_club_members(club_id);

-- Index on user_id for user membership queries
CREATE INDEX IF NOT EXISTS idx_club_members_user_id ON calendar_club_members(user_id);

-- Index on role for filtering by member role
CREATE INDEX IF NOT EXISTS idx_club_members_role ON calendar_club_members(role);

-- Index on joined_at for sorting
CREATE INDEX IF NOT EXISTS idx_club_members_joined_at ON calendar_club_members(joined_at);

-- Composite index for club and role
CREATE INDEX IF NOT EXISTS idx_club_members_club_role ON calendar_club_members(club_id, role);

-- Composite index for user and club (unique membership)
CREATE INDEX IF NOT EXISTS idx_club_members_user_club ON calendar_club_members(user_id, club_id);

-- ===== CALENDAR_EVENTS TABLE INDEXES (Additional) =====
SELECT 'Adding additional indexes to calendar_events table...' as status;

-- Index on title for search operations
CREATE INDEX IF NOT EXISTS idx_events_title ON calendar_events(title);

-- Index on start_date for date filtering
CREATE INDEX IF NOT EXISTS idx_events_start_date ON calendar_events(start_date);

-- Index on end_date for date filtering
CREATE INDEX IF NOT EXISTS idx_events_end_date ON calendar_events(end_date);

-- Index on privacy for filtering
CREATE INDEX IF NOT EXISTS idx_events_privacy ON calendar_events(privacy);

-- Index on created_by for user's events
CREATE INDEX IF NOT EXISTS idx_events_created_by ON calendar_events(created_by);

-- Composite index for calendar and date range
CREATE INDEX IF NOT EXISTS idx_events_calendar_dates ON calendar_events(calendar_id, start_date, end_date);

-- Composite index for title and description searches
CREATE INDEX IF NOT EXISTS idx_events_title_desc ON calendar_events(title, description(100));

-- Index for date range queries (common for calendar views)
CREATE INDEX IF NOT EXISTS idx_events_date_range ON calendar_events(start_date, end_date, privacy);

-- ===== CLUB_OWNERSHIP_VERIFICATIONS TABLE INDEXES =====
SELECT 'Adding indexes to club_ownership_verifications table...' as status;

-- Index on club_id for club verification queries
CREATE INDEX IF NOT EXISTS idx_ownership_club_id ON club_ownership_verifications(club_id);

-- Index on user_id for user verification queries
CREATE INDEX IF NOT EXISTS idx_ownership_user_id ON club_ownership_verifications(user_id);

-- Index on status for filtering by verification status
CREATE INDEX IF NOT EXISTS idx_ownership_status ON club_ownership_verifications(status);

-- Index on requested_at for sorting
CREATE INDEX IF NOT EXISTS idx_ownership_requested_at ON club_ownership_verifications(requested_at);

-- Index on reviewed_at for sorting
CREATE INDEX IF NOT EXISTS idx_ownership_reviewed_at ON club_ownership_verifications(reviewed_at);

-- Composite index for status and requested date
CREATE INDEX IF NOT EXISTS idx_ownership_status_date ON club_ownership_verifications(status, requested_at);

-- Show final index status for all calendar tables
SELECT 'Final index status for calendar tables:' as status;

SELECT 'calendar_clubs indexes:' as table_name;
SELECT 
    INDEX_NAME, 
    COLUMN_NAME, 
    NON_UNIQUE,
    INDEX_TYPE,
    CARDINALITY
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'calendar_clubs' 
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT 'calendar_venues indexes:' as table_name;
SELECT 
    INDEX_NAME, 
    COLUMN_NAME, 
    NON_UNIQUE,
    INDEX_TYPE,
    CARDINALITY
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'calendar_venues' 
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT 'calendars indexes:' as table_name;
SELECT 
    INDEX_NAME, 
    COLUMN_NAME, 
    NON_UNIQUE,
    INDEX_TYPE,
    CARDINALITY
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'calendars' 
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT 'calendar_club_members indexes:' as table_name;
SELECT 
    INDEX_NAME, 
    COLUMN_NAME, 
    NON_UNIQUE,
    INDEX_TYPE,
    CARDINALITY
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'calendar_club_members' 
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT 'Calendar performance indexes migration completed!' as status;
