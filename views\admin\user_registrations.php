<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <?php if (isset($view_mode) && $view_mode === 'all'): ?>
                <h1 class="h2 mb-0"><?php echo $title; ?></h1>
                <p class="text-muted mb-0">Optimized for managing thousands of registrations</p>
            <?php else: ?>
                <h1 class="h2 mb-0">Registrations for <?php echo $user->name; ?></h1>
                <p class="text-muted mb-0">Optimized registration management</p>
            <?php endif; ?>
        </div>
        <div class="col-4 col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
                <a href="<?php echo BASE_URL; ?>/admin/createRegistration<?php echo isset($selected_show_id) && $selected_show_id > 0 ? '/' . $selected_show_id : ''; ?>" class="btn btn-primary me-2 d-none d-sm-inline">
                    <i class="fas fa-plus me-2"></i> New Registration
                </a>
            <?php endif; ?>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2 d-none d-sm-inline"></i> Back
            </a>
        </div>
    </div>

    <?php flash('admin_message'); ?>

    <!-- Registration Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Registration Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm registration-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">All Registrations</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['registration_counts']['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Registrations</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm registration-overview-card" 
                                 data-filter="upcoming" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Upcoming</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['registration_counts']['upcoming'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Upcoming Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm registration-overview-card" 
                                 data-filter="past" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Past</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['registration_counts']['past'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Past Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm registration-overview-card" 
                                 data-filter="pending_payment" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Pending Payment</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['registration_counts']['pending_payment'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Need Payment</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($view_mode) && $view_mode === 'user'): ?>
    <!-- User Information Sidebar - Only shown in user view mode -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">User Information</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <?php 
                        // Use our new helper function to display the profile image
                        $imageUrl = getUserProfileImageUrl($user->id);
                        if ($imageUrl) : 
                        ?>
                            <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="Profile" class="rounded-circle me-3" width="64" height="64">
                        <?php else : ?>
                            <i class="fas fa-user-circle me-3 text-secondary" style="font-size: 4rem;"></i>
                        <?php endif; ?>
                        <div>
                            <h6 class="mb-1"><?php echo $user->name; ?></h6>
                            <small class="text-muted"><?php echo $user->email; ?></small>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="<?php echo BASE_URL; ?>/admin/editUser/<?php echo $user->id; ?>" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit me-1"></i> Edit User
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
    <?php else: ?>
    <div class="row mb-4">
        <div class="col-12">
    <?php endif; ?>

    <!-- Registration Details Section (Lazy Loaded) -->
    <div class="registration-section" id="registration-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">Registration Details</span>
                        <span class="badge bg-secondary" id="registration-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closeRegistrationSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-registrations" class="form-label">Search Registrations</label>
                        <input type="text" class="form-control" id="search-registrations" 
                               placeholder="Search by show, vehicle, or category...">
                    </div>
                    <div class="col-md-2">
                        <label for="show-filter" class="form-label">Show</label>
                        <select class="form-select" id="show-filter">
                            <option value="">All Shows</option>
                            <?php if (!empty($data['available_shows'])): ?>
                                <?php foreach ($data['available_shows'] as $show): ?>
                                    <option value="<?php echo $show->id; ?>"><?php echo $show->name; ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="all">All Status</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="past">Past</option>
                            <option value="pending_payment">Pending Payment</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-registrations" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-registrations">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchRegistrations()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearRegistrationSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportRegistrations()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-registrations">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading registrations...</p>
            </div>
            
            <!-- Registrations Content (Will be populated via AJAX) -->
            <div id="registrations-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
const userId = <?php echo isset($user) ? $user->id : 'null'; ?>;
const viewMode = '<?php echo $view_mode ?? 'all'; ?>';

document.addEventListener('DOMContentLoaded', function() {
    // Registration overview card click handlers
    document.querySelectorAll('.registration-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadRegistrationSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-registrations').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchRegistrations();
        }
    });

    // Filter change handlers
    document.getElementById('show-filter').addEventListener('change', searchRegistrations);
    document.getElementById('status-filter').addEventListener('change', searchRegistrations);
    document.getElementById('per-page-registrations').addEventListener('change', searchRegistrations);
});

function loadRegistrationSection(filter = 'all') {
    // Show the registration section
    const section = document.getElementById('registration-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        document.getElementById('status-filter').value = filter;
    }

    // Load registrations
    loadRegistrations(1);
}

function closeRegistrationSection() {
    const section = document.getElementById('registration-section');
    section.style.display = 'none';
}

function loadRegistrations(page = 1) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-registrations').value;
    const showFilter = document.getElementById('show-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    const perPage = document.getElementById('per-page-registrations').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        show_filter: showFilter,
        status_filter: statusFilter
    });

    // Build URL based on view mode
    let url;
    if (viewMode === 'user' && userId) {
        url = `<?php echo BASE_URL; ?>/admin/loadUserRegistrations/${userId}?` + params.toString();
    } else {
        url = `<?php echo BASE_URL; ?>/admin/loadAllRegistrations?` + params.toString();
    }

    // Make AJAX request
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderRegistrations(data);
        } else {
            showRegistrationError(data.error || 'Failed to load registrations');
        }
    })
    .catch(error => {
        console.error('Error loading registrations:', error);
        showRegistrationError('Network error occurred');
    });
}

function searchRegistrations() {
    loadRegistrations(1);
}

function clearRegistrationSearch() {
    document.getElementById('search-registrations').value = '';
    document.getElementById('show-filter').value = '';
    document.getElementById('status-filter').value = 'all';
    loadRegistrations(1);
}

function renderRegistrations(data) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render registrations table and pagination
    let html = '';

    if (data.registrations.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No registrations found.</p></div>';
    } else {
        html = renderRegistrationsTable(data.registrations, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update registration count display
    document.getElementById('registration-count-display').textContent = data.pagination.total_registrations.toLocaleString();
}

function renderRegistrationsTable(registrations, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>ID</th><th>Show</th>';
    if (viewMode === 'all') {
        html += '<th>Owner</th>';
    }
    html += '<th>Vehicle</th><th>Category</th><th>Date</th><th>Status</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    registrations.forEach(registration => {
        html += '<tr>';
        html += '<td><strong>#' + registration.id + '</strong></td>';
        html += '<td>' + registration.show_name + '</td>';

        if (viewMode === 'all') {
            html += '<td>' + (registration.owner_name || 'Unknown') + '</td>';
        }

        html += '<td>';
        html += '<strong>' + registration.year + ' ' + registration.make + ' ' + registration.model + '</strong>';
        if (registration.color) {
            html += '<br><small class="text-muted">' + registration.color + '</small>';
        }
        html += '</td>';

        html += '<td>' + (registration.category_name || 'N/A') + '</td>';

        html += '<td>';
        if (registration.start_date) {
            html += formatDate(registration.start_date);
        } else {
            html += 'N/A';
        }
        html += '</td>';

        html += '<td>' + getStatusBadge(registration) + '</td>';
        html += '<td>' + getRegistrationActions(registration.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderRegistrationPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_registrations.toLocaleString()} registrations`;
    html += '</div>';

    return html;
}

function getStatusBadge(registration) {
    const now = new Date();
    const startDate = new Date(registration.start_date);

    if (registration.payment_status === 'pending') {
        return '<span class="badge bg-warning text-dark">Pending Payment</span>';
    } else if (startDate > now) {
        return '<span class="badge bg-success">Upcoming</span>';
    } else {
        return '<span class="badge bg-secondary">Past</span>';
    }
}

function getRegistrationActions(registrationId) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/admin/viewRegistration/${registrationId}" class="btn btn-info">
                <i class="fas fa-eye"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/editRegistration/${registrationId}" class="btn btn-primary">
                <i class="fas fa-edit"></i>
            </a>
            <button class="btn btn-danger" onclick="deleteRegistration(${registrationId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
}

function renderRegistrationPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadRegistrations(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showRegistrationError(message) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function deleteRegistration(registrationId) {
    if (confirm('Are you sure you want to delete this registration?')) {
        // Implementation for registration deletion
        alert('Registration deletion functionality would be implemented here');
    }
}

function exportRegistrations() {
    // Get current filter values
    const search = document.getElementById('search-registrations').value;
    const showFilter = document.getElementById('show-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        show_filter: showFilter,
        status_filter: statusFilter,
        export: 'csv'
    });

    let exportUrl;
    if (viewMode === 'user' && userId) {
        exportUrl = `<?php echo BASE_URL; ?>/admin/exportUserRegistrations/${userId}?` + params.toString();
    } else {
        exportUrl = `<?php echo BASE_URL; ?>/admin/exportAllRegistrations?` + params.toString();
    }

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
