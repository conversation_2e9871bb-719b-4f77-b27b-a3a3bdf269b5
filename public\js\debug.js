/**
 * Debug Helper Script
 * This script helps diagnose JavaScript loading and execution issues
 */

(function() {
    console.log('Debug script loaded and running');
    
    // Function to check and initialize Bootstrap
    function checkAndInitializeBootstrap() {
        console.log('Checking Bootstrap availability...');
        
        // Check if Bootstrap is loaded
        if (typeof bootstrap === 'undefined') {
            console.warn('Bootstrap is not yet defined. Waiting for it to load...');
            
            // Check if the Bootstrap script tag exists
            const bootstrapScripts = document.querySelectorAll('script[src*="bootstrap"]');
            console.log('Found ' + bootstrapScripts.length + ' Bootstrap script tags:', bootstrapScripts);
            
            // If no Bootstrap script found, try to load it dynamically
            if (bootstrapScripts.length === 0) {
                console.log('No Bootstrap script found. Attempting to load Bootstrap dynamically...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js';
                script.integrity = 'sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN';
                script.crossOrigin = 'anonymous';
                
                script.onload = function() {
                    console.log('Bootstrap loaded successfully via dynamic loading!');
                    initializeBootstrapComponents();
                };
                
                script.onerror = function() {
                    console.error('Failed to load Bootstrap dynamically.');
                };
                
                document.body.appendChild(script);
            } else {
                // Bootstrap script exists, wait for it to load
                console.log('Bootstrap script found, waiting for it to load...');
                waitForBootstrap();
            }
        } else {
            console.log('Bootstrap is properly loaded.');
            initializeBootstrapComponents();
        }
    }
    
    // Function to wait for Bootstrap to be available
    function waitForBootstrap() {
        let attempts = 0;
        const maxAttempts = 50; // Wait up to 5 seconds (50 * 100ms)
        
        const checkBootstrap = setInterval(function() {
            attempts++;
            
            if (typeof bootstrap !== 'undefined') {
                console.log('Bootstrap is now available after ' + attempts + ' attempts');
                clearInterval(checkBootstrap);
                initializeBootstrapComponents();
            } else if (attempts >= maxAttempts) {
                console.error('Bootstrap failed to load after ' + maxAttempts + ' attempts');
                clearInterval(checkBootstrap);
                // Try manual dropdown initialization as fallback
                initializeManualDropdowns();
            }
        }, 100);
    }
    
    // Wait for DOM to be ready before checking Bootstrap
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAndInitializeBootstrap);
    } else {
        // DOM is already ready
        checkAndInitializeBootstrap();
    }
    
    // Function to initialize Bootstrap components
    function initializeBootstrapComponents() {
        console.log('Initializing Bootstrap components');
        
        // Initialize dropdowns
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle, [data-bs-toggle="dropdown"]');
        console.log('Found ' + dropdownToggles.length + ' dropdown toggles');
        
        dropdownToggles.forEach(function(toggle, index) {
            console.log('Initializing dropdown #' + (index + 1), toggle);
            
            // Ensure the toggle has the correct attribute
            if (!toggle.hasAttribute('data-bs-toggle')) {
                toggle.setAttribute('data-bs-toggle', 'dropdown');
            }
            
            // For dropdowns in tables or cards, set boundary to viewport
            if (toggle.closest('.table-responsive') || toggle.closest('.card-body')) {
                toggle.setAttribute('data-bs-boundary', 'viewport');
                toggle.setAttribute('data-bs-display', 'static');
            }
            
            // Create a new dropdown instance
            try {
                new bootstrap.Dropdown(toggle);
                console.log('Dropdown #' + (index + 1) + ' initialized successfully');
            } catch (error) {
                console.error('Failed to initialize dropdown #' + (index + 1) + ':', error);
            }
        });
    }
    
    // Manual dropdown initialization as fallback
    function initializeManualDropdowns() {
        console.log('Initializing manual dropdowns as fallback');
        
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle, [data-bs-toggle="dropdown"]');
        console.log('Found ' + dropdownToggles.length + ' dropdown toggles for manual initialization');
        
        dropdownToggles.forEach(function(toggle) {
            // Ensure the toggle has the correct attributes
            if (!toggle.hasAttribute('data-bs-toggle')) {
                toggle.setAttribute('data-bs-toggle', 'dropdown');
            }
            
            // Set boundary to viewport for proper positioning
            toggle.setAttribute('data-bs-boundary', 'viewport');
            toggle.setAttribute('data-bs-display', 'static');
            
            // Add click handler
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Find the dropdown menu
                const dropdownMenu = this.nextElementSibling;
                if (!dropdownMenu || !dropdownMenu.classList.contains('dropdown-menu')) {
                    console.warn('No dropdown menu found for toggle:', this);
                    return;
                }
                
                // Toggle the show class
                const isShown = dropdownMenu.classList.contains('show');
                
                // Close all other dropdowns first
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    if (menu !== dropdownMenu) {
                        menu.classList.remove('show');
                    }
                });
                
                // Toggle this dropdown
                if (isShown) {
                    dropdownMenu.classList.remove('show');
                } else {
                    dropdownMenu.classList.add('show');
                    
                    // Position the dropdown
                    const toggleRect = toggle.getBoundingClientRect();
                    
                    // For split button dropdowns, align to the right edge of the toggle
                    if (toggle.classList.contains('dropdown-toggle-split')) {
                        dropdownMenu.style.left = 'auto';
                        dropdownMenu.style.right = '0';
                    }
                    
                    // Ensure the dropdown doesn't go off-screen
                    setTimeout(function() {
                        const menuRect = dropdownMenu.getBoundingClientRect();
                        
                        if (menuRect.right > window.innerWidth) {
                            dropdownMenu.style.left = 'auto';
                            dropdownMenu.style.right = '0';
                        }
                        
                        if (menuRect.bottom > window.innerHeight) {
                            dropdownMenu.style.top = 'auto';
                            dropdownMenu.style.bottom = '100%';
                        }
                    }, 0);
                }
                
                // Add click handler to close when clicking outside
                if (!isShown) {
                    setTimeout(function() {
                        const closeHandler = function(event) {
                            if (!dropdownMenu.contains(event.target) && !toggle.contains(event.target)) {
                                dropdownMenu.classList.remove('show');
                                document.removeEventListener('click', closeHandler);
                            }
                        };
                        
                        document.addEventListener('click', closeHandler);
                    }, 0);
                }
            });
        });
    }
    
    // Initialize debugging features after DOM is ready
    function initializeDebugging() {
        // Log all script tags on the page
        const allScripts = document.querySelectorAll('script');
        console.log('All script tags on the page:', allScripts);
        
        // Check for 404 errors
        if (typeof window.fetch !== 'undefined') {
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                console.log('Fetch request to:', url);
                return originalFetch.apply(this, arguments)
                    .then(response => {
                        if (!response.ok) {
                            console.warn('Fetch request failed:', url, response.status);
                        }
                        return response;
                    })
                    .catch(error => {
                        console.error('Fetch error:', url, error);
                        throw error;
                    });
            };
        }
        
        // Add event listener for dropdown events
        document.addEventListener('show.bs.dropdown', function(e) {
            console.log('Dropdown show event fired:', e.target);
        });
        
        document.addEventListener('shown.bs.dropdown', function(e) {
            console.log('Dropdown shown event fired:', e.target);
        });
        
        document.addEventListener('hide.bs.dropdown', function(e) {
            console.log('Dropdown hide event fired:', e.target);
        });
        
        document.addEventListener('hidden.bs.dropdown', function(e) {
            console.log('Dropdown hidden event fired:', e.target);
        });
        
        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Window load event fired - page fully loaded');
        });
    }
    
    // Initialize debugging features
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDebugging);
    } else {
        initializeDebugging();
    }
})();