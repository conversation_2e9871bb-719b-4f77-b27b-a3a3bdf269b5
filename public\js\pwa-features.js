/**
 * PWA Features Module for Rowan Elite Rides Events & Shows
 * Handles installation, notifications, offline functionality, and enhanced mobile features
 */

// Prevent duplicate loading
if (typeof window.PWAFeatures !== 'undefined') {
    console.warn('PWAFeatures already loaded, skipping duplicate initialization');
} else {

class PWAFeatures {
    constructor() {
        this.version = '3.64.0-dual-camera-banner-fix';
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.notificationPermission = 'default';
        console.log('[PWA] PWAFeatures loaded - version:', this.version);
        this.isOnline = navigator.onLine;
        this.db = null;
        
        // Camera/QR Scanner state management
        this.currentStream = null;
        this.currentModal = null;

        // Audio system for QR beep sounds
        this.audioContext = null;
        this.audioInitialized = false;
        
        this.init();
    }
    
    async init() {
        console.log('[PWA] Initializing PWA features...');
        
        // Register service worker
        await this.registerServiceWorker();
        
        // Initialize IndexedDB
        await this.initIndexedDB();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Check installation status
        this.checkInstallationStatus();
        
        // Initialize push notifications
        this.initPushNotifications();
        
        // Setup offline handling - DISABLED
        // this.setupOfflineHandling();
        
        // Initialize enhanced mobile features
        this.initMobileFeatures();
        
        console.log('[PWA] PWA features initialized');
    }
    
    // Service Worker Registration
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });
                
                console.log('[PWA] Service Worker registered:', registration.scope);
                
                // Handle updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });
                
                return registration;
            } catch (error) {
                console.error('[PWA] Service Worker registration failed:', error);
            }
        }
    }
    
    // IndexedDB Initialization
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('REREventsDB', 1);
            
            request.onerror = () => {
                console.error('[PWA] IndexedDB failed to open');
                reject(request.error);
            };
            
            request.onsuccess = () => {
                this.db = request.result;
                console.log('[PWA] IndexedDB initialized');
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores for offline data
                if (!db.objectStoreNames.contains('pendingRegistrations')) {
                    db.createObjectStore('pendingRegistrations', { keyPath: 'id' });
                }
                if (!db.objectStoreNames.contains('pendingPayments')) {
                    db.createObjectStore('pendingPayments', { keyPath: 'id' });
                }
                if (!db.objectStoreNames.contains('pendingScores')) {
                    db.createObjectStore('pendingScores', { keyPath: 'id' });
                }
                if (!db.objectStoreNames.contains('cachedData')) {
                    db.createObjectStore('cachedData', { keyPath: 'key' });
                }
                if (!db.objectStoreNames.contains('offlineQueue')) {
                    db.createObjectStore('offlineQueue', { keyPath: 'id', autoIncrement: true });
                }
            };
        });
    }
    
    // Event Listeners Setup
    setupEventListeners() {
        // Install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });
        
        // App installed
        window.addEventListener('appinstalled', () => {
            console.log('[PWA] App installed');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showWelcomeMessage();
        });
        
        // Online/Offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStatus();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOfflineStatus();
        });
        
        // Visibility change for background sync and modal cleanup
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isOnline) {
                this.syncPendingData();
            }
            
            // Force close camera/QR modals when app becomes visible again
            // This helps with PWA modal issues where modals might get stuck
            if (!document.hidden) {
                // Small delay to ensure proper cleanup
                setTimeout(() => {
                    const hasOpenModals = document.querySelector('.camera-modal') || document.querySelector('.qr-scanner-modal');
                    if (hasOpenModals && this.currentStream) {
                        console.log('[PWA] Detected stuck modals on visibility change, force closing');
                        this.forceCloseAllModals();
                    }
                }, 100);
            }
        });
        
        // Page unload handler to clean up camera streams
        window.addEventListener('beforeunload', () => {
            if (this.currentStream) {
                console.log('[PWA] Page unloading, cleaning up camera streams');
                this.forceCloseAllModals();
            }
        });
        
        // Page hide handler for mobile PWA
        window.addEventListener('pagehide', () => {
            if (this.currentStream) {
                console.log('[PWA] Page hiding, cleaning up camera streams');
                this.forceCloseAllModals();
            }
        });
    }
    
    // Installation Management
    checkInstallationStatus() {
        // Check if running as installed app
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('[PWA] Running as installed app');
        }
    }
    
    showInstallButton() {
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.style.display = 'block';
            installButton.addEventListener('click', () => this.promptInstall());
        } else {
            // Create install button if it doesn't exist
            this.createInstallButton();
        }
    }
    
    createInstallButton() {
        const button = document.createElement('button');
        button.id = 'pwa-install-button';
        button.className = 'btn btn-primary pwa-install-btn';
        button.innerHTML = '<i class="fas fa-download"></i> Install App';
        button.addEventListener('click', () => this.promptInstall());
        
        // Add to navigation or header
        const nav = document.querySelector('.navbar') || document.querySelector('header');
        if (nav) {
            nav.appendChild(button);
        }
    }
    
    async promptInstall() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('[PWA] User accepted install prompt');
            } else {
                console.log('[PWA] User dismissed install prompt');
            }
            
            this.deferredPrompt = null;
        }
    }
    
    hideInstallButton() {
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.style.display = 'none';
        }
    }
    
    showWelcomeMessage() {
        this.showNotification('Welcome!', 'App installed successfully. You can now access RER Events from your home screen!', 'success');
    }
    
    // Push Notifications
    async initPushNotifications() {
        // Only initialize push notifications if user is logged in
        if (!window.PWA_CONFIG || !window.PWA_CONFIG.userId) {
            console.log('[PWA] Skipping push notifications - user not logged in');
            return;
        }

        if ('Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window) {
            this.notificationPermission = Notification.permission;

            if (this.notificationPermission === 'default') {
                this.showNotificationPrompt();
            } else if (this.notificationPermission === 'granted') {
                await this.subscribeToPush();
            }
        }
    }
    
    showNotificationPrompt() {
        const promptDiv = document.createElement('div');
        promptDiv.className = 'notification-prompt alert alert-info';
        promptDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>Stay Updated!</strong> Enable notifications for event reminders and updates.
                </div>
                <div>
                    <button class="btn btn-sm btn-primary me-2" onclick="pwaFeatures.requestNotificationPermission()">
                        <i class="fas fa-bell"></i> Enable
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">
                        Later
                    </button>
                </div>
            </div>
        `;
        
        document.body.insertBefore(promptDiv, document.body.firstChild);
    }
    
    async requestNotificationPermission() {
        // Check if user is logged in before requesting permission
        if (!window.PWA_CONFIG || !window.PWA_CONFIG.userId) {
            console.log('[PWA] Cannot request notification permission - user not logged in');
            this.showNotification('Login Required', 'Please log in to enable notifications', 'warning');
            return;
        }

        try {
            const permission = await Notification.requestPermission();
            this.notificationPermission = permission;

            if (permission === 'granted') {
                await this.subscribeToPush();
                this.showNotification('Notifications Enabled', 'You\'ll now receive important updates and reminders!', 'success');
            }

            // Remove prompt
            const prompt = document.querySelector('.notification-prompt');
            if (prompt) prompt.remove();

        } catch (error) {
            console.error('[PWA] Notification permission request failed:', error);
        }
    }
    
    async subscribeToPush() {
        try {
            const registration = await navigator.serviceWorker.ready;
            
            // Check if already subscribed
            let subscription = await registration.pushManager.getSubscription();
            
            if (!subscription) {
                // Get VAPID public key
                const vapidKey = await this.getVAPIDPublicKey();
                
                if (!vapidKey) {
                    throw new Error('VAPID public key not available');
                }
                
                // Subscribe to push notifications
                subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: this.urlBase64ToUint8Array(vapidKey)
                });
            }
            
            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
            
            console.log('[PWA] Push subscription successful');
            
        } catch (error) {
            console.error('[PWA] Push subscription failed:', error);
        }
    }
    
    async getVAPIDPublicKey() {
        try {
            const response = await fetch('/api/notifications/vapid-key');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success || !data.publicKey) {
                throw new Error('Invalid VAPID key response');
            }
            
            return data.publicKey;
        } catch (error) {
            console.error('[PWA] Failed to get VAPID key:', error);
            // Return null instead of fallback key to prevent invalid subscriptions
            return null;
        }
    }
    
    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/api/notifications/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    subscription: subscription.toJSON(),
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                })
            });

            if (!response.ok) {
                throw new Error('Failed to send subscription to server');
            }

        } catch (error) {
            console.error('[PWA] Failed to send subscription to server:', error);
        }
    }

    /**
     * Enable push notifications after user login
     * This method can be called from login success handlers
     */
    async enablePushNotificationsAfterLogin() {
        console.log('[PWA] Enabling push notifications after login');

        if ('Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window) {
            this.notificationPermission = Notification.permission;

            if (this.notificationPermission === 'default') {
                this.showNotificationPrompt();
            } else if (this.notificationPermission === 'granted') {
                await this.subscribeToPush();
            }
        }
    }
    
    urlBase64ToUint8Array(base64String) {
        if (!base64String || typeof base64String !== 'string') {
            throw new Error('Invalid base64 string provided to urlBase64ToUint8Array');
        }
        
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }
    
    // Offline Functionality
    setupOfflineHandling() {
        // Show offline indicator
        this.updateOnlineStatus();
        
        // Intercept form submissions for offline handling
        this.interceptFormSubmissions();
        
        // Setup periodic sync attempts
        setInterval(() => {
            if (this.isOnline) {
                this.syncPendingData();
            }
        }, 30000); // Every 30 seconds
    }
    
    handleOnlineStatus() {
        console.log('[PWA] Back online');
        this.updateOnlineStatus();
        this.syncPendingData();
        this.showNotification('Back Online', 'Syncing your offline actions...', 'success');
    }
    
    handleOfflineStatus() {
        console.log('[PWA] Gone offline');
        this.updateOnlineStatus();
        this.showNotification('Offline Mode', 'You can continue using the app. Changes will sync when you\'re back online.', 'warning');
    }
    
    updateOnlineStatus() {
        // Online status indicator disabled
        return;
    }

    createOnlineStatusIndicator() {
        // Online status indicator disabled
        return;
    }
    
    interceptFormSubmissions() {
        document.addEventListener('submit', async (event) => {
            if (!this.isOnline) {
                event.preventDefault();
                await this.handleOfflineFormSubmission(event.target);
            }
        });
    }
    
    async handleOfflineFormSubmission(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Determine form type and handle accordingly
        const formType = this.determineFormType(form);
        
        if (formType) {
            await this.queueOfflineAction(formType, data);
            this.showNotification('Saved Offline', 'Your submission will be processed when you\'re back online.', 'info');
        }
    }
    
    determineFormType(form) {
        const action = form.action || '';
        const className = form.className || '';
        
        if (action.includes('registration') || className.includes('registration')) {
            return 'registration';
        } else if (action.includes('payment') || className.includes('payment')) {
            return 'payment';
        } else if (action.includes('score') || className.includes('judging')) {
            return 'scoring';
        }
        
        return 'general';
    }
    
    async queueOfflineAction(type, data) {
        if (!this.db) return;
        
        const transaction = this.db.transaction(['offlineQueue'], 'readwrite');
        const store = transaction.objectStore('offlineQueue');
        
        const queueItem = {
            type: type,
            data: data,
            timestamp: new Date().toISOString(),
            synced: false
        };
        
        await store.add(queueItem);
    }
    
    async syncPendingData() {
        if (!this.db || !this.isOnline) return;
        
        try {
            const transaction = this.db.transaction(['offlineQueue'], 'readwrite');
            const store = transaction.objectStore('offlineQueue');
            const request = store.getAll();
            
            request.onsuccess = async () => {
                const pendingItems = request.result.filter(item => !item.synced);
                
                for (const item of pendingItems) {
                    try {
                        await this.syncItem(item);
                        
                        // Mark as synced
                        item.synced = true;
                        await store.put(item);
                        
                    } catch (error) {
                        console.error('[PWA] Failed to sync item:', error);
                    }
                }
                
                if (pendingItems.length > 0) {
                    this.showNotification('Sync Complete', `${pendingItems.length} offline actions synced successfully.`, 'success');
                }
            };
            
        } catch (error) {
            console.error('[PWA] Sync failed:', error);
        }
    }
    
    async syncItem(item) {
        const endpoint = this.getEndpointForType(item.type);
        
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(item.data)
        });
        
        if (!response.ok) {
            throw new Error(`Sync failed for ${item.type}`);
        }
        
        return response.json();
    }
    
    getEndpointForType(type) {
        const endpoints = {
            'registration': '/api/registrations',
            'payment': '/api/payments',
            'scoring': '/api/judging/scores',
            'general': '/api/offline-sync'
        };
        
        return endpoints[type] || endpoints.general;
    }
    
    // Enhanced Mobile Features
    initMobileFeatures() {
        this.initCameraFeatures();
        this.initQRScanner();
        this.initGestureSupport();
        this.initBiometricAuth();
        this.initWebShare();
    }
    
    // Camera Integration
    initCameraFeatures() {
        const cameraButtons = document.querySelectorAll('[data-camera-capture]');
        // Camera buttons found and initialized
        
        cameraButtons.forEach(button => {
            this.attachCameraListener(button);
        });

        // Watch for dynamically added camera buttons (like FAB buttons)
        this.watchForDynamicCameraButtons();

        // Also check for FAB buttons specifically after a delay
        setTimeout(() => {
            const fabButtons = document.querySelectorAll('.fab-action[data-camera-capture]');
            console.log('[PWA] Found', fabButtons.length, 'FAB camera buttons');
            fabButtons.forEach(button => {
                console.log('[PWA] FAB camera button:', button, 'visible:', button.offsetParent !== null);
                // Re-attach listeners to any FAB buttons that might have been missed
                if (!button.hasAttribute('data-camera-listener')) {
                    button.addEventListener('click', (e) => {
                        console.log('[PWA] FAB Camera button clicked, target:', button.dataset.cameraCapture);
                        e.preventDefault();
                        this.openCamera(button.dataset.cameraCapture);
                    });
                    button.setAttribute('data-camera-listener', 'true');
                }
            });
        }, 3000);
    }

    // Watch for dynamically added camera buttons
    watchForDynamicCameraButtons() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is a camera button
                        if (node.hasAttribute && node.hasAttribute('data-camera-capture')) {
                            console.log('[PWA] Dynamic camera button detected:', node);
                            this.attachCameraListener(node);
                        }

                        // Check if the added node contains camera buttons
                        const cameraButtons = node.querySelectorAll ? node.querySelectorAll('[data-camera-capture]') : [];
                        cameraButtons.forEach(button => {
                            console.log('[PWA] Dynamic camera button found in added content:', button);
                            this.attachCameraListener(button);
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Attach camera listener to a button
    attachCameraListener(button) {
        if (!button.hasAttribute('data-camera-listener')) {
            button.addEventListener('click', (e) => {
                console.log('[PWA] Dynamic camera button clicked, target:', button.dataset.cameraCapture);
                e.preventDefault();
                this.openCamera(button.dataset.cameraCapture);
            });
            button.setAttribute('data-camera-listener', 'true');
            console.log('[PWA] Camera listener attached to:', button);
        }
    }

    // Helper method to get site logo from database
    async getSiteLogo() {
        try {
            const response = await fetch('/api/getSiteLogo');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.logo_path) {
                    return data.logo_path;
                }
            }
        } catch (error) {
            console.log('[PWA] Failed to load site logo from database, using fallback');
        }
        // Fallback logo
        return '/uploads/branding/logo_1751468505_rides_logo.png';
    }
    
    async openCamera(targetInput) {
        console.log('[PWA] openCamera called with targetInput:', targetInput);

        if (!('mediaDevices' in navigator) || !('getUserMedia' in navigator.mediaDevices)) {
            console.error('[PWA] Camera not supported');
            this.showNotification('Camera Not Available', 'Camera access is not supported on this device.', 'error');
            return;
        }

        try {
            console.log('[PWA] Requesting camera access...');
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                }
            });

            console.log('[PWA] Camera access granted, showing modal...');
            await this.showCameraModal(stream, targetInput);

        } catch (error) {
            console.error('[PWA] Camera access failed:', error);
            this.showNotification('Camera Access Denied', 'Please allow camera access to take photos.', 'error');
        }
    }
    
    async showCameraModal(stream, targetInput) {
        console.log('[PWA] showCameraModal called');

        // Get site logo from database
        const siteLogo = await this.getSiteLogo();
        console.log('[PWA] Site logo loaded:', siteLogo);

        // Remove any existing backdrops first to prevent duplicates
        const existingBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        existingBackdrops.forEach(bd => bd.remove());

        // Create backdrop to block page content
        const backdrop = document.createElement('div');
        backdrop.className = 'camera-modal-backdrop';
        backdrop.id = 'camera-backdrop';
        backdrop.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 1.0) !important;
            z-index: 999997 !important;
            pointer-events: none !important;
        `;
        document.body.appendChild(backdrop);
        console.log('[PWA] Camera backdrop created with ID:', backdrop.id);





        const modal = document.createElement('div');
        modal.className = 'camera-modal';
        modal.innerHTML = `
            <div class="camera-container">
                <div class="camera-banner" id="camera-banner-content">
                    <!-- Banner content will be loaded dynamically -->
                </div>
                <div class="camera-viewfinder">
                    <video id="camera-video" autoplay playsinline></video>
                    <canvas id="camera-canvas" style="display: none;"></canvas>
                </div>
                <div class="camera-controls">
                    <button class="btn btn-danger camera-cancel-btn">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button class="btn btn-primary camera-capture-btn" data-target="${targetInput}">
                        <i class="fas fa-camera"></i> Capture
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);



        const video = document.getElementById('camera-video');
        video.srcObject = stream;

        this.currentStream = stream;
        this.currentModal = modal;
        
        // Add event listeners for modal controls
        const cancelBtn = modal.querySelector('.camera-cancel-btn');
        const captureBtn = modal.querySelector('.camera-capture-btn');
        
        cancelBtn.addEventListener('click', () => {
            this.closeCamera();
        });
        
        captureBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Get entity data from the original button that triggered the camera
            const originalButton = document.querySelector(`[data-camera-capture="${targetInput}"]`);
            const entityType = originalButton ? originalButton.dataset.entityType : null;
            const entityId = originalButton ? originalButton.dataset.entityId : null;
            const csrfToken = originalButton ? originalButton.dataset.csrfToken : null;

            this.capturePhoto(targetInput, entityType, entityId, csrfToken);
        });
        
        // Add backdrop click to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeCamera();
            }
        });
        
        // Add escape key to close
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeCamera();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Store escape handler for cleanup
        modal.escapeHandler = escapeHandler;
        
        // Force reload camera-banner script to bypass cache
        const forceReloadBannerScript = () => {
            return new Promise((resolve, reject) => {
                // Remove existing script if present
                const existingScript = document.querySelector('script[src*="camera-banner.js"]');
                if (existingScript) {
                    existingScript.remove();
                }
                
                // Load fresh script with timestamp
                const script = document.createElement('script');
                script.src = '/public/js/camera-banner.js?v=' + Date.now() + '&cache=bust';
                script.onload = () => {
                    console.log('Camera banner script reloaded successfully');
                    resolve();
                };
                script.onerror = () => {
                    console.error('Failed to reload camera banner script');
                    reject(new Error('Script reload failed'));
                };
                document.head.appendChild(script);
            });
        };

        // Initialize banner system
        const bannerContainer = document.getElementById('camera-banner-content');

        // Show fallback logo immediately while banners load
        const showFallbackLogo = () => {
            if (bannerContainer) {
                bannerContainer.innerHTML = `<img src="${siteLogo}" alt="Rowan Elite Rides" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">`;
            }
        };

        // Show logo immediately
        showFallbackLogo();

        // Apply CSS fix for modal background that was covering banners
        const style = document.createElement('style');
        style.textContent = `
            .camera-modal {
                background: transparent !important;
            }
            .camera-banner {
                background: transparent !important;
                overflow: visible !important;
            }
        `;
        document.head.appendChild(style);

        // Initialize banner system
        setTimeout(() => {
            if (window.cameraBanner) {
                // Use existing banner system
                window.cameraBanner.startRotation('camera-banner-content');
            } else {
                // Load banner system
                forceReloadBannerScript().then(() => {
                    setTimeout(() => {
                        if (window.cameraBanner) {
                            window.cameraBanner.startRotation('camera-banner-content');
                        }
                    }, 500);
                }).catch((error) => {
                    // Fallback: Load banners directly from API
                    console.log('Banner script failed, using direct API fallback');
                    this.loadBannersDirectly(bannerContainer);
                });
            }
        }, 100);
    }

    loadBannersDirectly(bannerContainer) {
        fetch('/api/camera-banners.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.banners && data.banners.length > 0) {
                    let currentIndex = 0;
                    const showBanner = () => {
                        const banner = data.banners[currentIndex];
                        bannerContainer.innerHTML = `<img src="${banner.image_url}" alt="${banner.title}" class="logo-banner">`;
                        currentIndex = (currentIndex + 1) % data.banners.length;
                    };
                    showBanner();
                    setInterval(showBanner, 5000);
                }
            })
            .catch(error => {
                console.error('Failed to load banners:', error);
            });
    }

    capturePhoto(targetInput, entityType = null, entityId = null, csrfToken = null) {
        const video = document.getElementById('camera-video');
        const canvas = document.getElementById('camera-canvas');

        if (!video || !canvas) {
            console.error('Camera Error: Camera elements not found');
            return;
        }

        if (!video.videoWidth || !video.videoHeight) {
            console.error('Camera Error: Video not ready');
            return;
        }

        // Add visual feedback for capture
        const captureFlash = document.createElement('div');
        captureFlash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 9999;
            opacity: 0;
            pointer-events: none;
        `;
        document.body.appendChild(captureFlash);

        // Flash effect
        captureFlash.style.opacity = '0.8';
        setTimeout(() => {
            captureFlash.style.opacity = '0';
            setTimeout(() => {
                if (captureFlash.parentNode) {
                    captureFlash.parentNode.removeChild(captureFlash);
                }
            }, 200);
        }, 100);

        const context = canvas.getContext('2d');

        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        context.drawImage(video, 0, 0);

        canvas.toBlob((blob) => {
            if (!blob) {
                console.error('Capture Error: Failed to create photo');
                this.showNotification('Capture Error', 'Failed to capture photo', 'error');
                return;
            }

            const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' });

            // Close camera immediately after capture for better UX
            this.closeCamera();

            // Handle the captured photo
            this.handleCapturedPhoto(file, targetInput, entityType, entityId, csrfToken);
        }, 'image/jpeg', 0.8);
    }
    
    handleCapturedPhoto(file, targetInput, entityType = null, entityId = null, csrfToken = null) {
        // If we have entity data, upload directly to image editor
        if (entityType && entityId) {
            // Show immediate feedback that photo was captured
            this.showNotification('Photo Captured', 'Photo captured! Starting upload...', 'success');

            // Start upload with progress
            this.uploadToImageEditor(file, entityType, entityId, csrfToken);
            return;
        }

        // Otherwise, handle as file input (existing behavior)
        const input = document.querySelector(`[name="${targetInput}"]`);
        if (input && input.type === 'file') {
            // Create a new FileList with the captured photo
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            input.files = dataTransfer.files;

            // Trigger change event
            input.dispatchEvent(new Event('change', { bubbles: true }));

            this.showNotification('Photo Captured', 'Photo captured successfully!', 'success');
        } else {
            console.error('Error: No entity data or file input found');
            this.showNotification('Capture Error', 'Unable to save captured photo', 'error');
        }
    }

    // Upload captured photo directly to image editor
    async uploadToImageEditor(file, entityType, entityId, csrfToken) {
        let progressContainer = null;

        try {
            // Create and show progress indicator
            progressContainer = this.createUploadProgress();
            this.updateUploadProgress(progressContainer, 0, 'Preparing upload...');

            const formData = new FormData();
            formData.append('image', file);
            formData.append('csrf_token', csrfToken);
            formData.append('after_upload', 'browser'); // Return to previous page after upload
            formData.append('pwa_camera_upload', '1'); // Flag to indicate this is a PWA camera upload

            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();

            // Track upload progress
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    this.updateUploadProgress(progressContainer, percentComplete, 'Uploading photo...');
                }
            });

            // Handle completion
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    this.updateUploadProgress(progressContainer, 100, 'Upload complete! Opening editor...');

                    // Small delay to show completion, then follow the server redirect
                    setTimeout(() => {
                        this.removeUploadProgress(progressContainer);

                        // The server will automatically redirect to the image editor
                        // For PWA camera uploads, it will go directly to /image_editor/edit/{imageId}
                        const finalUrl = xhr.responseURL;
                        if (finalUrl && finalUrl !== window.location.href) {
                            window.location.href = finalUrl;
                        } else {
                            // Fallback if no redirect URL is available
                            window.location.href = `/image_editor/${entityType}/${entityId}`;
                        }
                    }, 1000);
                } else {
                    throw new Error(`HTTP ${xhr.status}: ${xhr.statusText}`);
                }
            });

            // Handle errors
            xhr.addEventListener('error', () => {
                throw new Error('Network error during upload');
            });

            // Start the upload
            xhr.open('POST', `/image_editor/upload/${entityType}/${entityId}`);
            xhr.send(formData);

        } catch (error) {
            console.error('Upload error:', error);
            if (progressContainer) {
                this.removeUploadProgress(progressContainer);
            }
            this.showNotification('Upload Failed', error.message, 'error');
        }
    }

    // Create upload progress indicator
    createUploadProgress() {
        // Remove any existing progress indicator
        const existing = document.getElementById('camera-upload-progress');
        if (existing) {
            existing.remove();
        }

        const progressContainer = document.createElement('div');
        progressContainer.id = 'camera-upload-progress';
        progressContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            text-align: center;
            min-width: 250px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        `;

        progressContainer.innerHTML = `
            <div style="margin-bottom: 15px;">
                <i class="fas fa-camera" style="font-size: 24px; color: #007bff;"></i>
            </div>
            <div id="upload-status" style="margin-bottom: 10px; font-weight: bold;">Preparing upload...</div>
            <div style="background: #333; border-radius: 10px; overflow: hidden; margin-bottom: 10px;">
                <div id="upload-progress-bar" style="
                    height: 8px;
                    background: linear-gradient(90deg, #007bff, #0056b3);
                    width: 0%;
                    transition: width 0.3s ease;
                "></div>
            </div>
            <div id="upload-percentage" style="font-size: 14px; color: #ccc;">0%</div>
        `;

        document.body.appendChild(progressContainer);
        return progressContainer;
    }

    // Update upload progress
    updateUploadProgress(container, percentage, status) {
        if (!container) return;

        const progressBar = container.querySelector('#upload-progress-bar');
        const statusText = container.querySelector('#upload-status');
        const percentageText = container.querySelector('#upload-percentage');

        if (progressBar) {
            progressBar.style.width = `${Math.min(percentage, 100)}%`;
        }

        if (statusText) {
            statusText.textContent = status;
        }

        if (percentageText) {
            percentageText.textContent = `${Math.round(percentage)}%`;
        }
    }

    // Remove upload progress indicator
    removeUploadProgress(container) {
        if (container && container.parentNode) {
            container.style.opacity = '0';
            container.style.transform = 'translate(-50%, -50%) scale(0.9)';
            container.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 300);
        }
    }
    
    closeCamera() {
        // Stop all camera tracks first
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => {
                track.stop();
                console.log('[PWA] Camera track stopped:', track.kind);
            });
            this.currentStream = null;
        }

        // Clear video source
        const video = document.getElementById('camera-video');
        if (video) {
            video.srcObject = null;
            video.src = '';
        }

        // Stop banner rotation
        if (window.cameraBanner) {
            window.cameraBanner.stopRotation();
        }

        // Remove modal completely
        if (this.currentModal) {
            // Remove escape key listener if it exists
            if (this.currentModal.escapeHandler) {
                document.removeEventListener('keydown', this.currentModal.escapeHandler);
            }
            
            this.currentModal.remove();
            console.log('[PWA] Camera modal removed');
            this.currentModal = null;
        }

        // Fallback cleanup for any remaining modal
        const modal = document.querySelector('.camera-modal');
        if (modal) {
            modal.remove();
            console.log('[PWA] Fallback camera modal cleanup');
        }

        // Remove all camera backdrops (more reliable than ID-based removal)
        const allBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        if (allBackdrops.length > 0) {
            console.log(`[PWA] Removing ${allBackdrops.length} camera backdrop(s)`);
            allBackdrops.forEach(bd => bd.remove());
        }

        // Clear any remaining references
        this.currentStream = null;
    }
    
    // QR Code Scanner
    initQRScanner() {
        const qrButtons = document.querySelectorAll('[data-qr-scanner]');
        qrButtons.forEach(button => {
            button.addEventListener('click', () => this.openQRScanner());
        });
    }
    
    async openQRScanner() {
        // Initialize audio context (user interaction required for mobile)
        this.initializeAudio();

        if (!('BarcodeDetector' in window)) {
            // Fallback to library-based QR scanning
            await this.loadQRLibrary();
        }

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' }
            });

            await this.showQRScannerModal(stream);

        } catch (error) {
            console.error('[PWA] QR Scanner failed:', error);
            this.showNotification('QR Scanner Error', 'Unable to access camera for QR scanning.', 'error');
        }
    }
    
    async showQRScannerModal(stream) {
        // Get site logo from database
        const siteLogo = await this.getSiteLogo();

        // Remove any existing backdrops first to prevent duplicates
        const existingBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        existingBackdrops.forEach(bd => bd.remove());

        // Create backdrop to block page content
        const backdrop = document.createElement('div');
        backdrop.className = 'camera-modal-backdrop';
        backdrop.id = 'qr-backdrop';
        backdrop.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 1.0) !important;
            z-index: 999997 !important;
            pointer-events: none !important;
        `;
        document.body.appendChild(backdrop);
        console.log('[PWA] QR backdrop created with ID:', backdrop.id);



        const modal = document.createElement('div');
        modal.className = 'qr-scanner-modal';
        modal.innerHTML = `
            <div class="qr-scanner-container">


                <div class="qr-banner" id="qr-banner-content">
                    <!-- Banner content will be loaded dynamically -->
                </div>
                <div class="qr-viewfinder">
                    <video id="qr-video" autoplay playsinline></video>
                    <div class="qr-overlay">
                        <div class="qr-target"></div>
                    </div>
                </div>
                <div class="qr-controls">
                    <button class="btn btn-danger qr-cancel-btn">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <div class="qr-instructions">
                        Position QR code within the frame
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);



        const video = document.getElementById('qr-video');
        video.srcObject = stream;
        
        // Add event listeners for modal controls
        const cancelBtn = modal.querySelector('.qr-cancel-btn');
        
        cancelBtn.addEventListener('click', () => {
            this.closeQRScanner();
        });
        
        // Add backdrop click to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeQRScanner();
            }
        });
        
        // Add escape key to close
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeQRScanner();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Store escape handler for cleanup
        modal.escapeHandler = escapeHandler;
        
        // Force reload camera-banner script to bypass cache
        const forceReloadBannerScript = () => {
            return new Promise((resolve, reject) => {
                // Remove existing script if present
                const existingScript = document.querySelector('script[src*="camera-banner.js"]');
                if (existingScript) {
                    existingScript.remove();
                }
                
                // Load fresh script with timestamp
                const script = document.createElement('script');
                script.src = '/public/js/camera-banner.js?v=' + Date.now() + '&cache=bust';
                script.onload = () => {
                    console.log('QR Camera banner script reloaded successfully');
                    resolve();
                };
                script.onerror = () => {
                    console.error('Failed to reload QR camera banner script');
                    reject(new Error('Script reload failed'));
                };
                document.head.appendChild(script);
            });
        };

        // Initialize banner system immediately - no delays
        const bannerContainer = document.getElementById('qr-banner-content');
        
        // Show fallback logo immediately while banners load
        const showFallbackLogo = () => {
            if (bannerContainer) {
                bannerContainer.innerHTML = `<img src="${siteLogo}" alt="Rowan Elite Rides" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">`;
            }
        };
        
        // Show logo immediately
        showFallbackLogo();
        
        // Apply CSS fix for modal background that was covering banners
        const style = document.createElement('style');
        style.textContent = `
            .qr-scanner-modal {
                background: transparent !important;
            }
            .qr-banner {
                background: transparent !important;
                overflow: visible !important;
            }
        `;
        document.head.appendChild(style);
        
        // Initialize banner system
        setTimeout(() => {
            if (window.cameraBanner) {
                // Use existing banner system
                window.cameraBanner.startRotation('qr-banner-content');
            } else {
                // Load banner system
                forceReloadBannerScript().then(() => {
                    setTimeout(() => {
                        if (window.cameraBanner) {
                            window.cameraBanner.startRotation('qr-banner-content');
                        }
                    }, 500);
                }).catch(() => {
                    // Fallback: Load banners directly from API
                    this.loadBannersDirectly(bannerContainer);
                });
            }
        }, 100);

        this.currentStream = stream;
        this.currentModal = modal;
        this.startQRDetection(video);
    }
    
    loadBannersDirectly(bannerContainer) {
        fetch('/api/camera-banners.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.banners && data.banners.length > 0) {
                    let currentIndex = 0;
                    const showBanner = () => {
                        const banner = data.banners[currentIndex];
                        bannerContainer.innerHTML = `<img src="${banner.image_url}" alt="${banner.title}" class="logo-banner">`;
                        currentIndex = (currentIndex + 1) % data.banners.length;
                    };
                    showBanner();
                    setInterval(showBanner, 5000);
                }
            })
            .catch(error => {
                console.error('Failed to load banners:', error);
            });
    }
    
    async startQRDetection(video) {
        if ('BarcodeDetector' in window) {
            const barcodeDetector = new BarcodeDetector({ formats: ['qr_code'] });
            
            const detectQR = async () => {
                try {
                    const barcodes = await barcodeDetector.detect(video);
                    if (barcodes.length > 0) {
                        this.handleQRDetected(barcodes[0].rawValue);
                        return;
                    }
                } catch (error) {
                    console.error('[PWA] QR detection error:', error);
                }
                
                if (this.currentStream) {
                    requestAnimationFrame(detectQR);
                }
            };
            
            video.addEventListener('loadedmetadata', detectQR);
        } else {
            // Fallback QR detection using library
            this.fallbackQRDetection(video);
        }
    }
    
    handleQRDetected(qrData) {
        console.log('[PWA] QR Code detected:', qrData);

        // Play beep sound at max volume
        this.playQRBeepSound();

        // Add vibration feedback for additional confirmation
        if ('vibrate' in navigator) {
            navigator.vibrate([200, 100, 200]); // Two short vibrations
        }

        this.closeQRScanner();

        // Handle different QR code types
        if (qrData.includes('/vote/')) {
            window.location.href = qrData;
        } else if (qrData.includes('/registration/')) {
            window.location.href = qrData;
        } else if (qrData.includes('/show/')) {
            window.location.href = qrData;
        } else {
            this.showNotification('QR Code Scanned', `Detected: ${qrData}`, 'info');
        }
    }

    /**
     * Initialize audio context for QR beep sounds
     * Must be called after user interaction on mobile devices
     */
    initializeAudio() {
        if (this.audioInitialized) return;

        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

            // Resume audio context if suspended
            if (this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }

            this.audioInitialized = true;
            console.log('[PWA] Audio context initialized for QR beeps');
        } catch (error) {
            console.log('[PWA] Audio context initialization failed:', error);
        }
    }

    /**
     * Play QR code detection beep sound at maximum volume
     * Works even when phone volume is disabled/muted
     */
    playQRBeepSound() {
        try {
            // Initialize audio if not already done
            if (!this.audioInitialized) {
                this.initializeAudio();
            }

            // Use existing audio context or create new one
            const audioContext = this.audioContext || new (window.AudioContext || window.webkitAudioContext)();

            // Resume audio context if suspended (required for mobile)
            if (audioContext.state === 'suspended') {
                audioContext.resume();
            }

            // Create oscillator for beep sound
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            // Connect audio nodes
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Configure beep sound - high frequency for attention
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime); // 1kHz tone
            oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.1);

            // Set maximum volume (1.0 = 100%)
            gainNode.gain.setValueAtTime(1.0, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            // Use square wave for more piercing sound
            oscillator.type = 'square';

            // Play beep for 200ms
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);

            console.log('[PWA] QR beep sound played');

            // Create a second shorter beep for confirmation
            setTimeout(() => {
                try {
                    const oscillator2 = audioContext.createOscillator();
                    const gainNode2 = audioContext.createGain();

                    oscillator2.connect(gainNode2);
                    gainNode2.connect(audioContext.destination);

                    oscillator2.frequency.setValueAtTime(1200, audioContext.currentTime);
                    oscillator2.type = 'square';

                    gainNode2.gain.setValueAtTime(1.0, audioContext.currentTime);
                    gainNode2.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                    oscillator2.start(audioContext.currentTime);
                    oscillator2.stop(audioContext.currentTime + 0.1);
                } catch (e) {
                    console.log('[PWA] Second beep failed:', e);
                }
            }, 300);

        } catch (error) {
            console.log('[PWA] QR beep sound failed:', error);

            // Fallback: Try to use HTML5 audio with data URI
            try {
                // Create a short beep using data URI
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.volume = 1.0; // Maximum volume
                audio.play().catch(e => console.log('[PWA] Fallback audio failed:', e));
            } catch (e) {
                console.log('[PWA] Fallback beep failed:', e);

                // Final fallback: Vibration if available
                if ('vibrate' in navigator) {
                    navigator.vibrate([200, 100, 200]);
                    console.log('[PWA] Using vibration as audio fallback');
                }
            }
        }
    }

    closeQRScanner() {
        // Stop all camera tracks first
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => {
                track.stop();
                console.log('[PWA] QR Scanner track stopped:', track.kind);
            });
            this.currentStream = null;
        }

        // Clear video source
        const video = document.getElementById('qr-video');
        if (video) {
            video.srcObject = null;
            video.src = '';
        }

        // Stop banner rotation
        if (window.cameraBanner) {
            window.cameraBanner.stopRotation();
        }

        // Remove modal completely
        if (this.currentModal) {
            // Remove escape key listener if it exists
            if (this.currentModal.escapeHandler) {
                document.removeEventListener('keydown', this.currentModal.escapeHandler);
            }
            
            this.currentModal.remove();
            console.log('[PWA] QR Scanner modal removed');
            this.currentModal = null;
        }

        // Fallback cleanup for any remaining modal
        const modal = document.querySelector('.qr-scanner-modal');
        if (modal) {
            modal.remove();
            console.log('[PWA] Fallback QR scanner modal cleanup');
        }

        // Remove all camera backdrops (more reliable than ID-based removal)
        const allBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        if (allBackdrops.length > 0) {
            console.log(`[PWA] Removing ${allBackdrops.length} QR backdrop(s)`);
            allBackdrops.forEach(bd => bd.remove());
        }

        // Clear any remaining references
        this.currentStream = null;
    }
    
    // Force close any open camera/QR modals - safety method
    forceCloseAllModals() {
        console.log('[PWA] Force closing all camera/QR modals');
        
        // Stop any active streams
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => {
                track.stop();
                console.log('[PWA] Force stopped track:', track.kind);
            });
            this.currentStream = null;
        }
        
        // Remove camera modal
        const cameraModal = document.querySelector('.camera-modal');
        if (cameraModal) {
            if (cameraModal.escapeHandler) {
                document.removeEventListener('keydown', cameraModal.escapeHandler);
            }
            cameraModal.remove();
            console.log('[PWA] Force removed camera modal');
        }
        
        // Remove QR scanner modal
        const qrModal = document.querySelector('.qr-scanner-modal');
        if (qrModal) {
            if (qrModal.escapeHandler) {
                document.removeEventListener('keydown', qrModal.escapeHandler);
            }
            qrModal.remove();
            console.log('[PWA] Force removed QR scanner modal');
        }
        
        // Clear video sources
        const cameraVideo = document.getElementById('camera-video');
        const qrVideo = document.getElementById('qr-video');
        
        if (cameraVideo) {
            cameraVideo.srcObject = null;
            cameraVideo.src = '';
        }
        
        if (qrVideo) {
            qrVideo.srcObject = null;
            qrVideo.src = '';
        }
        
        // Stop banner rotation
        if (window.cameraBanner) {
            window.cameraBanner.stopRotation();
        }
        
        // Clear references
        this.currentStream = null;
        this.currentModal = null;
    }
    
    async loadQRLibrary() {
        // Load QR code library if needed
        if (!window.QrScanner) {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/qr-scanner@1.4.2/qr-scanner.min.js';
            document.head.appendChild(script);
            
            return new Promise((resolve) => {
                script.onload = resolve;
            });
        }
    }
    
    // Gesture Support
    initGestureSupport() {
        let startX, startY, currentX, currentY;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchmove', (e) => {
            currentX = e.touches[0].clientX;
            currentY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', () => {
            if (!startX || !startY || !currentX || !currentY) return;
            
            const diffX = startX - currentX;
            const diffY = startY - currentY;
            
            // Swipe detection
            if (Math.abs(diffX) > Math.abs(diffY)) {
                if (Math.abs(diffX) > 50) {
                    if (diffX > 0) {
                        this.handleSwipeLeft();
                    } else {
                        this.handleSwipeRight();
                    }
                }
            }
            
            // Reset
            startX = startY = currentX = currentY = null;
        });
    }
    
    handleSwipeLeft() {
        // Navigate to next page/section
        const nextButton = document.querySelector('[data-next]');
        if (nextButton) {
            nextButton.click();
        }
    }
    
    handleSwipeRight() {
        // Navigate to previous page/section
        const prevButton = document.querySelector('[data-prev]');
        if (prevButton) {
            prevButton.click();
        }
    }
    
    // Biometric Authentication
    async initBiometricAuth() {
        if ('credentials' in navigator && 'create' in navigator.credentials) {
            const biometricButton = document.getElementById('biometric-login');
            if (biometricButton) {
                biometricButton.addEventListener('click', () => this.authenticateWithBiometrics());
            }
        }
    }
    
    async authenticateWithBiometrics() {
        try {
            const credential = await navigator.credentials.get({
                publicKey: {
                    challenge: new Uint8Array(32),
                    allowCredentials: [],
                    userVerification: 'required'
                }
            });
            
            // Send credential to server for verification
            const response = await fetch('/api/auth/biometric', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ credential })
            });
            
            if (response.ok) {
                this.showNotification('Login Successful', 'Authenticated with biometrics!', 'success');
                // Enable push notifications after successful biometric login
                if (typeof enablePushNotifications === "function") {
                    enablePushNotifications();
                }
                window.location.reload();
            }
            
        } catch (error) {
            console.error('[PWA] Biometric authentication failed:', error);
            this.showNotification('Authentication Failed', 'Biometric authentication failed. Please try again.', 'error');
        }
    }
    
    // Web Share API
    initWebShare() {
        const shareButtons = document.querySelectorAll('[data-share]');
        shareButtons.forEach(button => {
            if (navigator.share) {
                button.addEventListener('click', () => this.shareContent(button.dataset));
            } else {
                button.style.display = 'none';
            }
        });
    }
    
    async shareContent(data) {
        try {
            await navigator.share({
                title: data.title || document.title,
                text: data.text || 'Check out this event!',
                url: data.url || window.location.href
            });
            
            console.log('[PWA] Content shared successfully');
            
        } catch (error) {
            console.error('[PWA] Share failed:', error);
        }
    }
    
    // Utility Methods
    showNotification(title, message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <strong>${title}</strong>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="toast-body">${message}</div>
        `;
        
        // Add to toast container or create one
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
        }
        
        container.appendChild(toast);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
    
    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'update-banner alert alert-info';
        updateBanner.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>Update Available!</strong> A new version of the app is ready.
                </div>
                <button class="btn btn-sm btn-primary" onclick="window.location.reload()">
                    Update Now
                </button>
            </div>
        `;

        document.body.insertBefore(updateBanner, document.body.firstChild);
    }

    // Clear authentication-related cache when login state changes
    async clearAuthCache() {
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            try {
                navigator.serviceWorker.controller.postMessage({
                    type: 'CLEAR_AUTH_CACHE'
                });
                console.log('[PWA] Sent cache clear message to service worker');
            } catch (error) {
                console.error('[PWA] Failed to clear auth cache:', error);
            }
        }
    }




}

// Make PWAFeatures available globally
window.PWAFeatures = PWAFeatures;

// Initialize PWA features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Reset Facebook login flag on page load
    window.facebookLoginInProgress = false;
    if (!window.pwaFeatures) {
        window.pwaFeatures = new PWAFeatures();
    }
});

// Reset Facebook login flag when page becomes visible (user returns from Facebook)
document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
        window.facebookLoginInProgress = false;
    }
});

// Global function to clear auth cache (can be called from anywhere)
window.clearAuthCache = function() {
    if (window.pwaFeatures) {
        window.pwaFeatures.clearAuthCache();
    }
};

// Global function to force close camera modals (can be called from anywhere)
window.forceCloseCameraModals = function() {
    if (window.pwaFeatures) {
        window.pwaFeatures.forceCloseAllModals();
    }
};

// Global function to enable push notifications after login
window.enablePushNotifications = function() {
    if (window.pwaFeatures) {
        window.pwaFeatures.enablePushNotificationsAfterLogin();
    }
};

// Global function to handle Facebook login (can be called from anywhere)
window.handleFacebookLogin = function() {
    // Prevent multiple simultaneous calls
    if (window.facebookLoginInProgress) {
        console.log('[PWA] Facebook login already in progress, ignoring duplicate click');
        return;
    }

    console.log('[PWA] Starting Facebook login process');
    window.facebookLoginInProgress = true;

    const statusDiv = document.getElementById('facebook-login-status');
    const statusText = document.getElementById('facebook-status-text');
    const loginBtn = document.getElementById('facebook-login-btn');

    // IMMEDIATELY disable button and show feedback to prevent double-clicks
    if (loginBtn) {
        loginBtn.disabled = true;
        loginBtn.style.opacity = '0.6';
        loginBtn.style.cursor = 'not-allowed';
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting to Facebook...';
    }

    // Show loading status
    if (statusDiv) {
        statusDiv.style.display = 'block';
        statusDiv.style.color = '#007bff';
    }
    if (statusText) {
        statusText.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Please wait, connecting to Facebook...';
    }

    // Use direct redirect for all cases - simpler and more reliable
    const baseUrl = window.PWA_CONFIG ? window.PWA_CONFIG.baseUrl : '';

    // Clear the flag after a timeout as a safety measure
    setTimeout(() => {
        window.facebookLoginInProgress = false;
    }, 15000); // 15 seconds timeout

    // Immediate redirect (no delay needed since we have visual feedback)
    console.log('[PWA] Redirecting to Facebook login');
    window.location.href = baseUrl + '/auth/facebook';
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAFeatures;
}

} // End duplicate loading prevention