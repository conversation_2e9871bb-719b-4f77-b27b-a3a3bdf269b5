<?php
/**
 * URL Helper
 * 
 * Functions for URL manipulation.
 */

/**
 * Redirect to URL
 * 
 * @param string $url URL to redirect to
 * @return void
 */
function redirect($url) {
    // Sanitize the URL to prevent header injection
    $url = filter_var($url, FILTER_SANITIZE_URL);
    
    // Ensure the URL is relative to prevent open redirect vulnerabilities
    if (strpos($url, '://') !== false) {
        // If it's an absolute URL, make sure it's on our domain
        $urlHost = parse_url($url, PHP_URL_HOST);
        $baseHost = parse_url(BASE_URL, PHP_URL_HOST);
        
        if ($urlHost !== $baseHost) {
            // If not on our domain, redirect to home page
            $url = '';
        }
    }
    
    header('Location: ' . BASE_URL . '/' . ltrim($url, '/'));
    exit;
}

/**
 * Get current URL
 * 
 * @return string Current URL
 */
function getCurrentUrl() {
    // Determine protocol
    $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') || 
                (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ? 
                'https' : 'http';
    
    // Get host, handling potential proxy situations
    $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'];
    
    // Get URI and sanitize it
    $uri = filter_var($_SERVER['REQUEST_URI'], FILTER_SANITIZE_URL);
    
    return $protocol . '://' . $host . $uri;
}

/**
 * Get base URL
 * 
 * @param string $path Path to append to base URL
 * @return string Base URL with optional path
 */
function getBaseUrl($path = '') {
    return BASE_URL . ($path ? '/' . ltrim($path, '/') : '');
}

/**
 * Check if current URL matches pattern
 * 
 * @param string $pattern URL pattern to match
 * @return bool True if URL matches pattern, false otherwise
 */
function isCurrentUrl($pattern) {
    $currentUrl = $_SERVER['REQUEST_URI'];
    $baseUrl = parse_url(BASE_URL, PHP_URL_PATH) ?: '';
    
    // Remove base URL from current URL
    if ($baseUrl && strpos($currentUrl, $baseUrl) === 0) {
        $currentUrl = substr($currentUrl, strlen($baseUrl));
    }
    
    // Remove query string
    $currentUrl = strtok($currentUrl, '?');
    
    // Remove leading and trailing slashes
    $currentUrl = trim($currentUrl, '/');
    $pattern = trim($pattern, '/');
    
    // Check if URL matches pattern
    return $currentUrl === $pattern || preg_match('#^' . str_replace('*', '.*', $pattern) . '$#', $currentUrl);
}

/**
 * Check if the current request is an AJAX request
 *
 * @return bool True if AJAX request, false otherwise
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Get active class if URL matches pattern
 * 
 * @param string $pattern URL pattern to match
 * @param string $class CSS class to return if URL matches pattern
 * @return string CSS class if URL matches pattern, empty string otherwise
 */
function getActiveClass($pattern, $class = 'active') {
    return isCurrentUrl($pattern) ? $class : '';
}

/**
 * Get pagination URL
 * 
 * @param int $page Page number
 * @return string Pagination URL
 */
function getPaginationUrl($page) {
    $url = getCurrentUrl();
    $url = strtok($url, '?');
    $query = $_GET;
    $query['page'] = $page;
    
    return $url . '?' . http_build_query($query);
}