<?php
/**
 * Show Model
 * 
 * This model handles all database operations related to car shows.
 * 
 * Version 3.34.43 - Fixed property redeclaration error
 * - Fixed fatal error: "Cannot redeclare ShowModel::$systemFields" in ShowModel.php
 * - Removed duplicate declarations of $systemFields and $standardFields properties
 * - Merged system fields from both declarations to maintain functionality
 * 
 * Version 3.34.10 - Fixed PHP 8.4 deprecation warning
 * - Added proper declaration for $eventTrigger property to fix PHP 8.4 deprecation warning
 * 
 * Version 3.34.9 - Added event trigger support
 * - Added integration with EventTrigger for scheduled tasks
 * - Modified updateStatus method to trigger events on status changes
 * 
 * Version 2.19.95 - Fixed duplicate method declaration
 * - Fixed fatal error: "Cannot redeclare ShowModel::tableExists()" in ShowModel.php
 * - Removed duplicate tableExists() method declaration at the end of ShowModel class
 * - Maintained consistent database table existence checking functionality
 * 
 * Version 2.19.90 - Added age weights management functionality
 * - Added getAgeWeights method to retrieve age weights for a show
 * - Added createAgeWeight method to create new age weights
 * - Added updateAgeWeight method to update existing age weights
 * - Added deleteAgeWeight method to delete age weights
 * - Added createAgeWeightsTable method to create the age_weights table if it doesn't exist
 * - Added tableExists method to check if a table exists in the database
 * 
 * Version 2.19.94 - Fixed issues with missing methods and deprecated properties
 * - Added assignJudge method to fix fatal error in CoordinatorController
 * - Added removeJudgeAssignment method to fix fatal error in CoordinatorController
 * - Added category support to judging metrics
 * - Enhanced createJudgingMetricsTable to add category_id column
 * - Updated createMetric and updateMetric to handle category_id
 * - Added getMetricById method to fix fatal error in CoordinatorController
 * - Added updateMetric method to support editing metrics
 * - Added createMetric method to support adding new metrics
 * - Added getJudgeAssignments method to fix fatal error in CoordinatorController
 * - Added getShowCategories and getJudgingMetrics methods
 * - Replaced calls to EmergencyFormFieldManager::detectFieldType with our own implementation
 * - Fixed all occurrences of $standardFields and $systemFields
 * - Added proper variable initialization
 * - Fixed duplicate method declaration for detectFieldType
 * - Added missing property declaration for $formFieldManager
 * - Added missing getUserCompletedShows method with proper error handling
 */
class ShowModel {
    private $db;
    private $customFieldValuesModel;
    private $emergencyFormFieldManager;
    private $formFieldManager; // Added to fix deprecation warning
    private $eventTrigger; // Added to fix PHP 8.4 deprecation of dynamic properties
    private $calendarModel; // Added for calendar integration
    
    // Standard fields that are part of the shows table
    private $standardFields = [
        'id', 'name', 'description', 'location', 'start_date', 'end_date', 
        'registration_start', 'registration_end', 'coordinator_id', 'status', 
        'fan_voting_enabled', 'registration_fee', 'is_free', 'listing_fee', 
        'listing_paid', 'featured_image_id', 'created_at', 'updated_at'
    ];
    
    // System fields that should not be saved to the database
    private $systemFields = [
        'name_err', 'location_err', 'start_date_err', 'end_date_err', 
        'registration_start_err', 'registration_end_err', 'coordinator_id_err', 
        'status_err', 'title', 'coordinators', 'template', 'data',
        'id', 'created_at', 'updated_at'
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Initialize CustomFieldValuesModel if it exists
        if (file_exists(APPROOT . '/models/CustomFieldValuesModel.php')) {
            require_once APPROOT . '/models/CustomFieldValuesModel.php';
            $this->customFieldValuesModel = new CustomFieldValuesModel();
        }
        
        // Initialize EventTrigger if it exists
        if (file_exists(APPROOT . '/core/EventTrigger.php')) {
            require_once APPROOT . '/core/EventTrigger.php';
            $this->eventTrigger = new EventTrigger();
        }
        
        // Initialize DynamicFormFieldManager
        if (file_exists(APPROOT . '/models/DynamicFormFieldManager.php')) {
            require_once APPROOT . '/models/DynamicFormFieldManager.php';
            $this->formFieldManager = new DynamicFormFieldManager();
        } else {
            // Fallback to FormFieldManager if needed
            require_once APPROOT . '/models/FormFieldManager.php';
            $this->formFieldManager = new FormFieldManager();
        }
        
        // Initialize CalendarModel if it exists
        if (file_exists(APPROOT . '/models/CalendarModel.php')) {
            // We need to avoid circular dependency, so we'll initialize it only when needed
            $this->calendarModel = null;
        }
    }
    
    /**
     * Get the CalendarModel instance
     * 
     * @return CalendarModel The calendar model instance
     */
    private function getCalendarModel() {
        if ($this->calendarModel === null) {
            require_once APPROOT . '/models/CalendarModel.php';
            $this->calendarModel = new CalendarModel();
        }
        return $this->calendarModel;
    }
    
    /**
     * Execute completion scripts directly for a show
     * 
     * This method is used as a fallback when event triggering fails
     * 
     * @param int $showId The ID of the show
     * @return void
     */
    private function executeCompletionScripts($showId) {
        error_log("ShowModel::executeCompletionScripts - Directly executing completion scripts for show ID: {$showId}");
        
        // Get the scheduled tasks that should run on show completion
        $this->db->query("SELECT * FROM scheduled_tasks WHERE 
                         (event_name LIKE '%show_completed%' OR 
                          event_name LIKE '%Show Completed%' OR 
                          event_name LIKE '%show completed%') 
                         AND status = 'active'");
        $tasks = $this->db->resultSet();
        
        if (empty($tasks)) {
            error_log("ShowModel::executeCompletionScripts - No completion tasks found in the database");
            error_log("ShowModel::executeCompletionScripts - No tasks will be executed");
            return;
        }
        
        // Execute each task
        foreach ($tasks as $task) {
            error_log("ShowModel::executeCompletionScripts - Executing task: {$task->name} (ID: {$task->id})");
            
            // Update task status to running
            $this->db->query("UPDATE scheduled_tasks SET status = 'running', last_run = NOW(), current_parameters = :params WHERE id = :id");
            $this->db->bind(':id', $task->id);
            $this->db->bind(':params', $showId);
            $this->db->execute();
            
            // Store the task ID for later reset
            $taskId = $task->id;
            
            // BACKGROUND EXECUTION: Run the scoring script in the background using exec()
            // This is the most reliable method and doesn't affect the web UI
            try {
                // Get PHP executable path
                $phpPath = PHP_BINARY;
                if (empty($phpPath)) {
                    $phpPath = 'php'; // Default fallback
                }
                
                // Build the command to run the scoring script
                $scoringScript = APPROOT . '/scripts/run_all_scoring.php';
                
                if (file_exists($scoringScript)) {
                    error_log("ShowModel::executeCompletionScripts - Scoring script exists: {$scoringScript}");
                    
                    // Create a background command that will run without affecting the web UI
                    // The '> /dev/null 2>&1 &' part makes it run in the background
                    $command = $phpPath . ' ' . escapeshellarg($scoringScript) . ' ' . escapeshellarg($showId) . ' > /dev/null 2>&1 &';
                    
                    error_log("ShowModel::executeCompletionScripts - Executing command in background: {$command}");
                    
                    // Execute the command in the background
                    if (function_exists('exec')) {
                        exec($command);
                        error_log("ShowModel::executeCompletionScripts - Command executed successfully using exec()");
                    } elseif (function_exists('shell_exec')) {
                        shell_exec($command);
                        error_log("ShowModel::executeCompletionScripts - Command executed successfully using shell_exec()");
                    } elseif (function_exists('system')) {
                        system($command . ' &');
                        error_log("ShowModel::executeCompletionScripts - Command executed successfully using system()");
                    } elseif (function_exists('passthru')) {
                        passthru($command . ' &');
                        error_log("ShowModel::executeCompletionScripts - Command executed successfully using passthru()");
                    } else {
                        error_log("ShowModel::executeCompletionScripts - No shell execution function available");
                    }
                } else {
                    error_log("ShowModel::executeCompletionScripts - Scoring script does not exist: {$scoringScript}");
                }
                
                // Also try to run the task-specific script if it exists
                if (!empty($task->script_path)) {
                    $scriptPath = APPROOT . $task->script_path;
                    
                    if (file_exists($scriptPath)) {
                        error_log("ShowModel::executeCompletionScripts - Task script exists: {$scriptPath}");
                        
                        // Create a background command for the task script
                        $taskCommand = $phpPath . ' ' . escapeshellarg($scriptPath) . ' ' . escapeshellarg($showId) . ' > /dev/null 2>&1 &';
                        
                        error_log("ShowModel::executeCompletionScripts - Executing task command in background: {$taskCommand}");
                        
                        // Execute the command in the background
                        if (function_exists('exec')) {
                            exec($taskCommand);
                            error_log("ShowModel::executeCompletionScripts - Task command executed successfully using exec()");
                        } elseif (function_exists('shell_exec')) {
                            shell_exec($taskCommand);
                            error_log("ShowModel::executeCompletionScripts - Task command executed successfully using shell_exec()");
                        }
                    } else {
                        error_log("ShowModel::executeCompletionScripts - Task script does not exist: {$scriptPath}");
                    }
                }
            } catch (Exception $e) {
                error_log("ShowModel::executeCompletionScripts - Error executing scripts: " . $e->getMessage());
            }
            
            // Update task status back to active
            $this->db->query("UPDATE scheduled_tasks SET status = 'active' WHERE id = :id");
            $this->db->bind(':id', $task->id);
            $this->db->execute();
            
            error_log("ShowModel::executeCompletionScripts - Task execution completed for task ID: {$task->id}");
        }
    }
    
    /**
     * Check if a table exists
     * 
     * @param string $tableName The name of the table to check
     * @return bool True if the table exists, false otherwise
     */
    private function tableExists($tableName) {
        // We can't use parameter binding with SHOW TABLES LIKE
        $this->db->query("SHOW TABLES LIKE '{$tableName}'");
        $this->db->execute();
        return $this->db->rowCount() > 0;
    }
    
    /**
     * Update the featured image ID for a show
     * 
     * @param int $showId The ID of the show
     * @param int $imageId The ID of the image to set as featured
     * @return bool True if successful, false otherwise
     */
    public function updateFeaturedImage($showId, $imageId) {
        try {
            $this->db->query('UPDATE shows SET featured_image_id = :image_id WHERE id = :show_id');
            $this->db->bind(':image_id', $imageId);
            $this->db->bind(':show_id', $showId);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in ShowModel::updateFeaturedImage: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update the banner image for a show
     * 
     * @param int $showId The ID of the show
     * @param string $fileName The filename of the banner image
     * @return bool True if successful, false otherwise
     */
    public function updateShowBannerImage($showId, $fileName) {
        try {
            $this->db->query('UPDATE shows SET banner_image = :banner_image WHERE id = :show_id');
            $this->db->bind(':banner_image', $fileName);
            $this->db->bind(':show_id', $showId);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in ShowModel::updateShowBannerImage: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get shows with fan voting enabled
     * 
     * @return array Shows with fan voting enabled
     */
    public function getShowsWithFanVotingEnabled() {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            
            // Build SQL based on available tables
            if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id
                        WHERE s.fan_voting_enabled = 1 
                        AND s.status NOT IN ("cancelled")
                        ORDER BY s.start_date DESC';
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name 
                        FROM shows s 
                        WHERE s.fan_voting_enabled = 1 
                        AND s.status NOT IN ("cancelled")
                        ORDER BY s.start_date DESC';
                error_log("Users table not found, using simplified query for getShowsWithFanVotingEnabled");
            }
            
            $this->db->query($sql);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowsWithFanVotingEnabled: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all shows
     * 
     * @param string $status Optional status filter
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @param bool $excludeCompletedCancelled Whether to exclude completed and cancelled shows
     * @return array
     */
    public function getShows($status = null, $limit = null, $offset = null, $excludeCompletedCancelled = false) {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            $registrationsTableExists = $this->tableExists('registrations');
            
            // Build SQL based on available tables
            if ($usersTableExists && $registrationsTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
            } else if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
                error_log("Registrations table not found, using simplified query for getShows");
            } else if ($registrationsTableExists) {
                $sql = 'SELECT s.*, NULL as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s';
                error_log("Users table not found, using simplified query for getShows");
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name, 0 as registration_count 
                        FROM shows s';
                error_log("Users and registrations tables not found, using simplified query for getShows");
            }
            
            $whereConditions = [];
            
            if ($status) {
                $whereConditions[] = 's.status = :status';
            }
            
            if ($excludeCompletedCancelled) {
                $whereConditions[] = 's.status NOT IN ("completed", "cancelled")';
            }
            
            if (!empty($whereConditions)) {
                $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
            }
            
            $sql .= ' ORDER BY s.start_date DESC';
            
            if ($limit) {
                $sql .= ' LIMIT :limit';
                if ($offset) {
                    $sql .= ' OFFSET :offset';
                }
            }
            
            $this->db->query($sql);
            
            if ($status) {
                $this->db->bind(':status', $status);
            }
            
            if ($limit) {
                $this->db->bind(':limit', $limit, PDO::PARAM_INT);
                if ($offset) {
                    $this->db->bind(':offset', $offset, PDO::PARAM_INT);
                }
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Parse location string to extract city and state
     * 
     * @param string $location Location string (e.g. "New York, NY", "Los Angeles CA", "Miami, Florida")
     * @return array Associative array with 'city' and 'state' keys
     */
    /**
     * Parse location string to extract city and state
     * 
     * This is an advanced location parser that handles various formats of addresses
     * and extracts city and state information.
     * 
     * @param string $location The location string to parse
     * @return array Associative array with 'city' and 'state' keys
     */
    public function parseLocation($location) {
        $result = [
            'city' => '',
            'state' => ''
        ];
        
        if (empty($location)) {
            return $result;
        }
        
        // Common state abbreviations and full names mapping
        $stateMap = [
            'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas',
            'CA' => 'California', 'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware',
            'FL' => 'Florida', 'GA' => 'Georgia', 'HI' => 'Hawaii', 'ID' => 'Idaho',
            'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa', 'KS' => 'Kansas',
            'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
            'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi',
            'MO' => 'Missouri', 'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada',
            'NH' => 'New Hampshire', 'NJ' => 'New Jersey', 'NM' => 'New Mexico', 'NY' => 'New York',
            'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio', 'OK' => 'Oklahoma',
            'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
            'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah',
            'VT' => 'Vermont', 'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia',
            'WI' => 'Wisconsin', 'WY' => 'Wyoming', 'DC' => 'District of Columbia'
        ];
        
        // Flip the map to also search by full state name
        $stateNameToAbbr = array_flip($stateMap);
        
        // Normalize the location string
        $location = trim($location);
        
        // Debug mode check
        $debug = defined('DEBUG_MODE') && DEBUG_MODE;
        if ($debug) {
            error_log("ShowModel::parseLocation - Parsing location: " . $location);
        }
        
        // Special case: Check if the location is just "USA" or contains only "USA"
        if (preg_match('/^USA$/i', $location) || preg_match('/^United\s+States(\s+of\s+America)?$/i', $location)) {
            $result['city'] = '';
            $result['state'] = 'USA';
            if ($debug) error_log("ShowModel::parseLocation - Matched USA only");
            return $result;
        }
        
        // Try to match common patterns in order of specificity
        
        // Pattern 1: Full address with street, city, state, zip
        // Example: "123 Main St, Anytown, CA 12345"
        if (preg_match('/^.+,\s*(.+?),\s*([A-Za-z]{2})\s*\d{5}(-\d{4})?$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $result['state'] = strtoupper(trim($matches[2]));
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 1 (Full address)");
        }
        // Pattern 2: City, State Zip
        // Example: "Anytown, CA 12345"
        else if (preg_match('/^(.+?),\s*([A-Za-z]{2})\s*\d{5}(-\d{4})?$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $result['state'] = strtoupper(trim($matches[2]));
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 2 (City, State Zip)");
        }
        // Pattern 3: "City, State" or "City, ST"
        // Example: "Anytown, California" or "Anytown, CA"
        else if (preg_match('/^(.+?),\s*([A-Za-z]{2,})$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $stateCandidate = trim($matches[2]);
            
            // Check if it's a state abbreviation
            if (strlen($stateCandidate) == 2 && isset($stateMap[strtoupper($stateCandidate)])) {
                $result['state'] = strtoupper($stateCandidate);
            } 
            // Check if it's a full state name
            else if (isset($stateNameToAbbr[ucwords(strtolower($stateCandidate))])) {
                $result['state'] = $stateNameToAbbr[ucwords(strtolower($stateCandidate))];
            } 
            // Check if it's "USA"
            else if (strtoupper($stateCandidate) == 'USA' || 
                     strtoupper($stateCandidate) == 'UNITED STATES' || 
                     strtoupper($stateCandidate) == 'UNITED STATES OF AMERICA') {
                $result['state'] = 'USA';
            } else {
                $result['state'] = $stateCandidate;
            }
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 3 (City, State)");
        }
        // Pattern 4: "City ST Zip" or "City State Zip"
        // Example: "Anytown CA 12345" or "Anytown California 12345"
        else if (preg_match('/^(.+?)\s+([A-Za-z]{2})\s+\d{5}(-\d{4})?$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $result['state'] = strtoupper(trim($matches[2]));
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 4 (City ST Zip)");
        }
        // Pattern 5: "City ST" or "City State"
        // Example: "Anytown CA" or "Anytown California"
        else if (preg_match('/^(.+?)\s+([A-Za-z]{2,})$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $stateCandidate = trim($matches[2]);
            
            // Check if it's a state abbreviation
            if (strlen($stateCandidate) == 2 && isset($stateMap[strtoupper($stateCandidate)])) {
                $result['state'] = strtoupper($stateCandidate);
                if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 5 (City ST)");
            } 
            // Check if it's a full state name
            else if (isset($stateNameToAbbr[ucwords(strtolower($stateCandidate))])) {
                $result['state'] = $stateNameToAbbr[ucwords(strtolower($stateCandidate))];
                if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 5 (City State)");
            }
            // Check if it's "USA"
            else if (strtoupper($stateCandidate) == 'USA' || 
                     strtoupper($stateCandidate) == 'UNITED STATES' || 
                     strtoupper($stateCandidate) == 'UNITED STATES OF AMERICA') {
                $result['state'] = 'USA';
                if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 5 (City USA)");
            } else {
                // This might be part of the city name, not a state
                $result['city'] = $location;
                $result['state'] = '';
                if ($debug) error_log("ShowModel::parseLocation - Pattern 5 failed state validation, treating as city only");
            }
        }
        // Pattern 6: Look for state abbreviation or name within the string
        else {
            if ($debug) error_log("ShowModel::parseLocation - Trying Pattern 6 (State within string)");
            $foundState = false;
            
            // Check for USA in the string
            if (stripos($location, 'USA') !== false || 
                stripos($location, 'United States') !== false || 
                stripos($location, 'United States of America') !== false) {
                $result['state'] = 'USA';
                $foundState = true;
                
                // Try to extract city by removing USA references
                $cityCandidate = preg_replace('/\b(USA|United\s+States(\s+of\s+America)?)\b/i', '', $location);
                $cityCandidate = trim($cityCandidate, " ,;-");
                
                if (!empty($cityCandidate)) {
                    $result['city'] = $cityCandidate;
                }
                
                if ($debug) error_log("ShowModel::parseLocation - Found USA reference, city candidate: " . $result['city']);
            }
            
            // If we didn't find USA, try to find a state abbreviation or name
            if (!$foundState) {
                // First try to find a state abbreviation
                foreach ($stateMap as $abbr => $fullName) {
                    // Look for state abbreviation surrounded by spaces, commas, or end of string
                    if (preg_match('/(\s|,|^)' . preg_quote($abbr, '/') . '(\s|,|$)/i', $location, $matches)) {
                        $result['state'] = $abbr;
                        $foundState = true;
                        
                        // Try to extract city by looking at what comes before the state
                        if (preg_match('/(.+?)(\s|,)' . preg_quote($abbr, '/') . '(\s|,|$)/i', $location, $cityMatches)) {
                            $result['city'] = trim($cityMatches[1]);
                            // Remove any trailing comma or other punctuation
                            $result['city'] = rtrim($result['city'], " ,;-");
                        }
                        if ($debug) error_log("ShowModel::parseLocation - Found state abbreviation: " . $abbr);
                        break;
                    }
                    
                    // Look for full state name
                    if (preg_match('/(\s|,|^)' . preg_quote($fullName, '/') . '(\s|,|$)/i', $location, $matches)) {
                        $result['state'] = $abbr;
                        $foundState = true;
                        
                        // Try to extract city by looking at what comes before the state
                        if (preg_match('/(.+?)(\s|,)' . preg_quote($fullName, '/') . '(\s|,|$)/i', $location, $cityMatches)) {
                            $result['city'] = trim($cityMatches[1]);
                            // Remove any trailing comma or other punctuation
                            $result['city'] = rtrim($result['city'], " ,;-");
                        }
                        if ($debug) error_log("ShowModel::parseLocation - Found state name: " . $fullName);
                        break;
                    }
                }
            }
            
            // If we still don't have a city or state, try to extract from common formats
            if (!$foundState) {
                // Try to find a comma-separated list and assume the last part might be a state
                $parts = explode(',', $location);
                if (count($parts) >= 2) {
                    // Last part might contain state
                    $lastPart = trim(end($parts));
                    
                    // Check if it contains a state abbreviation
                    foreach ($stateMap as $abbr => $fullName) {
                        if (preg_match('/\b' . preg_quote($abbr, '/') . '\b/i', $lastPart)) {
                            $result['state'] = $abbr;
                            // The part before the last comma is likely the city
                            $result['city'] = trim($parts[count($parts) - 2]);
                            $foundState = true;
                            if ($debug) error_log("ShowModel::parseLocation - Found state in comma-separated list: " . $abbr);
                            break;
                        }
                    }
                    
                    // If we still don't have a state, check for full state names
                    if (!$foundState) {
                        foreach ($stateMap as $abbr => $fullName) {
                            if (preg_match('/\b' . preg_quote($fullName, '/') . '\b/i', $lastPart)) {
                                $result['state'] = $abbr;
                                // The part before the last comma is likely the city
                                $result['city'] = trim($parts[count($parts) - 2]);
                                $foundState = true;
                                if ($debug) error_log("ShowModel::parseLocation - Found state name in comma-separated list: " . $fullName);
                                break;
                            }
                        }
                    }
                    
                    // Check for USA in the last part
                    if (!$foundState && (stripos($lastPart, 'USA') !== false || 
                                        stripos($lastPart, 'United States') !== false || 
                                        stripos($lastPart, 'United States of America') !== false)) {
                        $result['state'] = 'USA';
                        // The part before the last comma is likely the city
                        $result['city'] = trim($parts[count($parts) - 2]);
                        $foundState = true;
                        if ($debug) error_log("ShowModel::parseLocation - Found USA in comma-separated list");
                    }
                }
            }
            
            // If we still don't have a city, use the whole string as the city
            if (empty($result['city'])) {
                $result['city'] = $location;
                if ($debug) error_log("ShowModel::parseLocation - No city found, using entire string as city");
            }
            
            // If we still don't have a state, default to USA
            if (empty($result['state'])) {
                $result['state'] = 'USA';
                if ($debug) error_log("ShowModel::parseLocation - No state found, defaulting to USA");
            }
        }
        
        // Clean up city name - remove any trailing/leading punctuation
        $result['city'] = trim($result['city'], " ,;-");
        
        // If we have a state abbreviation, make sure it's uppercase
        if (strlen($result['state']) == 2) {
            $result['state'] = strtoupper($result['state']);
        }
        
        if ($debug) {
            error_log("ShowModel::parseLocation - Final result: City = '" . $result['city'] . "', State = '" . $result['state'] . "'");
        }
        
        return $result;
    }
    
    /**
     * Get filtered shows with pagination
     * 
     * @param string $status Status filter (e.g., 'published')
     * @param string $search Search term for show name or description
     * @param string $state State filter
     * @param string $city City filter
     * @param string $showDate Show date filter (YYYY-MM-DD)
     * @param int $fanVoting Fan voting filter (-1 = all, 0 = disabled, 1 = enabled)
     * @param int $limit Number of records per page
     * @param int $offset Offset for pagination
     * @return array Array of show objects
     */
    public function getFilteredShows($status, $search = '', $state = '', $city = '', $showDate = '', $fanVoting = -1, $limit = 20, $offset = 0) {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            $registrationsTableExists = $this->tableExists('registrations');
            
            // Build SQL based on available tables
            if ($usersTableExists && $registrationsTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
            } else if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
            } else if ($registrationsTableExists) {
                $sql = 'SELECT s.*, NULL as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s';
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name, 0 as registration_count 
                        FROM shows s';
            }
            
            // Start building WHERE clause
            $whereConditions = [];
            $params = [];
            
            // Status filter (required)
            $whereConditions[] = 's.status = :status';
            $params[':status'] = $status;
            
            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(s.name LIKE :search OR s.description LIKE :search)';
                $params[':search'] = '%' . $search . '%';
            }
            
            // Get all shows first to apply advanced filtering
            if (!empty($state) || !empty($city)) {
                // We'll filter these after getting the results
                $preFilterByLocation = true;
            } else {
                $preFilterByLocation = false;
            }
            
            // Show date filter
            if (!empty($showDate)) {
                $whereConditions[] = '(:show_date BETWEEN s.start_date AND s.end_date)';
                $params[':show_date'] = $showDate;
            }
            
            // Fan voting filter
            if ($fanVoting !== -1) {
                $whereConditions[] = 's.fan_voting_enabled = :fan_voting';
                $params[':fan_voting'] = $fanVoting;
            }
            
            // Combine WHERE conditions
            if (!empty($whereConditions)) {
                $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
            }
            
            // Add ORDER BY
            $sql .= ' ORDER BY s.start_date DESC';
            
            // If we're doing location filtering, we need to get all results first
            if ($preFilterByLocation) {
                // Execute query without pagination
                $this->db->query($sql);
                
                // Bind parameters
                foreach ($params as $param => $value) {
                    $this->db->bind($param, $value);
                }
                
                $allShows = $this->db->resultSet();
                $filteredShows = [];
                
                // Parse location for each show and apply city/state filters
                foreach ($allShows as $show) {
                    $locationData = $this->parseLocation($show->location);
                    $show->city = $locationData['city'];
                    $show->state = $locationData['state'];
                    
                    // Debug mode check
                    $debug = defined('DEBUG_MODE') && DEBUG_MODE;
                    if ($debug) {
                        error_log("ShowModel::getFilteredShows - Show ID: " . $show->id . 
                                  ", Location: '" . $show->location . "'" .
                                  ", Parsed City: '" . $show->city . "'" . 
                                  ", Parsed State: '" . $show->state . "'");
                    }
                    
                    // Apply state filter
                    $stateMatch = empty($state);
                    if (!$stateMatch && !empty($show->state)) {
                        // Direct match on state code
                        if (strtoupper($show->state) === strtoupper($state)) {
                            $stateMatch = true;
                            if ($debug) error_log("ShowModel::getFilteredShows - State match (direct): " . $state);
                        }
                        // Match state name to code
                        else if (isset($stateMap[strtoupper($state)]) && 
                                 strtolower($stateMap[strtoupper($state)]) === strtolower($show->state)) {
                            $stateMatch = true;
                            if ($debug) error_log("ShowModel::getFilteredShows - State match (name to code): " . $state);
                        }
                        // Match state code to name
                        else if (isset($stateMap[strtoupper($show->state)]) && 
                                 strtolower($state) === strtolower($stateMap[strtoupper($show->state)])) {
                            $stateMatch = true;
                            if ($debug) error_log("ShowModel::getFilteredShows - State match (code to name): " . $state);
                        }
                        // Check if state appears anywhere in the location string
                        else if (stripos($show->location, $state) !== false) {
                            $stateMatch = true;
                            if ($debug) error_log("ShowModel::getFilteredShows - State match (in location): " . $state);
                        }
                    }
                    
                    // Apply city filter
                    $cityMatch = empty($city);
                    if (!$cityMatch && !empty($show->city)) {
                        // Direct match on city name
                        if (strtolower($show->city) === strtolower($city)) {
                            $cityMatch = true;
                            if ($debug) error_log("ShowModel::getFilteredShows - City match (direct): " . $city);
                        }
                        // Check if city appears anywhere in the location string
                        else if (stripos($show->location, $city) !== false) {
                            $cityMatch = true;
                            if ($debug) error_log("ShowModel::getFilteredShows - City match (in location): " . $city);
                        }
                    }
                    
                    // Add to filtered results if both filters match
                    if ($stateMatch && $cityMatch) {
                        $filteredShows[] = $show;
                        if ($debug) error_log("ShowModel::getFilteredShows - Show ID: " . $show->id . " matched filters");
                    } else {
                        if ($debug) error_log("ShowModel::getFilteredShows - Show ID: " . $show->id . " did not match filters. State match: " . 
                                             ($stateMatch ? 'Yes' : 'No') . ", City match: " . ($cityMatch ? 'Yes' : 'No'));
                    }
                }
                
                // Apply pagination to the filtered results
                $totalCount = count($filteredShows);
                $paginatedShows = array_slice($filteredShows, $offset, $limit);
                
                // Store the total count for later use in countFilteredShows
                $GLOBALS['_filtered_shows_count'] = $totalCount;
                
                return $paginatedShows;
            } else {
                // Add LIMIT and OFFSET for pagination when not filtering by location
                $sql .= ' LIMIT :limit OFFSET :offset';
                $params[':limit'] = $limit;
                $params[':offset'] = $offset;
                
                // Execute query
                $this->db->query($sql);
                
                // Bind parameters
                foreach ($params as $param => $value) {
                    if ($param === ':limit' || $param === ':offset') {
                        $this->db->bind($param, $value, PDO::PARAM_INT);
                    } else {
                        $this->db->bind($param, $value);
                    }
                }
                
                $shows = $this->db->resultSet();
                
                // Parse location for each show
                foreach ($shows as $show) {
                    $locationData = $this->parseLocation($show->location);
                    $show->city = $locationData['city'];
                    $show->state = $locationData['state'];
                }
                
                return $shows;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::getFilteredShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count filtered shows (for pagination)
     * 
     * @param string $status Status filter (e.g., 'published')
     * @param string $search Search term for show name or description
     * @param string $state State filter
     * @param string $city City filter
     * @param string $showDate Show date filter (YYYY-MM-DD)
     * @param int $fanVoting Fan voting filter (-1 = all, 0 = disabled, 1 = enabled)
     * @return int Total count of filtered shows
     */
    public function countFilteredShows($status, $search = '', $state = '', $city = '', $showDate = '', $fanVoting = -1) {
        try {
            // If we're filtering by location and we already have the count from getFilteredShows
            if ((!empty($state) || !empty($city)) && isset($GLOBALS['_filtered_shows_count'])) {
                return $GLOBALS['_filtered_shows_count'];
            }
            
            // Otherwise, perform a count query
            // Build SQL
            $sql = 'SELECT COUNT(*) as total FROM shows s';
            
            // Start building WHERE clause
            $whereConditions = [];
            $params = [];
            
            // Status filter (required)
            $whereConditions[] = 's.status = :status';
            $params[':status'] = $status;
            
            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(s.name LIKE :search OR s.description LIKE :search)';
                $params[':search'] = '%' . $search . '%';
            }
            
            // If we're filtering by location, we need to get all results and filter them
            if (!empty($state) || !empty($city)) {
                // We'll need to get all shows and filter them manually
                if (!empty($whereConditions)) {
                    $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
                }
                
                // Execute query
                $this->db->query($sql);
                
                // Bind parameters
                foreach ($params as $param => $value) {
                    $this->db->bind($param, $value);
                }
                
                $allShows = $this->db->resultSet();
                $count = 0;
                
                // Parse location for each show and apply city/state filters
                foreach ($allShows as $show) {
                    $locationData = $this->parseLocation($show->location);
                    $show->city = $locationData['city'];
                    $show->state = $locationData['state'];
                    
                    // Debug mode check
                    $debug = defined('DEBUG_MODE') && DEBUG_MODE;
                    if ($debug) {
                        error_log("ShowModel::countFilteredShows - Show ID: " . $show->id . 
                                  ", Location: '" . $show->location . "'" .
                                  ", Parsed City: '" . $show->city . "'" . 
                                  ", Parsed State: '" . $show->state . "'");
                    }
                    
                    // Apply state filter
                    $stateMatch = empty($state);
                    if (!$stateMatch && !empty($show->state)) {
                        // Direct match on state code
                        if (strtoupper($show->state) === strtoupper($state)) {
                            $stateMatch = true;
                        }
                        // Match state name to code
                        else if (isset($stateMap[strtoupper($state)]) && 
                                 strtolower($stateMap[strtoupper($state)]) === strtolower($show->state)) {
                            $stateMatch = true;
                        }
                        // Match state code to name
                        else if (isset($stateMap[strtoupper($show->state)]) && 
                                 strtolower($state) === strtolower($stateMap[strtoupper($show->state)])) {
                            $stateMatch = true;
                        }
                        // Check if state appears anywhere in the location string
                        else if (stripos($show->location, $state) !== false) {
                            $stateMatch = true;
                        }
                    }
                    
                    // Apply city filter
                    $cityMatch = empty($city);
                    if (!$cityMatch && !empty($show->city)) {
                        // Direct match on city name
                        if (strtolower($show->city) === strtolower($city)) {
                            $cityMatch = true;
                        }
                        // Check if city appears anywhere in the location string
                        else if (stripos($show->location, $city) !== false) {
                            $cityMatch = true;
                        }
                    }
                    
                    // Count if both filters match
                    if ($stateMatch && $cityMatch) {
                        $count++;
                        if ($debug) error_log("ShowModel::countFilteredShows - Show ID: " . $show->id . " matched filters");
                    }
                }
                
                return $count;
            } else {
                // For non-location filters, we can use a simple COUNT query
                
                // Show date filter
                if (!empty($showDate)) {
                    $whereConditions[] = '(:show_date BETWEEN s.start_date AND s.end_date)';
                    $params[':show_date'] = $showDate;
                }
                
                // Fan voting filter
                if ($fanVoting !== -1) {
                    $whereConditions[] = 's.fan_voting_enabled = :fan_voting';
                    $params[':fan_voting'] = $fanVoting;
                }
                
                // Combine WHERE conditions
                if (!empty($whereConditions)) {
                    $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
                }
                
                // Execute query
                $this->db->query($sql);
                
                // Bind parameters
                foreach ($params as $param => $value) {
                    $this->db->bind($param, $value);
                }
                
                $result = $this->db->single();
                return $result->total;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::countFilteredShows: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get unique states from shows
     * 
     * @param string $selectedState Optional state to filter cities by
     * @return array Array of unique states
     */
    public function getUniqueStatesFromShows($selectedState = '') {
        try {
            // Debug mode check
            $debug = defined('DEBUG_MODE') && DEBUG_MODE;
            
            // Common state abbreviations and full names mapping
            $stateAbbreviations = [
                'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas',
                'CA' => 'California', 'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware',
                'FL' => 'Florida', 'GA' => 'Georgia', 'HI' => 'Hawaii', 'ID' => 'Idaho',
                'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa', 'KS' => 'Kansas',
                'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
                'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi',
                'MO' => 'Missouri', 'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada',
                'NH' => 'New Hampshire', 'NJ' => 'New Jersey', 'NM' => 'New Mexico', 'NY' => 'New York',
                'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio', 'OK' => 'Oklahoma',
                'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
                'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah',
                'VT' => 'Vermont', 'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia',
                'WI' => 'Wisconsin', 'WY' => 'Wyoming', 'DC' => 'District of Columbia'
            ];
            
            // Get all published shows
            $shows = $this->getShows('published');
            $states = [];
            
            if ($debug) {
                error_log("ShowModel::getUniqueStatesFromShows - Processing " . count($shows) . " shows");
            }
            
            // Extract states using the location parser
            foreach ($shows as $show) {
                $locationData = $this->parseLocation($show->location);
                
                if ($debug) {
                    error_log("ShowModel::getUniqueStatesFromShows - Show ID: " . $show->id . 
                              ", Location: '" . $show->location . "'" .
                              ", Parsed State: '" . $locationData['state'] . "'");
                }
                
                if (!empty($locationData['state'])) {
                    $stateCode = $locationData['state'];
                    
                    // If we have a 2-letter state code, use it with its full name
                    if (strlen($stateCode) == 2 && isset($stateAbbreviations[strtoupper($stateCode)])) {
                        $stateCode = strtoupper($stateCode);
                        if (!isset($states[$stateCode])) {
                            $states[$stateCode] = $stateCode . ' - ' . $stateAbbreviations[$stateCode];
                            if ($debug) {
                                error_log("ShowModel::getUniqueStatesFromShows - Added state: " . $stateCode . 
                                          " (" . $stateAbbreviations[$stateCode] . ")");
                            }
                        }
                    } 
                    // If it's a full state name, try to map it to a code
                    else {
                        $stateFound = false;
                        foreach ($stateAbbreviations as $abbr => $name) {
                            if (strtolower($name) === strtolower($stateCode)) {
                                if (!isset($states[$abbr])) {
                                    $states[$abbr] = $abbr . ' - ' . $name;
                                    if ($debug) {
                                        error_log("ShowModel::getUniqueStatesFromShows - Mapped state name to code: " . 
                                                  $stateCode . " -> " . $abbr);
                                    }
                                }
                                $stateFound = true;
                                break;
                            }
                        }
                        
                        // If it's not a recognized state name, use as-is
                        if (!$stateFound && !isset($states[$stateCode])) {
                            $states[$stateCode] = $stateCode;
                            if ($debug) {
                                error_log("ShowModel::getUniqueStatesFromShows - Added non-standard state: " . $stateCode);
                            }
                        }
                    }
                }
            }
            
            // Sort states alphabetically
            asort($states);
            
            // Convert to objects to match expected format
            $result = [];
            foreach ($states as $code => $displayName) {
                $stateObj = new stdClass();
                $stateObj->state = $code;
                $stateObj->display_name = $displayName;
                $result[] = $stateObj;
            }
            
            if ($debug) {
                error_log("ShowModel::getUniqueStatesFromShows - Returning " . count($result) . " unique states");
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUniqueStatesFromShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get unique cities from shows
     * 
     * @param string $stateFilter Optional state to filter cities by
     * @return array Array of unique cities
     */
    public function getUniqueCitiesFromShows($stateFilter = '') {
        try {
            // Debug mode check
            $debug = defined('DEBUG_MODE') && DEBUG_MODE;
            
            // Get all published shows
            $shows = $this->getShows('published');
            $cities = [];
            
            if ($debug) {
                error_log("ShowModel::getUniqueCitiesFromShows - Processing " . count($shows) . " shows" . 
                          ($stateFilter ? " for state: " . $stateFilter : ""));
            }
            
            // Common state abbreviations and full names mapping
            $stateAbbreviations = [
                'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas',
                'CA' => 'California', 'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware',
                'FL' => 'Florida', 'GA' => 'Georgia', 'HI' => 'Hawaii', 'ID' => 'Idaho',
                'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa', 'KS' => 'Kansas',
                'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
                'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi',
                'MO' => 'Missouri', 'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada',
                'NH' => 'New Hampshire', 'NJ' => 'New Jersey', 'NM' => 'New Mexico', 'NY' => 'New York',
                'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio', 'OK' => 'Oklahoma',
                'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
                'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah',
                'VT' => 'Vermont', 'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia',
                'WI' => 'Wisconsin', 'WY' => 'Wyoming', 'DC' => 'District of Columbia'
            ];
            
            // Flip the map to search by full state name
            $stateNameToAbbr = array_flip($stateAbbreviations);
            
            // Extract cities using the location parser
            foreach ($shows as $show) {
                $locationData = $this->parseLocation($show->location);
                
                if ($debug) {
                    error_log("ShowModel::getUniqueCitiesFromShows - Show ID: " . $show->id . 
                              ", Location: '" . $show->location . "'" .
                              ", Parsed City: '" . $locationData['city'] . "'" . 
                              ", Parsed State: '" . $locationData['state'] . "'");
                }
                
                // Skip if city is empty
                if (empty($locationData['city'])) {
                    continue;
                }
                
                // If state filter is provided, only include cities from that state
                if (!empty($stateFilter)) {
                    $stateMatch = false;
                    
                    // Direct match on state code
                    if (strtoupper($locationData['state']) === strtoupper($stateFilter)) {
                        $stateMatch = true;
                    }
                    // Match state name to code
                    else if (isset($stateAbbreviations[strtoupper($stateFilter)]) && 
                             strtolower($stateAbbreviations[strtoupper($stateFilter)]) === strtolower($locationData['state'])) {
                        $stateMatch = true;
                    }
                    // Match state code to name
                    else if (isset($stateAbbreviations[strtoupper($locationData['state'])]) && 
                             strtolower($stateFilter) === strtolower($stateAbbreviations[strtoupper($locationData['state'])])) {
                        $stateMatch = true;
                    }
                    
                    if ($stateMatch && !in_array($locationData['city'], $cities)) {
                        $cities[] = $locationData['city'];
                        if ($debug) {
                            error_log("ShowModel::getUniqueCitiesFromShows - Added city: " . $locationData['city'] . 
                                      " for state: " . $stateFilter);
                        }
                    }
                } else {
                    // No state filter, include all cities
                    if (!in_array($locationData['city'], $cities)) {
                        $cities[] = $locationData['city'];
                        if ($debug) {
                            error_log("ShowModel::getUniqueCitiesFromShows - Added city: " . $locationData['city']);
                        }
                    }
                }
            }
            
            // Sort cities alphabetically
            sort($cities);
            
            // Convert to objects to match expected format
            $result = [];
            foreach ($cities as $city) {
                $cityObj = new stdClass();
                $cityObj->city = $city;
                $result[] = $cityObj;
            }
            
            if ($debug) {
                error_log("ShowModel::getUniqueCitiesFromShows - Returning " . count($result) . " unique cities" . 
                          ($stateFilter ? " for state: " . $stateFilter : ""));
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUniqueCitiesFromShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get upcoming shows
     * 
     * @param int $limit Optional limit
     * @return array
     */
    public function getUpcomingShows($limit = null) {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            $registrationsTableExists = $this->tableExists('registrations');
            
            // Build SQL based on available tables
            if ($usersTableExists && $registrationsTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id 
                        WHERE s.status = :status AND s.start_date >= CURDATE() 
                        ORDER BY s.start_date ASC';
            } else if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id 
                        WHERE s.status = :status AND s.start_date >= CURDATE() 
                        ORDER BY s.start_date ASC';
                error_log("Registrations table not found, using simplified query for getUpcomingShows");
            } else if ($registrationsTableExists) {
                $sql = 'SELECT s.*, NULL as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        WHERE s.status = :status AND s.start_date >= CURDATE() 
                        ORDER BY s.start_date ASC';
                error_log("Users table not found, using simplified query for getUpcomingShows");
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        WHERE s.status = :status AND s.start_date >= CURDATE() 
                        ORDER BY s.start_date ASC';
                error_log("Users and registrations tables not found, using simplified query for getUpcomingShows");
            }
            
            if ($limit) {
                $sql .= ' LIMIT :limit';
            }
            
            $this->db->query($sql);
            $this->db->bind(':status', 'published');
            
            if ($limit) {
                $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            }
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUpcomingShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get FormFieldManager instance
     * 
     * @return FormFieldManager
     */
    private function getFieldManager() {
        try {
            require_once APPROOT . '/models/FormFieldManager.php';
            return new FormFieldManager();
        } catch (Exception $e) {
            error_log("Error creating FormFieldManager: " . $e->getMessage());
            // Still try to return a FormFieldManager instance
            return new FormFieldManager();
        }
    }
    
    /**
     * Get FormFieldManager instance (replaces old emergency manager)
     * 
     * @return FormFieldManager
     */
    private function getEmergencyFieldManager() {
        require_once APPROOT . '/models/FormFieldManager.php';
        return new FormFieldManager();
    }
    
    /**
     * Detect field type based on field name
     * 
     * @param string $fieldName Field name
     * @return string Field type
     */
    private function detectFieldType($fieldName) {
        // Known field types
        $knownFields = [
            'field_1747938252386' => 'textarea',
            'field_1747938237290' => 'text',
            'description' => 'textarea',
            'listing_fee' => 'number'
        ];
        
        if (isset($knownFields[$fieldName])) {
            return $knownFields[$fieldName];
        }
        
        // Check for field type in the name
        if (strpos($fieldName, 'textarea_') === 0 || $fieldName === 'description' || 
            strpos($fieldName, '_description') !== false || strpos($fieldName, '_notes') !== false) {
            return 'textarea';
        } elseif (strpos($fieldName, 'richtext_') === 0 || strpos($fieldName, '_richtext') !== false || 
                 strpos($fieldName, '_content') !== false || strpos($fieldName, '_html') !== false) {
            return 'richtext';
        } elseif (strpos($fieldName, 'checkbox_') === 0 || strpos($fieldName, '_checkbox') !== false || 
                 strpos($fieldName, '_enabled') !== false || strpos($fieldName, '_active') !== false || 
                 strpos($fieldName, '_is_') !== false) {
            return 'checkbox';
        } elseif (strpos($fieldName, 'number_') === 0 || strpos($fieldName, '_number') !== false || 
                 strpos($fieldName, '_fee') !== false || strpos($fieldName, '_price') !== false || 
                 strpos($fieldName, '_amount') !== false) {
            return 'number';
        } elseif (strpos($fieldName, 'date_') === 0 || strpos($fieldName, '_date') !== false) {
            return 'date';
        } elseif (strpos($fieldName, 'email_') === 0 || strpos($fieldName, '_email') !== false) {
            return 'email';
        } elseif (strpos($fieldName, 'tel_') === 0 || strpos($fieldName, '_tel') !== false || 
                 strpos($fieldName, '_phone') !== false || strpos($fieldName, '_mobile') !== false) {
            return 'tel';
        } elseif (strpos($fieldName, 'url_') === 0 || strpos($fieldName, '_url') !== false || 
                 strpos($fieldName, '_link') !== false || strpos($fieldName, '_website') !== false) {
            return 'url';
        } elseif (strpos($fieldName, 'file_') === 0 || strpos($fieldName, '_file') !== false || 
                 strpos($fieldName, '_upload') !== false || strpos($fieldName, '_attachment') !== false) {
            return 'file';
        } elseif (strpos($fieldName, 'select_') === 0 || strpos($fieldName, '_select') !== false || 
                 strpos($fieldName, '_dropdown') !== false || strpos($fieldName, '_options') !== false) {
            return 'select';
        } elseif (strpos($fieldName, 'radio_') === 0 || strpos($fieldName, '_radio') !== false || 
                 strpos($fieldName, '_choice') !== false || strpos($fieldName, '_option') !== false) {
            return 'radio';
        }
        
        // Try to detect field type from field_mappings table
        try {
            $db = new Database();
            $db->query("SELECT field_type FROM field_mappings WHERE form_field_id = :field_id");
            $db->bind(':field_id', $fieldName);
            $result = $db->single();
            
            if ($result && isset($result->field_type) && !empty($result->field_type)) {
                error_log("ShowModel::detectFieldType - Found field type '{$result->field_type}' for field '{$fieldName}' in field_mappings table");
                return $result->field_type;
            }
        } catch (Exception $e) {
            error_log("ShowModel::detectFieldType - Error checking field_mappings table: " . $e->getMessage());
        }
        
        // Default to text
        return 'text';
    }
    
    /**
     * Get mapped database column for a template field
     * 
     * @param string $fieldId Template field ID
     * @return string|null Mapped column name or null if no mapping exists
     */
    private function getMappedColumnForField($fieldId) {
        try {
            $fieldManager = $this->getFieldManager();
            return $fieldManager->getMappedColumn($fieldId);
        } catch (Exception $e) {
            error_log("Error in getMappedColumnForField: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get completed shows with awards for a user
     * 
     * @param int $userId User ID
     * @return array
     */
    public function getUserCompletedShows($userId) {
        try {
            // First get all completed shows the user has registered for
            $this->db->query('SELECT DISTINCT s.id, s.name, s.start_date as date, s.location
                              FROM shows s
                              JOIN registrations r ON s.id = r.show_id
                              JOIN vehicles v ON r.vehicle_id = v.id
                              WHERE v.owner_id = :user_id 
                              AND s.status = :status
                              AND s.start_date < NOW()
                              ORDER BY s.start_date DESC');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':status', 'completed');
            
            $shows = $this->db->resultSet();
            
            // Check if awards table exists
            $awardsTableExists = $this->tableExists('awards');
            $judgingResultsTableExists = $this->tableExists('judging_results');
            
            // For each show, get the awards if the table exists
            foreach ($shows as $show) {
                // Initialize empty arrays for awards and results
                $show->awards = [];
                $show->results = [];
                
                if ($awardsTableExists) {
                    // Get awards for this user in this show
                    $this->db->query('SELECT a.id, a.name, v.year as vehicle_year, v.make as vehicle_make, v.model as vehicle_model
                                      FROM awards a
                                      JOIN registrations r ON a.registration_id = r.id
                                      JOIN vehicles v ON r.vehicle_id = v.id
                                      WHERE v.owner_id = :user_id AND r.show_id = :show_id');
                    $this->db->bind(':user_id', $userId);
                    $this->db->bind(':show_id', $show->id);
                    
                    $show->awards = $this->db->resultSet();
                }
                
                if ($judgingResultsTableExists) {
                    // Get judging results for this user in this show
                    $this->db->query('SELECT jr.*, jm.name as metric_name, jm.max_score,
                                      v.year as vehicle_year, v.make as vehicle_make, v.model as vehicle_model
                                      FROM judging_results jr
                                      JOIN judging_metrics jm ON jr.metric_id = jm.id
                                      JOIN registrations r ON jr.registration_id = r.id
                                      JOIN vehicles v ON r.vehicle_id = v.id
                                      WHERE v.owner_id = :user_id AND r.show_id = :show_id');
                    $this->db->bind(':user_id', $userId);
                    $this->db->bind(':show_id', $show->id);
                    
                    $show->results = $this->db->resultSet();
                }
            }
            
            return $shows;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUserCompletedShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get show by ID
     * 
     * @param int $id Show ID
     * @return object|bool Show object or false if not found
     */
    public function getShowById($id) {
        try {
            // Validate ID
            $id = intval($id);
            if ($id <= 0) {
                error_log("Invalid show ID in getShowById: " . $id);
                return false;
            }
            
            // Check if users table exists
            $usersTableExists = $this->tableExists('users');
            
            // Get all columns from the shows table
            $this->db->query("SHOW COLUMNS FROM shows");
            $this->db->execute();
            $columns = $this->db->resultSet();
            
            // Build column list
            $columnList = [];
            foreach ($columns as $column) {
                $columnList[] = 's.' . $column->Field;
            }
            
            // Use different query based on whether users table exists
            if ($usersTableExists) {
                // Query with users join
                $this->db->query('SELECT ' . implode(', ', $columnList) . ', u.name as coordinator_name 
                                FROM shows s 
                                LEFT JOIN users u ON s.coordinator_id = u.id 
                                WHERE s.id = :id');
            } else {
                // Query without users join
                $this->db->query('SELECT ' . implode(', ', $columnList) . ', NULL as coordinator_name 
                                FROM shows s 
                                WHERE s.id = :id');
                error_log("Users table not found, using simplified query for show ID: " . $id);
            }
            
            $this->db->bind(':id', $id);
            
            $result = $this->db->single();
            
            // Log if no result found
            if (!$result) {
                error_log("No show found with ID: " . $id);
                return false;
            }
            
            // Get custom field values from the new table if available
            if (isset($this->customFieldValuesModel) && $this->customFieldValuesModel->tableExists()) {
                $customFieldValues = $this->customFieldValuesModel->getCustomFieldValues($id);
                
                if (!empty($customFieldValues)) {
                    error_log("ShowModel::getShowById - Found " . count($customFieldValues) . " custom field values in custom_field_values table");
                    
                    // Add custom field values to the result
                    foreach ($customFieldValues as $fieldId => $fieldData) {
                        // Create a property for each custom field
                        $result->{$fieldId} = $fieldData['value'];
                        
                        // Also log the field type for debugging
                        error_log("ShowModel::getShowById - Field {$fieldId} has type {$fieldData['type']} and value length: " . strlen($fieldData['value']));
                    }
                }
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowById: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new show
     * 
     * @param array $data Show data
     * @return int|bool New show ID or false on failure
     */
    public function createShow($data) {
        try {
            // Get all columns from the shows table
            $this->db->query("SHOW COLUMNS FROM shows");
            $this->db->execute();
            $columns = $this->db->resultSet();
            
            // Extract column names
            $columnNames = [];
            foreach ($columns as $column) {
                $columnNames[] = $column->Field;
            }
            
            // Build dynamic SQL query
            // Use class properties instead of local variables
            $fields = [];
            $values = [];
            $bindings = [];
            
            foreach ($data as $key => $value) {
                // Skip system fields
                if (in_array($key, $this->systemFields)) {
                    continue;
                }
                
                // Check if the column exists in the database
                if (in_array($key, $columnNames)) {
                    $fields[] = $key;
                    $values[] = ':' . $key;
                    $bindings[$key] = $value;
                } else {
                    // Log missing column
                    error_log("Warning: Column '{$key}' does not exist in shows table. Value will not be saved.");
                }
            }
            
            // Build and execute the query
            $sql = 'INSERT INTO shows (' . implode(', ', $fields) . ') VALUES (' . implode(', ', $values) . ')';
            $this->db->query($sql);
            
            // Bind all parameters
            foreach ($bindings as $param => $value) {
                $this->db->bind(':' . $param, $value);
            }
            
            $result = $this->db->execute();
            
            if ($result) {
                $showId = $this->db->lastInsertId();
                
                // Save custom field values to the new table if available
                if (isset($this->customFieldValuesModel)) {
                    $customFieldValues = [];
                    $customFieldTypes = [];
                    
                    // Extract custom fields from the data
                    foreach ($data as $key => $value) {
                        // Skip standard fields and system fields
                        if (in_array($key, $this->standardFields) || in_array($key, $this->systemFields)) {
                            continue;
                        }
                        
                        // If the key starts with field_ or is a known custom field, add it to the custom field values
                        if (strpos($key, 'field_') === 0 || strpos($key, 'custom_') === 0) {
                            $customFieldValues[$key] = $value;
                            
                            // Use our own detectFieldType method
                            $customFieldTypes[$key] = $this->detectFieldType($key);
                        }
                    }
                    
                    // Save custom field values to the new table
                    if (!empty($customFieldValues)) {
                        $saveResult = $this->customFieldValuesModel->saveCustomFieldValues($showId, $customFieldValues, $customFieldTypes);
                        if ($saveResult) {
                            error_log("ShowModel::createShow - Saved " . count($customFieldValues) . " custom field values to custom_field_values table");
                        } else {
                            error_log("ShowModel::createShow - Failed to save custom field values to custom_field_values table");
                        }
                    }
                }
                
                error_log("Successfully created show with ID: {$showId}");
                
                // Create calendar event for the show if calendar integration is enabled
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("ShowModel::createShow - Attempting to create calendar event for show ID: {$showId}");
                }
                
                try {
                    // Initialize CustomFieldValuesModel for use throughout this method
                    require_once APPROOT . '/models/CustomFieldValuesModel.php';
                    $customFieldValuesModel = new CustomFieldValuesModel();
                    
                    // Get address components from custom field values if available
                    $address1 = '';
                    $address2 = '';
                    $city = '';
                    $state = null;
                    $zipcode = '';
                    
                    // Get address1 (either from data or custom fields)
                    if (!empty($data['address1'])) {
                        $address1 = $data['address1'];
                    } else {
                        $address1Value = $customFieldValuesModel->getCustomFieldValue($showId, 'address1');
                        if ($address1Value) {
                            $address1 = $address1Value;
                        } else {
                            // Fallback to location field
                            $address1 = $data['location'] ?? '';
                        }
                    }
                    
                    // Get address2 if available
                    if (!empty($data['address2'])) {
                        $address2 = $data['address2'];
                    } else {
                        $address2Value = $customFieldValuesModel->getCustomFieldValue($showId, 'address2');
                        if ($address2Value) {
                            $address2 = $address2Value;
                        }
                    }
                    
                    // Get city if available
                    if (!empty($data['city'])) {
                        $city = $data['city'];
                    } else {
                        $cityValue = $customFieldValuesModel->getCustomFieldValue($showId, 'city');
                        if ($cityValue) {
                            $city = $cityValue;
                        } else if (!empty($data['location'])) {
                            // Fallback to location parsing
                            $locationData = $this->parseLocation($data['location']);
                            $city = $locationData['city'];
                        }
                    }
                    
                    // Get state from custom field values if available
                    if (!empty($data['state'])) {
                        $state = $data['state'];
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("ShowModel::createShow - Using state from form data: {$state}");
                        }
                    } else {
                        // Try to get state from custom field values
                        $stateValue = $customFieldValuesModel->getCustomFieldValue($showId, 'state');
                        
                        if ($stateValue) {
                            $state = $stateValue;
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("ShowModel::createShow - Using state from custom field values: {$state}");
                            }
                        } else if (!empty($data['location'])) {
                            // Fallback to location parsing if no state field is found
                            $locationData = $this->parseLocation($data['location']);
                            $state = $locationData['state'];
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("ShowModel::createShow - Extracted state from location: {$state}");
                            }
                        }
                    }
                    
                    // Get zipcode if available
                    if (!empty($data['zipcode'])) {
                        $zipcode = $data['zipcode'];
                    } else {
                        $zipcodeValue = $customFieldValuesModel->getCustomFieldValue($showId, 'zipcode');
                        if ($zipcodeValue) {
                            $zipcode = $zipcodeValue;
                        }
                    }
                    
                    // Geocode the show's address if we have enough address information
                    if (!empty($address1) || !empty($city) || !empty($state) || !empty($zipcode)) {
                        try {
                            // Load geocoding helper
                            require_once APPROOT . '/helpers/geocoding_helper.php';
                            
                            // Create address array for geocoding
                            $addressData = [
                                'address1' => $address1,
                                'address2' => $address2,
                                'city' => $city,
                                'state' => $state,
                                'zipcode' => $zipcode
                            ];
                            
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("ShowModel::createShow - Attempting to geocode show address: " . json_encode($addressData));
                            }
                            
                            // Get coordinates
                            $coordinates = geocodeAddress($addressData);
                            
                            // If geocoding was successful, update the show's lat/lng
                            if ($coordinates && isset($coordinates['lat']) && isset($coordinates['lng'])) {
                                $this->db->query("UPDATE shows SET lat = :lat, lng = :lng WHERE id = :id");
                                $this->db->bind(':id', $showId);
                                $this->db->bind(':lat', $coordinates['lat']);
                                $this->db->bind(':lng', $coordinates['lng']);
                                $this->db->execute();
                                
                                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                    error_log("ShowModel::createShow - Successfully geocoded show address: lat={$coordinates['lat']}, lng={$coordinates['lng']}");
                                }
                            } else {
                                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                    error_log("ShowModel::createShow - Failed to geocode show address");
                                }
                            }
                        } catch (Exception $e) {
                            error_log("ShowModel::createShow - Error geocoding show address: " . $e->getMessage());
                        }
                    }
                    
                    $calendarModel = $this->getCalendarModel();
                    
                    // Create event in state-specific calendar
                    if (!empty($state)) {
                        // Get or create a calendar for this state
                        $stateCalendarId = $calendarModel->getOrCreateStateCalendar($state, $data['coordinator_id'] ?? null);
                        
                        if ($stateCalendarId) {
                            // Create event in the state calendar
                            $eventId = $calendarModel->createEventFromShow($showId, $stateCalendarId, $data['coordinator_id'] ?? null);
                            
                            if ($eventId) {
                                error_log("ShowModel::createShow - Successfully created calendar event with ID: {$eventId} in state calendar for show ID: {$showId}");
                            } else {
                                error_log("ShowModel::createShow - Failed to create calendar event in state calendar for show ID: {$showId}");
                            }
                        } else {
                            error_log("ShowModel::createShow - Failed to get or create state calendar for state: {$state}");
                        }
                    } else {
                        // Fallback to default calendar if state cannot be determined
                        $settingsModel = new SettingsModel();
                        $defaultCalendarId = $settingsModel->getSetting('default_show_calendar_id', null);
                        
                        if ($defaultCalendarId) {
                            $eventId = $calendarModel->createEventFromShow($showId, $defaultCalendarId, $data['coordinator_id'] ?? null);
                            
                            if ($eventId) {
                                error_log("ShowModel::createShow - Successfully created calendar event with ID: {$eventId} in default calendar for show ID: {$showId}");
                            } else {
                                error_log("ShowModel::createShow - Failed to create calendar event in default calendar for show ID: {$showId}");
                            }
                        } else {
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("ShowModel::createShow - No default calendar ID found in settings and no state determined, skipping calendar event creation");
                            }
                        }
                    }
                } catch (Exception $e) {
                    error_log("ShowModel::createShow - Error creating calendar event: " . $e->getMessage());
                }
                
                return $showId;
            } else {
                error_log("Failed to create show");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::createShow: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update an existing show
     * 
     * @param array $data Show data
     * @return bool True on success, false on failure
     */
    public function updateShow($data) {
        try {
            // Process form data with DynamicFormFieldManager if available
            if (isset($this->formFieldManager)) {
                error_log("ShowModel::updateShow - Using DynamicFormFieldManager to process form data");
                $data = $this->formFieldManager->processFormData($data);
            }
            
            // Get all columns from the shows table
            $this->db->query("SHOW COLUMNS FROM shows");
            $this->db->execute();
            $columns = $this->db->resultSet();
            
            // Extract column names
            $columnNames = [];
            foreach ($columns as $column) {
                $columnNames[] = $column->Field;
            }
            
            // Build dynamic SQL query
            // Use class properties instead of local variables
            $updates = [];
            $bindings = [];
            
            foreach ($data as $key => $value) {
                // Skip system fields except id
                if (in_array($key, $this->systemFields) && $key !== 'id') {
                    continue;
                }
                
                // Check if the column exists in the database
                if (in_array($key, $columnNames)) {
                    if ($key !== 'id') { // Don't update the ID
                        $updates[] = $key . ' = :' . $key;
                    }
                    $bindings[$key] = $value;
                } else {
                    // Log missing column
                    error_log("Warning: Column '{$key}' does not exist in shows table. Value will not be saved.");
                }
            }
            
            // Build and execute the query
            $sql = 'UPDATE shows SET ' . implode(', ', $updates) . ' WHERE id = :id';
            $this->db->query($sql);
            
            // Bind all parameters
            foreach ($bindings as $param => $value) {
                $this->db->bind(':' . $param, $value);
            }
            
            $result = $this->db->execute();
            
            if ($result) {
                // Log success with detailed information about custom fields
                error_log("Successfully updated show with ID: {$data['id']}");
                
                // Update calendar event for the show if calendar integration is enabled
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("ShowModel::updateShow - Attempting to update calendar event for show ID: {$data['id']}");
                }
                
                try {
                    $calendarModel = $this->getCalendarModel();
                    $events = $calendarModel->getEventsByShowId($data['id']);
                    
                    // Initialize CustomFieldValuesModel for use throughout this method
                    require_once APPROOT . '/models/CustomFieldValuesModel.php';
                    $customFieldValuesModel = new CustomFieldValuesModel();
                    
                    // Get state from custom field values if available
                    $state = null;
                    
                    // First check if state is directly provided in the data
                    if (!empty($data['state'])) {
                        $state = $data['state'];
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("ShowModel::updateShow - Using state from form data: {$state}");
                        }
                    } else {
                        // Try to get state from custom field values
                        $stateValue = $customFieldValuesModel->getCustomFieldValue($data['id'], 'state');
                        
                        if ($stateValue) {
                            $state = $stateValue;
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("ShowModel::updateShow - Using state from custom field values: {$state}");
                            }
                        } else if (!empty($data['location'])) {
                            // Fallback to location parsing if no state field is found
                            $locationData = $this->parseLocation($data['location']);
                            $state = $locationData['state'];
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("ShowModel::updateShow - Extracted state from location: {$state}");
                            }
                        }
                    }
                    
                    // Get address components from custom field values if available
                    $address1 = '';
                    $address2 = '';
                    $city = '';
                    $zipcode = '';
                    
                    // Check if address fields are directly provided in the data
                    if (!empty($data['address1'])) {
                        $address1 = $data['address1'];
                    } else {
                        // Try to get address1 from custom field values
                        $address1Value = $customFieldValuesModel->getCustomFieldValue($data['id'], 'address1');
                        if ($address1Value) {
                            $address1 = $address1Value;
                        } else {
                            // Fallback to location field
                            $address1 = $data['location'] ?? '';
                        }
                    }
                    
                    // Get address2 if available
                    if (!empty($data['address2'])) {
                        $address2 = $data['address2'];
                    } else {
                        $address2Value = $customFieldValuesModel->getCustomFieldValue($data['id'], 'address2');
                        if ($address2Value) {
                            $address2 = $address2Value;
                        }
                    }
                    
                    // Get city if available
                    if (!empty($data['city'])) {
                        $city = $data['city'];
                    } else {
                        $cityValue = $customFieldValuesModel->getCustomFieldValue($data['id'], 'city');
                        if ($cityValue) {
                            $city = $cityValue;
                        } else if (!empty($data['location'])) {
                            // Fallback to location parsing
                            $locationData = $this->parseLocation($data['location']);
                            $city = $locationData['city'];
                        }
                    }
                    
                    // Get zipcode if available
                    if (!empty($data['zipcode'])) {
                        $zipcode = $data['zipcode'];
                    } else {
                        $zipcodeValue = $customFieldValuesModel->getCustomFieldValue($data['id'], 'zipcode');
                        if ($zipcodeValue) {
                            $zipcode = $zipcodeValue;
                        }
                    }
                    
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("ShowModel::updateShow - Address components: address1={$address1}, city={$city}, state={$state}, zipcode={$zipcode}");
                    }
                    
                    // ALWAYS log address components for debugging
                    error_log("ShowModel::updateShow - GEOCODING DEBUG - Address components for show ID {$data['id']}: address1='{$address1}', address2='{$address2}', city='{$city}', state='{$state}', zipcode='{$zipcode}'");
                    
                    // Geocode the show's address if we have enough address information
                    if (!empty($address1) || !empty($city) || !empty($state) || !empty($zipcode)) {
                        error_log("ShowModel::updateShow - GEOCODING DEBUG - Starting geocoding process for show ID {$data['id']}");
                        
                        try {
                            // Load geocoding helper
                            error_log("ShowModel::updateShow - GEOCODING DEBUG - Loading geocoding_helper.php");
                            if (file_exists(APPROOT . '/helpers/geocoding_helper.php')) {
                                error_log("ShowModel::updateShow - GEOCODING DEBUG - geocoding_helper.php file exists");
                                require_once APPROOT . '/helpers/geocoding_helper.php';
                                error_log("ShowModel::updateShow - GEOCODING DEBUG - geocoding_helper.php loaded successfully");
                            } else {
                                error_log("ShowModel::updateShow - GEOCODING DEBUG - ERROR: geocoding_helper.php file not found!");
                                return true; // Continue with the update even if geocoding fails
                            }
                            
                            // Create address array for geocoding
                            $addressData = [
                                'address1' => $address1,
                                'address2' => $address2,
                                'city' => $city,
                                'state' => $state,
                                'zipcode' => $zipcode
                            ];
                            
                            error_log("ShowModel::updateShow - GEOCODING DEBUG - Address data for geocoding: " . json_encode($addressData));
                            
                            // Check if geocodeAddress function exists
                            if (!function_exists('geocodeAddress')) {
                                error_log("ShowModel::updateShow - GEOCODING DEBUG - ERROR: geocodeAddress function does not exist!");
                                return true; // Continue with the update even if geocoding fails
                            }
                            
                            error_log("ShowModel::updateShow - GEOCODING DEBUG - Calling geocodeAddress function");
                            
                            // Get coordinates
                            $coordinates = geocodeAddress($addressData);
                            
                            error_log("ShowModel::updateShow - GEOCODING DEBUG - geocodeAddress result: " . ($coordinates ? json_encode($coordinates) : "false"));
                            
                            // If geocoding was successful, update the show's lat/lng
                            if ($coordinates && isset($coordinates['lat']) && isset($coordinates['lng'])) {
                                error_log("ShowModel::updateShow - GEOCODING DEBUG - Updating database with coordinates: lat={$coordinates['lat']}, lng={$coordinates['lng']}");
                                
                                $this->db->query("UPDATE shows SET lat = :lat, lng = :lng WHERE id = :id");
                                $this->db->bind(':id', $data['id']);
                                $this->db->bind(':lat', $coordinates['lat']);
                                $this->db->bind(':lng', $coordinates['lng']);
                                $result = $this->db->execute();
                                
                                error_log("ShowModel::updateShow - GEOCODING DEBUG - Database update result: " . ($result ? "SUCCESS" : "FAILED"));
                                
                                // Double-check that the update worked
                                $this->db->query("SELECT lat, lng FROM shows WHERE id = :id");
                                $this->db->bind(':id', $data['id']);
                                $updatedShow = $this->db->single();
                                
                                if ($updatedShow) {
                                    error_log("ShowModel::updateShow - GEOCODING DEBUG - Verification check: lat={$updatedShow->lat}, lng={$updatedShow->lng}");
                                } else {
                                    error_log("ShowModel::updateShow - GEOCODING DEBUG - Verification check failed: Could not retrieve show data");
                                }
                            } else {
                                error_log("ShowModel::updateShow - GEOCODING DEBUG - Failed to geocode show address");
                            }
                        } catch (Exception $e) {
                            error_log("ShowModel::updateShow - GEOCODING DEBUG - Exception: " . $e->getMessage());
                            error_log("ShowModel::updateShow - GEOCODING DEBUG - Stack trace: " . $e->getTraceAsString());
                        }
                    } else {
                        error_log("ShowModel::updateShow - GEOCODING DEBUG - Not enough address information to geocode for show ID {$data['id']}");
                    }
                    
                    if (!empty($events)) {
                        // Update each event associated with this show
                        foreach ($events as $event) {
                            // Get the latest show data to ensure we have the most up-to-date lat/lng values
                            $this->db->query("SELECT lat, lng FROM shows WHERE id = :id");
                            $this->db->bind(':id', $data['id']);
                            $currentShow = $this->db->single();
                            
                            $eventData = [
                                'id' => $event->id,
                                'calendar_id' => $event->calendar_id,
                                'title' => $data['name'],
                                'description' => $data['description'] ?? $event->description,
                                'start_date' => $data['start_date'],
                                'end_date' => $data['end_date'],
                                'all_day' => $event->all_day,
                                'location' => $data['location'] ?? $event->location,
                                'address1' => $address1,
                                'address2' => $address2,
                                'city' => $city,
                                'state' => $state,
                                'zipcode' => $zipcode,
                                'lat' => $currentShow->lat ?? null, // Add lat from the updated show
                                'lng' => $currentShow->lng ?? null, // Add lng from the updated show
                                'venue_id' => $event->venue_id,
                                'url' => $event->url,
                                'color' => $event->color,
                                'is_recurring' => $event->is_recurring,
                                'recurrence_pattern' => $event->recurrence_pattern,
                                'recurrence_end_date' => $event->recurrence_end_date,
                                'privacy' => 'public', // Always ensure events are public
                                'show_id' => $data['id'],
                                'created_by' => $data['coordinator_id'] ?? $event->created_by // Use coordinator ID if available
                            ];
                            
                            // Log the lat/lng values being sent to the calendar event
                            error_log("ShowModel::updateShow - GEOCODING DEBUG - Updating calendar event with coordinates: lat=" . 
                                     ($currentShow->lat ?? 'null') . ", lng=" . ($currentShow->lng ?? 'null'));
                            
                            if ($calendarModel->updateEvent($eventData)) {
                                error_log("ShowModel::updateShow - Successfully updated calendar event with ID: {$event->id} for show ID: {$data['id']}");
                            } else {
                                error_log("ShowModel::updateShow - Failed to update calendar event with ID: {$event->id} for show ID: {$data['id']}");
                            }
                        }
                    } else {
                        // No events found, create a new one
                        // First try to create in state-specific calendar
                        $stateCalendarId = null;
                        
                        error_log("ShowModel::updateShow - State value for calendar creation: " . ($state ? $state : 'empty'));
                        
                        if (!empty($state)) {
                            error_log("ShowModel::updateShow - Attempting to create state calendar for state: {$state}");
                            $stateCalendarId = $calendarModel->getOrCreateStateCalendar($state, $data['coordinator_id'] ?? null);
                            error_log("ShowModel::updateShow - Result of getOrCreateStateCalendar: " . ($stateCalendarId ? $stateCalendarId : 'false'));
                        } else {
                            error_log("ShowModel::updateShow - State is empty, skipping state calendar creation");
                        }
                        
                        if ($stateCalendarId) {
                            // Create event in the state calendar
                            $eventId = $calendarModel->createEventFromShow($data['id'], $stateCalendarId, $data['coordinator_id'] ?? null);
                            
                            if ($eventId) {
                                error_log("ShowModel::updateShow - Successfully created calendar event with ID: {$eventId} in state calendar for show ID: {$data['id']}");
                            } else {
                                error_log("ShowModel::updateShow - Failed to create calendar event in state calendar for show ID: {$data['id']}");
                            }
                        } else {
                            // Fallback to default calendar
                            $settingsModel = new SettingsModel();
                            $defaultCalendarId = $settingsModel->getSetting('default_show_calendar_id', null);
                            
                            if ($defaultCalendarId) {
                                $eventId = $calendarModel->createEventFromShow($data['id'], $defaultCalendarId, $data['coordinator_id'] ?? null);
                                
                                if ($eventId) {
                                    error_log("ShowModel::updateShow - Successfully created calendar event with ID: {$eventId} for show ID: {$data['id']}");
                                } else {
                                    error_log("ShowModel::updateShow - Failed to create calendar event for show ID: {$data['id']}");
                                }
                            } else {
                                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                    error_log("ShowModel::updateShow - No default calendar ID found in settings, skipping calendar event creation");
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    error_log("ShowModel::updateShow - Error updating calendar event: " . $e->getMessage());
                }
                
                // Check if status has changed to 'completed' and trigger event if needed
                // Use case-insensitive comparison for status
                if (isset($data['status'])) {
                    // Log all status changes for debugging
                    error_log("ShowModel::updateShow - Status field detected in data. Value: '" . $data['status'] . "' for show ID: " . $data['id']);
                    error_log("ShowModel::updateShow - Status type: " . gettype($data['status']) . ", length: " . strlen($data['status']));
                    
                    // Get the current status from the database to check if it's actually changed
                    $this->db->query("SELECT status FROM shows WHERE id = :id");
                    $this->db->bind(':id', $data['id']);
                    $currentShow = $this->db->single();
                    
                    // Log the current status from database
                    if ($currentShow) {
                        error_log("ShowModel::updateShow - Current status in database: '" . $currentShow->status . "'");
                        error_log("ShowModel::updateShow - Current status type: " . gettype($currentShow->status) . ", length: " . strlen($currentShow->status));
                    }
                    
                    // Check if the status is 'completed' (case-insensitive)
                    $isCompleted = (strtolower(trim($data['status'])) === 'completed');
                    error_log("ShowModel::updateShow - Is status 'completed'? " . ($isCompleted ? 'YES' : 'NO'));
                    
                    // Check if the current status is not 'completed' (case-insensitive)
                    $wasNotCompleted = $currentShow && (strtolower(trim($currentShow->status)) !== 'completed');
                    error_log("ShowModel::updateShow - Was status not 'completed' before? " . ($wasNotCompleted ? 'YES' : 'NO'));
                    
                    // If status is 'completed' - ALWAYS run the completion scripts
                    // This ensures the scripts run even if the status was already 'completed'
                    if ($isCompleted) {
                        error_log("ShowModel::updateShow - Show status is 'completed'. Running completion scripts for show ID: {$data['id']}");
                        
                        // CRITICAL: Directly execute the completion scripts
                        // This is our most reliable method and will run regardless of status change
                        $this->executeCompletionScripts($data['id']);
                        
                        // Log that we've executed the scripts directly
                        error_log("ShowModel::updateShow - Directly executed completion scripts for show ID: {$data['id']}");
                        
                        // Only log status change if it actually changed
                        if ($wasNotCompleted) {
                            error_log("ShowModel::updateShow - STATUS CHANGED TO COMPLETED! Additional events triggered for show ID: {$data['id']}");
                            
                            // Also try the event trigger system as a backup
                            // Make sure EventTrigger is initialized
                            if (!isset($this->eventTrigger) && file_exists(APPROOT . '/core/EventTrigger.php')) {
                                require_once APPROOT . '/core/EventTrigger.php';
                                $this->eventTrigger = new EventTrigger();
                                error_log("ShowModel::updateShow - EventTrigger initialized");
                            }
                            
                            // Trigger events if EventTrigger is available
                            if (isset($this->eventTrigger)) {
                                // Try all possible event name formats for maximum compatibility
                                $eventNames = [
                                    'show_completed',
                                    'Show Completed',
                                    'show completed',
                                    'Show completed',
                                    'SHOW_COMPLETED'
                                ];
                                
                                foreach ($eventNames as $eventName) {
                                    error_log("ShowModel::updateShow - Triggering event: {$eventName} with parameter: {$data['id']}");
                                    $this->eventTrigger->trigger($eventName, $data['id']);
                                }
                                
                                // Also trigger status change events
                                $this->eventTrigger->trigger('show_status_change', 'completed');
                                $this->eventTrigger->trigger('Show Status Change', 'completed');
                                
                                error_log("ShowModel::updateShow - All events triggered for show completion: show_id={$data['id']}");
                            } else {
                                error_log("ShowModel::updateShow - EventTrigger not available. Only direct script execution was used.");
                            }
                        }
                    }
                }
                
                // Save custom field values to the new table if available
                if (isset($this->customFieldValuesModel)) {
                    $customFieldValues = [];
                    $customFieldTypes = [];
                    
                    // Extract custom fields from the data
                    foreach ($data as $key => $value) {
                        // Skip standard fields and system fields
                        if (in_array($key, $this->standardFields) || in_array($key, $this->systemFields)) {
                            continue;
                        }
                        
                        // Process all non-standard fields as custom fields
                        // This includes fields with standard naming patterns (field_*, custom_*) 
                        // and custom fields added through the form designer (like "test")
                        
                        // Add the field to our custom field values collection
                        $customFieldValues[$key] = $value;
                        
                        // Use our own detectFieldType method
                        $customFieldTypes[$key] = $this->detectFieldType($key);
                        
                        // Log the custom field being processed
                        error_log("ShowModel::updateShow - Processing custom field {$key} with type {$customFieldTypes[$key]} and value: " . (is_string($value) ? substr($value, 0, 50) . (strlen($value) > 50 ? '...' : '') : gettype($value)));
                    }
                    
                    // Save custom field values to the new table
                    if (!empty($customFieldValues)) {
                        error_log("ShowModel::updateShow - Attempting to save " . count($customFieldValues) . " custom field values to custom_field_values table");
                        
                        // Create the table if it doesn't exist
                        $this->customFieldValuesModel->createTableIfNotExists();
                        
                        // Debug each custom field value before saving
                        foreach ($customFieldValues as $fieldId => $value) {
                            error_log("ShowModel::updateShow - CUSTOM FIELD TO SAVE: {$fieldId} = " . (is_string($value) ? $value : gettype($value)));
                            
                            // Also use the direct method to save each field individually
                            // This provides an additional layer of protection to ensure fields are saved
                            $fieldType = isset($customFieldTypes[$fieldId]) ? $customFieldTypes[$fieldId] : 'text';
                            $this->customFieldValuesModel->saveCustomField($data['id'], $fieldId, $value, $fieldType);
                        }
                        
                        // Check for any custom fields in the data that might not have been processed
                        foreach ($data as $key => $value) {
                            // Skip standard and system fields
                            if (in_array($key, $this->standardFields) || in_array($key, $this->systemFields)) {
                                continue;
                            }
                            
                            // If this field wasn't already processed, save it directly
                            if (!isset($customFieldValues[$key])) {
                                error_log("ShowModel::updateShow - FOUND ADDITIONAL FIELD: {$key} = " . (is_string($value) ? $value : gettype($value)));
                                $fieldType = $this->detectFieldType($key);
                                $this->customFieldValuesModel->saveCustomField($data['id'], $key, $value, $fieldType);
                                
                                // Also add it to our collection for the batch save
                                $customFieldValues[$key] = $value;
                                $customFieldTypes[$key] = $fieldType;
                            }
                        }
                        
                        $saveResult = $this->customFieldValuesModel->saveCustomFieldValues($data['id'], $customFieldValues, $customFieldTypes);
                        if ($saveResult) {
                            error_log("ShowModel::updateShow - Successfully saved " . count($customFieldValues) . " custom field values to custom_field_values table");
                        } else {
                            error_log("ShowModel::updateShow - Failed to save custom field values to custom_field_values table");
                        }
                    } else {
                        error_log("ShowModel::updateShow - No custom field values found to save");
                        
                        // Check for any custom fields in the data
                        $customFieldValues = [];
                        $customFieldTypes = [];
                        
                        foreach ($data as $key => $value) {
                            // Skip standard and system fields
                            if (in_array($key, $this->standardFields) || in_array($key, $this->systemFields)) {
                                continue;
                            }
                            
                            error_log("ShowModel::updateShow - FOUND CUSTOM FIELD IN DATA: {$key} = " . (is_string($value) ? $value : gettype($value)));
                            $fieldType = $this->detectFieldType($key);
                            
                            // Create the table if it doesn't exist
                            $this->customFieldValuesModel->createTableIfNotExists();
                            
                            // Save the field directly
                            $this->customFieldValuesModel->saveCustomField($data['id'], $key, $value, $fieldType);
                            
                            // Also add it to our collection for batch save
                            $customFieldValues[$key] = $value;
                            $customFieldTypes[$key] = $fieldType;
                        }
                        
                        if (!empty($customFieldValues)) {
                            // Also do a batch save for good measure
                            $saveResult = $this->customFieldValuesModel->saveCustomFieldValues($data['id'], $customFieldValues, $customFieldTypes);
                            if ($saveResult) {
                                error_log("ShowModel::updateShow - Successfully saved " . count($customFieldValues) . " custom fields found in data");
                            } else {
                                error_log("ShowModel::updateShow - Failed to save custom fields found in data");
                            }
                        }
                    }
                } else {
                    error_log("ShowModel::updateShow - CustomFieldValuesModel is not available");
                }
                
                return true;
            } else {
                error_log("Failed to update show with ID: {$data['id']}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::updateShow: " . $e->getMessage());
            return false;
        }
    }
    
    // Second implementation of detectFieldType removed to fix duplicate method error
    
    /**
     * Update show status
     * 
     * @param int $showId Show ID
     * @param string $status New status
     * @return bool True if successful, false otherwise
     */
    public function updateStatus($showId, $status) {
        try {
            // Get current status
            $this->db->query("SELECT status FROM shows WHERE id = :id");
            $this->db->bind(':id', $showId);
            $show = $this->db->single();
            
            if (!$show) {
                error_log("Show not found with ID: {$showId}");
                return false;
            }
            
            $currentStatus = $show->status;
            
            // Update status
            $this->db->query("UPDATE shows SET status = :status, updated_at = NOW() WHERE id = :id");
            $this->db->bind(':id', $showId);
            $this->db->bind(':status', $status);
            $result = $this->db->execute();
            
            if ($result) {
                // Trigger event for status change if EventTrigger is available
                if (isset($this->eventTrigger)) {
                    $this->eventTrigger->trigger('show_status_change', $status);
                    
                    // If status changed to completed, trigger specific event
                    if ($status === 'completed' && $currentStatus !== 'completed') {
                        $this->eventTrigger->trigger('show_completed', $showId);
                        
                        // Log the status change
                        error_log("Show ID {$showId} status changed from {$currentStatus} to completed. Events triggered.");
                    }
                }
                
                return true;
            } else {
                error_log("Failed to update status for show ID: {$showId}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error updating show status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a show
     * 
     * @param int $id Show ID
     * @return bool
     */
    public function deleteShow($id) {
        // Delete custom field values first if the table exists
        if (isset($this->customFieldValuesModel) && $this->customFieldValuesModel->tableExists()) {
            $this->customFieldValuesModel->deleteCustomFieldValues($id);
        }
        
        // Now delete the show
        $this->db->query('DELETE FROM shows WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }
    
    /**
     * Get shows by status
     * 
     * @param string $status Status to filter by
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array
     */
    public function getShowsByStatus($status, $limit = null, $offset = null) {
        return $this->getShows($status, $limit, $offset);
    }
    
    /**
     * Get shows by coordinator
     * 
     * @param int $coordinatorId Coordinator ID
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array
     */
    public function getShowsByCoordinator($coordinatorId, $limit = null, $offset = null, $status = null, $excludeCompletedCancelled = false) {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            $registrationsTableExists = $this->tableExists('registrations');
            
            // Build SQL based on available tables
            if ($usersTableExists && $registrationsTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id 
                        WHERE s.coordinator_id = :coordinator_id';
            } else if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id 
                        WHERE s.coordinator_id = :coordinator_id';
                error_log("Registrations table not found, using simplified query for getShowsByCoordinator");
            } else if ($registrationsTableExists) {
                $sql = 'SELECT s.*, NULL as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        WHERE s.coordinator_id = :coordinator_id';
                error_log("Users table not found, using simplified query for getShowsByCoordinator");
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        WHERE s.coordinator_id = :coordinator_id';
                error_log("Users and registrations tables not found, using simplified query for getShowsByCoordinator");
            }
            
            if ($status) {
                $sql .= ' AND s.status = :status';
            }
            
            if ($excludeCompletedCancelled) {
                $sql .= ' AND s.status NOT IN ("completed", "cancelled")';
            }
            
            $sql .= ' ORDER BY s.start_date DESC';
            
            if ($limit) {
                $sql .= ' LIMIT :limit';
                if ($offset) {
                    $sql .= ' OFFSET :offset';
                }
            }
            
            $this->db->query($sql);
            $this->db->bind(':coordinator_id', $coordinatorId);
            
            if ($status) {
                $this->db->bind(':status', $status);
            }
            
            if ($limit) {
                $this->db->bind(':limit', $limit, PDO::PARAM_INT);
                if ($offset) {
                    $this->db->bind(':offset', $offset, PDO::PARAM_INT);
                }
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowsByCoordinator: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count shows by status
     * 
     * @param string $status Status to count
     * @return int
     */
    public function countShowsByStatus($status) {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM shows WHERE status = :status');
            $this->db->bind(':status', $status);
            $result = $this->db->single();
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log("Error in ShowModel::countShowsByStatus: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Count all shows
     * 
     * @return int
     */
    public function countAllShows() {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM shows');
            $result = $this->db->single();
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log("Error in ShowModel::countAllShows: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get featured shows
     * 
     * @param int $limit Optional limit
     * @return array
     */
    public function getFeaturedShows($limit = 3) {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            $registrationsTableExists = $this->tableExists('registrations');
            
            // Build SQL based on available tables
            if ($usersTableExists && $registrationsTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id 
                        WHERE s.status = :status AND s.is_featured = 1 
                        ORDER BY s.start_date ASC';
            } else if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id 
                        WHERE s.status = :status AND s.is_featured = 1 
                        ORDER BY s.start_date ASC';
                error_log("Registrations table not found, using simplified query for getFeaturedShows");
            } else if ($registrationsTableExists) {
                $sql = 'SELECT s.*, NULL as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        WHERE s.status = :status AND s.is_featured = 1 
                        ORDER BY s.start_date ASC';
                error_log("Users table not found, using simplified query for getFeaturedShows");
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        WHERE s.status = :status AND s.is_featured = 1 
                        ORDER BY s.start_date ASC';
                error_log("Users and registrations tables not found, using simplified query for getFeaturedShows");
            }
            
            if ($limit) {
                $sql .= ' LIMIT :limit';
            }
            
            $this->db->query($sql);
            $this->db->bind(':status', 'published');
            
            if ($limit) {
                $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            }
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getFeaturedShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get categories for a show
     * 
     * @param int $showId Show ID
     * @return array Array of category objects
     */
    public function getShowCategories($showId) {
        try {
            // Check if the show_categories table exists
            if (!$this->tableExists('show_categories')) {
                // Create the table if it doesn't exist
                $this->createShowCategoriesTable();
            }
            
            // Get categories for the show
            $this->db->query('SELECT * FROM show_categories WHERE show_id = :show_id ORDER BY display_order ASC');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowCategories: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a specific category by ID
     * 
     * @param int $categoryId Category ID
     * @return object|false Category object or false if not found
     */
    public function getCategoryById($categoryId) {
        try {
            // Check if the show_categories table exists
            if (!$this->tableExists('show_categories')) {
                // Create the table if it doesn't exist
                $this->createShowCategoriesTable();
                return false;
            }
            
            // Get category by ID
            $this->db->query('SELECT * FROM show_categories WHERE id = :id');
            $this->db->bind(':id', $categoryId);
            
            $result = $this->db->single();
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getCategoryById: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create the show_categories table if it doesn't exist
     */
    private function createShowCategoriesTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS show_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                show_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                display_order INT DEFAULT 0,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX (show_id)
            )";
            
            $this->db->query($sql);
            $this->db->execute();
            
            error_log("Created show_categories table");
        } catch (Exception $e) {
            error_log("Error creating show_categories table: " . $e->getMessage());
        }
    }
    
    /**
     * Get judging metrics for a show
     * 
     * @param int $showId Show ID
     * @return array Array of judging metric objects
     */
    public function getJudgingMetrics($showId) {
        try {
            // Check if the judging_metrics table exists
            if (!$this->tableExists('judging_metrics')) {
                // Create the table if it doesn't exist
                $this->createJudgingMetricsTable();
            }
            
            // Get judging metrics for the show
            $this->db->query('SELECT * FROM judging_metrics WHERE show_id = :show_id ORDER BY display_order ASC');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getJudgingMetrics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get judge assignments for a show
     * 
     * @param int $showId Show ID
     * @return array List of judge assignments
     */
    public function getJudgeAssignments($showId) {
        try {
            // Check if the necessary tables exist
            if (!$this->tableExists('judge_assignments')) {
                error_log("judge_assignments table does not exist");
                return [];
            }
            
            // Check if users table exists
            $usersTableExists = $this->tableExists('users');
            $categoriesTableExists = $this->tableExists('show_categories');
            
            // Build query based on available tables
            if ($usersTableExists && $categoriesTableExists) {
                $this->db->query('SELECT ja.*, u.name as judge_name, u.email as judge_email, 
                                 sc.name as category_name 
                                 FROM judge_assignments ja 
                                 LEFT JOIN users u ON ja.judge_id = u.id 
                                 LEFT JOIN show_categories sc ON ja.category_id = sc.id 
                                 WHERE ja.show_id = :show_id 
                                 ORDER BY u.name ASC');
            } else if ($usersTableExists) {
                $this->db->query('SELECT ja.*, u.name as judge_name, u.email as judge_email, 
                                 NULL as category_name 
                                 FROM judge_assignments ja 
                                 LEFT JOIN users u ON ja.judge_id = u.id 
                                 WHERE ja.show_id = :show_id 
                                 ORDER BY u.name ASC');
                error_log("show_categories table not found, using simplified query for getJudgeAssignments");
            } else if ($categoriesTableExists) {
                $this->db->query('SELECT ja.*, NULL as judge_name, NULL as judge_email, 
                                 sc.name as category_name 
                                 FROM judge_assignments ja 
                                 LEFT JOIN show_categories sc ON ja.category_id = sc.id 
                                 WHERE ja.show_id = :show_id');
                error_log("users table not found, using simplified query for getJudgeAssignments");
            } else {
                $this->db->query('SELECT ja.*, NULL as judge_name, NULL as judge_email, 
                                 NULL as category_name 
                                 FROM judge_assignments ja 
                                 WHERE ja.show_id = :show_id');
                error_log("users and show_categories tables not found, using simplified query for getJudgeAssignments");
            }
            
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getJudgeAssignments: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get shows assigned to a judge
     * 
     * @param int $judgeId Judge user ID
     * @return array List of shows
     */
    public function getShowsByJudge($judgeId) {
        try {
            // Check if the judge_assignments table exists
            if (!$this->tableExists('judge_assignments')) {
                error_log("judge_assignments table does not exist");
                return [];
            }
            
            // Use DISTINCT to prevent duplicate shows when a judge is assigned to multiple categories
            $this->db->query('SELECT DISTINCT s.* FROM shows s 
                              JOIN judge_assignments ja ON s.id = ja.show_id 
                              WHERE ja.judge_id = :judge_id AND s.status = :status 
                              ORDER BY s.start_date DESC');
            $this->db->bind(':judge_id', $judgeId);
            $this->db->bind(':status', 'published');
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowsByJudge: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get categories assigned to a judge for a specific show
     * 
     * @param int $showId Show ID
     * @param int $judgeId Judge user ID
     * @return array List of categories
     */
    public function getJudgeCategories($showId, $judgeId) {
        try {
            // Check if the necessary tables exist
            if (!$this->tableExists('judge_assignments') || !$this->tableExists('show_categories')) {
                error_log("Required tables for getJudgeCategories do not exist");
                return [];
            }
            
            $this->db->query('SELECT sc.* FROM show_categories sc 
                              JOIN judge_assignments ja ON sc.id = ja.category_id 
                              WHERE ja.show_id = :show_id AND ja.judge_id = :judge_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':judge_id', $judgeId);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getJudgeCategories: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Assign a judge to a show or category
     * 
     * @param array $data Assignment data (show_id, judge_id, category_id)
     * @return int|bool The ID of the new assignment or false on failure
     */
    public function assignJudge($data) {
        try {
            // Check if the judge_assignments table exists
            if (!$this->tableExists('judge_assignments')) {
                error_log("judge_assignments table does not exist");
                return false;
            }
            
            // Log the data for debugging
            error_log("ShowModel::assignJudge - Data: " . print_r($data, true));
            
            // Create the assignment - handle NULL category_id differently
            if ($data['category_id'] === null) {
                $this->db->query('INSERT INTO judge_assignments (show_id, judge_id, category_id) 
                                  VALUES (:show_id, :judge_id, NULL)');
                $this->db->bind(':show_id', $data['show_id']);
                $this->db->bind(':judge_id', $data['judge_id']);
            } else {
                $this->db->query('INSERT INTO judge_assignments (show_id, judge_id, category_id) 
                                  VALUES (:show_id, :judge_id, :category_id)');
                $this->db->bind(':show_id', $data['show_id']);
                $this->db->bind(':judge_id', $data['judge_id']);
                $this->db->bind(':category_id', $data['category_id']);
            }
            
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            } else {
                error_log("Failed to insert judge assignment");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::assignJudge: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove a judge assignment
     * 
     * @param int $id Assignment ID
     * @return bool True on success, false on failure
     */
    public function removeJudgeAssignment($id) {
        try {
            // Check if the judge_assignments table exists
            if (!$this->tableExists('judge_assignments')) {
                error_log("judge_assignments table does not exist");
                return false;
            }
            
            // Delete the assignment
            $this->db->query('DELETE FROM judge_assignments WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::removeJudgeAssignment: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create the judging_metrics table if it doesn't exist
     */
    private function createJudgingMetricsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS judging_metrics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                show_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                max_score INT DEFAULT 10,
                weight DECIMAL(5,2) DEFAULT 1.00,
                display_order INT DEFAULT 0,
                is_active TINYINT(1) DEFAULT 1,
                category_id INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX (show_id),
                INDEX (category_id)
            )";
            
            $this->db->query($sql);
            $this->db->execute();
            
            error_log("Created judging_metrics table");
            
            // Check if the table exists but doesn't have the category_id column
            try {
                $this->db->query("SHOW COLUMNS FROM judging_metrics LIKE 'category_id'");
                $columnExists = $this->db->single() !== false;
                
                if (!$columnExists) {
                    // Add the category_id column
                    $this->db->query("ALTER TABLE judging_metrics ADD COLUMN category_id INT DEFAULT 0, ADD INDEX (category_id)");
                    $this->db->execute();
                    error_log("Added category_id column to judging_metrics table");
                }
            } catch (Exception $e) {
                error_log("Error checking or adding category_id column: " . $e->getMessage());
            }
        } catch (Exception $e) {
            error_log("Error creating judging_metrics table: " . $e->getMessage());
        }
    }
    
    /**
     * Add a category to a show
     * 
     * @param int $showId Show ID
     * @param array $data Category data
     * @return int|bool New category ID or false on failure
     */
    public function addShowCategory($showId, $data) {
        try {
            // Check if the show_categories table exists
            if (!$this->tableExists('show_categories')) {
                // Create the table if it doesn't exist
                $this->createShowCategoriesTable();
            }
            
            // Insert the category
            $this->db->query('INSERT INTO show_categories (show_id, name, description, display_order, is_active) 
                            VALUES (:show_id, :name, :description, :display_order, :is_active)');
            
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? '');
            $this->db->bind(':display_order', $data['display_order'] ?? 0);
            $this->db->bind(':is_active', $data['is_active'] ?? 1);
            
            $result = $this->db->execute();
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                error_log("Failed to add category to show ID: {$showId}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::addShowCategory: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add a judging metric to a show
     * 
     * @param int $showId Show ID
     * @param array $data Metric data
     * @return int|bool New metric ID or false on failure
     */
    public function addJudgingMetric($showId, $data) {
        try {
            // Check if the judging_metrics table exists
            if (!$this->tableExists('judging_metrics')) {
                // Create the table if it doesn't exist
                $this->createJudgingMetricsTable();
            }
            
            // Insert the metric
            $this->db->query('INSERT INTO judging_metrics (show_id, name, description, max_score, weight, display_order, is_active) 
                            VALUES (:show_id, :name, :description, :max_score, :weight, :display_order, :is_active)');
            
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? '');
            $this->db->bind(':max_score', $data['max_score'] ?? 10);
            $this->db->bind(':weight', $data['weight'] ?? 1.00);
            $this->db->bind(':display_order', $data['display_order'] ?? 0);
            $this->db->bind(':is_active', $data['is_active'] ?? 1);
            
            $result = $this->db->execute();
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                error_log("Failed to add judging metric to show ID: {$showId}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::addJudgingMetric: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if registration is open for a show
     * 
     * @param int $showId Show ID
     * @return bool True if registration is open, false otherwise
     */
    /**
     * Check if registration is open for a show
     * 
     * @param int $showId Show ID
     * @param bool $adminOverride If true, bypass date check for admins/coordinators
     * @return bool True if registration is open, false otherwise
     */
    public function isRegistrationOpen($showId, $adminOverride = false) {
        try {
            // If admin override is enabled, skip date check
            if ($adminOverride) {
                // Still need to check if the show exists
                $this->db->query('SELECT id FROM shows WHERE id = :id');
                $this->db->bind(':id', $showId);
                $show = $this->db->single();
                
                if (!$show) {
                    error_log("Show not found with ID: {$showId}");
                    return false;
                }
                
                // Admin override is enabled, so return true regardless of dates
                return true;
            }
            
            // Regular date check for non-admin users
            $this->db->query('SELECT registration_start, registration_end FROM shows WHERE id = :id');
            $this->db->bind(':id', $showId);
            $show = $this->db->single();
            
            if (!$show) {
                error_log("Show not found with ID: {$showId}");
                return false;
            }
            
            // Check if registration dates are set
            if (empty($show->registration_start) || empty($show->registration_end)) {
                error_log("Registration dates not set for show ID: {$showId}");
                return false;
            }
            
            // Get current date/time
            $now = new DateTime();
            $startDate = new DateTime($show->registration_start);
            $endDate = new DateTime($show->registration_end);
            
            // Check if current date is within registration period
            return ($now >= $startDate && $now <= $endDate);
        } catch (Exception $e) {
            error_log("Error in ShowModel::isRegistrationOpen: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a category
     * 
     * @param int $id Category ID
     * @return bool True if successful, false otherwise
     */
    public function deleteCategory($id) {
        try {
            // Check if the show_categories table exists
            if (!$this->tableExists('show_categories')) {
                error_log('Table show_categories does not exist');
                return false;
            }
            
            $this->db->query('DELETE FROM show_categories WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::deleteCategory: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new category for a show
     * 
     * @param array $data Category data
     * @return int|bool New category ID or false on failure
     */
    public function createCategory($data) {
        try {
            // Check if the show_categories table exists
            if (!$this->tableExists('show_categories')) {
                // Create the table if it doesn't exist
                $this->createShowCategoriesTable();
            }
            
            // Insert the category
            $this->db->query('INSERT INTO show_categories (show_id, name, description, registration_fee, max_entries, display_order, is_active) 
                            VALUES (:show_id, :name, :description, :registration_fee, :max_entries, :display_order, :is_active)');
            
            $this->db->bind(':show_id', $data['show_id']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? '');
            $this->db->bind(':registration_fee', $data['registration_fee'] ?? 0);
            $this->db->bind(':max_entries', $data['max_entries'] ?? 0);
            $this->db->bind(':display_order', $data['display_order'] ?? 0);
            $this->db->bind(':is_active', $data['is_active'] ?? 1);
            
            $result = $this->db->execute();
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                error_log("Failed to create category for show ID: {$data['show_id']}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::createCategory: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete all categories for a show
     * 
     * @param int $showId Show ID
     * @return bool True if successful, false otherwise
     */
    public function deleteAllCategoriesByShowId($showId) {
        try {
            // Check if the show_categories table exists
            if (!$this->tableExists('show_categories')) {
                error_log('Table show_categories does not exist');
                return false;
            }
            
            $this->db->query('DELETE FROM show_categories WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            
            // Execute
            if ($this->db->execute()) {
                // Debug log
                error_log('Deleted all categories for show ' . $showId);
                return true;
            } else {
                error_log('Failed to delete categories for show ' . $showId);
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::deleteAllCategoriesByShowId: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a judging metric
     * 
     * @param int $id Metric ID
     * @return bool True if successful, false otherwise
     */
    public function deleteMetric($id) {
        try {
            // Check if the judging_metrics table exists
            if (!$this->tableExists('judging_metrics')) {
                error_log('Table judging_metrics does not exist');
                return false;
            }
            
            $this->db->query('DELETE FROM judging_metrics WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::deleteMetric: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a judging metric by ID
     * 
     * @param int $id Metric ID
     * @return object|bool Metric object or false if not found
     */
    public function getMetricById($id) {
        try {
            // Check if the judging_metrics table exists
            if (!$this->tableExists('judging_metrics')) {
                error_log('Table judging_metrics does not exist');
                return false;
            }
            
            $this->db->query('SELECT * FROM judging_metrics WHERE id = :id');
            $this->db->bind(':id', $id);
            
            $result = $this->db->single();
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getMetricById: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new judging metric
     * 
     * @param array $data Metric data
     * @return int|bool New metric ID or false on failure
     */
    public function createMetric($data) {
        try {
            // Check if the judging_metrics table exists
            if (!$this->tableExists('judging_metrics')) {
                // Create the table if it doesn't exist
                $this->createJudgingMetricsTable();
            }
            
            // Check if category_id column exists
            $categoryColumnExists = false;
            try {
                $this->db->query("SHOW COLUMNS FROM judging_metrics LIKE 'category_id'");
                $categoryColumnExists = $this->db->single() !== false;
            } catch (Exception $e) {
                error_log("Error checking for category_id column: " . $e->getMessage());
            }
            
            // Check if is_active column exists
            $isActiveColumnExists = false;
            try {
                $this->db->query("SHOW COLUMNS FROM judging_metrics LIKE 'is_active'");
                $isActiveColumnExists = $this->db->single() !== false;
            } catch (Exception $e) {
                error_log("Error checking for is_active column: " . $e->getMessage());
            }
            
            // Build the SQL query dynamically based on existing columns
            $columns = ['show_id', 'name', 'description', 'max_score', 'weight', 'display_order'];
            $values = [':show_id', ':name', ':description', ':max_score', ':weight', ':display_order'];
            
            if ($isActiveColumnExists) {
                $columns[] = 'is_active';
                $values[] = ':is_active';
            }
            
            if ($categoryColumnExists) {
                $columns[] = 'category_id';
                $values[] = ':category_id';
            }
            
            $sql = 'INSERT INTO judging_metrics (' . implode(', ', $columns) . ') VALUES (' . implode(', ', $values) . ')';
            $this->db->query($sql);
            
            // Bind parameters
            $this->db->bind(':show_id', $data['show_id']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? '');
            $this->db->bind(':max_score', $data['max_score'] ?? 10);
            $this->db->bind(':weight', $data['weight'] ?? 1.00);
            $this->db->bind(':display_order', $data['display_order'] ?? 0);
            
            if ($isActiveColumnExists) {
                $this->db->bind(':is_active', 1);
            }
            
            if ($categoryColumnExists) {
                $this->db->bind(':category_id', $data['category_id'] ?? 0);
            }
            
            $result = $this->db->execute();
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                error_log("Failed to create judging metric for show ID: {$data['show_id']}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::createMetric: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a judging metric
     * 
     * @param array $data Metric data
     * @return bool True if successful, false otherwise
     */
    public function updateMetric($data) {
        try {
            // Check if the judging_metrics table exists
            if (!$this->tableExists('judging_metrics')) {
                error_log('Table judging_metrics does not exist');
                return false;
            }
            
            // Check if category_id column exists
            $categoryColumnExists = false;
            try {
                $this->db->query("SHOW COLUMNS FROM judging_metrics LIKE 'category_id'");
                $categoryColumnExists = $this->db->single() !== false;
            } catch (Exception $e) {
                error_log("Error checking for category_id column: " . $e->getMessage());
            }
            
            // Update the metric
            if ($categoryColumnExists) {
                $this->db->query('UPDATE judging_metrics 
                                SET name = :name, 
                                    description = :description, 
                                    max_score = :max_score, 
                                    weight = :weight, 
                                    display_order = :display_order,
                                    category_id = :category_id
                                WHERE id = :id');
                
                $this->db->bind(':category_id', $data['category_id'] ?? 0);
            } else {
                $this->db->query('UPDATE judging_metrics 
                                SET name = :name, 
                                    description = :description, 
                                    max_score = :max_score, 
                                    weight = :weight, 
                                    display_order = :display_order
                                WHERE id = :id');
            }
            
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? '');
            $this->db->bind(':max_score', $data['max_score'] ?? 10);
            $this->db->bind(':weight', $data['weight'] ?? 1.00);
            $this->db->bind(':display_order', $data['display_order'] ?? 0);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::updateMetric: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete all judging metrics for a show
     * 
     * @param int $showId Show ID
     * @return bool True if successful, false otherwise
     */
    public function deleteAllMetricsByShowId($showId) {
        try {
            // Check if the judging_metrics table exists
            if (!$this->tableExists('judging_metrics')) {
                error_log('Table judging_metrics does not exist');
                return false;
            }
            
            $this->db->query('DELETE FROM judging_metrics WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            
            // Execute
            if ($this->db->execute()) {
                // Debug log
                error_log('Deleted all judging metrics for show ' . $showId);
                return true;
            } else {
                error_log('Failed to delete judging metrics for show ' . $showId);
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::deleteAllMetricsByShowId: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get age weights for a show
     * 
     * @param int $showId Show ID
     * @return array Age weights
     */
    public function getAgeWeights($showId) {
        try {
            // Check if the age_weights table exists
            if (!$this->tableExists('age_weights')) {
                // Create the table if it doesn't exist
                $this->createAgeWeightsTable();
            }
            
            $this->db->query('SELECT * FROM age_weights WHERE show_id = :show_id ORDER BY min_age ASC');
            $this->db->bind(':show_id', $showId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getAgeWeights: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create a new age weight
     * 
     * @param array $data Age weight data
     * @return int|bool New age weight ID or false on failure
     */
    public function createAgeWeight($data) {
        try {
            // Check if the age_weights table exists
            if (!$this->tableExists('age_weights')) {
                // Create the table if it doesn't exist
                $this->createAgeWeightsTable();
            }
            
            // Prepare query
            $this->db->query('INSERT INTO age_weights (show_id, min_age, max_age, multiplier) 
                             VALUES (:show_id, :min_age, :max_age, :multiplier)');
            
            // Bind values
            $this->db->bind(':show_id', $data['show_id']);
            $this->db->bind(':min_age', $data['min_age']);
            $this->db->bind(':max_age', $data['max_age']);
            $this->db->bind(':multiplier', $data['multiplier']);
            
            // Execute
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            } else {
                return false;
            }
        } catch (Exception $e) {
            error_log("Error in ShowModel::createAgeWeight: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update an age weight
     * 
     * @param array $data Age weight data
     * @return bool True on success, false on failure
     */
    public function updateAgeWeight($data) {
        try {
            // Prepare query
            $this->db->query('UPDATE age_weights 
                             SET min_age = :min_age, max_age = :max_age, multiplier = :multiplier 
                             WHERE id = :id');
            
            // Bind values
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':min_age', $data['min_age']);
            $this->db->bind(':max_age', $data['max_age']);
            $this->db->bind(':multiplier', $data['multiplier']);
            
            // Execute
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::updateAgeWeight: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete an age weight
     * 
     * @param int $id Age weight ID
     * @return bool True on success, false on failure
     */
    public function deleteAgeWeight($id) {
        try {
            // Prepare query
            $this->db->query('DELETE FROM age_weights WHERE id = :id');
            
            // Bind values
            $this->db->bind(':id', $id);
            
            // Execute
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::deleteAgeWeight: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create the age_weights table if it doesn't exist
     * 
     * @return bool True on success, false on failure
     */
    private function createAgeWeightsTable() {
        try {
            $this->db->query('CREATE TABLE IF NOT EXISTS `age_weights` (
                `id` int UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
                `show_id` int UNSIGNED NOT NULL,
                `min_age` int NOT NULL,
                `max_age` int NOT NULL,
                `multiplier` decimal(5,2) NOT NULL DEFAULT "1.00",
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX `show_id_idx` (`show_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::createAgeWeightsTable: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all active shows (not past end date)
     * 
     * @return array Array of show objects
     */
    public function getActiveShows() {
        try {
            $this->db->query('SELECT * FROM shows WHERE end_date >= CURDATE() ORDER BY start_date ASC');
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getActiveShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all active shows assigned to a coordinator
     * 
     * @param int $coordinatorId Coordinator user ID
     * @return array Array of show objects
     */
    public function getCoordinatorActiveShows($coordinatorId) {
        try {
            $this->db->query('SELECT * FROM shows WHERE coordinator_id = :coordinator_id AND end_date >= CURDATE() ORDER BY start_date ASC');
            $this->db->bind(':coordinator_id', $coordinatorId);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getCoordinatorActiveShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get coordinator show count
     * 
     * @param int $coordinatorId Coordinator ID
     * @return int
     */
    public function getCoordinatorShowCount($coordinatorId) {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM shows WHERE coordinator_id = :coordinator_id');
            $this->db->bind(':coordinator_id', $coordinatorId);
            
            $result = $this->db->single();
            
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getCoordinatorShowCount: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Update show listing payment status
     * 
     * @param int $showId Show ID
     * @param bool $paid Payment status
     * @return bool True if successful, false otherwise
     */
    public function updateShowListingPaymentStatus($showId, $paid) {
        try {
            $this->db->query("UPDATE shows SET listing_paid = :paid, updated_at = NOW() WHERE id = :id");
            $this->db->bind(':id', $showId);
            $this->db->bind(':paid', $paid ? 1 : 0);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in ShowModel::updateShowListingPaymentStatus: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Alias for updateShowListingPaymentStatus
     */
    public function updateShowListingPaid($showId, $paid) {
        return $this->updateShowListingPaymentStatus($showId, $paid);
    }
    
    /**
     * Check if a show's listing fee has been paid
     * 
     * @param int $showId Show ID
     * @return bool True if paid, false otherwise
     */
    public function isListingFeePaid($showId) {
        try {
            $this->db->query("SELECT listing_paid FROM shows WHERE id = :id");
            $this->db->bind(':id', $showId);
            $result = $this->db->single();
            
            return $result && $result->listing_paid;
        } catch (Exception $e) {
            error_log("Error in ShowModel::isListingFeePaid: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Alias for updateStatus
     */
    public function updateShowStatus($showId, $status) {
        return $this->updateStatus($showId, $status);
    }
    
    /**
     * Get total show count
     * 
     * @return int Total number of shows
     */
    public function getShowCount() {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM shows');
            $result = $this->db->single();
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowCount: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get recent shows
     * 
     * @param int $limit Number of shows to return
     * @return array Array of recent shows
     */
    public function getRecentShows($limit = 5) {
        try {
            $this->db->query('SELECT s.*, u.name as coordinator_name 
                             FROM shows s 
                             LEFT JOIN users u ON s.coordinator_id = u.id 
                             ORDER BY s.created_at DESC 
                             LIMIT :limit');
            $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getRecentShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get show status statistics
     * 
     * @return array Array of status counts
     */
    public function getShowStatusStatistics() {
        try {
            $this->db->query('SELECT status, COUNT(*) as count 
                             FROM shows 
                             GROUP BY status 
                             ORDER BY count DESC');
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowStatusStatistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get show counts for coordinator dashboard
     *
     * @param int $coordinatorId Coordinator ID
     * @return array Show counts by category
     */
    public function getCoordinatorShowCounts($coordinatorId) {
        try {
            $counts = [
                'total' => 0,
                'upcoming' => 0,
                'past' => 0,
                'draft' => 0
            ];

            // Get total count
            $this->db->query('SELECT COUNT(*) as total
                             FROM shows
                             WHERE coordinator_id = :coordinator_id');
            $this->db->bind(':coordinator_id', $coordinatorId);
            $totalResult = $this->db->single();
            $counts['total'] = $totalResult->total ?? 0;

            // Get upcoming count
            $this->db->query('SELECT COUNT(*) as upcoming
                             FROM shows
                             WHERE coordinator_id = :coordinator_id AND start_date > NOW()');
            $this->db->bind(':coordinator_id', $coordinatorId);
            $upcomingResult = $this->db->single();
            $counts['upcoming'] = $upcomingResult->upcoming ?? 0;

            // Get past count
            $this->db->query('SELECT COUNT(*) as past
                             FROM shows
                             WHERE coordinator_id = :coordinator_id AND start_date <= NOW()');
            $this->db->bind(':coordinator_id', $coordinatorId);
            $pastResult = $this->db->single();
            $counts['past'] = $pastResult->past ?? 0;

            // Get draft count
            $this->db->query('SELECT COUNT(*) as draft
                             FROM shows
                             WHERE coordinator_id = :coordinator_id AND status = "draft"');
            $this->db->bind(':coordinator_id', $coordinatorId);
            $draftResult = $this->db->single();
            $counts['draft'] = $draftResult->draft ?? 0;

            return $counts;

        } catch (Exception $e) {
            error_log('Error in ShowModel::getCoordinatorShowCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'upcoming' => 0,
                'past' => 0,
                'draft' => 0
            ];
        }
    }

    /**
     * Get paginated shows for coordinator
     *
     * @param int $coordinatorId Coordinator ID
     * @param int $page Page number (1-based)
     * @param int $perPage Records per page
     * @param string $search Search term
     * @param string $statusFilter Status filter
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC/DESC)
     * @return array Paginated results with metadata
     */
    public function getPaginatedCoordinatorShows($coordinatorId, $page = 1, $perPage = 20, $search = '', $statusFilter = 'all', $orderBy = 'start_date', $orderDir = 'DESC') {
        $startTime = microtime(true);

        try {
            // Validate parameters
            $page = max(1, (int)$page);
            $perPage = min(100, max(1, (int)$perPage));
            $offset = ($page - 1) * $perPage;

            // Validate order by field
            $allowedOrderFields = ['name', 'start_date', 'location', 'status', 'created_at'];
            if (!in_array($orderBy, $allowedOrderFields)) {
                $orderBy = 'start_date';
            }

            $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

            // Build WHERE conditions
            $whereConditions = ['s.coordinator_id = :coordinator_id'];
            $bindParams = ['coordinator_id' => $coordinatorId];

            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(s.name LIKE :search_name OR s.location LIKE :search_location)';
                $bindParams['search_name'] = '%' . $search . '%';
                $bindParams['search_location'] = '%' . $search . '%';
            }

            // Status filter
            if ($statusFilter !== 'all') {
                switch ($statusFilter) {
                    case 'upcoming':
                        $whereConditions[] = 's.start_date > NOW()';
                        break;
                    case 'past':
                        $whereConditions[] = 's.start_date <= NOW()';
                        break;
                    case 'draft':
                        $whereConditions[] = 's.status = "draft"';
                        break;
                    case 'published':
                        $whereConditions[] = 's.status = "published"';
                        break;
                }
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            // Count total records
            $countQuery = "SELECT COUNT(*) as total
                          FROM shows s
                          $whereClause";

            $this->db->query($countQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }

            $totalResult = $this->db->single();
            $totalRecords = $totalResult->total ?? 0;

            // Get paginated data
            $dataQuery = "SELECT s.*,
                         (SELECT COUNT(*) FROM registrations r WHERE r.show_id = s.id) as registration_count
                         FROM shows s
                         $whereClause
                         ORDER BY $orderBy $orderDir
                         LIMIT :limit OFFSET :offset";

            $this->db->query($dataQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
            $this->db->bind(':offset', $offset, PDO::PARAM_INT);

            $shows = $this->db->resultSet();

            // Calculate pagination metadata
            $totalPages = ceil($totalRecords / $perPage);
            $startRecord = $totalRecords > 0 ? $offset + 1 : 0;
            $endRecord = min($offset + $perPage, $totalRecords);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 1);

            error_log("ShowModel::getPaginatedCoordinatorShows - Page: $page, Shows: " . count($shows) . ", Time: {$executionTime}ms");

            return [
                'shows' => $shows,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_shows' => $totalRecords,
                    'start_record' => $startRecord,
                    'end_record' => $endRecord,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ]
            ];

        } catch (Exception $e) {
            error_log('Error in ShowModel::getPaginatedCoordinatorShows: ' . $e->getMessage());
            return [
                'shows' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $perPage,
                    'total_pages' => 0,
                    'total_shows' => 0,
                    'start_record' => 0,
                    'end_record' => 0,
                    'has_prev' => false,
                    'has_next' => false
                ]
            ];
        }
    }
}