<?php
/**
 * App Class
 * 
 * This class handles the routing and dispatching of requests to the appropriate controllers.
 */
class App {
    protected $controller = 'HomeController';
    protected $method = 'index';
    protected $params = [];

    public function __construct() {
        try {
            $url = $this->parseUrl();
            
            // Debug URL parsing
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('URL parsed: ' . print_r($url, true));
            }
            
            // Get the application root directory with multiple fallbacks
            $appRoot = defined('APPROOT') ? APPROOT : dirname(dirname(__FILE__));
            
            // Ensure the controllers directory exists
            $controllersDir = $appRoot . '/controllers/';
            if (!is_dir($controllersDir)) {
                // Try alternative paths if the standard one doesn't work
                $altPaths = [
                    dirname(dirname(realpath(__FILE__))) . '/controllers/',
                    $_SERVER['DOCUMENT_ROOT'] . '/../controllers/',
                    $_SERVER['DOCUMENT_ROOT'] . '/controllers/'
                ];
                
                foreach ($altPaths as $path) {
                    if (is_dir($path)) {
                        $controllersDir = $path;
                        break;
                    }
                }
                
                // If still not found, show an error
                if (!is_dir($controllersDir)) {
                    error_log('Error: Controllers directory not found. Paths tried: ' . implode(', ', $altPaths));
                    $this->handleError('Controllers directory not found. Please check your installation.');
                    return;
                }
            }
            
            // Check for API routes first
            if (isset($url[0]) && $url[0] === 'api') {
                $this->handleApiRoute($url);
                return;
            }
            
            // Check if controller exists
            if (isset($url[0])) {
                // Try standard format first (e.g., HomeController)
                if (file_exists($controllersDir . ucfirst($url[0]) . 'Controller.php')) {
                    $this->controller = ucfirst($url[0]) . 'Controller';
                    unset($url[0]);
                    // Reindex the array after unsetting
                    $url = array_values($url);
                } 
                // Try with underscores converted to camelcase (e.g., layout_editor -> LayoutEditorController)
                else {
                    $controllerName = str_replace('_', ' ', $url[0]);
                    $controllerName = ucwords($controllerName);
                    $controllerName = str_replace(' ', '', $controllerName);
                    
                    if (file_exists($controllersDir . $controllerName . 'Controller.php')) {
                        $this->controller = $controllerName . 'Controller';
                        unset($url[0]);
                        // Reindex the array after unsetting
                        $url = array_values($url);
                    }
                }
            }
    
            // Load the controller
            $controllerFile = $controllersDir . $this->controller . '.php';
            if (!file_exists($controllerFile)) {
                error_log('Error: Controller file not found: ' . $this->controller);
                $this->handleError('Controller not found: ' . $this->controller);
                return;
            }
            
            require_once $controllerFile;
            
            // Check if controller class exists
            if (!class_exists($this->controller)) {
                error_log('Error: Controller class not found: ' . $this->controller);
                $this->handleError('Controller class not found: ' . $this->controller);
                return;
            }
            
            $this->controller = new $this->controller();
    
            // Check if method exists
            if (isset($url[0])) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('App.php: Looking for method: ' . $url[0] . ' in controller: ' . get_class($this->controller));
                }

                // Convert hyphenated and underscored method names to camelCase 
                // (e.g., header-settings -> headerSettings, manage_clubs -> manageClubs)
                $methodName = $url[0];
                $camelCaseMethod = lcfirst(str_replace(' ', '', ucwords(str_replace(['-', '_'], ' ', $methodName))));

                // Special handling for generateQrCode and other special methods
                if ($url[0] === 'generateQrCode' && method_exists($this->controller, 'generateQrCode')) {
                    $this->method = 'generateQrCode';
                    unset($url[0]);
                    // Reindex the array after unsetting
                    $url = array_values($url);
                }
                // Try camelCase version first (for hyphenated URLs)
                elseif (method_exists($this->controller, $camelCaseMethod)) {
                    // Check if the method is public
                    $reflection = new ReflectionMethod($this->controller, $camelCaseMethod);
                    if ($reflection->isPublic()) {
                        $this->method = $camelCaseMethod;
                        unset($url[0]);
                        // Reindex the array after unsetting
                        $url = array_values($url);
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log('App.php: Using camelCase method: ' . $camelCaseMethod . ' for URL: ' . $methodName);
                        }
                    } else {
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log('App.php: CamelCase method ' . $camelCaseMethod . ' exists but is not public in ' . get_class($this->controller));
                        }
                    }
                }
                // General method check - only allow public methods
                elseif (method_exists($this->controller, $url[0])) {
                    // Check if the method is public
                    $reflection = new ReflectionMethod($this->controller, $url[0]);
                    if ($reflection->isPublic()) {
                        $this->method = $url[0];
                        unset($url[0]);
                        // Reindex the array after unsetting
                        $url = array_values($url);
                    } else {
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log('App.php: Method ' . $url[0] . ' exists but is not public in ' . get_class($this->controller));
                        }
                    }
                }
            }
            
            // Check if the method is callable
            if (!method_exists($this->controller, $this->method)) {
                error_log('Error: Method not found: ' . $this->method . ' in controller ' . get_class($this->controller));
                $this->handleError('Method not found: ' . $this->method);
                return;
            }
    
            // Get parameters
            $this->params = $url ? array_values($url) : [];
            
            // Debug parameters
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Controller: ' . get_class($this->controller) . ', Method: ' . $this->method . ', Params: ' . print_r($this->params, true));
                error_log('App.php: About to call method: ' . $this->method . ' on controller: ' . get_class($this->controller));
            }
    
            // Call the controller method with parameters
            call_user_func_array([$this->controller, $this->method], $this->params);
            
        } catch (Exception $e) {
            // Log the error
            error_log('Fatal error in App::__construct: ' . $e->getMessage());
            $this->handleError('An unexpected error occurred. Please try again later.');
        }
    }
    
    /**
     * Handle errors by redirecting to the error page
     * 
     * @param string $message Error message
     */
    protected function handleError($message) {
        // Try to redirect to the error page
        if (class_exists('HomeController')) {
            $controller = new HomeController();
            if (method_exists($controller, 'error')) {
                $controller->error(urlencode($message));
            } else {
                die('Error: ' . $message);
            }
        } else {
            die('Error: ' . $message);
        }
    }

    /**
     * Parse the URL into controller, method, and parameters
     * 
     * @return array Array of URL segments
     */
    protected function parseUrl() {
        try {
            if (isset($_GET['url'])) {
                // Clean and sanitize the URL
                $url = $_GET['url'];
                
                // Check if this is an asset file request (JS, CSS, images, etc.)
                if (preg_match('/\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$/i', $url)) {
                    // This is an asset file request, we should not process it
                    // Just return the default route to avoid routing errors
                    error_log('Asset file requested: ' . $url . '. Skipping routing.');
                    return ['home', 'index'];
                }
                
                // Log the raw URL for debugging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('Raw URL: ' . $url);
                }
                
                // Remove trailing slashes
                $url = rtrim($url, '/');
                
                // Sanitize URL
                $url = filter_var($url, FILTER_SANITIZE_URL);
                
                // Split into segments
                $segments = explode('/', $url);
                
                // Filter out empty segments
                $segments = array_filter($segments, function($segment) {
                    return $segment !== '';
                });
                
                // Reindex array
                $segments = array_values($segments);
                
                // Log the processed URL segments for debugging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('Processed URL segments: ' . print_r($segments, true));
                }
                
                return $segments;
            }
        } catch (Exception $e) {
            error_log('Error in parseUrl: ' . $e->getMessage());
        }
        
        // If we get here, there was no URL parameter or an error occurred
        // Instead of returning an empty array, return a default route
        error_log('No URL parameter found or error occurred, using default route');
        return ['home', 'index'];
    }
    
    /**
     * Handle API routes for PWA and AJAX requests
     * 
     * @param array $url URL segments
     */
    protected function handleApiRoute($url) {
        // Remove 'api' from the URL
        array_shift($url);
        
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[API_ROUTING] URL after removing api: ' . print_r($url, true));
        }
        
        if (empty($url)) {
            http_response_code(404);
            echo json_encode(['error' => 'API endpoint not specified']);
            return;
        }
        
        $endpoint = $url[0];
        $action = isset($url[1]) ? $url[1] : 'index';
        
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[API_ROUTING] Endpoint: ' . $endpoint . ', Action: ' . $action);
        }
        
        try {
            switch ($endpoint) {
                case 'pwa':
                    $this->handlePWAApi($action, array_slice($url, 2));
                    break;
                case 'notifications':
                    $this->handleNotificationApi($action, array_slice($url, 2));
                    break;
                case 'cameraBanners':
                    $this->handleCameraBannersApi($action, array_slice($url, 2));
                    break;
                case 'getSiteLogo':
                    $this->handleApiControllerMethod('getSiteLogo');
                    break;
                default:
                    http_response_code(404);
                    echo json_encode(['error' => 'API endpoint not found']);
            }
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[API] Error: ' . $e->getMessage());
            }
            http_response_code(500);
            echo json_encode(['error' => 'Internal server error']);
        }
    }
    
    /**
     * Handle PWA API requests
     * 
     * @param string $action API action
     * @param array $params Additional parameters
     */
    protected function handlePWAApi($action, $params) {
        require_once APPROOT . '/controllers/PWAController.php';
        $controller = new PWAController();
        
        switch ($action) {
            case 'vapid-key':
                $controller->getVapidKey();
                break;
            case 'subscribe':
                $controller->subscribe();
                break;
            case 'sync':
                $controller->offlineSync();
                break;
            case 'cached-data':
                $controller->getCachedData();
                break;
            case 'qr-scan':
                $controller->processQRScan();
                break;
            case 'metrics':
                $controller->getInstallMetrics();
                break;
            case 'usage':
                $controller->updateUsageData();
                break;

            default:
                http_response_code(404);
                echo json_encode(['error' => 'PWA API action not found']);
        }
    }
    
    /**
     * Handle Notification API requests
     * 
     * @param string $action API action
     * @param array $params Additional parameters
     */
    protected function handleNotificationApi($action, $params) {
        require_once APPROOT . '/controllers/NotificationController.php';
        $controller = new NotificationController();
        
        switch ($action) {
            case 'vapid-key':
                // Delegate to PWA controller for VAPID key
                require_once APPROOT . '/controllers/PWAController.php';
                $pwaController = new PWAController();
                $pwaController->getVapidKey();
                break;
            case 'subscribe':
                // Delegate to PWA controller for push subscription
                require_once APPROOT . '/controllers/PWAController.php';
                $pwaController = new PWAController();
                $pwaController->subscribe();
                break;
            case 'send':
                if (method_exists($controller, 'sendNotification')) {
                    $controller->sendNotification();
                } else {
                    // Fallback to PWA controller for push notifications
                    require_once APPROOT . '/controllers/PWAController.php';
                    $pwaController = new PWAController();
                    // Handle through PWA controller if needed
                    http_response_code(501);
                    echo json_encode(['error' => 'Push notification sending not implemented']);
                }
                break;
            case 'preferences':
                if (method_exists($controller, 'getPreferences')) {
                    $controller->getPreferences();
                } else {
                    http_response_code(501);
                    echo json_encode(['error' => 'Notification preferences not implemented']);
                }
                break;
            default:
                http_response_code(404);
                echo json_encode(['error' => 'Notification API action not found']);
        }
    }
    
    /**
     * Handle Camera Banners API requests
     * 
     * @param string $action API action
     * @param array $params Additional parameters
     */
    protected function handleCameraBannersApi($action, $params) {
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[CAMERA_BANNERS_API] Handler called with action: ' . $action);
        }
        
        require_once APPROOT . '/controllers/ApiController.php';
        $controller = new ApiController();
        
        switch ($action) {
            case 'index':
            default:
                // Default action is to get camera banners
                if (method_exists($controller, 'cameraBanners')) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('[CAMERA_BANNERS_API] Calling cameraBanners method');
                    }
                    $controller->cameraBanners();
                } else {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('[CAMERA_BANNERS_API] cameraBanners method not found');
                    }
                    http_response_code(501);
                    echo json_encode(['error' => 'Camera banners API not implemented']);
                }
                break;
        }
    }

    /**
     * Handle general API controller methods
     *
     * @param string $methodName Method name to call on ApiController
     */
    protected function handleApiControllerMethod($methodName) {
        require_once APPROOT . '/controllers/ApiController.php';
        $controller = new ApiController();

        if (method_exists($controller, $methodName)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[API] Calling ApiController::' . $methodName);
            }
            $controller->$methodName();
        } else {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[API] Method not found: ApiController::' . $methodName);
            }
            http_response_code(404);
            echo json_encode(['error' => 'API method not found']);
        }
    }
}