/**
 * Advanced Image Viewer
 * Provides AJAX popout with zooming and moving capabilities for thumbnails
 */

// Prevent duplicate loading
if (typeof window.ImageViewer !== 'undefined') {
    console.warn('ImageViewer already loaded, skipping duplicate initialization');
} else {

class ImageViewer {
    constructor(options = {}) {
        this.options = Object.assign({
            selector: '[data-image-viewer]',
            zoomStep: 0.1,
            maxZoom: 3,
            minZoom: 0.5,
            initialZoom: 1,
            transitionDuration: 300,
            overlayColor: 'rgba(0, 0, 0, 0.9)',
            closeOnEscape: true,
            closeOnOverlayClick: true,
            showZoomControls: true,
            showCloseButton: true,
            enableKeyboardControls: true,
            enableMouseWheelZoom: true,
            enableDragging: true,
            preloadImages: true,
            showImageCount: true,
            showLoader: true
        }, options);

        this.currentZoom = this.options.initialZoom;
        this.isDragging = false;
        this.startX = 0;
        this.startY = 0;
        this.translateX = 0;
        this.translateY = 0;
        this.images = [];
        this.currentIndex = 0;
        this.isOpen = false;

        this.init();
    }

    init() {
        // Create viewer elements
        this.createViewerElements();
        
        // Bind events
        this.bindEvents();
    }

    createViewerElements() {
        // Create overlay
        this.overlay = document.createElement('div');
        this.overlay.className = 'image-viewer-overlay';
        this.overlay.style.display = 'none';
        
        // Create container
        this.container = document.createElement('div');
        this.container.className = 'image-viewer-container';
        
        // Create image wrapper
        this.imageWrapper = document.createElement('div');
        this.imageWrapper.className = 'image-viewer-image-wrapper';
        
        // Create image element
        this.image = document.createElement('img');
        this.image.className = 'image-viewer-image';
        this.imageWrapper.appendChild(this.image);
        
        // Create loader
        this.loader = document.createElement('div');
        this.loader.className = 'image-viewer-loader';
        this.loader.innerHTML = '<div class="spinner"></div>';
        
        // Create controls
        this.controls = document.createElement('div');
        this.controls.className = 'image-viewer-controls';
        
        // Create close button
        this.closeButton = document.createElement('button');
        this.closeButton.className = 'image-viewer-close';
        this.closeButton.innerHTML = '<i class="fas fa-times"></i>';
        this.closeButton.setAttribute('aria-label', 'Close');
        
        // Create zoom controls
        this.zoomControls = document.createElement('div');
        this.zoomControls.className = 'image-viewer-zoom-controls';
        
        this.zoomInButton = document.createElement('button');
        this.zoomInButton.className = 'image-viewer-zoom-in';
        this.zoomInButton.innerHTML = '<i class="fas fa-search-plus"></i>';
        this.zoomInButton.setAttribute('aria-label', 'Zoom in');
        
        this.zoomOutButton = document.createElement('button');
        this.zoomOutButton.className = 'image-viewer-zoom-out';
        this.zoomOutButton.innerHTML = '<i class="fas fa-search-minus"></i>';
        this.zoomOutButton.setAttribute('aria-label', 'Zoom out');
        
        this.resetZoomButton = document.createElement('button');
        this.resetZoomButton.className = 'image-viewer-zoom-reset';
        this.resetZoomButton.innerHTML = '<i class="fas fa-compress-arrows-alt"></i>';
        this.resetZoomButton.setAttribute('aria-label', 'Reset zoom');
        
        this.zoomControls.appendChild(this.zoomOutButton);
        this.zoomControls.appendChild(this.resetZoomButton);
        this.zoomControls.appendChild(this.zoomInButton);
        
        // Create navigation controls
        this.navControls = document.createElement('div');
        this.navControls.className = 'image-viewer-nav-controls';
        
        this.prevButton = document.createElement('button');
        this.prevButton.className = 'image-viewer-prev';
        this.prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
        this.prevButton.setAttribute('aria-label', 'Previous image');
        
        this.nextButton = document.createElement('button');
        this.nextButton.className = 'image-viewer-next';
        this.nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
        this.nextButton.setAttribute('aria-label', 'Next image');
        
        this.imageCounter = document.createElement('div');
        this.imageCounter.className = 'image-viewer-counter';
        
        this.navControls.appendChild(this.prevButton);
        this.navControls.appendChild(this.imageCounter);
        this.navControls.appendChild(this.nextButton);
        
        // Assemble controls
        this.controls.appendChild(this.closeButton);
        this.controls.appendChild(this.zoomControls);
        
        // Assemble container
        this.container.appendChild(this.imageWrapper);
        this.container.appendChild(this.controls);
        this.container.appendChild(this.navControls);
        this.container.appendChild(this.loader);
        
        // Add to overlay
        this.overlay.appendChild(this.container);
        
        // Add to document
        document.body.appendChild(this.overlay);
    }

    bindEvents() {
        // Find all image thumbnails
        const thumbnails = document.querySelectorAll(this.options.selector);
        console.log('Image Viewer: bindEvents found ' + thumbnails.length + ' thumbnails');
        
        // Bind click event to each thumbnail
        thumbnails.forEach((thumbnail, index) => {
            console.log('Image Viewer: Binding click event to thumbnail', thumbnail);
            thumbnail.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Image Viewer: Thumbnail clicked', e.target);
                
                // Get all images in the same gallery
                const galleryId = thumbnail.getAttribute('data-gallery-id') || 'default';
                console.log('Image Viewer: Gallery ID', galleryId);
                this.images = Array.from(document.querySelectorAll(`${this.options.selector}[data-gallery-id="${galleryId}"]`));
                console.log('Image Viewer: Found ' + this.images.length + ' images in gallery');
                
                // Find index of clicked image
                this.currentIndex = this.images.findIndex(img => img === thumbnail);
                console.log('Image Viewer: Current index', this.currentIndex);
                
                // Get image source
                let imageSrc = thumbnail.getAttribute('href') || thumbnail.getAttribute('src');
                
                // Ensure the image path is absolute
                if (imageSrc && imageSrc.indexOf('http') !== 0 && typeof BASE_URL !== 'undefined') {
                    // If the path already starts with a slash, remove it to avoid double slashes
                    if (imageSrc.startsWith('/')) {
                        imageSrc = imageSrc.substring(1);
                    }
                    
                    // If the path already includes BASE_URL, don't add it again
                    if (imageSrc.indexOf(BASE_URL) !== 0) {
                        imageSrc = BASE_URL + '/' + imageSrc;
                    }
                }
                
                console.log('Image Viewer: Opening image', imageSrc);
                this.open(imageSrc);
            });
        });
        
        // Close button click
        this.closeButton.addEventListener('click', () => this.close());
        
        // Overlay click
        if (this.options.closeOnOverlayClick) {
            this.overlay.addEventListener('click', (e) => {
                if (e.target === this.overlay) {
                    this.close();
                }
            });
        }
        
        // Zoom controls
        this.zoomInButton.addEventListener('click', () => this.zoomIn());
        this.zoomOutButton.addEventListener('click', () => this.zoomOut());
        this.resetZoomButton.addEventListener('click', () => this.resetZoom());
        
        // Navigation controls
        this.prevButton.addEventListener('click', () => this.showPrevImage());
        this.nextButton.addEventListener('click', () => this.showNextImage());
        
        // Keyboard controls
        if (this.options.enableKeyboardControls) {
            document.addEventListener('keydown', (e) => {
                if (!this.isOpen) return;
                
                switch (e.key) {
                    case 'Escape':
                        if (this.options.closeOnEscape) this.close();
                        break;
                    case 'ArrowLeft':
                        this.showPrevImage();
                        break;
                    case 'ArrowRight':
                        this.showNextImage();
                        break;
                    case '+':
                        this.zoomIn();
                        break;
                    case '-':
                        this.zoomOut();
                        break;
                    case '0':
                        this.resetZoom();
                        break;
                }
            });
        }
        
        // Mouse wheel zoom
        if (this.options.enableMouseWheelZoom) {
            this.imageWrapper.addEventListener('wheel', (e) => {
                if (!this.isOpen) return;
                
                e.preventDefault();
                
                if (e.deltaY < 0) {
                    this.zoomIn();
                } else {
                    this.zoomOut();
                }
            }, { passive: false });
        }
        
        // Dragging
        if (this.options.enableDragging) {
            // Mouse events
            this.imageWrapper.addEventListener('mousedown', (e) => this.startDrag(e));
            document.addEventListener('mousemove', (e) => this.drag(e));
            document.addEventListener('mouseup', () => this.endDrag());
            
            // Touch events
            this.imageWrapper.addEventListener('touchstart', (e) => this.startDrag(e), { passive: false });
            document.addEventListener('touchmove', (e) => this.drag(e), { passive: false });
            document.addEventListener('touchend', () => this.endDrag());
        }
        
        // Image load event
        this.image.addEventListener('load', () => {
            this.loader.style.display = 'none';
            this.imageWrapper.style.opacity = '1';
        });
    }

    open(src) {
        console.log('Image Viewer: Opening image', src);
        
        // Show overlay
        this.overlay.style.display = 'flex';
        setTimeout(() => {
            this.overlay.style.opacity = '1';
        }, 10);
        
        // Reset zoom and position
        this.resetZoom();
        
        // Show loader
        this.loader.style.display = 'flex';
        this.imageWrapper.style.opacity = '0';
        
        // Load image
        if (!src) {
            console.error('Image Viewer: No source provided for image');
            this.close();
            return;
        }
        
        // Create a new image to test loading
        const testImg = new Image();
        testImg.onload = () => {
            console.log('Image Viewer: Image loaded successfully', src);
            // Now set the actual image src
            this.image.src = src;
        };
        
        testImg.onerror = () => {
            console.error('Image Viewer: Failed to load image', src);
            // Try with BASE_URL prefix if the path is relative
            if (src.indexOf('http') !== 0 && typeof BASE_URL !== 'undefined') {
                const newSrc = BASE_URL + src;
                console.log('Image Viewer: Trying with BASE_URL', newSrc);
                this.image.src = newSrc;
            } else {
                this.close();
                alert('Failed to load image. Please try again.');
            }
        };
        
        testImg.src = src;
        
        // Set up image load event
        this.image.onload = () => {
            console.log('Image Viewer: Main image loaded');
            this.loader.style.display = 'none';
            this.imageWrapper.style.opacity = '1';
        };
        
        this.image.onerror = () => {
            console.error('Image Viewer: Main image failed to load');
            this.close();
            alert('Failed to load image. Please try again.');
        };
        
        // Update counter
        this.updateCounter();
        
        // Update navigation buttons
        this.updateNavButtons();
        
        // Set flag
        this.isOpen = true;
        
        // Preload adjacent images
        if (this.options.preloadImages) {
            this.preloadAdjacentImages();
        }
        
        // Prevent body scrolling
        document.body.style.overflow = 'hidden';
    }

    close() {
        // Hide overlay
        this.overlay.style.opacity = '0';
        setTimeout(() => {
            this.overlay.style.display = 'none';
        }, this.options.transitionDuration);
        
        // Reset zoom and position
        this.resetZoom();
        
        // Set flag
        this.isOpen = false;
        
        // Restore body scrolling
        document.body.style.overflow = '';
    }

    zoomIn() {
        if (this.currentZoom >= this.options.maxZoom) return;
        
        this.currentZoom += this.options.zoomStep;
        this.applyZoom();
    }

    zoomOut() {
        if (this.currentZoom <= this.options.minZoom) return;
        
        this.currentZoom -= this.options.zoomStep;
        this.applyZoom();
    }

    resetZoom() {
        this.currentZoom = this.options.initialZoom;
        this.translateX = 0;
        this.translateY = 0;
        this.applyZoom();
    }

    applyZoom() {
        this.image.style.transform = `translate(${this.translateX}px, ${this.translateY}px) scale(${this.currentZoom})`;
    }

    startDrag(e) {
        if (this.currentZoom <= 1) return;
        
        this.isDragging = true;
        
        // Get start position
        if (e.type === 'touchstart') {
            this.startX = e.touches[0].clientX - this.translateX;
            this.startY = e.touches[0].clientY - this.translateY;
        } else {
            this.startX = e.clientX - this.translateX;
            this.startY = e.clientY - this.translateY;
        }
        
        // Add dragging class
        this.imageWrapper.classList.add('dragging');
    }

    drag(e) {
        if (!this.isDragging) return;
        
        e.preventDefault();
        
        // Calculate new position
        if (e.type === 'touchmove') {
            this.translateX = e.touches[0].clientX - this.startX;
            this.translateY = e.touches[0].clientY - this.startY;
        } else {
            this.translateX = e.clientX - this.startX;
            this.translateY = e.clientY - this.startY;
        }
        
        // Apply new position
        this.applyZoom();
    }

    endDrag() {
        this.isDragging = false;
        
        // Remove dragging class
        this.imageWrapper.classList.remove('dragging');
    }

    showPrevImage() {
        if (this.images.length <= 1) return;
        
        this.currentIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
        this.open(this.images[this.currentIndex].getAttribute('href') || this.images[this.currentIndex].getAttribute('src'));
    }

    showNextImage() {
        if (this.images.length <= 1) return;
        
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
        this.open(this.images[this.currentIndex].getAttribute('href') || this.images[this.currentIndex].getAttribute('src'));
    }

    updateCounter() {
        if (!this.options.showImageCount || this.images.length <= 1) {
            this.imageCounter.style.display = 'none';
            return;
        }
        
        this.imageCounter.style.display = 'block';
        this.imageCounter.textContent = `${this.currentIndex + 1} / ${this.images.length}`;
    }

    updateNavButtons() {
        if (this.images.length <= 1) {
            this.navControls.style.display = 'none';
        } else {
            this.navControls.style.display = 'flex';
        }
    }

    preloadAdjacentImages() {
        if (this.images.length <= 1) return;
        
        // Preload next image
        const nextIndex = (this.currentIndex + 1) % this.images.length;
        let nextSrc = this.images[nextIndex].getAttribute('href') || this.images[nextIndex].getAttribute('src');
        
        // Preload previous image
        const prevIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
        let prevSrc = this.images[prevIndex].getAttribute('href') || this.images[prevIndex].getAttribute('src');
        
        // Ensure paths are absolute
        if (nextSrc && nextSrc.indexOf('http') !== 0 && typeof BASE_URL !== 'undefined') {
            if (nextSrc.startsWith('/')) nextSrc = nextSrc.substring(1);
            if (nextSrc.indexOf(BASE_URL) !== 0) nextSrc = BASE_URL + '/' + nextSrc;
        }
        
        if (prevSrc && prevSrc.indexOf('http') !== 0 && typeof BASE_URL !== 'undefined') {
            if (prevSrc.startsWith('/')) prevSrc = prevSrc.substring(1);
            if (prevSrc.indexOf(BASE_URL) !== 0) prevSrc = BASE_URL + '/' + prevSrc;
        }
        
        // Create image objects to trigger preloading
        if (nextSrc) {
            const nextImg = new Image();
            nextImg.src = nextSrc;
        }
        
        if (prevSrc) {
            const prevImg = new Image();
            prevImg.src = prevSrc;
        }
    }
}

/**
 * Initialize the image viewer
 * This function can be called after the DOM is loaded or after dynamic content is added
 */
function initImageViewer() {
    console.log('Image Viewer: Initializing...');
    
    // Find all thumbnails with data-image-viewer attribute
    const thumbnails = document.querySelectorAll('[data-image-viewer]');
    console.log('Image Viewer: Found ' + thumbnails.length + ' thumbnails');
    
    // Initialize image viewer if not already initialized
    if (!window.imageViewer) {
        try {
            window.imageViewer = new ImageViewer();
            console.log('Image Viewer: Successfully initialized');
        } catch (error) {
            console.error('Image Viewer: Error initializing', error);
        }
    } else {
        console.log('Image Viewer: Already initialized, rebinding events');
        window.imageViewer.bindEvents();
    }
}

// Make ImageViewer available globally
window.ImageViewer = ImageViewer;

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize image viewer
    initImageViewer();

    // Add a small delay to ensure all images are loaded
    setTimeout(function() {
        initImageViewer();
    }, 500);
});

} // End duplicate loading prevention