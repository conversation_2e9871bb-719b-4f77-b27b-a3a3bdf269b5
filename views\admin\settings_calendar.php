<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Calendar & Map Settings</h1>
            <p class="text-muted">Configure calendar display, event chart, map provider, and image settings</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <!-- Calendar & Map Settings Sections -->
    <div class="row g-4 mb-5">
        
        <!-- Calendar Display Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calendar-alt text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Calendar Display</h4>
                    </div>
                    <p class="card-text text-muted">Configure basic calendar display options, date formats, and view settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_calendar_display" class="stretched-link text-decoration-none">
                        <span class="d-none">View Calendar Display Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Event Chart Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-chart-bar text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Event Chart</h4>
                    </div>
                    <p class="card-text text-muted">Configure Monthly Event Chart timeline view, drag & drop, and mobile settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_chart" class="stretched-link text-decoration-none">
                        <span class="d-none">View Event Chart Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Map Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-map-marked-alt text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Map Settings</h4>
                    </div>
                    <p class="card-text text-muted">Configure map provider, API keys, default location, and zoom settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_map" class="stretched-link text-decoration-none">
                        <span class="d-none">View Map Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Map Tools Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-tools text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Map Tools</h4>
                    </div>
                    <p class="card-text text-muted">Access geocoding tools, batch processing, and map verification utilities.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_map_tools" class="stretched-link text-decoration-none">
                        <span class="d-none">View Map Tools</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Event Image Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-images text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Event Images</h4>
                    </div>
                    <p class="card-text text-muted">Configure event image upload limits, file types, and social sharing options.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_images" class="stretched-link text-decoration-none">
                        <span class="d-none">View Event Image Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-bolt text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Quick Actions</h4>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-calendar me-1"></i> View Calendar
                        </a>
                        <a href="<?php echo BASE_URL; ?>/calendar/map" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-map me-1"></i> View Map
                        </a>
                        <a href="<?php echo BASE_URL; ?>/admin/geocodeEvents" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-map-marker-alt me-1"></i> Geocode Events
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Calendar System Information</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Current Calendar System</h5>
                            <p>The system uses a <strong>Monthly Event Chart</strong> as the primary calendar view, which displays events in a timeline format for easy viewing and management.</p>
                            
                            <h5>Key Features</h5>
                            <ul>
                                <li><strong>Timeline View:</strong> Events displayed horizontally across dates</li>
                                <li><strong>Mobile Responsive:</strong> Automatic card view on smaller screens</li>
                                <li><strong>Interactive Maps:</strong> Location-based event display</li>
                                <li><strong>Image Support:</strong> Event images with social sharing</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Settings Organization</h5>
                            <p>Calendar and map settings are organized into focused sections for easier management:</p>
                            
                            <ul>
                                <li><strong>Calendar Display:</strong> Basic calendar formatting and view options</li>
                                <li><strong>Event Chart:</strong> Timeline-specific settings and behavior</li>
                                <li><strong>Map Settings:</strong> Provider configuration and location defaults</li>
                                <li><strong>Map Tools:</strong> Geocoding and location utilities</li>
                                <li><strong>Event Images:</strong> Upload limits and display options</li>
                            </ul>
                            
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>Tip:</strong> Start with Map Settings to configure your provider, then use Map Tools to geocode existing events.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
        
        <div class="row">
            <!-- Calendar Settings -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm border-0 h-100">
                    <div class="card-header bg-primary text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-calendar-alt me-2"></i> Calendar Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <label for="calendar_default_view" class="form-label">Default Calendar View</label>
                            <select class="form-select" id="calendar_default_view" name="calendar_default_view">
                                <option value="month" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'month' ? 'selected' : ''; ?>>Month</option>
                                <option value="week" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'week' ? 'selected' : ''; ?>>Week</option>
                                <option value="day" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'day' ? 'selected' : ''; ?>>Day</option>
                                <option value="list" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'list' ? 'selected' : ''; ?>>List</option>
                                <option value="event" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'event' ? 'selected' : ''; ?>>Monthly Event Chart</option>
                            </select>
                            <div class="form-text">The default view when users visit the calendar page</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_start_day" class="form-label">Week Start Day</label>
                            <select class="form-select" id="calendar_start_day" name="calendar_start_day">
                                <option value="0" <?php echo isset($calendarSettings['start_day']) && $calendarSettings['start_day'] == '0' ? 'selected' : ''; ?>>Sunday</option>
                                <option value="1" <?php echo isset($calendarSettings['start_day']) && $calendarSettings['start_day'] == '1' ? 'selected' : ''; ?>>Monday</option>
                                <option value="6" <?php echo isset($calendarSettings['start_day']) && $calendarSettings['start_day'] == '6' ? 'selected' : ''; ?>>Saturday</option>
                            </select>
                            <div class="form-text">The first day of the week in calendar views</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_time_format" class="form-label">Time Format</label>
                            <select class="form-select" id="calendar_time_format" name="calendar_time_format">
                                <option value="12" <?php echo isset($calendarSettings['time_format']) && $calendarSettings['time_format'] == '12' ? 'selected' : ''; ?>>12-hour (1:30 PM)</option>
                                <option value="24" <?php echo isset($calendarSettings['time_format']) && $calendarSettings['time_format'] == '24' ? 'selected' : ''; ?>>24-hour (13:30)</option>
                            </select>
                            <div class="form-text">Format for displaying time in the calendar</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_date_format" class="form-label">Date Format</label>
                            <select class="form-select" id="calendar_date_format" name="calendar_date_format">
                                <option value="MM/DD/YYYY" <?php echo isset($calendarSettings['date_format']) && $calendarSettings['date_format'] == 'MM/DD/YYYY' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                <option value="DD/MM/YYYY" <?php echo isset($calendarSettings['date_format']) && $calendarSettings['date_format'] == 'DD/MM/YYYY' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                <option value="YYYY-MM-DD" <?php echo isset($calendarSettings['date_format']) && $calendarSettings['date_format'] == 'YYYY-MM-DD' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                            </select>
                            <div class="form-text">Format for displaying dates in the calendar</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_events_per_page" class="form-label">Events Per Page</label>
                            <input type="number" class="form-control" id="calendar_events_per_page" name="calendar_events_per_page" value="<?php echo isset($calendarSettings['events_per_page']) ? $calendarSettings['events_per_page'] : '10'; ?>" min="5" max="100">
                            <div class="form-text">Number of events to display per page in list view</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Event Chart Settings -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm border-0 h-100">
                    <div class="card-header bg-success text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-chart-event me-2"></i> Monthly Event Chart Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_show_weekends" name="event_show_weekends" <?php echo isset($calendarSettings['event_show_weekends']) && $calendarSettings['event_show_weekends'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_show_weekends">
                                    Show Weekends in Event Chart
                                </label>
                            </div>
                            <div class="form-text">Display weekend columns in the Event chart timeline</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_enable_drag_drop" name="event_enable_drag_drop" <?php echo isset($calendarSettings['event_enable_drag_drop']) && $calendarSettings['event_enable_drag_drop'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_enable_drag_drop">
                                    Enable Drag & Drop in Event Chart
                                </label>
                            </div>
                            <div class="form-text">Allow users to drag and drop events to change dates (requires edit permissions)</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_show_today_line" name="event_show_today_line" <?php echo isset($calendarSettings['event_show_today_line']) && $calendarSettings['event_show_today_line'] !== false ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_show_today_line">
                                    Show Today Indicator Line
                                </label>
                            </div>
                            <div class="form-text">Display animated today indicator line across the Event chart</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_show_event_hover" name="event_show_event_hover" <?php echo isset($calendarSettings['event_show_event_hover']) && $calendarSettings['event_show_event_hover'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_show_event_hover">
                                    Show Event Hover Popups
                                </label>
                            </div>
                            <div class="form-text">Display event details when hovering over events in the Event chart</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_mobile_breakpoint" class="form-label">Mobile Breakpoint</label>
                            <select class="form-select" id="event_mobile_breakpoint" name="event_mobile_breakpoint">
                                <option value="576" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '576' ? 'selected' : ''; ?>>Small (576px)</option>
                                <option value="768" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '768' ? 'selected' : ''; ?>>Medium (768px)</option>
                                <option value="992" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '992' ? 'selected' : ''; ?>>Large (992px) - Default</option>
                                <option value="1200" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '1200' ? 'selected' : ''; ?>>Extra Large (1200px)</option>
                            </select>
                            <div class="form-text">Screen width below which the mobile card view is used instead of the Event chart</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Map Settings -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm border-0 h-100">
                    <div class="card-header bg-primary text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-map-marked-alt me-2"></i> Map Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <label for="map_provider" class="form-label">Map Provider</label>
                            <select class="form-select" id="map_provider" name="map_provider">
                                <option value="openstreetmap" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'openstreetmap' ? 'selected' : ''; ?>>OpenStreetMap (Free)</option>
                                <option value="google" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'google' ? 'selected' : ''; ?>>Google Maps (Paid)</option>
                                <option value="mapbox" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'mapbox' ? 'selected' : ''; ?>>Mapbox (Paid)</option>
                                <option value="here" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'here' ? 'selected' : ''; ?>>HERE Maps (Paid)</option>

                            </select>
                            <div class="form-text">The map provider to use for displaying event locations</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_api_key" class="form-label">API Key (Client-Side)</label>
                            <input type="text" class="form-control" id="map_api_key" name="map_api_key" value="<?php echo isset($mapSettings['api_key']) ? $mapSettings['api_key'] : ''; ?>">
                            <div class="form-text">
                                <strong>For Google:</strong> Maps JavaScript API key with HTTP referrer restrictions.<br>
                                <strong>For others:</strong> Main API key for the selected provider.
                            </div>
                        </div>
                        
                        <div class="mb-3 google-server-key" style="display: <?php echo (isset($mapSettings['provider']) && $mapSettings['provider'] == 'google') ? 'block' : 'none'; ?>;">
                            <label for="map_server_api_key" class="form-label">Server-Side API Key <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="map_server_api_key" name="map_server_api_key" value="<?php echo isset($mapSettings['server_api_key']) ? $mapSettings['server_api_key'] : ''; ?>">
                            <div class="form-text">
                                <strong>Google Places & Geocoding API key</strong> with IP address restrictions (no referrer restrictions).
                            </div>
                        </div>
                        
                        <div class="mb-3 google-api-info" style="display: <?php echo (isset($mapSettings['provider']) && $mapSettings['provider'] == 'google') ? 'block' : 'none'; ?>;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Google API Key Requirements:</h6>
                                <ul class="mb-0 small">
                                    <li><strong>Client-Side Key:</strong> Maps JavaScript API with HTTP referrer restrictions</li>
                                    <li><strong>Server-Side Key:</strong> Places API + Geocoding API with IP address restrictions</li>
                                    <li>Both keys must be enabled for the same Google Cloud project</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_default_lat" class="form-label">Default Latitude</label>
                            <input type="text" class="form-control" id="map_default_lat" name="map_default_lat" value="<?php echo isset($mapSettings['default_lat']) ? $mapSettings['default_lat'] : '39.8283'; ?>">
                            <div class="form-text">Default center latitude for the map (e.g., 39.8283 for center of US)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_default_lng" class="form-label">Default Longitude</label>
                            <input type="text" class="form-control" id="map_default_lng" name="map_default_lng" value="<?php echo isset($mapSettings['default_lng']) ? $mapSettings['default_lng'] : '-98.5795'; ?>">
                            <div class="form-text">Default center longitude for the map (e.g., -98.5795 for center of US)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_default_zoom" class="form-label">Default Zoom Level</label>
                            <input type="number" class="form-control" id="map_default_zoom" name="map_default_zoom" value="<?php echo isset($mapSettings['default_zoom']) ? $mapSettings['default_zoom'] : '4'; ?>" min="1" max="20">
                            <div class="form-text">Default zoom level for the map (1-20, where 1 is zoomed out)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_filter_radius" class="form-label">Default Filter Radius (miles)</label>
                            <input type="number" class="form-control" id="map_filter_radius" name="map_filter_radius" value="<?php echo isset($mapSettings['filter_radius']) ? $mapSettings['filter_radius'] : '100'; ?>" min="1" max="1000">
                            <div class="form-text">Default radius for location-based filtering in miles</div>
                        </div>
                        
                        <div id="openstreetmap_settings" class="<?php echo (!isset($mapSettings['provider']) || $mapSettings['provider'] == 'openstreetmap') ? '' : 'd-none'; ?>">
                            <div class="mb-3">
                                <label for="map_tile_url" class="form-label">Tile URL (OpenStreetMap)</label>
                                <input type="text" class="form-control" id="map_tile_url" name="map_tile_url" value="<?php echo isset($mapSettings['tile_url']) ? $mapSettings['tile_url'] : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'; ?>">
                                <div class="form-text">URL template for OpenStreetMap tiles</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="map_attribution" class="form-label">Attribution (OpenStreetMap)</label>
                                <input type="text" class="form-control" id="map_attribution" name="map_attribution" value="<?php echo isset($mapSettings['attribution']) ? $mapSettings['attribution'] : '&copy; <a href=&quot;https://www.openstreetmap.org/copyright&quot;>OpenStreetMap</a> contributors'; ?>">
                                <div class="form-text">Attribution text required by OpenStreetMap</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-info text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-tools me-2"></i> Map Tools</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-4">
                                <h5>Enhanced Geocoding</h5>
                                <p>Use the enhanced geocoding tool to add coordinates to events that don't have them.</p>
                                <a href="<?php echo BASE_URL; ?>/admin/geocodeEvents" class="btn btn-primary">
                                    <i class="fas fa-map-marker-alt me-2"></i> Enhanced Geocoding
                                </a>
                            </div>
                            <div class="col-md-4">
                                <h5>Batch Geocoding</h5>
                                <p>Use the batch geocoding tool to add coordinates to venues and events that don't have them.</p>
                                <a href="<?php echo BASE_URL; ?>/calendar/batchGeocode" class="btn btn-info">
                                    <i class="fas fa-map-marker-alt me-2"></i> Batch Geocoding Tool
                                </a>
                            </div>
                            <div class="col-md-4">
                                <h5>Map View</h5>
                                <p>View all events on the map to verify locations and settings.</p>
                                <a href="<?php echo BASE_URL; ?>/calendar/map" class="btn btn-info">
                                    <i class="fas fa-map me-2"></i> View Map
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Event Image Settings -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm border-0 h-100">
                    <div class="card-header bg-success text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-images me-2"></i> Event Image Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <label for="event_max_images" class="form-label">Maximum Images per Event</label>
                            <input type="number" class="form-control" id="event_max_images" name="event_max_images" 
                                   value="<?php echo isset($calendarSettings['event_max_images']) ? $calendarSettings['event_max_images'] : '2'; ?>" 
                                   min="1" max="10">
                            <div class="form-text">Maximum number of images allowed per event (1-10)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_max_image_size" class="form-label">Maximum Image Size (MB)</label>
                            <input type="number" class="form-control" id="event_max_image_size" name="event_max_image_size" 
                                   value="<?php echo isset($calendarSettings['event_max_image_size']) ? $calendarSettings['event_max_image_size'] : '2'; ?>" 
                                   min="1" max="10" step="0.5">
                            <div class="form-text">Maximum file size for each image in megabytes (1-10 MB)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_allowed_image_types" class="form-label">Allowed Image Types</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_jpeg" name="event_allowed_image_types[]" value="image/jpeg" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/jpeg', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_jpeg">JPEG</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_jpg" name="event_allowed_image_types[]" value="image/jpg" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/jpg', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_jpg">JPG</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_png" name="event_allowed_image_types[]" value="image/png" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/png', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_png">PNG</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_gif" name="event_allowed_image_types[]" value="image/gif" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/gif', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_gif">GIF</label>
                            </div>
                            <div class="form-text">Select which image file types are allowed for event uploads</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_enable_wysiwyg" name="event_enable_wysiwyg" 
                                       <?php echo (!isset($calendarSettings['event_enable_wysiwyg']) || $calendarSettings['event_enable_wysiwyg']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_enable_wysiwyg">
                                    Enable WYSIWYG Editor
                                </label>
                            </div>
                            <div class="form-text">Enable rich text editor with image upload for event descriptions</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_social_sharing_images" name="event_social_sharing_images" 
                                       <?php echo (!isset($calendarSettings['event_social_sharing_images']) || $calendarSettings['event_social_sharing_images']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_social_sharing_images">
                                    Include Images in Social Sharing
                                </label>
                            </div>
                            <div class="form-text">Include event images when sharing events on social media</div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> Images are stored as Base64 encoded data in the database. 
                            Large images or many images per event may impact database performance.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-5">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i> Save Settings
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide provider-specific settings based on provider selection
    const mapProviderSelect = document.getElementById('map_provider');
    const osmSettings = document.getElementById('openstreetmap_settings');
    const googleServerKeyField = document.querySelector('.google-server-key');
    const googleApiInfo = document.querySelector('.google-api-info');
    
    function toggleProviderSettings() {
        const provider = mapProviderSelect.value;
        
        // Show/hide OpenStreetMap settings
        if (osmSettings) {
            if (provider === 'openstreetmap') {
                osmSettings.classList.remove('d-none');
            } else {
                osmSettings.classList.add('d-none');
            }
        }
        
        // Show/hide Google server-side API key field
        if (googleServerKeyField && googleApiInfo) {
            if (provider === 'google') {
                googleServerKeyField.style.display = 'block';
                googleApiInfo.style.display = 'block';
            } else {
                googleServerKeyField.style.display = 'none';
                googleApiInfo.style.display = 'none';
            }
        }
    }
    
    // Initial toggle
    toggleProviderSettings();
    
    // Toggle on change
    mapProviderSelect.addEventListener('change', toggleProviderSettings);
    
    // Preview map center and zoom
    const latInput = document.getElementById('map_default_lat');
    const lngInput = document.getElementById('map_default_lng');
    const zoomInput = document.getElementById('map_default_zoom');
    
    // Add preview button after zoom input
    const previewBtn = document.createElement('button');
    previewBtn.type = 'button';
    previewBtn.className = 'btn btn-outline-secondary mt-2';
    previewBtn.innerHTML = '<i class="fas fa-eye me-2"></i> Preview Map Center';
    previewBtn.onclick = function(e) {
        e.preventDefault();
        const lat = parseFloat(latInput.value);
        const lng = parseFloat(lngInput.value);
        const zoom = parseInt(zoomInput.value);
        
        if (isNaN(lat) || isNaN(lng) || isNaN(zoom)) {
            alert('Please enter valid latitude, longitude, and zoom values.');
            return;
        }
        
        window.open(`https://www.google.com/maps/@${lat},${lng},${zoom}z`, '_blank');
    };
    
    zoomInput.parentNode.appendChild(previewBtn);
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>