-- Comprehensive Performance Indexes for Dashboard and Management Optimization
-- Run this to optimize queries for thousands of records across all optimized pages

-- ========================================
-- REGISTRATIONS TABLE INDEXES
-- ========================================
CREATE INDEX IF NOT EXISTS idx_registrations_show_id ON registrations(show_id);
CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_id ON registrations(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_registrations_category_id ON registrations(category_id);
CREATE INDEX IF NOT EXISTS idx_registrations_payment_status ON registrations(payment_status);
CREATE INDEX IF NOT EXISTS idx_registrations_status ON registrations(status);
CREATE INDEX IF NOT EXISTS idx_registrations_created_at ON registrations(created_at);
CREATE INDEX IF NOT EXISTS idx_registrations_updated_at ON registrations(updated_at);

-- Composite indexes for complex registration queries
CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_show ON registrations(vehicle_id, show_id);
CREATE INDEX IF NOT EXISTS idx_registrations_payment_created ON registrations(payment_status, created_at);
CREATE INDEX IF NOT EXISTS idx_registrations_status_date ON registrations(status, created_at);
CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_payment ON registrations(vehicle_id, payment_status);

-- ========================================
-- SHOWS TABLE INDEXES
-- ========================================
CREATE INDEX IF NOT EXISTS idx_shows_coordinator_id ON shows(coordinator_id);
CREATE INDEX IF NOT EXISTS idx_shows_start_date ON shows(start_date);
CREATE INDEX IF NOT EXISTS idx_shows_end_date ON shows(end_date);
CREATE INDEX IF NOT EXISTS idx_shows_status ON shows(status);
CREATE INDEX IF NOT EXISTS idx_shows_created_at ON shows(created_at);
CREATE INDEX IF NOT EXISTS idx_shows_name ON shows(name);
CREATE INDEX IF NOT EXISTS idx_shows_location ON shows(location);

-- Composite indexes for coordinator dashboard
CREATE INDEX IF NOT EXISTS idx_shows_coordinator_status ON shows(coordinator_id, status);
CREATE INDEX IF NOT EXISTS idx_shows_coordinator_date ON shows(coordinator_id, start_date);
CREATE INDEX IF NOT EXISTS idx_shows_status_date ON shows(status, start_date);

-- ========================================
-- VEHICLES TABLE INDEXES
-- ========================================
CREATE INDEX IF NOT EXISTS idx_vehicles_owner_id ON vehicles(owner_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_make ON vehicles(make);
CREATE INDEX IF NOT EXISTS idx_vehicles_model ON vehicles(model);
CREATE INDEX IF NOT EXISTS idx_vehicles_year ON vehicles(year);
CREATE INDEX IF NOT EXISTS idx_vehicles_make_model ON vehicles(make, model);
CREATE INDEX IF NOT EXISTS idx_vehicles_owner_make ON vehicles(owner_id, make);

-- ========================================
-- USERS TABLE INDEXES
-- ========================================
CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- ========================================
-- PAYMENTS TABLE INDEXES (if exists)
-- ========================================
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_related_id ON payments(related_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_status ON payments(payment_status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);
CREATE INDEX IF NOT EXISTS idx_payments_amount ON payments(amount);
CREATE INDEX IF NOT EXISTS idx_payments_payment_type ON payments(payment_type);

-- Composite indexes for payment management
CREATE INDEX IF NOT EXISTS idx_payments_user_status ON payments(user_id, payment_status);
CREATE INDEX IF NOT EXISTS idx_payments_status_date ON payments(payment_status, created_at);
CREATE INDEX IF NOT EXISTS idx_payments_type_related ON payments(payment_type, related_id);

-- ========================================
-- SHOW CATEGORIES TABLE INDEXES
-- ========================================
CREATE INDEX IF NOT EXISTS idx_show_categories_show_id ON show_categories(show_id);
CREATE INDEX IF NOT EXISTS idx_show_categories_name ON show_categories(name);

-- ========================================
-- STAFF ASSIGNMENTS TABLE INDEXES (if exists)
-- ========================================
CREATE INDEX IF NOT EXISTS idx_staff_assignments_staff_id ON staff_assignments(staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_assignments_show_id ON staff_assignments(show_id);
CREATE INDEX IF NOT EXISTS idx_staff_assignments_assigned_by ON staff_assignments(assigned_by);

-- ========================================
-- JUDGE ASSIGNMENTS TABLE INDEXES (if exists)
-- ========================================
CREATE INDEX IF NOT EXISTS idx_judge_assignments_judge_id ON judge_assignments(judge_id);
CREATE INDEX IF NOT EXISTS idx_judge_assignments_show_id ON judge_assignments(show_id);
CREATE INDEX IF NOT EXISTS idx_judge_assignments_category_id ON judge_assignments(category_id);

-- ========================================
-- CALENDAR TABLES INDEXES (from previous optimization)
-- ========================================
CREATE INDEX IF NOT EXISTS idx_calendar_events_user_id ON calendar_events(user_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_start_date ON calendar_events(start_date);
CREATE INDEX IF NOT EXISTS idx_calendar_events_end_date ON calendar_events(end_date);
CREATE INDEX IF NOT EXISTS idx_calendar_events_status ON calendar_events(status);

CREATE INDEX IF NOT EXISTS idx_calendar_venues_name ON calendar_venues(name);
CREATE INDEX IF NOT EXISTS idx_calendar_venues_city ON calendar_venues(city);
CREATE INDEX IF NOT EXISTS idx_calendar_venues_state ON calendar_venues(state);

CREATE INDEX IF NOT EXISTS idx_calendar_clubs_name ON calendar_clubs(name);
CREATE INDEX IF NOT EXISTS idx_calendar_clubs_status ON calendar_clubs(status);

-- ========================================
-- ANALYZE TABLES TO UPDATE STATISTICS
-- ========================================
ANALYZE TABLE registrations;
ANALYZE TABLE shows;
ANALYZE TABLE vehicles;
ANALYZE TABLE users;
ANALYZE TABLE payments;
ANALYZE TABLE show_categories;
ANALYZE TABLE staff_assignments;
ANALYZE TABLE judge_assignments;
ANALYZE TABLE calendar_events;
ANALYZE TABLE calendar_venues;
ANALYZE TABLE calendar_clubs;
