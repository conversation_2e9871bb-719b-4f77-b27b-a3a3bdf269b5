-- Performance indexes for registration management optimization
-- Run this to optimize queries for thousands of registrations

-- Registrations table indexes
CREATE INDEX IF NOT EXISTS idx_registrations_show_id ON registrations(show_id);
CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_id ON registrations(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_registrations_category_id ON registrations(category_id);
CREATE INDEX IF NOT EXISTS idx_registrations_payment_status ON registrations(payment_status);
CREATE INDEX IF NOT EXISTS idx_registrations_status ON registrations(status);
CREATE INDEX IF NOT EXISTS idx_registrations_created_at ON registrations(created_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_show ON registrations(vehicle_id, show_id);
CREATE INDEX IF NOT EXISTS idx_registrations_payment_created ON registrations(payment_status, created_at);

-- Vehicles table indexes (for owner lookups)
CREATE INDEX IF NOT EXISTS idx_vehicles_owner_id ON vehicles(owner_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_make_model ON vehicles(make, model);

-- Shows table indexes (for date filtering)
CREATE INDEX IF NOT EXISTS idx_shows_start_date ON shows(start_date);
CREATE INDEX IF NOT EXISTS idx_shows_end_date ON shows(end_date);
CREATE INDEX IF NOT EXISTS idx_shows_status ON shows(status);

-- Show categories table indexes
CREATE INDEX IF NOT EXISTS idx_show_categories_show_id ON show_categories(show_id);

-- Users table indexes (for owner information)
CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- Composite indexes for complex registration queries
CREATE INDEX IF NOT EXISTS idx_reg_complex_user_date ON registrations(vehicle_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_reg_complex_show_payment ON registrations(show_id, payment_status);

-- Full-text search indexes (if supported)
-- Note: These may need to be adjusted based on your MySQL version
-- CREATE FULLTEXT INDEX IF NOT EXISTS idx_vehicles_search ON vehicles(make, model, year);
-- CREATE FULLTEXT INDEX IF NOT EXISTS idx_shows_search ON shows(name, location);

-- Analyze tables to update statistics
ANALYZE TABLE registrations;
ANALYZE TABLE vehicles;
ANALYZE TABLE shows;
ANALYZE TABLE show_categories;
ANALYZE TABLE users;
