<?php
/**
 * Performance Test for Admin Users Page
 * 
 * This script tests the performance of the optimized admin users page
 * with various scenarios including pagination, search, and filtering.
 */

// Include the application configuration
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once dirname(__FILE__, 3) . '/models/UserModel.php';

class UserPerformanceTest {
    private $userModel;
    
    public function __construct() {
        $this->userModel = new UserModel();
    }
    
    /**
     * Run comprehensive performance tests
     */
    public function runTests() {
        echo "<h1>Admin Users Page Performance Test</h1>\n";
        echo "<p>Testing the optimized pagination and search functionality...</p>\n";
        
        // Test scenarios
        $scenarios = [
            [
                'name' => 'Basic Pagination - Page 1',
                'params' => [1, 20, '', '', '', 'created_at', 'DESC']
            ],
            [
                'name' => 'Basic Pagination - Page 10',
                'params' => [10, 20, '', '', '', 'created_at', 'DESC']
            ],
            [
                'name' => 'Large Page Size (100 users)',
                'params' => [1, 100, '', '', '', 'created_at', 'DESC']
            ],
            [
                'name' => 'Search by Name',
                'params' => [1, 20, '<PERSON>', '', '', 'name', 'ASC']
            ],
            [
                'name' => 'Search by Email',
                'params' => [1, 20, 'gmail.com', '', '', 'email', 'ASC']
            ],
            [
                'name' => 'Filter by Role (Admin)',
                'params' => [1, 20, '', 'admin', '', 'created_at', 'DESC']
            ],
            [
                'name' => 'Filter by Status (Active)',
                'params' => [1, 20, '', '', 'active', 'created_at', 'DESC']
            ],
            [
                'name' => 'Combined Search + Filter',
                'params' => [1, 20, 'Smith', 'user', 'active', 'name', 'ASC']
            ],
            [
                'name' => 'Sort by Name ASC',
                'params' => [1, 50, '', '', '', 'name', 'ASC']
            ],
            [
                'name' => 'Sort by Last Login DESC',
                'params' => [1, 50, '', '', '', 'last_login', 'DESC']
            ]
        ];
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background-color: #f0f0f0;'>\n";
        echo "<th>Test Scenario</th>\n";
        echo "<th>Execution Time (ms)</th>\n";
        echo "<th>Users Found</th>\n";
        echo "<th>Page Info</th>\n";
        echo "<th>Status</th>\n";
        echo "</tr>\n";
        
        $totalTests = count($scenarios);
        $passedTests = 0;
        $totalTime = 0;
        
        foreach ($scenarios as $scenario) {
            $startTime = microtime(true);
            
            try {
                $result = $this->userModel->getPaginatedUsers(...$scenario['params']);
                
                $endTime = microtime(true);
                $executionTime = round(($endTime - $startTime) * 1000, 2);
                $totalTime += $executionTime;
                
                $status = $executionTime < 100 ? 'PASS' : 'SLOW';
                $statusColor = $executionTime < 100 ? 'green' : 'orange';
                
                if ($executionTime < 500) {
                    $passedTests++;
                }
                
                $pageInfo = "Page {$result['pagination']['current_page']} of {$result['pagination']['total_pages']}";
                
                echo "<tr>\n";
                echo "<td>{$scenario['name']}</td>\n";
                echo "<td style='text-align: right;'>{$executionTime}</td>\n";
                echo "<td style='text-align: right;'>{$result['pagination']['total_users']}</td>\n";
                echo "<td>{$pageInfo}</td>\n";
                echo "<td style='color: {$statusColor}; font-weight: bold;'>{$status}</td>\n";
                echo "</tr>\n";
                
            } catch (Exception $e) {
                echo "<tr>\n";
                echo "<td>{$scenario['name']}</td>\n";
                echo "<td colspan='3' style='color: red;'>ERROR: {$e->getMessage()}</td>\n";
                echo "<td style='color: red; font-weight: bold;'>FAIL</td>\n";
                echo "</tr>\n";
            }
        }
        
        echo "</table>\n";
        
        // Summary
        $avgTime = round($totalTime / $totalTests, 2);
        echo "<h2>Performance Summary</h2>\n";
        echo "<ul>\n";
        echo "<li><strong>Total Tests:</strong> {$totalTests}</li>\n";
        echo "<li><strong>Passed Tests:</strong> {$passedTests}</li>\n";
        echo "<li><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</li>\n";
        echo "<li><strong>Average Execution Time:</strong> {$avgTime}ms</li>\n";
        echo "<li><strong>Total Test Time:</strong> " . round($totalTime, 2) . "ms</li>\n";
        echo "</ul>\n";
        
        // Performance recommendations
        echo "<h2>Performance Analysis</h2>\n";
        if ($avgTime < 50) {
            echo "<p style='color: green;'><strong>EXCELLENT:</strong> Average query time is under 50ms. The optimizations are working very well!</p>\n";
        } elseif ($avgTime < 100) {
            echo "<p style='color: blue;'><strong>GOOD:</strong> Average query time is under 100ms. Performance is acceptable for most use cases.</p>\n";
        } elseif ($avgTime < 200) {
            echo "<p style='color: orange;'><strong>ACCEPTABLE:</strong> Average query time is under 200ms. Consider additional optimizations for very large datasets.</p>\n";
        } else {
            echo "<p style='color: red;'><strong>NEEDS IMPROVEMENT:</strong> Average query time exceeds 200ms. Additional database optimizations may be needed.</p>\n";
        }
        
        return [
            'total_tests' => $totalTests,
            'passed_tests' => $passedTests,
            'average_time' => $avgTime,
            'total_time' => $totalTime
        ];
    }
    
    /**
     * Test database indexes
     */
    public function testIndexes() {
        echo "<h2>Database Index Analysis</h2>\n";
        
        try {
            $db = new Database();
            
            // Check for indexes on users table
            $db->query("SHOW INDEX FROM users");
            $indexes = $db->resultSet();
            
            echo "<h3>Current Indexes on Users Table</h3>\n";
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
            echo "<tr style='background-color: #f0f0f0;'>\n";
            echo "<th>Index Name</th><th>Column</th><th>Unique</th><th>Type</th>\n";
            echo "</tr>\n";
            
            foreach ($indexes as $index) {
                $unique = $index->Non_unique == 0 ? 'Yes' : 'No';
                echo "<tr>\n";
                echo "<td>{$index->Key_name}</td>\n";
                echo "<td>{$index->Column_name}</td>\n";
                echo "<td>{$unique}</td>\n";
                echo "<td>{$index->Index_type}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
            
            // Check table statistics
            $db->query("SELECT COUNT(*) as total_users FROM users");
            $stats = $db->single();
            
            echo "<h3>Table Statistics</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Total Users:</strong> " . number_format($stats->total_users) . "</li>\n";
            echo "</ul>\n";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error analyzing indexes: {$e->getMessage()}</p>\n";
        }
    }
}

// Run the tests
if (php_sapi_name() !== 'cli') {
    echo "<!DOCTYPE html><html><head><title>User Performance Test</title></head><body>\n";
}

$tester = new UserPerformanceTest();
$tester->testIndexes();
$results = $tester->runTests();

if (php_sapi_name() !== 'cli') {
    echo "</body></html>\n";
}
?>
