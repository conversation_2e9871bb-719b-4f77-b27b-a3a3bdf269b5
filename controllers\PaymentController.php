<?php
/**
 * Payment Controller
 * 
 * This controller handles all payment-related functionality.
 */
class PaymentController extends Controller {
    private $paymentModel;
    private $showModel;
    private $registrationModel;
    private $userModel;
    private $settingsModel;
    private $emailService;
    private $auth;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->auth = new Auth();
        
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        $this->paymentModel = $this->model('PaymentModel');
        $this->showModel = $this->model('ShowModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->userModel = $this->model('UserModel');
        $this->settingsModel = $this->model('SettingsModel');
        $this->emailService = $this->model('EmailService');
        
        // Include the PaymentMethodsHelper
        require_once APPROOT . '/models/PaymentMethodsHelper.php';
    }
    

    
    /**
     * Admin payment management dashboard
     */
    public function admin() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }

        // Get payment counts and statistics only (for performance)
        $paymentCounts = $this->paymentModel->getPaymentCounts();
        $paymentStats = $this->paymentModel->getPaymentStats();

        $data = [
            'title' => 'Payment Management Dashboard',
            'payment_counts' => $paymentCounts,
            'payment_stats' => $paymentStats
        ];
        
        $this->view('admin/payments/index_optimized', $data);
    }

    /**
     * AJAX endpoint for loading paginated payments
     *
     * @return void
     */
    public function loadPayments() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            http_response_code(403);
            echo json_encode(['error' => 'Admin access required']);
            return;
        }

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $typeFilter = $_GET['type_filter'] ?? '';
        $orderBy = $_GET['order_by'] ?? 'created_at';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            $result = $this->paymentModel->getPaginatedPayments(
                $page, $perPage, $search, $statusFilter, $typeFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'payments' => $result['payments'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in PaymentController::loadPayments: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load payments']);
        }
    }
    
    /**
     * View payment details
     * 
     * @param int $id Payment ID
     */
    public function details($id) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment details
        $payment = $this->paymentModel->getPaymentDetailsById($id);
        
        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if coordinator can view this payment (only their own show's payments)
        if ($this->auth->hasRole('coordinator') && !$this->auth->hasRole('admin')) {
            // Check if this payment is related to a show they coordinate
            if ($payment->payment_type == 'show_listing') {
                $show = $this->showModel->getShowById($payment->related_id);
                if (!$show || $show->coordinator_id != $this->auth->getCurrentUserId()) {
                    $this->redirect('home/access_denied');
                    return;
                }
            } elseif ($payment->payment_type == 'registration') {
                $registration = $this->registrationModel->getRegistrationById($payment->related_id);
                if ($registration) {
                    $show = $this->showModel->getShowById($registration->show_id);
                    if (!$show || $show->coordinator_id != $this->auth->getCurrentUserId()) {
                        $this->redirect('home/access_denied');
                        return;
                    }
                }
            }
        }
        
        $data = [
            'title' => 'Payment Details',
            'payment' => $payment
        ];
        
        $this->view('admin/payments/details', $data);
    }
    
    /**
     * Process registration payment
     * 
     * @param int $registrationId Registration ID
     */
    public function registration($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get payment methods for this show
        $paymentMethods = $this->paymentModel->getShowPaymentMethods($show->id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $paymentMethodId = trim($_POST['payment_method_id']);
            
            // Get payment method
            $paymentMethod = $this->paymentModel->getPaymentMethodById($paymentMethodId);
            
            if (!$paymentMethod) {
                $this->setFlashMessage('payment_error', 'Invalid payment method', 'danger');
                $this->redirect('payment/registration/' . $registrationId);
                return;
            }
            
            // Check if this is a free show and free payment method
            if ($show->is_free && $paymentMethod->name == 'Free') {
                // Update registration payment status
                $paymentData = [
                    'id' => $registrationId,
                    'payment_status' => 'completed',
                    'fee' => 0.00,
                    'payment_method_id' => $paymentMethodId
                ];
                
                if ($this->registrationModel->updatePayment($paymentData)) {
                    // Create payment record
                    $paymentData = [
                        'user_id' => $this->auth->getCurrentUserId(),
                        'amount' => 0.00,
                        'payment_method_id' => $paymentMethodId,
                        'payment_status' => 'completed',
                        'payment_type' => 'registration',
                        'related_id' => $registrationId,
                        'notes' => 'Free registration for ' . $show->name
                    ];
                    
                    $this->paymentModel->createPayment($paymentData);
                    
                    $this->setFlashMessage('payment_success', 'Registration confirmed for free show', 'success');
                    $this->redirect('registration/view/' . $registrationId);
                    return;
                } else {
                    $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
                    $this->redirect('payment/registration/' . $registrationId);
                    return;
                }
            }
            
            // Handle different payment methods
            switch ($paymentMethod->name) {
                case 'PayPal':
                    // Redirect to PayPal payment page
                    $this->redirect('payment/paypal/' . $registrationId);
                    break;
                    
                case 'CashApp':
                    // Redirect to CashApp payment page
                    $this->redirect('payment/cashapp/' . $registrationId);
                    break;
                    
                case 'Venmo':
                    // Redirect to Venmo payment page
                    $this->redirect('payment/venmo/' . $registrationId);
                    break;
                    
                case 'Cash':
                    // Update registration with payment method
                    $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
                    
                    $paymentData = [
                        'id' => $registrationId,
                        'payment_status' => 'pending',
                        'fee' => $show->registration_fee,
                        'payment_method_id' => $paymentMethodId,
                        'payment_reference' => $paymentReference
                    ];
                    
                    if ($this->registrationModel->updatePayment($paymentData)) {
                        // Create payment record
                        $paymentData = [
                            'user_id' => $this->auth->getCurrentUserId(),
                            'amount' => $show->registration_fee,
                            'payment_method_id' => $paymentMethodId,
                            'payment_status' => 'pending',
                            'payment_reference' => $paymentReference,
                            'payment_type' => 'registration',
                            'related_id' => $registrationId,
                            'notes' => 'Cash payment for registration in ' . $show->name
                        ];
                        
                        $this->paymentModel->createPayment($paymentData);
                        
                        $this->setFlashMessage('payment_success', 'Cash payment recorded. Your registration will be confirmed once payment is verified.', 'success');
                        $this->redirect('registration/view/' . $registrationId);
                    } else {
                        $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
                        $this->redirect('payment/registration/' . $registrationId);
                    }
                    break;
                    
                default:
                    // For other payment methods
                    $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
                    
                    $paymentData = [
                        'id' => $registrationId,
                        'payment_status' => 'pending',
                        'fee' => $show->registration_fee,
                        'payment_method_id' => $paymentMethodId,
                        'payment_reference' => $paymentReference
                    ];
                    
                    if ($this->registrationModel->updatePayment($paymentData)) {
                        // Create payment record
                        $paymentData = [
                            'user_id' => $this->auth->getCurrentUserId(),
                            'amount' => $show->registration_fee,
                            'payment_method_id' => $paymentMethodId,
                            'payment_status' => 'pending',
                            'payment_reference' => $paymentReference,
                            'payment_type' => 'registration',
                            'related_id' => $registrationId,
                            'notes' => 'Registration payment for ' . $show->name
                        ];
                        
                        $this->paymentModel->createPayment($paymentData);
                        
                        $this->setFlashMessage('payment_success', 'Payment information submitted. Your registration is pending approval.', 'success');
                        $this->redirect('registration/view/' . $registrationId);
                    } else {
                        $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
                        $this->redirect('payment/registration/' . $registrationId);
                    }
                    break;
            }
        } else {
            // Display payment form
            $data = [
                'title' => 'Registration Payment',
                'registration' => $registration,
                'show' => $show,
                'payment_methods' => $paymentMethods,
                'cashapp_id' => $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_id', true, false),
                'venmo_id' => $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_id', true, false)
            ];
            
            $this->view('payments/registration', $data);
        }
    }
    
    /**
     * Process show listing payment
     * 
     * @param int $showId Show ID
     */
    public function showListing($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if listing fee is already paid
        if ($show->listing_paid) {
            $this->setFlashMessage('payment_info', 'Show listing fee has already been paid', 'info');
            $this->redirect('show/manage/' . $showId);
            return;
        }
        
        // Check if user is exempt from listing fees
        if ($this->paymentModel->isUserExemptFromListingFees($this->auth->getCurrentUserId())) {
            // Mark show as paid
            $this->showModel->updateShowListingPaymentStatus($showId, true);
            
            // Update show status from payment_pending to draft
            if ($show->status == 'payment_pending') {
                $this->showModel->updateShowStatus($showId, 'draft');
            }
            
            $this->setFlashMessage('payment_success', 'Show listing fee waived based on your account privileges', 'success');
            $this->redirect('show/manage/' . $showId);
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $paymentMethodId = trim($_POST['payment_method_id']);
            
            // Get payment method
            $paymentMethod = $this->paymentModel->getPaymentMethodById($paymentMethodId);
            
            if (!$paymentMethod) {
                $this->setFlashMessage('payment_error', 'Invalid payment method', 'danger');
                $this->redirect('payment/showListing/' . $showId);
                return;
            }
            
            // Handle different payment methods
            switch ($paymentMethod->name) {
                case 'PayPal':
                    // Redirect to PayPal payment page
                    $this->redirect('payment/paypalShowListing/' . $showId);
                    break;
                    
                case 'CashApp':
                    // Redirect to CashApp payment page
                    $this->redirect('payment/cashappShowListing/' . $showId);
                    break;
                    
                case 'Venmo':
                    // Redirect to Venmo payment page
                    $this->redirect('payment/venmoShowListing/' . $showId);
                    break;
                    
                default:
                    // Create payment record
                    $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
                    
                    $paymentData = [
                        'user_id' => $this->auth->getCurrentUserId(),
                        'amount' => $listingFee,
                        'payment_method_id' => $paymentMethodId,
                        'payment_status' => 'pending',
                        'payment_reference' => $paymentReference,
                        'payment_type' => 'show_listing',
                        'related_id' => $showId,
                        'notes' => 'Show listing fee for ' . $show->name
                    ];
                    
                    if ($this->paymentModel->createPayment($paymentData)) {
                        $this->setFlashMessage('payment_success', 'Payment information submitted. Your show listing is pending approval.', 'success');
                        $this->redirect('show/manage/' . $showId);
                    } else {
                        $this->setFlashMessage('payment_error', 'Failed to process payment', 'danger');
                        $this->redirect('payment/showListing/' . $showId);
                    }
                    break;
            }
        } else {
            // Display payment form
            $data = [
                'title' => 'Show Listing Payment',
                'show' => $show,
                'listing_fee' => $listingFee,
                'payment_methods' => $paymentMethods,
                'cashapp_id' => $this->paymentModel->getPaymentSetting('cashapp_id', true),
                'venmo_id' => $this->paymentModel->getPaymentSetting('venmo_id', true)
            ];
            
            $this->view('payments/show_listing', $data);
        }
    }
    
    /**
     * Process a listing fee payment with a code
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function useCode($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator of this show
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if listing is already paid
        if ($show->listing_paid) {
            $this->setFlashMessage('payment_info', 'This show listing has already been paid for.', 'info');
            $this->redirect('show/manage/' . $showId);
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'code' => trim($_POST['code']),
                'code_err' => ''
            ];
            
            // Validate code
            if (empty($data['code'])) {
                $data['code_err'] = 'Please enter a code';
            } else {
                // Check if code exists and is valid
                $this->db->query('SELECT * FROM listing_fee WHERE code = :code AND code_used = 0 AND (expiry_date IS NULL OR expiry_date >= CURDATE())');
                $this->db->bind(':code', $data['code']);
                $code = $this->db->single();
                
                if (!$code) {
                    $data['code_err'] = 'Invalid or expired code';
                }
            }
            
            // Check for errors
            if (empty($data['code_err'])) {
                // Mark code as used
                $this->db->query('UPDATE listing_fee SET code_used = 1 WHERE id = :id');
                $this->db->bind(':id', $code->id);
                $this->db->execute();
                
                // Update show listing_paid status
                $this->showModel->updateShowListingPaymentStatus($showId, true);
                
                // Update show status to draft if it was payment_pending
                if ($show->status == 'payment_pending') {
                    $this->showModel->updateShowStatus($showId, 'draft');
                }
                
                // Create payment record
                $paymentData = [
                    'user_id' => $this->auth->getCurrentUserId(),
                    'amount' => 0, // Free with code
                    'payment_method_id' => 1, // Assuming 1 is a default payment method
                    'payment_status' => 'completed',
                    'payment_reference' => 'Code: ' . $data['code'],
                    'payment_type' => 'show_listing',
                    'related_id' => $showId,
                    'notes' => 'Show listing fee waived with code: ' . $data['code']
                ];
                
                $this->paymentModel->createPayment($paymentData);
                
                $this->setFlashMessage('payment_success', 'Code accepted! Your show has been published.', 'success');
                
                // Send email notification to admins
                $coordinator = $this->userModel->getUserById($this->auth->getCurrentUserId());
                $updatedShow = $this->showModel->getShowById($showId);
                $this->emailService->sendNewShowNotification($updatedShow, $coordinator);
                
                $this->redirect('show/manage/' . $showId);
            } else {
                // Load view with errors
                $viewData = [
                    'title' => 'Use Listing Fee Code',
                    'show' => $show,
                    'code' => $data['code'],
                    'code_err' => $data['code_err']
                ];
                
                $this->view('payments/use_code', $viewData);
            }
        } else {
            // Init data
            $viewData = [
                'title' => 'Use Listing Fee Code',
                'show' => $show,
                'code' => '',
                'code_err' => ''
            ];
            
            $this->view('payments/use_code', $viewData);
        }
    }
    
    /**
     * PayPal payment for registration
     * 
     * @param int $registrationId Registration ID
     */
    public function paypal($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get PayPal settings for coordinator
        $paypalClientId = $this->paymentModel->getShowPaymentSetting($show->id, 'paypal_client_id', true, false);
        $paypalSandbox = $this->paymentModel->getShowPaymentSetting($show->id, 'paypal_sandbox', true, false) === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        $data = [
            'title' => 'PayPal Payment',
            'registration' => $registration,
            'show' => $show,
            'paypal_client_id' => $paypalClientId,
            'paypal_sandbox' => $paypalSandbox,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/paypalSuccess/' . $registrationId,
            'cancel_url' => BASE_URL . '/payment/paypalCancel/' . $registrationId
        ];
        
        $this->view('payments/paypal', $data);
    }
    
    /**
     * PayPal payment for show listing
     * 
     * @param int $showId Show ID
     */
    public function paypalShowListing($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get PayPal settings for coordinator
        $paypalClientId = $this->paymentModel->getShowPaymentSetting($show->id, 'paypal_client_id', true, false);
        $paypalSandbox = $this->paymentModel->getShowPaymentSetting($show->id, 'paypal_sandbox', true, false) === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        $data = [
            'title' => 'PayPal Payment',
            'show' => $show,
            'listing_fee' => $listingFee,
            'paypal_client_id' => $paypalClientId,
            'paypal_sandbox' => $paypalSandbox,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/paypalShowSuccess/' . $showId,
            'cancel_url' => BASE_URL . '/payment/paypalShowCancel/' . $showId
        ];
        
        $this->view('payments/paypal_show', $data);
    }
    
    /**
     * PayPal success callback for registration
     * 
     * @param int $registrationId Registration ID
     */
    public function paypalSuccess($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        // Verify payment with PayPal API (simplified for now)
        $paymentVerified = true;
        
        if ($paymentVerified) {
            // Update registration payment status
            $paymentData = [
                'id' => $registrationId,
                'payment_status' => 'completed',
                'fee' => $show->registration_fee,
                'payment_method_id' => $paymentMethod->id,
                'payment_reference' => 'PayPal Transaction ID: ' . (isset($_GET['paymentId']) ? $_GET['paymentId'] : 'Unknown')
            ];
            
            if ($this->registrationModel->updatePayment($paymentData)) {
                // Create payment record
                $paymentData = [
                    'user_id' => $this->auth->getCurrentUserId(),
                    'amount' => $show->registration_fee,
                    'payment_method_id' => $paymentMethod->id,
                    'payment_status' => 'completed',
                    'payment_reference' => 'PayPal Transaction ID: ' . (isset($_GET['paymentId']) ? $_GET['paymentId'] : 'Unknown'),
                    'payment_type' => 'registration',
                    'related_id' => $registrationId,
                    'notes' => 'Registration payment for ' . $show->name
                ];
                
                $this->paymentModel->createPayment($paymentData);
                
                $this->setFlashMessage('payment_success', 'Payment completed successfully. Your registration is confirmed.', 'success');
                $this->redirect('registration/view/' . $registrationId);
            } else {
                $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
                $this->redirect('payment/registration/' . $registrationId);
            }
        } else {
            $this->setFlashMessage('payment_error', 'Payment verification failed', 'danger');
            $this->redirect('payment/registration/' . $registrationId);
        }
    }
    
    /**
     * PayPal cancel callback for registration
     * 
     * @param int $registrationId Registration ID
     */
    public function paypalCancel($registrationId) {
        $this->setFlashMessage('payment_info', 'Payment was cancelled', 'info');
        $this->redirect('payment/registration/' . $registrationId);
    }
    
    /**
     * PayPal success callback for show listing
     * 
     * @param int $showId Show ID
     */
    public function paypalShowSuccess($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Verify payment with PayPal API (simplified for now)
        $paymentVerified = true;
        
        if ($paymentVerified) {
            // Mark show as paid
            if ($this->showModel->updateShowListingPaymentStatus($showId, true)) {
                // Update show status from payment_pending to draft
                if ($show->status == 'payment_pending') {
                    $this->showModel->updateShowStatus($showId, 'draft');
                }
                
                // Create payment record
                $paymentData = [
                    'user_id' => $this->auth->getCurrentUserId(),
                    'amount' => $listingFee,
                    'payment_method_id' => $paymentMethod->id,
                    'payment_status' => 'completed',
                    'payment_reference' => 'PayPal Transaction ID: ' . (isset($_GET['paymentId']) ? $_GET['paymentId'] : 'Unknown'),
                    'payment_type' => 'show_listing',
                    'related_id' => $showId,
                    'notes' => 'Show listing fee for ' . $show->name
                ];
                
                $this->paymentModel->createPayment($paymentData);
                
                $this->setFlashMessage('payment_success', 'Payment completed successfully. Your show listing is confirmed.', 'success');
                $this->redirect('show/manage/' . $showId);
            } else {
                $this->setFlashMessage('payment_error', 'Failed to update show listing status', 'danger');
                $this->redirect('payment/showListing/' . $showId);
            }
        } else {
            $this->setFlashMessage('payment_error', 'Payment verification failed', 'danger');
            $this->redirect('payment/showListing/' . $showId);
        }
    }
    
    /**
     * PayPal cancel callback for show listing
     * 
     * @param int $showId Show ID
     */
    public function paypalShowCancel($showId) {
        $this->setFlashMessage('payment_info', 'Payment was cancelled', 'info');
        $this->redirect('payment/showListing/' . $showId);
    }
    
    /**
     * Venmo payment page
     * 
     * @param int $registrationId Registration ID
     */
    public function venmo($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get Venmo ID and API settings
        $venmoId = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_id');
        $clientId = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_api_client_id', true);
        $clientSecret = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_api_client_secret', true);
        $isSandbox = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_api_sandbox', true) === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('Venmo');
        
        // Check if API integration is enabled
        $useApi = $paymentMethod && $paymentMethod->api_integration && !empty($clientId) && !empty($clientSecret);
        
        // Include the VenmoAPI class
        require_once APPROOT . '/core/payment/VenmoAPI.php';
        
        // Initialize the API
        $venmoApi = new VenmoAPI($clientId, $clientSecret, $isSandbox, defined('DEBUG_MODE') && DEBUG_MODE);
        
        // Generate payment URLs
        $paymentNote = 'Registration #' . $registration->id . ' for ' . $show->name;
        $venmoAppUrl = $venmoApi->generatePaymentUrl($venmoId, $registration->fee, $paymentNote);
        $venmoWebUrl = $venmoApi->generateWebPaymentUrl($venmoId, $registration->fee, $paymentNote);
        
        $data = [
            'title' => 'Venmo Payment',
            'registration' => $registration,
            'show' => $show,
            'venmo_id' => $venmoId,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/venmoSuccess/' . $registrationId,
            'use_api' => $useApi,
            'venmo_app_url' => $venmoAppUrl,
            'venmo_web_url' => $venmoWebUrl
        ];
        
        // If API integration is enabled, create a payment request
        if ($useApi) {
            // Create a payment request
            $paymentRequest = $venmoApi->createPaymentRequest(
                $registration->fee,
                $paymentNote,
                $venmoId,
                BASE_URL . '/payment/venmoSuccess/' . $registrationId,
                BASE_URL . '/payment/venmoCancel/' . $registrationId
            );
            
            if ($paymentRequest) {
                // Store payment request ID in session
                $_SESSION['venmo_payment_request_id'] = $paymentRequest['id'];
                
                // Add payment button to data
                $data['payment_button'] = $venmoApi->generatePayButton($paymentRequest['payment_url']);
                $data['payment_request'] = $paymentRequest;
            } else {
                // API error
                $data['api_error'] = 'Failed to create Venmo payment request. Please try again or use a different payment method.';
                $data['use_api'] = false;
            }
        }
        
        $this->view('payments/venmo', $data);
    }
    
    /**
     * Venmo payment page for show listing
     * 
     * @param int $showId Show ID
     */
    public function venmoShowListing($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get Venmo ID and API settings
        $venmoId = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_id');
        $clientId = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_api_client_id', true);
        $clientSecret = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_api_client_secret', true);
        $isSandbox = $this->paymentModel->getShowPaymentSetting($show->id, 'venmo_api_sandbox', true) === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('Venmo');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Check if API integration is enabled
        $useApi = $paymentMethod && $paymentMethod->api_integration && !empty($clientId) && !empty($clientSecret);
        
        // Include the VenmoAPI class
        require_once APPROOT . '/core/payment/VenmoAPI.php';
        
        // Initialize the API
        $venmoApi = new VenmoAPI($clientId, $clientSecret, $isSandbox, defined('DEBUG_MODE') && DEBUG_MODE);
        
        // Generate payment URLs
        $paymentNote = 'Listing fee for ' . $show->name . ' (Show #' . $show->id . ')';
        $venmoAppUrl = $venmoApi->generatePaymentUrl($venmoId, $listingFee, $paymentNote);
        $venmoWebUrl = $venmoApi->generateWebPaymentUrl($venmoId, $listingFee, $paymentNote);
        
        $data = [
            'title' => 'Venmo Payment',
            'show' => $show,
            'listing_fee' => $listingFee,
            'venmo_id' => $venmoId,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/venmoShowSuccess/' . $showId,
            'use_api' => $useApi,
            'venmo_app_url' => $venmoAppUrl,
            'venmo_web_url' => $venmoWebUrl
        ];
        
        // If API integration is enabled, create a payment request
        if ($useApi) {
            // Create a payment request
            $paymentRequest = $venmoApi->createPaymentRequest(
                $listingFee,
                $paymentNote,
                $venmoId,
                BASE_URL . '/payment/venmoShowSuccess/' . $showId,
                BASE_URL . '/payment/venmoShowCancel/' . $showId
            );
            
            if ($paymentRequest) {
                // Store payment request ID in session
                $_SESSION['venmo_show_payment_request_id'] = $paymentRequest['id'];
                
                // Add payment button to data
                $data['payment_button'] = $venmoApi->generatePayButton($paymentRequest['payment_url']);
                $data['payment_request'] = $paymentRequest;
            } else {
                // API error
                $data['api_error'] = 'Failed to create Venmo payment request. Please try again or use a different payment method.';
                $data['use_api'] = false;
            }
        }
        
        $this->view('payments/venmo_show', $data);
    }
    
    /**
     * CashApp payment page
     * 
     * @param int $registrationId Registration ID
     */
    public function cashapp($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get CashApp ID and API settings
        $cashappId = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_id');
        $clientId = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_api_client_id', true);
        $clientSecret = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_api_client_secret', true);
        $isSandbox = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_api_sandbox', true) === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('CashApp');
        
        // Check if API integration is enabled
        $useApi = $paymentMethod && $paymentMethod->api_integration && !empty($clientId) && !empty($clientSecret);
        
        $data = [
            'title' => 'Cash App Payment',
            'registration' => $registration,
            'show' => $show,
            'cashapp_id' => $cashappId,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/cashappSuccess/' . $registrationId,
            'use_api' => $useApi
        ];
        
        // If API integration is enabled, create a payment request
        if ($useApi) {
            // Include the CashAppPayAPI class
            require_once APPROOT . '/core/payment/CashAppPayAPI.php';
            
            // Initialize the API
            $cashAppApi = new CashAppPayAPI($clientId, $clientSecret, $isSandbox, defined('DEBUG_MODE') && DEBUG_MODE);
            
            // Create a payment request
            $paymentRequest = $cashAppApi->createPaymentRequest(
                $registration->fee,
                'USD',
                'Registration fee for ' . $show->name,
                'reg_' . $registration->id,
                BASE_URL . '/payment/cashappSuccess/' . $registrationId,
                BASE_URL . '/payment/cashappCancel/' . $registrationId
            );
            
            if ($paymentRequest) {
                // Store payment request ID in session
                $_SESSION['cashapp_payment_request_id'] = $paymentRequest['id'];
                
                // Add payment button to data
                $data['payment_button'] = $cashAppApi->generatePayButton($paymentRequest['id']);
                $data['payment_request'] = $paymentRequest;
            } else {
                // API error
                $data['api_error'] = 'Failed to create Cash App payment request. Please try again or use a different payment method.';
                $data['use_api'] = false;
            }
        }
        
        $this->view('payments/cashapp', $data);
    }
    
    /**
     * CashApp payment page for show listing
     * 
     * @param int $showId Show ID
     */
    public function cashappShowListing($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get CashApp ID and API settings
        $cashappId = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_id');
        $clientId = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_api_client_id', true);
        $clientSecret = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_api_client_secret', true);
        $isSandbox = $this->paymentModel->getShowPaymentSetting($show->id, 'cashapp_api_sandbox', true) === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('CashApp');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Check if API integration is enabled
        $useApi = $paymentMethod && $paymentMethod->api_integration && !empty($clientId) && !empty($clientSecret);
        
        $data = [
            'title' => 'Cash App Payment',
            'show' => $show,
            'listing_fee' => $listingFee,
            'cashapp_id' => $cashappId,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/cashappShowSuccess/' . $showId,
            'use_api' => $useApi
        ];
        
        // If API integration is enabled, create a payment request
        if ($useApi) {
            // Include the CashAppPayAPI class
            require_once APPROOT . '/core/payment/CashAppPayAPI.php';
            
            // Initialize the API
            $cashAppApi = new CashAppPayAPI($clientId, $clientSecret, $isSandbox, defined('DEBUG_MODE') && DEBUG_MODE);
            
            // Create a payment request
            $paymentRequest = $cashAppApi->createPaymentRequest(
                $listingFee,
                'USD',
                'Listing fee for ' . $show->name,
                'show_' . $show->id,
                BASE_URL . '/payment/cashappShowSuccess/' . $showId,
                BASE_URL . '/payment/cashappShowCancel/' . $showId
            );
            
            if ($paymentRequest) {
                // Store payment request ID in session
                $_SESSION['cashapp_show_payment_request_id'] = $paymentRequest['id'];
                
                // Add payment button to data
                $data['payment_button'] = $cashAppApi->generatePayButton($paymentRequest['id']);
                $data['payment_request'] = $paymentRequest;
            } else {
                // API error
                $data['api_error'] = 'Failed to create Cash App payment request. Please try again or use a different payment method.';
                $data['use_api'] = false;
            }
        }
        
        $this->view('payments/cashapp_show', $data);
    }
    
    /**
     * Venmo success callback
     * 
     * @param int $registrationId Registration ID
     */
    public function venmoSuccess($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('Venmo');
        
        // Update registration with payment method
        $paymentData = [
            'id' => $registrationId,
            'payment_status' => 'pending',
            'fee' => $show->registration_fee,
            'payment_method_id' => $paymentMethod->id
        ];
        
        if ($this->registrationModel->updatePayment($paymentData)) {
            // Create payment record
            $paymentData = [
                'user_id' => $this->auth->getCurrentUserId(),
                'amount' => $show->registration_fee,
                'payment_method_id' => $paymentMethod->id,
                'payment_status' => 'pending',
                'payment_type' => 'registration',
                'related_id' => $registrationId,
                'notes' => 'Venmo payment for registration in ' . $show->name
            ];
            
            $this->paymentModel->createPayment($paymentData);
            
            $this->setFlashMessage('payment_success', 'Your Venmo payment is being processed. Your registration will be confirmed once payment is verified.', 'success');
            $this->redirect('registration/view/' . $registrationId);
        } else {
            $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
            $this->redirect('payment/registration/' . $registrationId);
        }
    }
    
    /**
     * Venmo success callback for show listing
     * 
     * @param int $showId Show ID
     */
    public function venmoShowSuccess($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('Venmo');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Create payment record
        $paymentData = [
            'user_id' => $this->auth->getCurrentUserId(),
            'amount' => $listingFee,
            'payment_method_id' => $paymentMethod->id,
            'payment_status' => 'pending',
            'payment_type' => 'show_listing',
            'related_id' => $showId,
            'notes' => 'Venmo payment for show listing: ' . $show->name
        ];
        
        if ($this->paymentModel->createPayment($paymentData)) {
            $this->setFlashMessage('payment_success', 'Your Venmo payment is being processed. Your show listing will be confirmed once payment is verified.', 'success');
            $this->redirect('show/manage/' . $showId);
        } else {
            $this->setFlashMessage('payment_error', 'Failed to process payment', 'danger');
            $this->redirect('payment/showListing/' . $showId);
        }
    }
    
    /**
     * CashApp success callback
     * 
     * @param int $registrationId Registration ID
     */
    public function cashappSuccess($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('CashApp');
        
        // Update registration with payment method
        $paymentData = [
            'id' => $registrationId,
            'payment_status' => 'pending',
            'fee' => $show->registration_fee,
            'payment_method_id' => $paymentMethod->id
        ];
        
        if ($this->registrationModel->updatePayment($paymentData)) {
            // Create payment record
            $paymentData = [
                'user_id' => $this->auth->getCurrentUserId(),
                'amount' => $show->registration_fee,
                'payment_method_id' => $paymentMethod->id,
                'payment_status' => 'pending',
                'payment_type' => 'registration',
                'related_id' => $registrationId,
                'notes' => 'Cash App payment for registration in ' . $show->name
            ];
            
            $this->paymentModel->createPayment($paymentData);
            
            $this->setFlashMessage('payment_success', 'Your Cash App payment is being processed. Your registration will be confirmed once payment is verified.', 'success');
            $this->redirect('registration/view/' . $registrationId);
        } else {
            $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
            $this->redirect('payment/registration/' . $registrationId);
        }
    }
    
    /**
     * CashApp success callback for show listing
     * 
     * @param int $showId Show ID
     */
    public function cashappShowSuccess($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('CashApp');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Create payment record
        $paymentData = [
            'user_id' => $this->auth->getCurrentUserId(),
            'amount' => $listingFee,
            'payment_method_id' => $paymentMethod->id,
            'payment_status' => 'pending',
            'payment_type' => 'show_listing',
            'related_id' => $showId,
            'notes' => 'Cash App payment for show listing: ' . $show->name
        ];
        
        if ($this->paymentModel->createPayment($paymentData)) {
            $this->setFlashMessage('payment_success', 'Your Cash App payment is being processed. Your show listing will be confirmed once payment is verified.', 'success');
            $this->redirect('show/manage/' . $showId);
        } else {
            $this->setFlashMessage('payment_error', 'Failed to process payment', 'danger');
            $this->redirect('payment/showListing/' . $showId);
        }
    }
    
    /**
     * Coordinator default payment settings
     */
    public function coordinatorSettings() {
        // Check if user is coordinator or admin
        if (!$this->auth->hasRole(['coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $isAdmin = $this->auth->hasRole('admin');
        
        // If user is a coordinator, redirect to their specific settings
        if (!$isAdmin) {
            $this->redirect('payment/myPaymentSettings');
            return;
        }
        
        // Debug mode - only visible to admins
        $debug = isset($_GET['debug']) && $_GET['debug'] == '1' && $isAdmin;
        
        if ($debug) {
            echo "<h2>Debug Information</h2>";
            echo "<p>User ID: {$userId}</p>";
            echo "<p>Is Admin: " . ($isAdmin ? 'Yes' : 'No') . "</p>";
        }
        
        // Get the default coordinator settings
        if ($debug) echo "<p>Getting default coordinator settings (is_admin = 0)...</p>";
        
        // Get the settings directly from the database to bypass any potential caching
        $db = new Database();
        $db->query('SELECT * FROM payment_settings WHERE is_admin = :is_admin ORDER BY setting_key');
        $db->bind(':is_admin', 0); // 0 for coordinator settings
        $results = $db->resultSet();
        
        $paymentSettings = [];
        foreach ($results as $result) {
            $paymentSettings[$result->setting_key] = $result->setting_value;
            
            if ($debug) {
                $value = $result->setting_key == 'paypal_secret' ? '********' : $result->setting_value;
                echo "<p>Setting: {$result->setting_key} = {$value}</p>";
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            if ($debug) {
                echo "<h3>POST Data</h3>";
                echo "<pre>" . print_r($_POST, true) . "</pre>";
            }
            
            // Update coordinator payment settings
            $coordinatorSettings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id'
            ];
            
            foreach ($coordinatorSettings as $setting) {
                if (isset($_POST[$setting])) {
                    if ($debug) echo "<p>Updating setting: {$setting}</p>";
                    $this->paymentModel->updatePaymentSetting($setting, $_POST[$setting], false);
                }
            }
            
            $this->setFlashMessage('settings_success', 'Default coordinator payment settings have been updated successfully', 'success');
            $this->redirect('payment/coordinatorSettings');
        } else {
            // Display settings form
            $data = [
                'title' => 'Default Coordinator Payment Settings',
                'payment_settings' => $paymentSettings,
                'is_admin_view' => true,
                'debug_mode' => $debug
            ];
            
            if ($debug) {
                echo "<h3>Data being passed to view</h3>";
                echo "<pre>" . print_r($data, true) . "</pre>";
                echo "<hr>";
            }
            
            $this->view('coordinator/default_payment_settings', $data);
        }
    }
    
    /**
     * Coordinator's personal payment settings
     */
    public function myPaymentSettings() {
        // Check if user is coordinator
        if (!$this->auth->hasRole('coordinator')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        
        // Debug mode
        $debug = isset($_GET['debug']) && $_GET['debug'] == '1';
        
        if ($debug) {
            echo "<h2>Debug Information</h2>";
            echo "<p>User ID: {$userId}</p>";
        }
        
        // Get this coordinator's payment settings
        if ($debug) echo "<p>Getting payment settings for coordinator ID: {$userId}...</p>";
        
        $paymentSettings = $this->paymentModel->getCoordinatorPaymentSettings($userId);
        
        if ($debug) {
            echo "<h3>Payment Settings</h3>";
            echo "<pre>";
            foreach ($paymentSettings as $key => $value) {
                $displayValue = $key == 'paypal_secret' ? '********' : $value;
                echo "{$key} = {$displayValue}\n";
            }
            echo "</pre>";
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            if ($debug) {
                echo "<h3>POST Data</h3>";
                echo "<pre>" . print_r($_POST, true) . "</pre>";
            }
            
            // Update coordinator payment settings
            $coordinatorSettings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id'
            ];
            
            foreach ($coordinatorSettings as $setting) {
                if (isset($_POST[$setting])) {
                    if ($debug) echo "<p>Updating setting: {$setting}</p>";
                    $this->paymentModel->updateCoordinatorPaymentSetting($setting, $_POST[$setting], $userId);
                }
            }
            
            $this->setFlashMessage('settings_success', 'Your payment settings have been updated successfully', 'success');
            $this->redirect('payment/myPaymentSettings');
        } else {
            // Display settings form
            $data = [
                'title' => 'My Payment Settings',
                'payment_settings' => $paymentSettings,
                'is_admin_view' => false,
                'debug_mode' => $debug
            ];
            
            if ($debug) {
                echo "<h3>Data being passed to view</h3>";
                echo "<pre>" . print_r($data, true) . "</pre>";
                echo "<hr>";
            }
            
            $this->view('coordinator/my_payment_settings', $data);
        }
    }
    
    /**
     * Admin view of a specific coordinator's payment settings
     * 
     * @param int $coordinatorId Coordinator user ID
     */
    public function viewCoordinatorSettings($coordinatorId) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get coordinator info
        $coordinator = $this->userModel->getUserById($coordinatorId);
        if (!$coordinator || !$this->userModel->hasRole($coordinatorId, 'coordinator')) {
            $this->setFlashMessage('error', 'Invalid coordinator ID', 'danger');
            $this->redirect('user/coordinators');
            return;
        }
        
        // Debug mode
        $debug = isset($_GET['debug']) && $_GET['debug'] == '1';
        
        if ($debug) {
            echo "<h2>Debug Information</h2>";
            echo "<p>Coordinator ID: {$coordinatorId}</p>";
            echo "<p>Coordinator: {$coordinator->username}</p>";
        }
        
        // Get this coordinator's payment settings
        if ($debug) echo "<p>Getting payment settings for coordinator ID: {$coordinatorId}...</p>";
        
        $paymentSettings = $this->paymentModel->getCoordinatorPaymentSettings($coordinatorId);
        
        if ($debug) {
            echo "<h3>Payment Settings</h3>";
            echo "<pre>";
            foreach ($paymentSettings as $key => $value) {
                $displayValue = $key == 'paypal_secret' ? '********' : $value;
                echo "{$key} = {$displayValue}\n";
            }
            echo "</pre>";
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            if ($debug) {
                echo "<h3>POST Data</h3>";
                echo "<pre>" . print_r($_POST, true) . "</pre>";
            }
            
            // Update coordinator payment settings
            $coordinatorSettings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id'
            ];
            
            foreach ($coordinatorSettings as $setting) {
                if (isset($_POST[$setting])) {
                    if ($debug) echo "<p>Updating setting: {$setting}</p>";
                    $this->paymentModel->updateCoordinatorPaymentSetting($setting, $_POST[$setting], $coordinatorId);
                }
            }
            
            $this->setFlashMessage('settings_success', "Payment settings for {$coordinator->username} have been updated successfully", 'success');
            $this->redirect('payment/viewCoordinatorSettings/' . $coordinatorId);
        } else {
            // Display settings form
            $data = [
                'title' => "Payment Settings for {$coordinator->username}",
                'payment_settings' => $paymentSettings,
                'is_admin_view' => true,
                'coordinator' => $coordinator,
                'debug_mode' => $debug
            ];
            
            if ($debug) {
                echo "<h3>Data being passed to view</h3>";
                echo "<pre>" . print_r($data, true) . "</pre>";
                echo "<hr>";
            }
            
            $this->view('coordinator/coordinator_payment_settings', $data);
        }
    }
    
    /**
     * Admin payment settings
     */
    public function settings() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods(false);
        
        // Get admin payment settings
        $paymentSettings = $this->paymentModel->getAllPaymentSettings(true);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Update admin payment settings
            $adminSettings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id',
                'currency',
                'show_listing_fee_enabled',
                'default_registration_fee',
                'default_show_listing_fee'
            ];
            
            foreach ($adminSettings as $setting) {
                if (isset($_POST[$setting])) {
                    $this->paymentModel->updatePaymentSetting($setting, $_POST[$setting], true);
                }
            }
            
            // Also update the currency setting for coordinators to keep them in sync
            if (isset($_POST['currency'])) {
                $this->paymentModel->updatePaymentSetting('currency', $_POST['currency'], false);
            }
            
            $this->setFlashMessage('settings_success', 'Admin payment settings updated successfully', 'success');
            $this->redirect('payment/settings');
        } else {
            // Display settings form
            $data = [
                'title' => 'Admin Payment Settings',
                'payment_methods' => $paymentMethods,
                'payment_settings' => $paymentSettings
            ];
            
            $this->view('admin/payments/settings', $data);
        }
    }
    
    /**
     * Show payment settings
     * 
     * @param int $showId Show ID
     */
    public function showSettings($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get all payment methods
        $allPaymentMethods = $this->paymentModel->getPaymentMethods(false);
        
        // Get show payment methods
        $showPaymentMethods = $this->paymentModel->getShowPaymentMethods($showId, false);
        
        // Get show payment settings for coordinator
        $showPaymentSettings = $this->paymentModel->getAllShowPaymentSettings($showId, true, false);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Update show payment methods
            $selectedMethods = isset($_POST['payment_methods']) ? $_POST['payment_methods'] : [];
            $this->paymentModel->setShowPaymentMethods($showId, $selectedMethods);
            
            // Update show payment settings for coordinator
            $settings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id'
            ];
            
            foreach ($settings as $setting) {
                if (isset($_POST[$setting])) {
                    $this->paymentModel->updateShowPaymentSetting($showId, $setting, $_POST[$setting], false);
                }
            }
            
            // If user wants to save these as default coordinator settings
            if (isset($_POST['use_as_default']) && $_POST['use_as_default'] == '1') {
                $userId = $this->auth->getCurrentUserId();
                
                // Update global coordinator settings
                foreach ($settings as $setting) {
                    if (isset($_POST[$setting])) {
                        $this->paymentModel->updatePaymentSetting($setting, $_POST[$setting], false);
                    }
                }
                
                $this->setFlashMessage('settings_success', 'Show payment settings updated and saved as your default settings', 'success');
            } else {
                $this->setFlashMessage('settings_success', 'Show payment settings updated successfully', 'success');
            }
            
            $this->redirect('payment/showSettings/' . $showId);
        } else {
            // Display settings form
            $data = [
                'title' => 'Show Payment Settings',
                'show' => $show,
                'all_payment_methods' => $allPaymentMethods,
                'show_payment_methods' => $showPaymentMethods,
                'payment_settings' => $showPaymentSettings
            ];
            
            $this->view('coordinator/payment_settings', $data);
        }
    }
    
    /**
     * Admin payment methods
     */
    public function methods() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods(false);
        
        $data = [
            'title' => 'Payment Methods',
            'payment_methods' => $paymentMethods
        ];
        
        $this->view('admin/payments/methods', $data);
    }
    
    /**
     * Admin add/edit payment method
     * 
     * @param int $id Payment method ID (optional)
     */
    public function editMethod($id = null) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment method if editing
        $paymentMethod = null;
        if ($id) {
            $paymentMethod = $this->paymentModel->getPaymentMethodById($id);
            
            if (!$paymentMethod) {
                $this->redirect('home/not_found');
                return;
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'instructions' => trim($_POST['instructions']),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'requires_approval' => isset($_POST['requires_approval']) ? 1 : 0,
                'is_online' => isset($_POST['is_online']) ? 1 : 0
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($data['name'])) {
                $errors['name'] = 'Name is required';
            }
            
            if (empty($errors)) {
                if ($id) {
                    // Update payment method
                    $data['id'] = $id;
                    
                    if ($this->paymentModel->updatePaymentMethod($data)) {
                        $this->setFlashMessage('method_success', 'Payment method updated successfully', 'success');
                        $this->redirect('payment/methods');
                    } else {
                        $this->setFlashMessage('method_error', 'Failed to update payment method', 'danger');
                    }
                } else {
                    // Create payment method
                    if ($this->paymentModel->createPaymentMethod($data)) {
                        $this->setFlashMessage('method_success', 'Payment method created successfully', 'success');
                        $this->redirect('payment/methods');
                    } else {
                        $this->setFlashMessage('method_error', 'Failed to create payment method', 'danger');
                    }
                }
            }
            
            // If we get here, there were errors
            $data['errors'] = $errors;
            $data['title'] = $id ? 'Edit Payment Method' : 'Add Payment Method';
            $data['payment_method'] = $paymentMethod;
            
            $this->view('admin/payments/edit_method', $data);
        } else {
            // Display form
            $data = [
                'title' => $id ? 'Edit Payment Method' : 'Add Payment Method',
                'payment_method' => $paymentMethod,
                'errors' => []
            ];
            
            $this->view('admin/payments/edit_method', $data);
        }
    }
    
    /**
     * Admin delete payment method
     * 
     * @param int $id Payment method ID
     */
    public function deleteMethod($id) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodById($id);
        
        if (!$paymentMethod) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete payment method
            if ($this->paymentModel->deletePaymentMethod($id)) {
                $this->setFlashMessage('method_success', 'Payment method deleted successfully', 'success');
            } else {
                $this->setFlashMessage('method_error', 'Failed to delete payment method', 'danger');
            }
            
            $this->redirect('payment/methods');
        } else {
            // Display confirmation form
            $data = [
                'title' => 'Delete Payment Method',
                'payment_method' => $paymentMethod
            ];
            
            $this->view('admin/payments/delete_method', $data);
        }
    }
    
    /**
     * Admin pending payments (optimized)
     */
    public function pending() {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }

        // Get pending payment counts and statistics only (for performance)
        $pendingCounts = $this->paymentModel->getPendingPaymentCounts();
        $pendingStats = $this->paymentModel->getPendingPaymentStats();

        $data = [
            'title' => 'Pending Payments',
            'pending_counts' => $pendingCounts,
            'pending_stats' => $pendingStats
        ];

        $this->view('admin/payments/pending_optimized', $data);
    }

    /**
     * AJAX endpoint for loading paginated pending payments
     *
     * @return void
     */
    public function loadPending() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            http_response_code(403);
            echo json_encode(['error' => 'Admin access required']);
            return;
        }

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $urgencyFilter = $_GET['urgency_filter'] ?? 'all';
        $amountFilter = $_GET['amount_filter'] ?? '';
        $orderBy = $_GET['order_by'] ?? 'created_at';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            $result = $this->paymentModel->getPaginatedPendingPayments(
                $page, $perPage, $search, $urgencyFilter, $amountFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'payments' => $result['payments'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in PaymentController::loadPending: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load pending payments']);
        }
    }
    
    /**
     * Admin approve payment
     * 
     * @param int $id Payment ID
     */
    public function approve($id) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment
        $payment = $this->paymentModel->getPaymentById($id);
        
        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $adminNotes = !empty($_POST['admin_notes']) ? trim($_POST['admin_notes']) : null;
            
            // Update payment status
            if ($this->paymentModel->updatePaymentStatus($id, 'completed', null, $adminNotes, $this->auth->getCurrentUserId())) {
                // If this is a registration payment, update registration status
                if ($payment->payment_type == 'registration') {
                    $paymentData = [
                        'id' => $payment->related_id,
                        'payment_status' => 'completed'
                    ];
                    
                    $this->registrationModel->updatePayment($paymentData);
                }
                
                // If this is a show listing payment, update show status
                if ($payment->payment_type == 'show_listing') {
                    $this->showModel->updateShowListingPaymentStatus($payment->related_id, true);
                    
                    // Update show status from payment_pending to draft
                    $show = $this->showModel->getShowById($payment->related_id);
                    if ($show && $show->status == 'payment_pending') {
                        $this->showModel->updateShowStatus($payment->related_id, 'draft');
                    }
                }
                
                $this->setFlashMessage('payment_success', 'Payment approved successfully', 'success');
            } else {
                $this->setFlashMessage('payment_error', 'Failed to approve payment', 'danger');
            }
            
            $this->redirect('payment/pending');
        } else {
            // Display confirmation form
            $data = [
                'title' => 'Approve Payment',
                'payment' => $payment
            ];
            
            $this->view('admin/payments/approve', $data);
        }
    }
    
    /**
     * Admin reject payment
     * 
     * @param int $id Payment ID
     */
    public function reject($id) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment
        $payment = $this->paymentModel->getPaymentById($id);
        
        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $adminNotes = !empty($_POST['admin_notes']) ? trim($_POST['admin_notes']) : null;
            
            // Update payment status
            if ($this->paymentModel->updatePaymentStatus($id, 'rejected', null, $adminNotes, $this->auth->getCurrentUserId())) {
                $this->setFlashMessage('payment_success', 'Payment rejected successfully', 'success');
            } else {
                $this->setFlashMessage('payment_error', 'Failed to reject payment', 'danger');
            }
            
            $this->redirect('payment/pending');
        } else {
            // Display confirmation form
            $data = [
                'title' => 'Reject Payment',
                'payment' => $payment
            ];
            
            $this->view('admin/payments/reject', $data);
        }
    }
    
    /**
     * Process manual payment for existing pending payment
     * 
     * @param int $id Payment ID
     */
    public function processManual($id) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment
        $payment = $this->paymentModel->getPaymentById($id);
        
        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if payment is still pending
        if ($payment->payment_status !== 'pending') {
            $this->setFlashMessage('payment_error', 'Payment is not in pending status', 'danger');
            $this->redirect('payment/details/' . $id);
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Validate required fields
            $errors = [];
            
            if (empty($_POST['manual_payment_method'])) {
                $errors[] = 'Payment method is required';
            }
            
            if (empty($_POST['manual_amount']) || !is_numeric($_POST['manual_amount']) || floatval($_POST['manual_amount']) <= 0) {
                $errors[] = 'Valid payment amount is required';
            }
            
            if (empty($_POST['manual_date'])) {
                $errors[] = 'Payment date is required';
            }
            
            if (!empty($errors)) {
                $this->setFlashMessage('payment_error', implode('<br>', $errors), 'danger');
                $this->redirect('payment/details/' . $id);
                return;
            }
            
            // Get form data
            $paymentMethod = trim($_POST['manual_payment_method']);
            $amount = floatval($_POST['manual_amount']);
            $reference = !empty($_POST['manual_reference']) ? trim($_POST['manual_reference']) : null;
            $paymentDate = trim($_POST['manual_date']);
            $adminNotes = !empty($_POST['manual_notes']) ? trim($_POST['manual_notes']) : null;
            
            // Create reference if not provided
            if (empty($reference)) {
                $reference = 'MANUAL-' . strtoupper($paymentMethod) . '-' . time();
            }
            
            // Build admin notes
            $fullAdminNotes = "Manual payment processed by admin.\n";
            $fullAdminNotes .= "Method: " . ucfirst(str_replace('_', ' ', $paymentMethod)) . "\n";
            $fullAdminNotes .= "Amount: $" . number_format($amount, 2) . "\n";
            $fullAdminNotes .= "Payment Date: " . $paymentDate . "\n";
            if ($reference) {
                $fullAdminNotes .= "Reference: " . $reference . "\n";
            }
            // Get current user information
            $currentUser = $this->userModel->getUserById($this->auth->getCurrentUserId());
            $fullAdminNotes .= "Processed by: " . ($currentUser ? $currentUser->name : 'Admin') . "\n";
            $fullAdminNotes .= "Processed on: " . gmdate('Y-m-d H:i:s') . "\n";
            if ($adminNotes) {
                $fullAdminNotes .= "Notes: " . $adminNotes;
            }
            
            // Update payment record
            $updateData = [
                'payment_status' => 'completed',
                'payment_reference' => $reference,
                'admin_notes' => $fullAdminNotes,
                'processed_by' => $this->auth->getCurrentUserId(),
                'processed_at' => gmdate('Y-m-d H:i:s')
            ];
            
            if ($this->paymentModel->updatePayment($id, $updateData)) {
                try {
                    // Handle payment type specific actions
                    if ($payment->payment_type == 'registration') {
                        // Update registration payment status
                        $paymentData = [
                            'payment_status' => 'paid',
                            'payment_amount' => $amount,
                            'payment_method' => $paymentMethod,
                            'payment_reference' => $reference,
                            'payment_date' => $paymentDate
                        ];
                        
                        $this->registrationModel->updatePayment($paymentData);
                        
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("Manual payment processed for registration ID: " . $payment->related_id);
                        }
                    }
                    
                    // If this is a show listing payment, update show status
                    if ($payment->payment_type == 'show_listing') {
                        $this->showModel->updateShowListingPaymentStatus($payment->related_id, true);
                        
                        // Update show status from payment_pending to draft
                        $show = $this->showModel->getShowById($payment->related_id);
                        if ($show && $show->status == 'payment_pending') {
                            $this->showModel->updateShowStatus($payment->related_id, 'draft');
                            
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("Show status updated from payment_pending to draft for show ID: " . $payment->related_id);
                            }
                        }
                        
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("Manual payment processed for show listing ID: " . $payment->related_id);
                        }
                    }
                    
                    $this->setFlashMessage('payment_success', 'Manual payment processed successfully. Payment status updated to completed.', 'success');
                    $this->redirect('payment/details/' . $id);
                    
                } catch (Exception $e) {
                    // Log the error but still show success since main payment was updated
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("Error in manual payment post-processing: " . $e->getMessage());
                    }
                    
                    $this->setFlashMessage('payment_warning', 'Payment processed but there may have been issues updating related records. Please verify manually.', 'warning');
                    $this->redirect('payment/details/' . $id);
                }
            } else {
                $this->setFlashMessage('payment_error', 'Failed to process manual payment. Please try again or contact system administrator.', 'danger');
                $this->redirect('payment/details/' . $id);
            }
        } else {
            // Redirect to payment details page
            $this->redirect('payment/details/' . $id);
        }
    }
    
    /**
     * Manual payment processing
     * 
     * @param string $type Payment type (registration or show_listing)
     * @param int $relatedId Related ID (registration ID or show ID)
     */
    public function manual($type, $relatedId) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Validate payment type
        if (!in_array($type, ['registration', 'show_listing'])) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get related record
        $related = null;
        $user = null;
        $amount = 0;
        
        if ($type == 'registration') {
            $related = $this->registrationModel->getRegistrationById($relatedId);
            if (!$related) {
                $this->redirect('home/not_found');
                return;
            }
            
            $user = $this->userModel->getUserById($related->owner_id);
            $show = $this->showModel->getShowById($related->show_id);
            $amount = $show->registration_fee;
            
            // Check if already paid
            if ($related->payment_status == 'completed') {
                $this->setFlashMessage('payment_info', 'This registration has already been paid', 'info');
                $this->redirect('registration/view/' . $relatedId);
                return;
            }
        } else if ($type == 'show_listing') {
            $related = $this->showModel->getShowById($relatedId);
            if (!$related) {
                $this->redirect('home/not_found');
                return;
            }
            
            $user = $this->userModel->getUserById($related->coordinator_id);
            $amount = $related->listing_fee;
            if ($amount <= 0) {
                $amount = $this->paymentModel->getPaymentSetting('default_show_listing_fee', true);
            }
            
            // Check if already paid
            if ($related->listing_paid) {
                $this->setFlashMessage('payment_info', 'This show listing has already been paid', 'info');
                $this->redirect('show/manage/' . $relatedId);
                return;
            }
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $paymentMethodId = trim($_POST['payment_method_id']);
            $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
            $adminNotes = !empty($_POST['admin_notes']) ? trim($_POST['admin_notes']) : null;
            
            // Get payment method
            $paymentMethod = $this->paymentModel->getPaymentMethodById($paymentMethodId);
            
            if (!$paymentMethod) {
                $this->setFlashMessage('payment_error', 'Invalid payment method', 'danger');
                $this->redirect('payment/manual/' . $type . '/' . $relatedId);
                return;
            }
            
            // Process manual payment
            $paymentData = [
                'user_id' => $user->id,
                'amount' => $amount,
                'payment_method_id' => $paymentMethodId,
                'payment_type' => $type,
                'related_id' => $relatedId,
                'payment_reference' => $paymentReference,
                'admin_notes' => $adminNotes,
                'processed_by' => $this->auth->getCurrentUserId()
            ];
            
            if ($this->paymentModel->processManualPayment($paymentData)) {
                $this->setFlashMessage('payment_success', 'Manual payment processed successfully', 'success');
                
                if ($type == 'registration') {
                    $this->redirect('registration/view/' . $relatedId);
                } else {
                    $this->redirect('show/manage/' . $relatedId);
                }
            } else {
                $this->setFlashMessage('payment_error', 'Failed to process manual payment', 'danger');
                $this->redirect('payment/manual/' . $type . '/' . $relatedId);
            }
        } else {
            // Display manual payment form
            $data = [
                'title' => 'Process Manual Payment',
                'type' => $type,
                'related' => $related,
                'user' => $user,
                'amount' => $amount,
                'payment_methods' => $paymentMethods
            ];
            
            $this->view('admin/payments/manual', $data);
        }
    }
    
    /**
     * Show payment statistics
     * 
     * @param int $showId Show ID
     */
    public function stats($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment statistics
        $stats = $this->paymentModel->getShowPaymentStats($showId);
        
        // Get all payments for this show
        $payments = $this->paymentModel->getAllPayments(['show_id' => $showId]);
        
        $data = [
            'title' => 'Payment Statistics',
            'show' => $show,
            'stats' => $stats,
            'payments' => $payments
        ];
        
        $this->view('coordinator/payment_stats', $data);
    }
    
    /**
     * Process a manual payment
     * 
     * @param string $type Payment type (registration or show_listing)
     * @param int $id Related ID (registration ID or show ID)
     */
    public function processManualPayment($type, $id) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Validate payment type
        if (!in_array($type, ['registration', 'show_listing'])) {
            $this->redirect('home/error/Invalid%20payment%20type');
            return;
        }
        
        // Get related record
        if ($type == 'registration') {
            $registration = $this->registrationModel->getRegistrationById($id);
            if (!$registration) {
                $this->redirect('home/not_found');
                return;
            }
            
            // Check if user is admin or coordinator of the show
            $show = $this->showModel->getShowById($registration->show_id);
            if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
                $this->redirect('home/access_denied');
                return;
            }
            
            $userId = $registration->owner_id;
            $amount = $show->registration_fee;
            $relatedId = $registration->id;
            
        } else { // show_listing
            // Only admins can process show listing payments
            if (!$this->auth->hasRole('admin')) {
                $this->redirect('home/access_denied');
                return;
            }
            
            $show = $this->showModel->getShowById($id);
            if (!$show) {
                $this->redirect('home/not_found');
                return;
            }
            
            $userId = $show->coordinator_id;
            $amount = $show->listing_fee;
            if ($amount <= 0) {
                $amount = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
            }
            $relatedId = $show->id;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $paymentMethodId = trim($_POST['payment_method_id']);
            $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
            $notes = !empty($_POST['notes']) ? trim($_POST['notes']) : null;
            
            // Process manual payment
            $paymentData = [
                'user_id' => $userId,
                'amount' => $amount,
                'payment_method_id' => $paymentMethodId,
                'payment_reference' => $paymentReference,
                'payment_type' => $type,
                'related_id' => $relatedId,
                'notes' => $notes,
                'processed_by' => $this->auth->getCurrentUserId(),
                'admin_notes' => 'Manual payment processed by ' . $this->auth->getCurrentUserName()
            ];
            
            if ($this->paymentModel->processManualPayment($paymentData)) {
                if ($type == 'registration') {
                    $this->setFlashMessage('payment_success', 'Registration payment processed successfully', 'success');
                    $this->redirect('registration/view/' . $id);
                } else {
                    $this->setFlashMessage('payment_success', 'Show listing payment processed successfully', 'success');
                    $this->redirect('show/manage/' . $id);
                }
            } else {
                $this->setFlashMessage('payment_error', 'Failed to process payment', 'danger');
                $this->redirect('payment/processManualPayment/' . $type . '/' . $id);
            }
        } else {
            // Get payment methods
            $paymentMethods = $this->paymentModel->getPaymentMethods();
            
            // Display form
            $data = [
                'title' => 'Process Manual Payment',
                'payment_type' => $type,
                'related_id' => $id,
                'amount' => $amount,
                'payment_methods' => $paymentMethods
            ];
            
            if ($type == 'registration') {
                $data['registration'] = $registration;
                $data['show'] = $show;
            } else {
                $data['show'] = $show;
            }
            
            $this->view('payments/process_manual', $data);
        }
    }
}