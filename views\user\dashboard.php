<?php require APPROOT . '/views/includes/header.php'; ?>



<div class="container py-4">
    <h1 class="mb-4">My Dashboard</h1>
    
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-primary text-white">
                <div class="stat-value"><?php echo isset($vehicles) && is_array($vehicles) ? count($vehicles) : 0; ?></div>
                <div class="stat-label">My Vehicles</div>
                <a href="<?php echo BASE_URL; ?>/user/vehicles" class="btn btn-light btn-sm">Manage Vehicles</a>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-success text-white">
                <div class="stat-value"><?php echo isset($registrations) && is_array($registrations) ? count($registrations) : 0; ?></div>
                <div class="stat-label">My Registrations</div>
                <a href="<?php echo BASE_URL; ?>/user/registrations" class="btn btn-light btn-sm">View Registrations</a>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-info text-white">
                <div class="stat-value">
                    <?php
                    // Get current user ID from session
                    $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
                    
                    // Get user's profile image
                    $db = new Database();
                    $db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
                    $db->bind(':entity_type', 'user');
                    $db->bind(':entity_id', $userId);
                    $profileImage = $db->single();
                    
                    if ($profileImage) :
                    ?>
                        <div class="rounded-circle overflow-hidden mx-auto" style="width: 60px; height: 60px; background-color: rgba(255,255,255,0.2);">
                            <img src="<?php echo BASE_URL; ?>/<?php echo $profileImage->file_path; ?>" alt="Profile" class="img-fluid" style="object-fit: cover; width: 100%; height: 100%;">
                        </div>
                    <?php else : ?>
                        <i class="fas fa-user"></i>
                    <?php endif; ?>
                </div>
                <div class="stat-label">My Profile</div>
                <a href="<?php echo BASE_URL; ?>/user/profile" class="btn btn-light btn-sm">Edit Profile</a>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-warning text-dark">
                <div class="stat-value">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-label">Notifications</div>
                <div class="btn-group-vertical w-100" role="group">
                    <a href="<?php echo BASE_URL; ?>/user/notifications" class="btn btn-dark btn-sm">
                        <i class="fas fa-cog me-1"></i>Settings
                    </a>
                    <a href="<?php echo BASE_URL; ?>/user/event_subscriptions" class="btn btn-outline-dark btn-sm">
                        <i class="fas fa-calendar-check me-1"></i>Subscriptions
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">My Vehicles</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($vehicles) || !is_array($vehicles)) : ?>
                        <p class="text-muted">You haven't added any vehicles yet.</p>
                    <?php else : ?>
                        <div class="list-group">
                            <?php foreach ($vehicles as $vehicle) : ?>
                                <a href="<?php echo BASE_URL; ?>/show/vehicle/<?php echo $vehicle->id; ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1"><?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?></h5>
                                        <small class="text-muted"><?php echo $vehicle->color; ?></small>
                                    </div>
                                    <?php if (!empty($vehicle->description)) : ?>
                                        <p class="mb-1"><?php echo $vehicle->description; ?></p>
                                    <?php endif; ?>
                                    <small>
                                        <?php if (!empty($vehicle->license_plate)) : ?>
                                            License: <?php echo $vehicle->license_plate; ?>
                                        <?php endif; ?>
                                    </small>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="<?php echo BASE_URL; ?>/user/vehicles" class="btn btn-primary">View All Vehicles</a>
                    <a href="<?php echo BASE_URL; ?>/user/addVehicle" class="btn btn-outline-primary">Add New Vehicle</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Upcoming Shows</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_shows) || !is_array($upcoming_shows)) : ?>
                        <p class="text-muted">No upcoming shows.</p>
                    <?php else : ?>
                        <div class="list-group">
                            <?php foreach ($upcoming_shows as $show) : ?>
                                <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1"><?php echo $show->name; ?></h5>
                                        <small>
                                            <?php if ($show->status == 'published') : ?>
                                                <span class="badge bg-success">Published</span>
                                            <?php elseif ($show->status == 'draft') : ?>
                                                <span class="badge bg-secondary">Draft</span>
                                            <?php elseif ($show->status == 'completed') : ?>
                                                <span class="badge bg-info">Completed</span>
                                            <?php else : ?>
                                                <span class="badge bg-danger">Cancelled</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <p class="mb-1"><?php echo $show->location; ?></p>
                                    <small>
                                        <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?> - 
                                        <?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                                    </small>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="<?php echo BASE_URL; ?>/show" class="btn btn-success">View All Shows</a>
                    <a href="<?php echo BASE_URL; ?>/show" class="btn btn-outline-success">Find a Show to Register</a>
                    <a href="<?php echo BASE_URL; ?>/user/createShow" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Show
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">My Registrations</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($registrations) || !is_array($registrations)) : ?>
                        <p class="text-muted">You haven't registered for any shows yet.</p>
                    <?php else : ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Show</th>
                                        <th>Category</th>
                                        <th>Vehicle</th>
                                        <th>Registration #</th>
                                        <th>Status</th>
                                        <th>Payment</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($registrations as $registration) : ?>
                                        <tr>
                                            <td><?php echo $registration->show_name; ?></td>
                                            <td><?php echo $registration->category_name; ?></td>
                                            <td><?php echo $registration->year; ?> <?php echo $registration->make; ?> <?php echo $registration->model; ?></td>
                                            <td><?php echo $registration->id; ?></td>
                                            <td>
                                                <?php if ($registration->payment_status == 'paid') : ?>
                                                    <span class="badge bg-success">Paid</span>
                                                <?php elseif ($registration->payment_status == 'pending') : ?>
                                                    <span class="badge bg-warning text-dark">Pending</span>
                                                <?php elseif ($registration->payment_status == 'cancelled') : ?>
                                                    <span class="badge bg-danger">Cancelled</span>
                                                <?php else : ?>
                                                    <span class="badge bg-secondary"><?php echo ucfirst($registration->payment_status); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <!-- Debug: <?php echo htmlspecialchars(var_export($registration->payment_status, true)); ?> -->
                                                <?php if ($registration->payment_status == 'paid') : ?>
                                                    <span class="badge bg-success">Paid</span>
                                                <?php elseif ($registration->payment_status == 'pending' || $registration->payment_status == 'unpaid') : ?>
                                                    <span class="badge bg-warning text-dark">Pending</span>
                                                <?php elseif ($registration->payment_status == 'free' || $registration->payment_status == 'waived') : ?>
                                                    <span class="badge bg-success">Free</span>
                                                <?php elseif ($registration->payment_status == 'refunded') : ?>
                                                    <span class="badge bg-warning text-dark">Refunded</span>
                                                <?php elseif ($registration->payment_status == 'cancelled') : ?>
                                                    <span class="badge bg-danger">Cancelled</span>
                                                <?php else : ?>
                                                    <span class="badge bg-secondary"><?php echo ucfirst($registration->payment_status); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?php echo BASE_URL; ?>/user/viewRegistration/<?php echo $registration->id; ?>" class="btn btn-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <?php if ($registration->payment_status == 'pending' || $registration->payment_status == 'unpaid') : ?>
                                                        <a href="<?php echo BASE_URL; ?>/payments/pay/<?php echo $registration->id; ?>" class="btn btn-success">
                                                            <i class="fas fa-credit-card"></i> Pay
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($registration->status == 'pending') : ?>
                                                        <a href="<?php echo BASE_URL; ?>/user/cancelRegistration/<?php echo $registration->id; ?>" class="btn btn-danger">
                                                            <i class="fas fa-times"></i> Cancel
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (isset($registration->show_status) && $registration->show_status == 'completed') : ?>
                                                        <a href="<?php echo BASE_URL; ?>/user/viewScores/<?php echo $registration->id; ?>" class="btn btn-info">
                                                            <i class="fas fa-star"></i> Scores
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="<?php echo BASE_URL; ?>/user/registrations" class="btn btn-info">View All Registrations</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Extra padding at the bottom -->
    <div class="pb-5 mb-5"></div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>