<?php require APPROOT . '/views/includes/header.php'; ?>

<?php if (isset($show)): ?>
    <!-- Show-Specific Registration View -->
    <div class="container-fluid container-lg">
        <div class="row mb-4 align-items-center">
            <div class="col-8 col-md-6">
                <h1 class="h2 mb-0">Registrations for <?php echo htmlspecialchars($show->name); ?></h1>
                <p class="text-muted mb-0"><?php echo count($registrations); ?> registrations found</p>
            </div>
            <div class="col-4 col-md-6 text-end">
                <a href="<?php echo BASE_URL; ?>/admin/registrations" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i> Back to Overview
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-primary">
                    <i class="fas fa-calendar me-2"></i> Manage Shows
                </a>
            </div>
        </div>

        <?php flash('admin_message'); ?>

        <!-- Registration Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary"><?php echo $total_registrations; ?></h5>
                        <p class="card-text">Total Registrations</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success"><?php echo $paid_registrations; ?></h5>
                        <p class="card-text">Paid</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning"><?php echo $pending_registrations; ?></h5>
                        <p class="card-text">Pending Payment</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">$<?php echo number_format($total_revenue, 2); ?></h5>
                        <p class="card-text">Total Revenue</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Registrations Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Registration Details
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($registrations)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No registrations found</h5>
                        <p class="text-muted">This show doesn't have any registrations yet.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Reg #</th>
                                    <th>Owner</th>
                                    <th>Vehicle</th>
                                    <th>Category</th>
                                    <th>Payment</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($registrations as $registration): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($registration->registration_number); ?></strong></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($registration->owner_name); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($registration->email); ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($registration->year . ' ' . $registration->make . ' ' . $registration->model); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($registration->color); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($registration->category_name); ?></td>
                                        <td>
                                            <?php if ($registration->payment_status === 'completed'): ?>
                                                <span class="badge bg-success">Paid</span><br>
                                                <small class="text-muted">$<?php echo number_format($registration->fee, 2); ?></small>
                                            <?php elseif ($registration->payment_status === 'pending'): ?>
                                                <span class="badge bg-warning text-dark">Pending</span><br>
                                                <small class="text-muted">$<?php echo number_format($registration->fee, 2); ?></small>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Not Paid</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($registration->status === 'approved'): ?>
                                                <span class="badge bg-success">Approved</span>
                                            <?php elseif ($registration->status === 'pending'): ?>
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?php echo ucfirst($registration->status); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo BASE_URL; ?>/admin/viewRegistration/<?php echo $registration->id; ?>"
                                                   class="btn btn-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>/admin/editRegistration/<?php echo $registration->id; ?>"
                                                   class="btn btn-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php else: ?>
    <!-- Overview Dashboard -->
    <div class="container-fluid container-lg">
        <div class="row mb-4 align-items-center">
            <div class="col-8 col-md-6">
                <h1 class="h2 mb-0">Registration Management</h1>
                <p class="text-muted mb-0">Manage all registrations across the platform</p>
            </div>
            <div class="col-4 col-md-6 text-end">
                <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-primary me-2 d-none d-sm-inline">
                    <i class="fas fa-calendar me-2"></i> Manage Shows
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/dashboard" class="btn btn-outline-primary">
                    <i class="fas fa-tachometer-alt me-2 d-none d-sm-inline"></i> Admin Dashboard
                </a>
            </div>
        </div>

        <?php flash('admin_message'); ?>

    <!-- Registration Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Registration Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm registration-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">Total Registrations</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['total_registrations'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">All Time</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm registration-overview-card" 
                                 data-filter="active" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Active Shows</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['active_registrations'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Upcoming Events</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm registration-overview-card" 
                                 data-filter="pending_payment" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Pending Payment</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['pending_payment'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Need Payment</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm registration-overview-card" 
                                 data-filter="today" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Today</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['today_registrations'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Registered Today</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration Statistics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>Revenue Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success">$<?php echo number_format($registration_stats['total_revenue'] ?? 0, 2); ?></h4>
                            <small class="text-muted">Total Revenue</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">$<?php echo number_format($registration_stats['monthly_revenue'] ?? 0, 2); ?></h4>
                            <small class="text-muted">This Month</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Activity Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary"><?php echo number_format($registration_stats['avg_per_show'] ?? 0, 1); ?></h4>
                            <small class="text-muted">Avg per Show</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning"><?php echo number_format($registration_stats['this_week'] ?? 0); ?></h4>
                            <small class="text-muted">This Week</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>Manage Shows</span>
                                <small class="text-muted mt-1">View all shows</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/admin/users" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <span>Manage Users</span>
                                <small class="text-muted mt-1">View all users</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/payment/admin" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-credit-card fa-2x mb-2"></i>
                                <span>Payment Management</span>
                                <small class="text-muted mt-1">Review payments</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/admin/reports" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>Reports</span>
                                <small class="text-muted mt-1">Generate reports</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration Details Section (Lazy Loaded) -->
    <div class="registration-section" id="registration-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">Registration Details</span>
                        <span class="badge bg-secondary" id="registration-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closeRegistrationSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-registrations" class="form-label">Search Registrations</label>
                        <input type="text" class="form-control" id="search-registrations" 
                               placeholder="Search by user, vehicle, or show...">
                    </div>
                    <div class="col-md-2">
                        <label for="show-filter" class="form-label">Show</label>
                        <select class="form-select" id="show-filter">
                            <option value="">All Shows</option>
                            <?php if (!empty($available_shows)): ?>
                                <?php foreach ($available_shows as $show): ?>
                                    <option value="<?php echo $show->id; ?>"><?php echo $show->name; ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="pending_payment">Pending Payment</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-registrations" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-registrations">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchRegistrations()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearRegistrationSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportRegistrations()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-registrations">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading registrations...</p>
            </div>
            
            <!-- Registrations Content (Will be populated via AJAX) -->
            <div id="registrations-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Registration overview card click handlers
    document.querySelectorAll('.registration-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadRegistrationSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-registrations').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchRegistrations();
        }
    });

    // Filter change handlers
    document.getElementById('show-filter').addEventListener('change', searchRegistrations);
    document.getElementById('status-filter').addEventListener('change', searchRegistrations);
    document.getElementById('per-page-registrations').addEventListener('change', searchRegistrations);
});

function loadRegistrationSection(filter = 'all') {
    // Show the registration section
    const section = document.getElementById('registration-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        document.getElementById('status-filter').value = filter;
    }

    // Load registrations
    loadRegistrations(1);
}

function closeRegistrationSection() {
    const section = document.getElementById('registration-section');
    section.style.display = 'none';
}

function loadRegistrations(page = 1) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-registrations').value;
    const showFilter = document.getElementById('show-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    const perPage = document.getElementById('per-page-registrations').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        show_filter: showFilter,
        status_filter: statusFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/admin/loadRegistrations?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderRegistrations(data);
        } else {
            showRegistrationError(data.error || 'Failed to load registrations');
        }
    })
    .catch(error => {
        console.error('Error loading registrations:', error);
        showRegistrationError('Network error occurred');
    });
}

function searchRegistrations() {
    loadRegistrations(1);
}

function clearRegistrationSearch() {
    document.getElementById('search-registrations').value = '';
    document.getElementById('show-filter').value = '';
    document.getElementById('status-filter').value = 'all';
    loadRegistrations(1);
}

function renderRegistrations(data) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render registrations table and pagination
    let html = '';

    if (data.registrations.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No registrations found.</p></div>';
    } else {
        html = renderRegistrationsTable(data.registrations, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update registration count display
    document.getElementById('registration-count-display').textContent = data.pagination.total_registrations.toLocaleString();
}

function renderRegistrationsTable(registrations, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>ID</th><th>User</th><th>Show</th><th>Vehicle</th><th>Date</th><th>Payment</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    registrations.forEach(registration => {
        html += '<tr>';
        html += '<td><strong>#' + registration.id + '</strong></td>';
        html += '<td>' + (registration.user_name || 'Unknown') + '</td>';
        html += '<td><strong>' + registration.show_name + '</strong></td>';

        html += '<td>';
        html += '<strong>' + registration.year + ' ' + registration.make + ' ' + registration.model + '</strong>';
        if (registration.color) {
            html += '<br><small class="text-muted">' + registration.color + '</small>';
        }
        html += '</td>';

        html += '<td>' + formatDate(registration.created_at) + '</td>';
        html += '<td>' + getPaymentStatusBadge(registration.payment_status) + '</td>';
        html += '<td>' + getRegistrationActions(registration.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderRegistrationPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_registrations.toLocaleString()} registrations`;
    html += '</div>';

    return html;
}

function getPaymentStatusBadge(status) {
    switch (status) {
        case 'pending':
            return '<span class="badge bg-warning text-dark">Pending</span>';
        case 'completed':
            return '<span class="badge bg-success">Completed</span>';
        case 'failed':
            return '<span class="badge bg-danger">Failed</span>';
        default:
            return '<span class="badge bg-secondary">' + status + '</span>';
    }
}

function getRegistrationActions(registrationId) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/admin/viewRegistration/${registrationId}" class="btn btn-info" title="View Details">
                <i class="fas fa-eye"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/editRegistration/${registrationId}" class="btn btn-primary" title="Edit Registration">
                <i class="fas fa-edit"></i>
            </a>
            <button class="btn btn-danger" onclick="deleteRegistration(${registrationId})" title="Delete Registration">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
}

function renderRegistrationPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadRegistrations(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showRegistrationError(message) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function exportRegistrations() {
    // Get current filter values
    const search = document.getElementById('search-registrations').value;
    const showFilter = document.getElementById('show-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        show_filter: showFilter,
        status_filter: statusFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/admin/exportRegistrations?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}

function deleteRegistration(registrationId) {
    if (confirm('Are you sure you want to delete this registration? This action cannot be undone.')) {
        // Disable the button to prevent double-clicks
        const button = event.target.closest('button');
        button.disabled = true;

        fetch(`<?php echo BASE_URL; ?>/admin/deleteRegistration/${registrationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the registrations to reflect the change
                loadRegistrations(1);
                alert('Registration deleted successfully.');
            } else {
                alert('Failed to delete registration: ' + (data.error || 'Unknown error'));
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error deleting registration:', error);
            alert('Network error occurred while deleting registration.');
            button.disabled = false;
        });
    }
}
</script>

<?php endif; ?>

<?php require APPROOT . '/views/includes/footer.php'; ?>
