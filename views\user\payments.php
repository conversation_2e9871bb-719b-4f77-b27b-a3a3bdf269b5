<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">My Payment History</h1>
            <p class="text-muted mb-0">View and manage your payment history</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/user/registrations" class="btn btn-primary me-2 d-none d-sm-inline">
                <i class="fas fa-list me-2"></i> My Registrations
            </a>
            <a href="<?php echo BASE_URL; ?>/user/dashboard" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt me-2 d-none d-sm-inline"></i> Dashboard
            </a>
        </div>
    </div>

    <?php flash('payment_message'); ?>

    <!-- Payment Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Payment Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm payment-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">All Payments</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($payment_counts['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Payments</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm payment-overview-card" 
                                 data-filter="pending" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Pending</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($payment_counts['pending'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Awaiting Processing</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm payment-overview-card" 
                                 data-filter="completed" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Completed</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($payment_counts['completed'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Successful</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm payment-overview-card" 
                                 data-filter="total_spent" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Total Spent</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        $<?php echo number_format($payment_stats['total_spent'] ?? 0, 2); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">All Time</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Summary -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>Spending Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success">$<?php echo number_format($payment_stats['this_year'] ?? 0, 2); ?></h4>
                            <small class="text-muted">This Year</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">$<?php echo number_format($payment_stats['this_month'] ?? 0, 2); ?></h4>
                            <small class="text-muted">This Month</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Payment Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">$<?php echo number_format($payment_stats['avg_payment'] ?? 0, 2); ?></h4>
                            <small class="text-muted">Avg Payment</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning"><?php echo date('M j', strtotime($payment_stats['last_payment_date'] ?? 'now')); ?></h4>
                            <small class="text-muted">Last Payment</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/user/registerVehicle" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span>New Registration</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/user/registrations" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-list fa-2x mb-2"></i>
                                <span>My Registrations</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/user/vehicles" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-car fa-2x mb-2"></i>
                                <span>My Vehicles</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/shows" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>Browse Shows</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Details Section (Lazy Loaded) -->
    <div class="payment-section" id="payment-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">Payment Details</span>
                        <span class="badge bg-secondary" id="payment-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closePaymentSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-payments" class="form-label">Search Payments</label>
                        <input type="text" class="form-control" id="search-payments" 
                               placeholder="Search by amount, type, or reference...">
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="all">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="type-filter" class="form-label">Type</label>
                        <select class="form-select" id="type-filter">
                            <option value="">All Types</option>
                            <option value="registration">Registration</option>
                            <option value="show_listing">Show Listing</option>
                            <option value="membership">Membership</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-payments" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-payments">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchPayments()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearPaymentSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportPayments()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-payments">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading payments...</p>
            </div>
            
            <!-- Payments Content (Will be populated via AJAX) -->
            <div id="payments-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment overview card click handlers
    document.querySelectorAll('.payment-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, '').replace('$', ''));

            if (count > 0 || filter === 'total_spent') {
                loadPaymentSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-payments').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchPayments();
        }
    });

    // Filter change handlers
    document.getElementById('status-filter').addEventListener('change', searchPayments);
    document.getElementById('type-filter').addEventListener('change', searchPayments);
    document.getElementById('per-page-payments').addEventListener('change', searchPayments);
});

function loadPaymentSection(filter = 'all') {
    // Show the payment section
    const section = document.getElementById('payment-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all' && filter !== 'total_spent') {
        document.getElementById('status-filter').value = filter;
    }

    // Load payments
    loadPayments(1);
}

function closePaymentSection() {
    const section = document.getElementById('payment-section');
    section.style.display = 'none';
}

function loadPayments(page = 1) {
    const loadingDiv = document.getElementById('loading-payments');
    const contentDiv = document.getElementById('payments-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-payments').value;
    const statusFilter = document.getElementById('status-filter').value;
    const typeFilter = document.getElementById('type-filter').value;
    const perPage = document.getElementById('per-page-payments').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        status_filter: statusFilter,
        type_filter: typeFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/user/loadPayments?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderPayments(data);
        } else {
            showPaymentError(data.error || 'Failed to load payments');
        }
    })
    .catch(error => {
        console.error('Error loading payments:', error);
        showPaymentError('Network error occurred');
    });
}

function searchPayments() {
    loadPayments(1);
}

function clearPaymentSearch() {
    document.getElementById('search-payments').value = '';
    document.getElementById('status-filter').value = 'all';
    document.getElementById('type-filter').value = '';
    loadPayments(1);
}

function renderPayments(data) {
    const loadingDiv = document.getElementById('loading-payments');
    const contentDiv = document.getElementById('payments-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render payments table and pagination
    let html = '';

    if (data.payments.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No payments found.</p></div>';
    } else {
        html = renderPaymentsTable(data.payments, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update payment count display
    document.getElementById('payment-count-display').textContent = data.pagination.total_payments.toLocaleString();
}

function renderPaymentsTable(payments, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Date</th><th>Description</th><th>Type</th><th>Amount</th><th>Method</th><th>Status</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    payments.forEach(payment => {
        html += '<tr>';
        html += '<td>' + formatDate(payment.created_at) + '</td>';
        html += '<td>' + (payment.description || payment.notes || 'Payment') + '</td>';
        html += '<td>' + (payment.payment_type || 'N/A') + '</td>';
        html += '<td><strong>$' + parseFloat(payment.amount).toFixed(2) + '</strong></td>';
        html += '<td>' + (payment.payment_method_name || 'N/A') + '</td>';
        html += '<td>' + getStatusBadge(payment.payment_status) + '</td>';
        html += '<td>' + getPaymentActions(payment) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderPaymentPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_payments.toLocaleString()} payments`;
    html += '</div>';

    return html;
}

function getStatusBadge(status) {
    switch (status) {
        case 'pending':
            return '<span class="badge bg-warning text-dark">Pending</span>';
        case 'completed':
            return '<span class="badge bg-success">Completed</span>';
        case 'failed':
            return '<span class="badge bg-danger">Failed</span>';
        case 'refunded':
            return '<span class="badge bg-info">Refunded</span>';
        default:
            return '<span class="badge bg-secondary">' + status + '</span>';
    }
}

function getPaymentActions(payment) {
    let actions = `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/user/paymentDetails/${payment.id}" class="btn btn-info" title="View Details">
                <i class="fas fa-eye"></i>
            </a>
    `;

    // Add receipt button for completed payments
    if (payment.status === 'paid' || payment.status === 'completed') {
        actions += `
            <a href="<?php echo BASE_URL; ?>/user/receipt/${payment.registration_id || payment.id}" class="btn btn-outline-primary" target="_blank" title="View Receipt">
                <i class="fas fa-file-invoice-dollar"></i>
            </a>
        `;
    }

    actions += '</div>';
    return actions;
}

function renderPaymentPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPayments(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadPayments(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPayments(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showPaymentError(message) {
    const loadingDiv = document.getElementById('loading-payments');
    const contentDiv = document.getElementById('payments-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function exportPayments() {
    // Get current filter values
    const search = document.getElementById('search-payments').value;
    const statusFilter = document.getElementById('status-filter').value;
    const typeFilter = document.getElementById('type-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        status_filter: statusFilter,
        type_filter: typeFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/user/exportPayments?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
