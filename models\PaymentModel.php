<?php
/**
 * Payment Model
 * 
 * This model handles all database operations related to payments.
 */
class PaymentModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all payment methods
     * 
     * @param bool $activeOnly Only return active payment methods
     * @param bool $includeAdminOnly Include admin-only payment methods
     * @return array
     */
    public function getPaymentMethods($activeOnly = true, $includeAdminOnly = false) {
        // Check if the is_active column exists
        try {
            $sql = 'SELECT * FROM payment_methods';
            $whereConditions = [];
            
            if ($activeOnly) {
                $whereConditions[] = 'is_active = TRUE';
            }
            
            if (!$includeAdminOnly) {
                $whereConditions[] = '(is_admin_only IS NULL OR is_admin_only = FALSE)';
            }
            
            if (!empty($whereConditions)) {
                $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
            }
            
            $sql .= ' ORDER BY id';
            
            $this->db->query($sql);
            return $this->db->resultSet();
        } catch (Exception $e) {
            // If there's an error (likely because columns don't exist), just get all methods
            $this->db->query('SELECT * FROM payment_methods ORDER BY id');
            return $this->db->resultSet();
        }
    }
    
    /**
     * Get payment methods for a specific show
     * 
     * @param int $showId Show ID
     * @param bool $activeOnly Only return active payment methods
     * @return array
     */
    public function getShowPaymentMethods($showId, $activeOnly = true) {
        try {
            // First check if the show has specific payment methods configured
            if ($activeOnly) {
                $this->db->query('SELECT pm.* 
                                FROM payment_methods pm
                                JOIN show_payment_methods spm ON pm.id = spm.payment_method_id
                                WHERE spm.show_id = :show_id 
                                AND spm.is_active = TRUE 
                                AND pm.is_active = TRUE
                                ORDER BY pm.id');
            } else {
                $this->db->query('SELECT pm.* 
                                FROM payment_methods pm
                                JOIN show_payment_methods spm ON pm.id = spm.payment_method_id
                                WHERE spm.show_id = :show_id
                                ORDER BY pm.id');
            }
            
            $this->db->bind(':show_id', $showId);
            $methods = $this->db->resultSet();
            
            // If no show-specific methods are found, return all active methods
            if (empty($methods)) {
                return $this->getPaymentMethods($activeOnly);
            }
            
            return $methods;
        } catch (Exception $e) {
            // If there's an error, fall back to all payment methods
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error getting show payment methods: ' . $e->getMessage());
            }
            return $this->getPaymentMethods($activeOnly);
        }
    }
    
    /**
     * Set payment methods for a show
     * 
     * @param int $showId Show ID
     * @param array $methodIds Array of payment method IDs
     * @return bool
     */
    public function setShowPaymentMethods($showId, $methodIds) {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Delete existing show payment methods
            $this->db->query('DELETE FROM show_payment_methods WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            $this->db->execute();
            
            // Insert new show payment methods
            foreach ($methodIds as $methodId) {
                $this->db->query('INSERT INTO show_payment_methods (show_id, payment_method_id) 
                                VALUES (:show_id, :method_id)');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':method_id', $methodId);
                $this->db->execute();
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error setting show payment methods: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get payment method by ID
     * 
     * @param int $id Payment method ID
     * @return object|bool
     */
    public function getPaymentMethodById($id) {
        $this->db->query('SELECT * FROM payment_methods WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get payment method by name
     * 
     * @param string $name Payment method name
     * @return object|bool
     */
    public function getPaymentMethodByName($name) {
        $this->db->query('SELECT * FROM payment_methods WHERE name = :name');
        $this->db->bind(':name', $name);
        
        return $this->db->single();
    }
    
    /**
     * Create payment method
     * 
     * @param array $data Payment method data
     * @return bool|int
     */
    public function createPaymentMethod($data) {
        $this->db->query('INSERT INTO payment_methods (name, description, instructions, is_active, requires_approval, is_online) 
                          VALUES (:name, :description, :instructions, :is_active, :requires_approval, :is_online)');
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':instructions', $data['instructions']);
        $this->db->bind(':is_active', $data['is_active']);
        $this->db->bind(':requires_approval', $data['requires_approval']);
        $this->db->bind(':is_online', $data['is_online'] ?? 0);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update payment method
     * 
     * @param array $data Payment method data
     * @return bool
     */
    public function updatePaymentMethod($data) {
        $this->db->query('UPDATE payment_methods 
                          SET name = :name, 
                              description = :description, 
                              instructions = :instructions, 
                              is_active = :is_active, 
                              requires_approval = :requires_approval,
                              is_online = :is_online
                          WHERE id = :id');
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':instructions', $data['instructions']);
        $this->db->bind(':is_active', $data['is_active']);
        $this->db->bind(':requires_approval', $data['requires_approval']);
        $this->db->bind(':is_online', $data['is_online'] ?? 0);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Delete payment method
     * 
     * @param int $id Payment method ID
     * @return bool
     */
    public function deletePaymentMethod($id) {
        $this->db->query('DELETE FROM payment_methods WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get payment setting
     * 
     * @param string $key Setting key
     * @param bool $isAdmin Whether to get admin payment settings (true) or coordinator settings (false)
     * @return string|null
     */
    public function getPaymentSetting($key, $isAdmin = true) {
        // Admin settings are for receiving listing fees from coordinators
        // Coordinator settings are for receiving registration fees from car owners
        
        $this->db->query('SELECT setting_value FROM payment_settings WHERE setting_key = :key AND is_admin = :is_admin');
        $this->db->bind(':key', $key);
        $this->db->bind(':is_admin', $isAdmin ? 1 : 0);
        
        $result = $this->db->single();
        
        return $result ? $result->setting_value : null;
    }
    
    /**
     * Get payment setting for a specific coordinator
     * 
     * @param string $key Setting key
     * @param int $coordinatorId Coordinator user ID
     * @return string|null
     */
    public function getCoordinatorPaymentSetting($key, $coordinatorId) {
        // Check if the coordinator_payment_settings table exists
        $this->db->query("SHOW TABLES LIKE 'coordinator_payment_settings'");
        $tableExists = $this->db->rowCount() > 0;
        
        if (!$tableExists) {
            // If the table doesn't exist, fall back to the old method
            return $this->getPaymentSetting($key, false);
        }
        
        $this->db->query('SELECT setting_value FROM coordinator_payment_settings 
                          WHERE setting_key = :key AND coordinator_id = :coordinator_id');
        $this->db->bind(':key', $key);
        $this->db->bind(':coordinator_id', $coordinatorId);
        
        $result = $this->db->single();
        
        return $result ? $result->setting_value : null;
    }
    
    /**
     * Get show payment setting
     * 
     * @param int $showId Show ID
     * @param string $key Setting key
     * @param bool $fallbackToGlobal Whether to fall back to global setting if show setting not found
     * @param bool $isAdmin Whether to get admin payment settings (true) or coordinator settings (false)
     * @return string|null
     */
    public function getShowPaymentSetting($showId, $key, $fallbackToGlobal = true, $isAdmin = false) {
        // Show payment settings are for coordinators to receive registration fees
        // Admin settings are only used for the admin to receive listing fees
        
        $this->db->query('SELECT setting_value FROM show_payment_settings 
                          WHERE show_id = :show_id AND setting_key = :key AND is_admin = :is_admin');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':key', $key);
        $this->db->bind(':is_admin', $isAdmin ? 1 : 0);
        
        $result = $this->db->single();
        
        if ($result) {
            return $result->setting_value;
        } else if ($fallbackToGlobal) {
            if ($isAdmin) {
                // For admin settings, fall back to global admin settings
                return $this->getPaymentSetting($key, true);
            } else {
                // For coordinator settings, we need to get the coordinator ID for this show
                $this->db->query('SELECT coordinator_id FROM shows WHERE id = :show_id');
                $this->db->bind(':show_id', $showId);
                $show = $this->db->single();
                
                if ($show && $show->coordinator_id) {
                    // Fall back to this coordinator's specific settings
                    return $this->getCoordinatorPaymentSetting($key, $show->coordinator_id);
                } else {
                    // If we can't find the coordinator, fall back to global coordinator settings
                    return $this->getPaymentSetting($key, false);
                }
            }
        }
        
        return null;
    }
    
    /**
     * Update payment setting
     * 
     * @param string $key Setting key
     * @param string $value Setting value
     * @param bool $isAdmin Whether this is an admin payment setting (true) or coordinator setting (false)
     * @return bool
     */
    public function updatePaymentSetting($key, $value, $isAdmin = true) {
        $this->db->query('INSERT INTO payment_settings (setting_key, setting_value, is_admin) 
                          VALUES (:key, :value, :is_admin) 
                          ON DUPLICATE KEY UPDATE setting_value = :update_value');
        
        $this->db->bind(':key', $key);
        $this->db->bind(':value', $value);
        $this->db->bind(':is_admin', $isAdmin ? 1 : 0);
        $this->db->bind(':update_value', $value);
        
        return $this->db->execute();
    }
    
    /**
     * Update payment setting for a specific coordinator
     * 
     * @param string $key Setting key
     * @param string $value Setting value
     * @param int $coordinatorId Coordinator user ID
     * @return bool
     */
    public function updateCoordinatorPaymentSetting($key, $value, $coordinatorId) {
        // Check if the coordinator_payment_settings table exists
        $this->db->query("SHOW TABLES LIKE 'coordinator_payment_settings'");
        $tableExists = $this->db->rowCount() > 0;
        
        if (!$tableExists) {
            // If the table doesn't exist, fall back to the old method
            return $this->updatePaymentSetting($key, $value, false);
        }
        
        $this->db->query('INSERT INTO coordinator_payment_settings (coordinator_id, setting_key, setting_value) 
                          VALUES (:coordinator_id, :key, :value)
                          ON DUPLICATE KEY UPDATE setting_value = :update_value');
        $this->db->bind(':coordinator_id', $coordinatorId);
        $this->db->bind(':key', $key);
        $this->db->bind(':value', $value);
        $this->db->bind(':update_value', $value);
        
        return $this->db->execute();
    }
    
    /**
     * Update show payment setting
     * 
     * @param int $showId Show ID
     * @param string $key Setting key
     * @param string $value Setting value
     * @param bool $isAdmin Whether this is an admin payment setting (true) or coordinator setting (false)
     * @return bool
     */
    public function updateShowPaymentSetting($showId, $key, $value, $isAdmin = false) {
        $this->db->query('INSERT INTO show_payment_settings (show_id, setting_key, setting_value, is_admin) 
                          VALUES (:show_id, :key, :value, :is_admin) 
                          ON DUPLICATE KEY UPDATE setting_value = :update_value');
        
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':key', $key);
        $this->db->bind(':value', $value);
        $this->db->bind(':is_admin', $isAdmin ? 1 : 0);
        $this->db->bind(':update_value', $value);
        
        return $this->db->execute();
    }
    
    /**
     * Get all payment settings
     * 
     * @param bool $isAdmin Whether to get admin payment settings (true) or coordinator settings (false)
     * @return array
     */
    public function getAllPaymentSettings($isAdmin = true) {
        // Get the appropriate settings based on whether we want admin or coordinator settings
        $this->db->query('SELECT * FROM payment_settings WHERE is_admin = :is_admin ORDER BY setting_key');
        $this->db->bind(':is_admin', $isAdmin ? 1 : 0);
        
        $results = $this->db->resultSet();
        $settings = [];
        
        foreach ($results as $result) {
            $settings[$result->setting_key] = $result->setting_value;
        }
        
        return $settings;
    }
    
    /**
     * Get all payment settings for a specific coordinator
     * 
     * @param int $coordinatorId Coordinator user ID
     * @return array
     */
    public function getCoordinatorPaymentSettings($coordinatorId) {
        // Check if the coordinator_payment_settings table exists
        $this->db->query("SHOW TABLES LIKE 'coordinator_payment_settings'");
        $tableExists = $this->db->rowCount() > 0;
        
        if (!$tableExists) {
            // If the table doesn't exist, fall back to the old method
            return $this->getAllPaymentSettings(false);
        }
        
        // Get settings for this specific coordinator
        $this->db->query('SELECT * FROM coordinator_payment_settings 
                          WHERE coordinator_id = :coordinator_id 
                          ORDER BY setting_key');
        $this->db->bind(':coordinator_id', $coordinatorId);
        
        $results = $this->db->resultSet();
        $settings = [];
        
        foreach ($results as $result) {
            $settings[$result->setting_key] = $result->setting_value;
        }
        
        return $settings;
    }
    
    /**
     * Get all show payment settings
     * 
     * @param int $showId Show ID
     * @param bool $includeGlobal Whether to include global settings as fallback
     * @param bool $isAdmin Whether to get admin payment settings (true) or coordinator settings (false)
     * @return array
     */
    public function getAllShowPaymentSettings($showId, $includeGlobal = true, $isAdmin = false) {
        $settings = [];
        
        if ($includeGlobal) {
            if ($isAdmin) {
                // For admin settings, use global admin settings
                $settings = $this->getAllPaymentSettings(true);
            } else {
                // For coordinator settings, get the coordinator ID for this show
                $this->db->query('SELECT coordinator_id FROM shows WHERE id = :show_id');
                $this->db->bind(':show_id', $showId);
                $show = $this->db->single();
                
                if ($show && $show->coordinator_id) {
                    // Use this coordinator's specific settings
                    $settings = $this->getCoordinatorPaymentSettings($show->coordinator_id);
                } else {
                    // If we can't find the coordinator, use global coordinator settings
                    $settings = $this->getAllPaymentSettings(false);
                }
            }
        }
        
        // Get show-specific settings
        $this->db->query('SELECT * FROM show_payment_settings 
                          WHERE show_id = :show_id AND is_admin = :is_admin
                          ORDER BY setting_key');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':is_admin', $isAdmin ? 1 : 0);
        
        $results = $this->db->resultSet();
        
        // Override global settings with show-specific ones
        foreach ($results as $result) {
            $settings[$result->setting_key] = $result->setting_value;
        }
        
        return $settings;
    }
    
    /**
     * Create payment record
     * 
     * @param array $data Payment data
     * @return bool|int
     */
    public function createPayment($data) {
        $this->db->query('INSERT INTO payments (user_id, amount, payment_method_id, payment_status, 
                                               payment_reference, payment_type, related_id, notes,
                                               is_manual, processed_by, admin_notes) 
                          VALUES (:user_id, :amount, :payment_method_id, :payment_status, 
                                 :payment_reference, :payment_type, :related_id, :notes,
                                 :is_manual, :processed_by, :admin_notes)');
        
        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':amount', $data['amount']);
        $this->db->bind(':payment_method_id', $data['payment_method_id']);
        $this->db->bind(':payment_status', $data['payment_status']);
        $this->db->bind(':payment_reference', $data['payment_reference'] ?? null);
        $this->db->bind(':payment_type', $data['payment_type']);
        $this->db->bind(':related_id', $data['related_id']);
        $this->db->bind(':notes', $data['notes'] ?? null);
        $this->db->bind(':is_manual', $data['is_manual'] ?? 0);
        $this->db->bind(':processed_by', $data['processed_by'] ?? null);
        $this->db->bind(':admin_notes', $data['admin_notes'] ?? null);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update payment status
     * 
     * @param int $id Payment ID
     * @param string $status New status
     * @param string $reference Payment reference
     * @param string $adminNotes Admin notes for the payment
     * @param int $processedBy User ID of admin/coordinator who processed the payment
     * @return bool
     */
    public function updatePaymentStatus($id, $status, $reference = null, $adminNotes = null, $processedBy = null) {
        $this->db->query('UPDATE payments 
                          SET payment_status = :status, 
                              payment_reference = COALESCE(:reference, payment_reference),
                              admin_notes = COALESCE(:admin_notes, admin_notes),
                              processed_by = COALESCE(:processed_by, processed_by),
                              updated_at = NOW()
                          WHERE id = :id');
        
        $this->db->bind(':status', $status);
        $this->db->bind(':reference', $reference);
        $this->db->bind(':admin_notes', $adminNotes);
        $this->db->bind(':processed_by', $processedBy);
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Update payment record with multiple fields
     * 
     * @param int $id Payment ID
     * @param array $data Array of field => value pairs to update
     * @return bool
     */
    public function updatePayment($id, $data) {
        if (empty($data)) {
            return false;
        }
        
        try {
            $setParts = [];
            $params = [':id' => $id];
            
            foreach ($data as $field => $value) {
                $setParts[] = "$field = :$field";
                $params[":$field"] = $value;
            }
            
            // Always update the updated_at timestamp
            $setParts[] = "updated_at = NOW()";
            
            $sql = 'UPDATE payments SET ' . implode(', ', $setParts) . ' WHERE id = :id';
            
            $this->db->query($sql);
            
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            return $this->db->execute();
            
        } catch (Exception $e) {
            // If there's a column error, try without the problematic columns
            if (strpos($e->getMessage(), 'Unknown column') !== false) {
                // Remove potentially problematic columns and try again
                $safeData = [];
                $knownColumns = ['payment_status', 'payment_reference', 'admin_notes'];
                
                foreach ($data as $field => $value) {
                    if (in_array($field, $knownColumns)) {
                        $safeData[$field] = $value;
                    }
                }
                
                if (!empty($safeData)) {
                    $setParts = [];
                    $params = [':id' => $id];
                    
                    foreach ($safeData as $field => $value) {
                        $setParts[] = "$field = :$field";
                        $params[":$field"] = $value;
                    }
                    
                    $setParts[] = "updated_at = NOW()";
                    $sql = 'UPDATE payments SET ' . implode(', ', $setParts) . ' WHERE id = :id';
                    
                    $this->db->query($sql);
                    
                    foreach ($params as $param => $value) {
                        $this->db->bind($param, $value);
                    }
                    
                    return $this->db->execute();
                }
            }
            
            // Log the error if debug mode is enabled
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("PaymentModel::updatePayment error: " . $e->getMessage());
            }
            
            return false;
        }
    }
    
    /**
     * Get payment by ID
     * 
     * @param int $id Payment ID
     * @return object|bool
     */
    public function getPaymentById($id) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                          pu.name as processor_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          LEFT JOIN users pu ON p.processed_by = pu.id
                          WHERE p.id = :id');
        
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get payments by user
     * 
     * @param int $userId User ID
     * @return array
     */
    public function getPaymentsByUser($userId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          WHERE p.user_id = :user_id
                          ORDER BY p.created_at DESC');
        
        $this->db->bind(':user_id', $userId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get payments by type and related ID
     * 
     * @param string $type Payment type
     * @param int $relatedId Related ID
     * @return array
     */
    public function getPaymentsByTypeAndRelatedId($type, $relatedId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                          pu.name as processor_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          LEFT JOIN users pu ON p.processed_by = pu.id
                          WHERE p.payment_type = :type AND p.related_id = :related_id
                          ORDER BY p.created_at DESC');
        
        $this->db->bind(':type', $type);
        $this->db->bind(':related_id', $relatedId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get payment by registration ID
     * 
     * @param int $registrationId Registration ID
     * @return object|bool Payment object or false if not found
     */
    public function getPaymentByRegistration($registrationId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                          pu.name as processor_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          LEFT JOIN users pu ON p.processed_by = pu.id
                          WHERE p.payment_type = "registration" AND p.related_id = :registration_id
                          AND p.payment_status = "completed"
                          ORDER BY p.created_at DESC
                          LIMIT 1');
        
        $this->db->bind(':registration_id', $registrationId);
        
        return $this->db->single();
    }
    
    /**
     * Get all payments with optional filters
     * 
     * @param array $filters Optional filters
     * @return array
     */
    public function getAllPayments($filters = []) {
        $sql = 'SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                pu.name as processor_name
                FROM payments p
                JOIN payment_methods pm ON p.payment_method_id = pm.id
                JOIN users u ON p.user_id = u.id
                LEFT JOIN users pu ON p.processed_by = pu.id
                WHERE 1=1';
        
        // Apply filters
        if (!empty($filters['payment_status'])) {
            $sql .= ' AND p.payment_status = :payment_status';
        }
        
        if (!empty($filters['payment_type'])) {
            $sql .= ' AND p.payment_type = :payment_type';
        }
        
        if (!empty($filters['payment_method_id'])) {
            $sql .= ' AND p.payment_method_id = :payment_method_id';
        }
        
        if (!empty($filters['is_manual'])) {
            $sql .= ' AND p.is_manual = :is_manual';
        }
        
        if (!empty($filters['processed_by'])) {
            $sql .= ' AND p.processed_by = :processed_by';
        }
        
        if (!empty($filters['start_date'])) {
            $sql .= ' AND p.created_at >= :start_date';
        }
        
        if (!empty($filters['end_date'])) {
            $sql .= ' AND p.created_at <= :end_date';
        }
        
        if (!empty($filters['show_id'])) {
            $sql .= ' AND (
                        (p.payment_type = "registration" AND p.related_id IN (
                            SELECT id FROM registrations WHERE show_id = :show_id
                        ))
                        OR
                        (p.payment_type = "show_listing" AND p.related_id = :show_id)
                    )';
        }
        
        $sql .= ' ORDER BY p.created_at DESC';
        
        // Add limit if specified
        if (!empty($filters['limit'])) {
            $sql .= ' LIMIT :limit';
        }
        
        $this->db->query($sql);
        
        // Bind filter values
        if (!empty($filters['payment_status'])) {
            $this->db->bind(':payment_status', $filters['payment_status']);
        }
        
        if (!empty($filters['payment_type'])) {
            $this->db->bind(':payment_type', $filters['payment_type']);
        }
        
        if (!empty($filters['payment_method_id'])) {
            $this->db->bind(':payment_method_id', $filters['payment_method_id']);
        }
        
        if (!empty($filters['is_manual'])) {
            $this->db->bind(':is_manual', $filters['is_manual']);
        }
        
        if (!empty($filters['processed_by'])) {
            $this->db->bind(':processed_by', $filters['processed_by']);
        }
        
        if (!empty($filters['start_date'])) {
            $this->db->bind(':start_date', $filters['start_date']);
        }
        
        if (!empty($filters['end_date'])) {
            $this->db->bind(':end_date', $filters['end_date']);
        }
        
        if (!empty($filters['show_id'])) {
            $this->db->bind(':show_id', $filters['show_id']);
        }
        
        if (!empty($filters['limit'])) {
            $this->db->bind(':limit', $filters['limit']);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if user is exempt from listing fees
     * 
     * @param int $userId User ID
     * @return bool
     */
    public function isUserExemptFromListingFees($userId) {
        $this->db->query('SELECT exempt_from_listing_fees FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        
        $result = $this->db->single();
        
        return $result && $result->exempt_from_listing_fees;
    }
    
    /**
     * Check if user can create free shows
     * 
     * @param int $userId User ID
     * @return bool
     */
    public function canUserCreateFreeShows($userId) {
        $this->db->query('SELECT can_create_free_shows FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        
        $result = $this->db->single();
        
        return $result && $result->can_create_free_shows;
    }
    
    /**
     * Update user payment permissions
     * 
     * @param int $userId User ID
     * @param bool $canCreateFreeShows Whether user can create free shows
     * @param bool $exemptFromListingFees Whether user is exempt from listing fees
     * @return bool
     */
    public function updateUserPaymentPermissions($userId, $canCreateFreeShows, $exemptFromListingFees) {
        $this->db->query('UPDATE users 
                          SET can_create_free_shows = :can_create_free_shows, 
                              exempt_from_listing_fees = :exempt_from_listing_fees 
                          WHERE id = :id');
        
        $this->db->bind(':can_create_free_shows', $canCreateFreeShows);
        $this->db->bind(':exempt_from_listing_fees', $exemptFromListingFees);
        $this->db->bind(':id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get pending payments
     * 
     * @return array
     */
    public function getPendingPayments() {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name, u.email as user_email
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          WHERE p.payment_status = "pending"
                          ORDER BY p.created_at DESC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get pending registration payments
     * 
     * @return array
     */
    public function getPendingRegistrationPayments() {
        $this->db->query('SELECT r.*, s.name as show_name, v.year, v.make, v.model, 
                                 u.name as owner_name, u.email as owner_email,
                                 pm.name as payment_method_name
                          FROM registrations r
                          JOIN shows s ON r.show_id = s.id
                          JOIN vehicles v ON r.vehicle_id = v.id
                          JOIN users u ON r.owner_id = u.id
                          JOIN payment_methods pm ON r.payment_method_id = pm.id
                          WHERE r.payment_status = "pending"
                          ORDER BY r.created_at DESC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Process a manual payment
     * 
     * @param array $data Payment data
     * @return bool|int
     */
    public function processManualPayment($data) {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Create payment record
            $paymentId = $this->createPayment([
                'user_id' => $data['user_id'] ?? null,
                'amount' => $data['amount'],
                'payment_method_id' => $data['payment_method_id'],
                'payment_status' => 'completed',
                'payment_reference' => $data['payment_reference'] ?? null,
                'payment_type' => $data['payment_type'] ?? 'registration',
                'related_id' => $data['related_id'] ?? ($data['registration_id'] ?? null),
                'notes' => $data['notes'] ?? null,
                'is_manual' => 1,
                'processed_by' => $data['processed_by'],
                'admin_notes' => $data['admin_notes'] ?? null
            ]);
            
            if (!$paymentId) {
                throw new Exception('Failed to create payment record');
            }
            
            // Update related record based on payment type
            if ($data['payment_type'] == 'registration') {
                $this->db->query('UPDATE registrations 
                                SET payment_status = "completed", 
                                    payment_method_id = :payment_method_id,
                                    updated_at = NOW()
                                WHERE id = :id');
                $this->db->bind(':payment_method_id', $data['payment_method_id']);
                $this->db->bind(':id', $data['related_id']);
                
                if (!$this->db->execute()) {
                    throw new Exception('Failed to update registration payment status');
                }
            } else if ($data['payment_type'] == 'show_listing') {
                $this->db->query('UPDATE shows 
                                SET listing_paid = 1, 
                                    updated_at = NOW()
                                WHERE id = :id');
                $this->db->bind(':id', $data['related_id']);
                
                if (!$this->db->execute()) {
                    throw new Exception('Failed to update show listing payment status');
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            return $paymentId;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error processing manual payment: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get payment statistics for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowPaymentStats($showId) {
        $stats = [
            'total_payments' => 0,
            'total_amount' => 0,
            'completed_payments' => 0,
            'completed_amount' => 0,
            'pending_payments' => 0,
            'pending_amount' => 0,
            'payment_methods' => []
        ];
        
        // Get registration payments
        $this->db->query('SELECT p.payment_status, p.amount, pm.name as method_name, COUNT(*) as count, SUM(p.amount) as total
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN registrations r ON p.related_id = r.id
                          WHERE p.payment_type = "registration" AND r.show_id = :show_id
                          GROUP BY p.payment_status, pm.name');
        $this->db->bind(':show_id', $showId);
        $results = $this->db->resultSet();
        
        foreach ($results as $result) {
            $stats['total_payments'] += $result->count;
            $stats['total_amount'] += $result->total;
            
            if ($result->payment_status == 'completed') {
                $stats['completed_payments'] += $result->count;
                $stats['completed_amount'] += $result->total;
            } else if ($result->payment_status == 'pending') {
                $stats['pending_payments'] += $result->count;
                $stats['pending_amount'] += $result->total;
            }
            
            if (!isset($stats['payment_methods'][$result->method_name])) {
                $stats['payment_methods'][$result->method_name] = [
                    'count' => 0,
                    'total' => 0
                ];
            }
            
            $stats['payment_methods'][$result->method_name]['count'] += $result->count;
            $stats['payment_methods'][$result->method_name]['total'] += $result->total;
        }
        
        // Add show listing payment if exists
        $this->db->query('SELECT p.payment_status, p.amount, pm.name as method_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          WHERE p.payment_type = "show_listing" AND p.related_id = :show_id
                          ORDER BY p.created_at DESC
                          LIMIT 1');
        $this->db->bind(':show_id', $showId);
        $listingPayment = $this->db->single();
        
        if ($listingPayment) {
            $stats['total_payments'] += 1;
            $stats['total_amount'] += $listingPayment->amount;
            
            if ($listingPayment->payment_status == 'completed') {
                $stats['completed_payments'] += 1;
                $stats['completed_amount'] += $listingPayment->amount;
            } else if ($listingPayment->payment_status == 'pending') {
                $stats['pending_payments'] += 1;
                $stats['pending_amount'] += $listingPayment->amount;
            }
            
            if (!isset($stats['payment_methods'][$listingPayment->method_name])) {
                $stats['payment_methods'][$listingPayment->method_name] = [
                    'count' => 0,
                    'total' => 0
                ];
            }
            
            $stats['payment_methods'][$listingPayment->method_name]['count'] += 1;
            $stats['payment_methods'][$listingPayment->method_name]['total'] += $listingPayment->amount;
        }
        
        return $stats;
    }
    
    /**
     * Migrate payment settings to the new admin/coordinator structure
     * 
     * @return bool
     */
    public function migratePaymentSettings() {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Check if is_admin column exists in payment_settings
            $this->db->query("SELECT COUNT(*) as column_exists FROM INFORMATION_SCHEMA.COLUMNS 
                             WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'payment_settings' 
                             AND COLUMN_NAME = 'is_admin'");
            $result = $this->db->single();
            
            if (!$result || $result->column_exists == 0) {
                // Add is_admin column to payment_settings
                $this->db->query("ALTER TABLE payment_settings ADD COLUMN is_admin TINYINT(1) NOT NULL DEFAULT 1");
                $this->db->execute();
            }
            
            // Check if is_admin column exists in show_payment_settings
            $this->db->query("SELECT COUNT(*) as column_exists FROM INFORMATION_SCHEMA.COLUMNS 
                             WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'show_payment_settings' 
                             AND COLUMN_NAME = 'is_admin'");
            $result = $this->db->single();
            
            if (!$result || $result->column_exists == 0) {
                // Add is_admin column to show_payment_settings
                $this->db->query("ALTER TABLE show_payment_settings ADD COLUMN is_admin TINYINT(1) NOT NULL DEFAULT 0");
                $this->db->execute();
            }
            
            // Create duplicate entries for coordinator settings
            $this->db->query("INSERT INTO payment_settings (setting_key, setting_value, is_admin)
                             SELECT setting_key, setting_value, 0
                             FROM payment_settings
                             WHERE setting_key IN ('cashapp_id', 'venmo_id', 'currency', 'default_registration_fee', 'default_show_listing_fee')
                             AND is_admin = 1
                             ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $this->db->execute();
            
            // Create duplicate entries for admin settings in show_payment_settings
            $this->db->query("INSERT INTO show_payment_settings (show_id, setting_key, setting_value, is_admin)
                             SELECT show_id, setting_key, setting_value, 1
                             FROM show_payment_settings
                             WHERE setting_key IN ('cashapp_id', 'venmo_id')
                             AND is_admin = 0
                             ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $this->db->execute();
            
            // Update existing payment settings to mark them as admin settings
            $this->db->query("UPDATE payment_settings SET is_admin = 1 WHERE is_admin IS NULL");
            $this->db->execute();
            
            // Update existing show payment settings to mark them as coordinator settings
            $this->db->query("UPDATE show_payment_settings SET is_admin = 0 WHERE is_admin IS NULL");
            $this->db->execute();
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error migrating payment settings: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get all payments with user information for admin dashboard
     * 
     * @return array
     */
    public function getAllPaymentsWithUserInfo() {
        $this->db->query('SELECT p.*, 
                                 u.name as user_name, 
                                 u.email as user_email,
                                 pm.name as payment_method_name,
                                 CASE 
                                     WHEN p.payment_type = "registration" THEN CONCAT("Registration #", r.registration_number)
                                     WHEN p.payment_type = "show_listing" THEN CONCAT("Show: ", s.name)
                                     ELSE p.notes
                                 END as description
                          FROM payments p
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
                          LEFT JOIN registrations r ON p.payment_type = "registration" AND p.related_id = r.id
                          LEFT JOIN shows s ON p.payment_type = "show_listing" AND p.related_id = s.id
                          ORDER BY p.created_at DESC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get payment statistics for admin dashboard
     * 
     * @return object
     */
    public function getPaymentStatistics() {
        // Get total payments
        $this->db->query('SELECT 
                            COUNT(*) as total_payments,
                            SUM(amount) as total_amount,
                            COUNT(CASE WHEN payment_status = "completed" THEN 1 END) as completed_payments,
                            SUM(CASE WHEN payment_status = "completed" THEN amount ELSE 0 END) as completed_amount,
                            COUNT(CASE WHEN payment_status = "pending" THEN 1 END) as pending_payments,
                            SUM(CASE WHEN payment_status = "pending" THEN amount ELSE 0 END) as pending_amount,
                            COUNT(CASE WHEN payment_status = "rejected" THEN 1 END) as rejected_payments,
                            COUNT(CASE WHEN payment_type = "registration" THEN 1 END) as registration_payments,
                            COUNT(CASE WHEN payment_type = "show_listing" THEN 1 END) as listing_payments
                          FROM payments');
        
        return $this->db->single();
    }
    
    /**
     * Get recent payments for admin dashboard
     * 
     * @param int $days Number of days to look back
     * @return array
     */
    public function getRecentPayments($days = 30) {
        $this->db->query('SELECT p.*, 
                                 u.name as user_name, 
                                 u.email as user_email,
                                 pm.name as payment_method_name
                          FROM payments p
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
                          WHERE p.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                          ORDER BY p.created_at DESC
                          LIMIT 50');
        
        $this->db->bind(':days', $days);
        return $this->db->resultSet();
    }
    
    /**
     * Get pending payments count
     * 
     * @return int
     */
    public function getPendingPaymentsCount() {
        $this->db->query('SELECT COUNT(*) as count FROM payments WHERE payment_status = "pending"');
        $result = $this->db->single();
        return $result ? $result->count : 0;
    }
    
    /**
     * Get detailed payment information by ID
     * 
     * @param int $id Payment ID
     * @return object|false
     */
    public function getPaymentDetailsById($id) {
        $this->db->query('SELECT p.*, 
                                 u.name as user_name, 
                                 u.email as user_email,
                                 u.phone as user_phone,
                                 pm.name as payment_method_name,
                                 pm.description as payment_method_description,
                                 processed_by_user.name as processed_by_name,
                                 CASE 
                                     WHEN p.payment_type = "registration" THEN r.registration_number
                                     WHEN p.payment_type = "show_listing" THEN s.name
                                     ELSE NULL
                                 END as related_name,
                                 CASE 
                                     WHEN p.payment_type = "registration" THEN CONCAT(v.year, " ", v.make, " ", v.model)
                                     WHEN p.payment_type = "show_listing" THEN s.description
                                     ELSE NULL
                                 END as related_description
                          FROM payments p
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
                          LEFT JOIN users processed_by_user ON p.processed_by = processed_by_user.id
                          LEFT JOIN registrations r ON p.payment_type = "registration" AND p.related_id = r.id
                          LEFT JOIN vehicles v ON r.vehicle_id = v.id
                          LEFT JOIN shows s ON p.payment_type = "show_listing" AND p.related_id = s.id
                          WHERE p.id = :id');
        
        $this->db->bind(':id', $id);
        return $this->db->single();
    }
    
    /**
     * Get total payment count
     * 
     * @return int Total number of payments
     */
    public function getPaymentCount() {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM payments');
            $result = $this->db->single();
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log("Error in PaymentModel::getPaymentCount: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get payment status statistics
     * 
     * @return array Array of status counts
     */
    public function getPaymentStatusStatistics() {
        try {
            $this->db->query('SELECT payment_status as status, COUNT(*) as count 
                             FROM payments 
                             GROUP BY payment_status 
                             ORDER BY count DESC');
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in PaymentModel::getPaymentStatusStatistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get payment counts for admin dashboard
     *
     * @return array Payment counts by status
     */
    public function getPaymentCounts() {
        try {
            $counts = [
                'total' => 0,
                'pending' => 0,
                'completed' => 0,
                'failed' => 0
            ];

            // Get total count
            $this->db->query('SELECT COUNT(*) as total FROM payments');
            $totalResult = $this->db->single();
            $counts['total'] = $totalResult->total ?? 0;

            // Get counts by status
            $this->db->query('SELECT payment_status, COUNT(*) as count
                             FROM payments
                             GROUP BY payment_status');
            $statusResults = $this->db->resultSet();

            foreach ($statusResults as $statusResult) {
                if (isset($counts[$statusResult->payment_status])) {
                    $counts[$statusResult->payment_status] = $statusResult->count;
                }
            }

            return $counts;

        } catch (Exception $e) {
            error_log('Error in PaymentModel::getPaymentCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'pending' => 0,
                'completed' => 0,
                'failed' => 0
            ];
        }
    }

    /**
     * Get payment statistics for admin dashboard
     *
     * @return array Payment statistics
     */
    public function getPaymentStats() {
        try {
            $stats = [
                'total_revenue' => 0,
                'monthly_revenue' => 0,
                'avg_payment' => 0,
                'today_count' => 0
            ];

            // Get total revenue
            $this->db->query('SELECT SUM(amount) as total_revenue
                             FROM payments
                             WHERE payment_status = "completed"');
            $revenueResult = $this->db->single();
            $stats['total_revenue'] = $revenueResult->total_revenue ?? 0;

            // Get monthly revenue
            $this->db->query('SELECT SUM(amount) as monthly_revenue
                             FROM payments
                             WHERE payment_status = "completed"
                             AND MONTH(created_at) = MONTH(NOW())
                             AND YEAR(created_at) = YEAR(NOW())');
            $monthlyResult = $this->db->single();
            $stats['monthly_revenue'] = $monthlyResult->monthly_revenue ?? 0;

            // Get average payment
            $this->db->query('SELECT AVG(amount) as avg_payment
                             FROM payments
                             WHERE payment_status = "completed"');
            $avgResult = $this->db->single();
            $stats['avg_payment'] = $avgResult->avg_payment ?? 0;

            // Get today's count
            $this->db->query('SELECT COUNT(*) as today_count
                             FROM payments
                             WHERE DATE(created_at) = CURDATE()');
            $todayResult = $this->db->single();
            $stats['today_count'] = $todayResult->today_count ?? 0;

            return $stats;

        } catch (Exception $e) {
            error_log('Error in PaymentModel::getPaymentStats: ' . $e->getMessage());
            return [
                'total_revenue' => 0,
                'monthly_revenue' => 0,
                'avg_payment' => 0,
                'today_count' => 0
            ];
        }
    }

    /**
     * Get paginated payments for admin dashboard
     *
     * @param int $page Page number (1-based)
     * @param int $perPage Records per page
     * @param string $search Search term
     * @param string $statusFilter Status filter
     * @param string $typeFilter Type filter
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC/DESC)
     * @return array Paginated results with metadata
     */
    public function getPaginatedPayments($page = 1, $perPage = 20, $search = '', $statusFilter = 'all', $typeFilter = '', $orderBy = 'created_at', $orderDir = 'DESC') {
        $startTime = microtime(true);

        try {
            // Validate parameters
            $page = max(1, (int)$page);
            $perPage = min(100, max(1, (int)$perPage));
            $offset = ($page - 1) * $perPage;

            // Validate order by field
            $allowedOrderFields = ['id', 'created_at', 'amount', 'payment_status', 'payment_type', 'user_name'];
            if (!in_array($orderBy, $allowedOrderFields)) {
                $orderBy = 'created_at';
            }

            $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

            // Build WHERE conditions
            $whereConditions = ['1=1'];
            $bindParams = [];

            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(u.name LIKE :search_user OR p.payment_reference LIKE :search_ref OR p.amount LIKE :search_amount OR pm.name LIKE :search_method)';
                $bindParams['search_user'] = '%' . $search . '%';
                $bindParams['search_ref'] = '%' . $search . '%';
                $bindParams['search_amount'] = '%' . $search . '%';
                $bindParams['search_method'] = '%' . $search . '%';
            }

            // Status filter
            if ($statusFilter !== 'all') {
                $whereConditions[] = 'p.payment_status = :status_filter';
                $bindParams['status_filter'] = $statusFilter;
            }

            // Type filter
            if (!empty($typeFilter)) {
                $whereConditions[] = 'p.payment_type = :type_filter';
                $bindParams['type_filter'] = $typeFilter;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            // Count total records
            $countQuery = "SELECT COUNT(*) as total
                          FROM payments p
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
                          $whereClause";

            $this->db->query($countQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }

            $totalResult = $this->db->single();
            $totalRecords = $totalResult->total ?? 0;

            // Get paginated data
            $dataQuery = "SELECT p.*,
                         u.name as user_name,
                         u.email as user_email,
                         pm.name as payment_method_name,
                         CASE
                             WHEN p.payment_type = 'registration' THEN CONCAT('Registration #', r.registration_number)
                             WHEN p.payment_type = 'show_listing' THEN CONCAT('Show: ', s.name)
                             ELSE p.notes
                         END as description
                         FROM payments p
                         LEFT JOIN users u ON p.user_id = u.id
                         LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
                         LEFT JOIN registrations r ON p.payment_type = 'registration' AND p.related_id = r.id
                         LEFT JOIN shows s ON p.payment_type = 'show_listing' AND p.related_id = s.id
                         $whereClause
                         ORDER BY $orderBy $orderDir
                         LIMIT :limit OFFSET :offset";

            $this->db->query($dataQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
            $this->db->bind(':offset', $offset, PDO::PARAM_INT);

            $payments = $this->db->resultSet();

            // Calculate pagination metadata
            $totalPages = ceil($totalRecords / $perPage);
            $startRecord = $totalRecords > 0 ? $offset + 1 : 0;
            $endRecord = min($offset + $perPage, $totalRecords);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 1);

            error_log("PaymentModel::getPaginatedPayments - Page: $page, Payments: " . count($payments) . ", Time: {$executionTime}ms");

            return [
                'payments' => $payments,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_payments' => $totalRecords,
                    'start_record' => $startRecord,
                    'end_record' => $endRecord,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ]
            ];

        } catch (Exception $e) {
            error_log('Error in PaymentModel::getPaginatedPayments: ' . $e->getMessage());
            return [
                'payments' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $perPage,
                    'total_pages' => 0,
                    'total_payments' => 0,
                    'start_record' => 0,
                    'end_record' => 0,
                    'has_prev' => false,
                    'has_next' => false
                ]
            ];
        }
    }

    /**
     * Get user payment counts for dashboard
     *
     * @param int $userId User ID
     * @return array Payment counts by status
     */
    public function getUserPaymentCounts($userId) {
        try {
            $counts = [
                'total' => 0,
                'pending' => 0,
                'completed' => 0,
                'failed' => 0
            ];

            // Get total count
            $this->db->query('SELECT COUNT(*) as total FROM payments WHERE user_id = :user_id');
            $this->db->bind(':user_id', $userId);
            $totalResult = $this->db->single();
            $counts['total'] = $totalResult->total ?? 0;

            // Get counts by status
            $this->db->query('SELECT payment_status, COUNT(*) as count
                             FROM payments
                             WHERE user_id = :user_id
                             GROUP BY payment_status');
            $this->db->bind(':user_id', $userId);
            $statusResults = $this->db->resultSet();

            foreach ($statusResults as $statusResult) {
                if (isset($counts[$statusResult->payment_status])) {
                    $counts[$statusResult->payment_status] = $statusResult->count;
                }
            }

            return $counts;

        } catch (Exception $e) {
            error_log('Error in PaymentModel::getUserPaymentCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'pending' => 0,
                'completed' => 0,
                'failed' => 0
            ];
        }
    }

    /**
     * Get user payment statistics for dashboard
     *
     * @param int $userId User ID
     * @return array Payment statistics
     */
    public function getUserPaymentStats($userId) {
        try {
            $stats = [
                'total_spent' => 0,
                'this_year' => 0,
                'this_month' => 0,
                'avg_payment' => 0,
                'last_payment_date' => null
            ];

            // Get total spent
            $this->db->query('SELECT SUM(amount) as total_spent
                             FROM payments
                             WHERE user_id = :user_id AND payment_status = "completed"');
            $this->db->bind(':user_id', $userId);
            $totalResult = $this->db->single();
            $stats['total_spent'] = $totalResult->total_spent ?? 0;

            // Get this year's spending
            $this->db->query('SELECT SUM(amount) as this_year
                             FROM payments
                             WHERE user_id = :user_id AND payment_status = "completed"
                             AND YEAR(created_at) = YEAR(NOW())');
            $this->db->bind(':user_id', $userId);
            $yearResult = $this->db->single();
            $stats['this_year'] = $yearResult->this_year ?? 0;

            // Get this month's spending
            $this->db->query('SELECT SUM(amount) as this_month
                             FROM payments
                             WHERE user_id = :user_id AND payment_status = "completed"
                             AND MONTH(created_at) = MONTH(NOW())
                             AND YEAR(created_at) = YEAR(NOW())');
            $this->db->bind(':user_id', $userId);
            $monthResult = $this->db->single();
            $stats['this_month'] = $monthResult->this_month ?? 0;

            // Get average payment
            $this->db->query('SELECT AVG(amount) as avg_payment
                             FROM payments
                             WHERE user_id = :user_id AND payment_status = "completed"');
            $this->db->bind(':user_id', $userId);
            $avgResult = $this->db->single();
            $stats['avg_payment'] = $avgResult->avg_payment ?? 0;

            // Get last payment date
            $this->db->query('SELECT MAX(created_at) as last_payment_date
                             FROM payments
                             WHERE user_id = :user_id AND payment_status = "completed"');
            $this->db->bind(':user_id', $userId);
            $lastResult = $this->db->single();
            $stats['last_payment_date'] = $lastResult->last_payment_date;

            return $stats;

        } catch (Exception $e) {
            error_log('Error in PaymentModel::getUserPaymentStats: ' . $e->getMessage());
            return [
                'total_spent' => 0,
                'this_year' => 0,
                'this_month' => 0,
                'avg_payment' => 0,
                'last_payment_date' => null
            ];
        }
    }

    /**
     * Get paginated user payments
     *
     * @param int $userId User ID
     * @param int $page Page number (1-based)
     * @param int $perPage Records per page
     * @param string $search Search term
     * @param string $statusFilter Status filter
     * @param string $typeFilter Type filter
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC/DESC)
     * @return array Paginated results with metadata
     */
    public function getPaginatedUserPayments($userId, $page = 1, $perPage = 20, $search = '', $statusFilter = 'all', $typeFilter = '', $orderBy = 'created_at', $orderDir = 'DESC') {
        $startTime = microtime(true);

        try {
            // Validate parameters
            $page = max(1, (int)$page);
            $perPage = min(100, max(1, (int)$perPage));
            $offset = ($page - 1) * $perPage;

            // Validate order by field
            $allowedOrderFields = ['id', 'created_at', 'amount', 'payment_status', 'payment_type'];
            if (!in_array($orderBy, $allowedOrderFields)) {
                $orderBy = 'created_at';
            }

            $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

            // Build WHERE conditions
            $whereConditions = ['p.user_id = :user_id'];
            $bindParams = ['user_id' => $userId];

            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(p.payment_reference LIKE :search_ref OR p.amount LIKE :search_amount OR pm.name LIKE :search_method OR p.notes LIKE :search_notes)';
                $bindParams['search_ref'] = '%' . $search . '%';
                $bindParams['search_amount'] = '%' . $search . '%';
                $bindParams['search_method'] = '%' . $search . '%';
                $bindParams['search_notes'] = '%' . $search . '%';
            }

            // Status filter
            if ($statusFilter !== 'all') {
                $whereConditions[] = 'p.payment_status = :status_filter';
                $bindParams['status_filter'] = $statusFilter;
            }

            // Type filter
            if (!empty($typeFilter)) {
                $whereConditions[] = 'p.payment_type = :type_filter';
                $bindParams['type_filter'] = $typeFilter;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            // Count total records
            $countQuery = "SELECT COUNT(*) as total
                          FROM payments p
                          LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
                          $whereClause";

            $this->db->query($countQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }

            $totalResult = $this->db->single();
            $totalRecords = $totalResult->total ?? 0;

            // Get paginated data
            $dataQuery = "SELECT p.*,
                         pm.name as payment_method_name,
                         CASE
                             WHEN p.payment_type = 'registration' THEN CONCAT('Registration #', r.registration_number)
                             WHEN p.payment_type = 'show_listing' THEN CONCAT('Show: ', s.name)
                             ELSE p.notes
                         END as description
                         FROM payments p
                         LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
                         LEFT JOIN registrations r ON p.payment_type = 'registration' AND p.related_id = r.id
                         LEFT JOIN shows s ON p.payment_type = 'show_listing' AND p.related_id = s.id
                         $whereClause
                         ORDER BY $orderBy $orderDir
                         LIMIT :limit OFFSET :offset";

            $this->db->query($dataQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
            $this->db->bind(':offset', $offset, PDO::PARAM_INT);

            $payments = $this->db->resultSet();

            // Calculate pagination metadata
            $totalPages = ceil($totalRecords / $perPage);
            $startRecord = $totalRecords > 0 ? $offset + 1 : 0;
            $endRecord = min($offset + $perPage, $totalRecords);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 1);

            error_log("PaymentModel::getPaginatedUserPayments - User: $userId, Page: $page, Payments: " . count($payments) . ", Time: {$executionTime}ms");

            return [
                'payments' => $payments,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_payments' => $totalRecords,
                    'start_record' => $startRecord,
                    'end_record' => $endRecord,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ]
            ];

        } catch (Exception $e) {
            error_log('Error in PaymentModel::getPaginatedUserPayments: ' . $e->getMessage());
            return [
                'payments' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $perPage,
                    'total_pages' => 0,
                    'total_payments' => 0,
                    'start_record' => 0,
                    'end_record' => 0,
                    'has_prev' => false,
                    'has_next' => false
                ]
            ];
        }
    }
}