<?php
/**
 * Test Targeted Image Cleanup
 * 
 * This script tests the enhanced image cleanup to ensure it only removes
 * images linked to demo vehicles and demo shows, protecting real images.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once 'generate_demo_data.php';

echo "<h1>Targeted Image Cleanup Test</h1>";
echo "<p>Testing the enhanced image cleanup to ensure it only removes demo images...</p>";

try {
    $db = new Database();
    $generator = new DemoDataGenerator();
    
    echo "<h2>Current Images in Database</h2>";
    
    // Get all images
    $db->query("SELECT COUNT(*) as count FROM images");
    $totalResult = $db->single();
    $totalImages = $totalResult->count ?? 0;
    
    echo "<p><strong>Total Images:</strong> {$totalImages}</p>";
    
    if ($totalImages > 0) {
        // Analyze images by type
        echo "<h3>Image Analysis</h3>";
        
        // Check for demo vehicle images
        $db->query("SELECT COUNT(*) as count FROM images i 
                    JOIN vehicles v ON i.vehicle_id = v.id 
                    WHERE v.vin REGEXP '^DEMO[0-9]{13}$' AND v.license_plate REGEXP '^DEMO[0-9]{3}$'");
        $demoVehicleImagesResult = $db->single();
        $demoVehicleImages = $demoVehicleImagesResult->count ?? 0;
        
        // Check for demo show images
        $demoShowNames = [
            'Southern California Classic Car Showcase',
            'Miami Beach Exotic Car Festival',
            'Texas Muscle Car Madness',
            'Arizona Desert Classic Concours',
            'Atlanta Import Tuner Expo',
            'Rocky Mountain Vintage Rally',
            'Pacific Northwest Euro Fest',
            'Music City Hot Rod Nationals',
            'Charlotte Motor Speedway Car Show',
            'Las Vegas Strip Supercar Spectacular'
        ];
        
        $demoShowImages = 0;
        foreach ($demoShowNames as $showName) {
            $stmt = $db->getConnection()->prepare("SELECT COUNT(*) as count FROM images i 
                                                   JOIN shows s ON i.show_id = s.id 
                                                   WHERE s.name = ?");
            $stmt->execute([$showName]);
            $result = $stmt->fetch(PDO::FETCH_OBJ);
            $demoShowImages += $result->count ?? 0;
        }
        
        // Check for demo pattern images
        $patterns = ['demo_vehicle_%', 'demo_show_%', 'demo_car_%', 'demo_event_%'];
        $demoPatternImages = 0;
        foreach ($patterns as $pattern) {
            $stmt = $db->getConnection()->prepare("SELECT COUNT(*) as count FROM images WHERE file_name LIKE ?");
            $stmt->execute([$pattern]);
            $result = $stmt->fetch(PDO::FETCH_OBJ);
            $demoPatternImages += $result->count ?? 0;
        }
        
        // Check for images from demo users (potentially unsafe)
        $db->query("SELECT COUNT(*) as count FROM images i 
                    JOIN users u ON i.user_id = u.id 
                    WHERE u.email LIKE '%@gmai1.com' OR u.email LIKE '%@yaho0.com' OR u.email LIKE '%@hotmai1.com' OR u.email LIKE '%@out1ook.com' OR u.email LIKE '%@test-example.com'");
        $demoUserImagesResult = $db->single();
        $demoUserImages = $demoUserImagesResult->count ?? 0;
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Image Type</th><th>Count</th><th>Cleanup Status</th></tr>";
        echo "<tr><td>Total Images</td><td>{$totalImages}</td><td>-</td></tr>";
        echo "<tr><td>Demo Vehicle Images</td><td>{$demoVehicleImages}</td><td><span style='color: red;'>WILL BE DELETED</span></td></tr>";
        echo "<tr><td>Demo Show Images</td><td>{$demoShowImages}</td><td><span style='color: red;'>WILL BE DELETED</span></td></tr>";
        echo "<tr><td>Demo Pattern Images</td><td>{$demoPatternImages}</td><td><span style='color: red;'>WILL BE DELETED</span></td></tr>";
        echo "<tr><td>Demo User Images</td><td>{$demoUserImages}</td><td><span style='color: green;'>PROTECTED</span></td></tr>";
        echo "</table>";
        
        $totalDemoImages = $demoVehicleImages + $demoShowImages + $demoPatternImages;
        $protectedImages = $totalImages - $totalDemoImages;
        
        echo "<h3>Cleanup Impact</h3>";
        echo "<ul>";
        echo "<li><strong>Images to be deleted:</strong> {$totalDemoImages} (demo vehicle/show/pattern images)</li>";
        echo "<li><strong>Images to be protected:</strong> {$protectedImages} (all other images)</li>";
        echo "<li><strong>Demo user images:</strong> {$demoUserImages} (PROTECTED - could be real images uploaded by demo users to real shows)</li>";
        echo "</ul>";
        
        if ($totalDemoImages > 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>🎯 Targeted Cleanup Available</strong><br>";
            echo "The cleanup will only remove images specifically linked to demo vehicles and demo shows.";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>✅ No Demo Images Found!</strong> No demo vehicle or show images to clean up.";
            echo "</div>";
        }
        
        // Show sample images for verification
        echo "<h3>Sample Images (First 10)</h3>";
        $db->query("SELECT i.id, i.file_name, i.file_path, i.user_id, i.vehicle_id, i.show_id, 
                           v.vin, v.license_plate, s.name as show_name, u.email
                    FROM images i 
                    LEFT JOIN vehicles v ON i.vehicle_id = v.id 
                    LEFT JOIN shows s ON i.show_id = s.id 
                    LEFT JOIN users u ON i.user_id = u.id 
                    ORDER BY i.created_at DESC LIMIT 10");
        $sampleImages = $db->resultSet();
        
        if (!empty($sampleImages)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
            echo "<tr><th>ID</th><th>Filename</th><th>User Email</th><th>Vehicle VIN</th><th>Show Name</th><th>Status</th></tr>";
            
            foreach ($sampleImages as $image) {
                $status = 'SAFE';
                $statusColor = 'green';
                
                // Check if this image would be deleted
                if ($image->vin && preg_match('/^DEMO[0-9]{13}$/', $image->vin) && 
                    $image->license_plate && preg_match('/^DEMO[0-9]{3}$/', $image->license_plate)) {
                    $status = 'DEMO VEHICLE';
                    $statusColor = 'red';
                } elseif ($image->show_name && in_array($image->show_name, $demoShowNames)) {
                    $status = 'DEMO SHOW';
                    $statusColor = 'red';
                } elseif ($image->file_name && (
                    strpos($image->file_name, 'demo_vehicle_') === 0 ||
                    strpos($image->file_name, 'demo_show_') === 0 ||
                    strpos($image->file_name, 'demo_car_') === 0 ||
                    strpos($image->file_name, 'demo_event_') === 0
                )) {
                    $status = 'DEMO PATTERN';
                    $statusColor = 'red';
                }
                
                echo "<tr>";
                echo "<td>{$image->id}</td>";
                echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
                echo "<td>" . htmlspecialchars($image->email ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($image->vin ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($image->show_name ?? 'N/A') . "</td>";
                echo "<td style='color: {$statusColor}; font-weight: bold;'>{$status}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Test actions
        if ($totalDemoImages > 0) {
            echo "<h2>Test Actions</h2>";
            echo "<p><a href='?action=test_image_cleanup' class='btn btn-warning'>🎯 Test Targeted Image Cleanup</a></p>";
            echo "<p><a href='?action=safe_cleanup' class='btn btn-primary'>🛡️ Run Safe Cleanup</a></p>";
        }
        
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>ℹ️ No Images Found</strong> The database contains no images.";
        echo "</div>";
    }
    
    echo "<p><a href='?' class='btn btn-secondary'>🔄 Refresh Status</a></p>";
    
    // Handle test actions
    if (isset($_GET['action'])) {
        echo "<hr>";
        
        if ($_GET['action'] === 'test_image_cleanup') {
            echo "<h2>Testing Targeted Image Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            // Get demo user IDs
            $db->query("SELECT id FROM users WHERE email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com'");
            $demoUsers = $db->resultSet();
            $demoUserIds = array_column($demoUsers, 'id');
            
            // Use reflection to call the private method for testing
            $reflection = new ReflectionClass($generator);
            $method = $reflection->getMethod('cleanupDemoImages');
            $method->setAccessible(true);
            
            $deletedImages = $method->invoke($generator, $demoUserIds, true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<p><strong>Result:</strong> Deleted {$deletedImages} demo images</p>";
            echo "<p><a href='?'>← Back to Status</a></p>";
            
        } elseif ($_GET['action'] === 'safe_cleanup') {
            echo "<h2>Running Safe Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            $deletedCount = $generator->safeCleanupDemoData(true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<p><strong>Result:</strong> Total {$deletedCount} demo records deleted safely</p>";
            echo "<p><a href='?'>← Back to Status</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 15px 0;
    font-size: 14px;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-warning { background-color: #ffc107; color: black; }

.btn:hover { opacity: 0.8; }
</style>
