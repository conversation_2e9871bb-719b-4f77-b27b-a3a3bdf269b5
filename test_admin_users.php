<?php
/**
 * Test script for Admin Users functionality
 * 
 * This script tests the optimized admin users page functionality
 * without requiring a web server.
 */

// Include the application configuration
require_once 'config/config.php';
require_once 'models/UserModel.php';

echo "Testing Admin Users Optimization...\n\n";

try {
    $userModel = new UserModel();
    
    // Test 1: Basic pagination (no filters)
    echo "Test 1: Basic pagination (page 1, 20 per page)\n";
    $result1 = $userModel->getPaginatedUsers(1, 20);
    echo "- Found {$result1['pagination']['total_users']} total users\n";
    echo "- Showing {$result1['pagination']['start_record']}-{$result1['pagination']['end_record']}\n";
    echo "- Execution time: {$result1['performance']['execution_time_ms']}ms\n";
    echo "- Status: " . ($result1['performance']['execution_time_ms'] < 100 ? "PASS" : "SLOW") . "\n\n";
    
    // Test 2: Search functionality
    echo "Test 2: Search by name\n";
    $result2 = $userModel->getPaginatedUsers(1, 20, 'admin');
    echo "- Found {$result2['pagination']['total_users']} users matching 'admin'\n";
    echo "- Execution time: {$result2['performance']['execution_time_ms']}ms\n";
    echo "- Status: " . ($result2['performance']['execution_time_ms'] < 100 ? "PASS" : "SLOW") . "\n\n";
    
    // Test 3: Role filter
    echo "Test 3: Filter by role (admin)\n";
    $result3 = $userModel->getPaginatedUsers(1, 20, '', 'admin');
    echo "- Found {$result3['pagination']['total_users']} admin users\n";
    echo "- Execution time: {$result3['performance']['execution_time_ms']}ms\n";
    echo "- Status: " . ($result3['performance']['execution_time_ms'] < 100 ? "PASS" : "SLOW") . "\n\n";
    
    // Test 4: Status filter
    echo "Test 4: Filter by status (active)\n";
    $result4 = $userModel->getPaginatedUsers(1, 20, '', '', 'active');
    echo "- Found {$result4['pagination']['total_users']} active users\n";
    echo "- Execution time: {$result4['performance']['execution_time_ms']}ms\n";
    echo "- Status: " . ($result4['performance']['execution_time_ms'] < 100 ? "PASS" : "SLOW") . "\n\n";
    
    // Test 5: Combined search and filter
    echo "Test 5: Combined search and filter\n";
    $result5 = $userModel->getPaginatedUsers(1, 20, 'test', 'user', 'active');
    echo "- Found {$result5['pagination']['total_users']} users matching criteria\n";
    echo "- Execution time: {$result5['performance']['execution_time_ms']}ms\n";
    echo "- Status: " . ($result5['performance']['execution_time_ms'] < 100 ? "PASS" : "SLOW") . "\n\n";
    
    // Test 6: Large page size
    echo "Test 6: Large page size (100 users)\n";
    $result6 = $userModel->getPaginatedUsers(1, 100);
    echo "- Found {$result6['pagination']['total_users']} total users\n";
    echo "- Showing {$result6['pagination']['start_record']}-{$result6['pagination']['end_record']}\n";
    echo "- Execution time: {$result6['performance']['execution_time_ms']}ms\n";
    echo "- Status: " . ($result6['performance']['execution_time_ms'] < 200 ? "PASS" : "SLOW") . "\n\n";
    
    // Test 7: Sorting
    echo "Test 7: Sort by name ASC\n";
    $result7 = $userModel->getPaginatedUsers(1, 20, '', '', '', 'name', 'ASC');
    echo "- Found {$result7['pagination']['total_users']} total users\n";
    echo "- First user: " . (isset($result7['users'][0]) ? $result7['users'][0]->name : 'None') . "\n";
    echo "- Execution time: {$result7['performance']['execution_time_ms']}ms\n";
    echo "- Status: " . ($result7['performance']['execution_time_ms'] < 100 ? "PASS" : "SLOW") . "\n\n";
    
    // Summary
    $allResults = [$result1, $result2, $result3, $result4, $result5, $result6, $result7];
    $totalTime = array_sum(array_column(array_column($allResults, 'performance'), 'execution_time_ms'));
    $avgTime = $totalTime / count($allResults);
    $fastQueries = count(array_filter($allResults, function($r) { return $r['performance']['execution_time_ms'] < 100; }));
    
    echo "=== SUMMARY ===\n";
    echo "Total tests: " . count($allResults) . "\n";
    echo "Fast queries (<100ms): {$fastQueries}\n";
    echo "Success rate: " . round(($fastQueries / count($allResults)) * 100, 1) . "%\n";
    echo "Average execution time: " . round($avgTime, 2) . "ms\n";
    echo "Total execution time: " . round($totalTime, 2) . "ms\n";
    
    if ($avgTime < 50) {
        echo "Performance: EXCELLENT ✅\n";
    } elseif ($avgTime < 100) {
        echo "Performance: GOOD ✅\n";
    } elseif ($avgTime < 200) {
        echo "Performance: ACCEPTABLE ⚠️\n";
    } else {
        echo "Performance: NEEDS IMPROVEMENT ❌\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest completed!\n";
?>
