<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">My Registrations</h1>
            <p class="text-muted mb-0">Manage your show registrations</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/user/registerVehicle" class="btn btn-primary me-2 d-none d-sm-inline">
                <i class="fas fa-plus me-2"></i> Register Vehicle
            </a>
            <a href="<?php echo BASE_URL; ?>/user/dashboard" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt me-2 d-none d-sm-inline"></i> Dashboard
            </a>
        </div>
    </div>

    <?php flash('user_message'); ?>

    <!-- Registration Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Registration Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm registration-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">All Registrations</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Registrations</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm registration-overview-card" 
                                 data-filter="upcoming" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Upcoming</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['upcoming'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Upcoming Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm registration-overview-card" 
                                 data-filter="past" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Past</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['past'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Past Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm registration-overview-card" 
                                 data-filter="pending_payment" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Pending Payment</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($registration_counts['pending_payment'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Need Payment</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/user/registerVehicle" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span>Register for Show</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/user/vehicles" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-car fa-2x mb-2"></i>
                                <span>My Vehicles</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/user/payments" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-credit-card fa-2x mb-2"></i>
                                <span>Payment History</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/shows" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>Browse Shows</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration Details Section (Lazy Loaded) -->
    <div class="registration-section" id="registration-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">Registration Details</span>
                        <span class="badge bg-secondary" id="registration-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closeRegistrationSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-registrations" class="form-label">Search Registrations</label>
                        <input type="text" class="form-control" id="search-registrations" 
                               placeholder="Search by show, vehicle, or category...">
                    </div>
                    <div class="col-md-2">
                        <label for="show-filter" class="form-label">Show</label>
                        <select class="form-select" id="show-filter">
                            <option value="">All Shows</option>
                            <?php if (!empty($available_shows)): ?>
                                <?php foreach ($available_shows as $show): ?>
                                    <option value="<?php echo $show->id; ?>"><?php echo $show->name; ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="all">All Status</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="past">Past</option>
                            <option value="pending_payment">Pending Payment</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-registrations" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-registrations">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchRegistrations()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearRegistrationSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportRegistrations()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-registrations">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading registrations...</p>
            </div>
            
            <!-- Registrations Content (Will be populated via AJAX) -->
            <div id="registrations-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Registration overview card click handlers
    document.querySelectorAll('.registration-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadRegistrationSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-registrations').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchRegistrations();
        }
    });

    // Filter change handlers
    document.getElementById('show-filter').addEventListener('change', searchRegistrations);
    document.getElementById('status-filter').addEventListener('change', searchRegistrations);
    document.getElementById('per-page-registrations').addEventListener('change', searchRegistrations);
});

function loadRegistrationSection(filter = 'all') {
    // Show the registration section
    const section = document.getElementById('registration-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        document.getElementById('status-filter').value = filter;
    }

    // Load registrations
    loadRegistrations(1);
}

function closeRegistrationSection() {
    const section = document.getElementById('registration-section');
    section.style.display = 'none';
}

function loadRegistrations(page = 1) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-registrations').value;
    const showFilter = document.getElementById('show-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    const perPage = document.getElementById('per-page-registrations').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        show_filter: showFilter,
        status_filter: statusFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/user/loadRegistrations?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderRegistrations(data);
        } else {
            showRegistrationError(data.error || 'Failed to load registrations');
        }
    })
    .catch(error => {
        console.error('Error loading registrations:', error);
        showRegistrationError('Network error occurred');
    });
}

function searchRegistrations() {
    loadRegistrations(1);
}

function clearRegistrationSearch() {
    document.getElementById('search-registrations').value = '';
    document.getElementById('show-filter').value = '';
    document.getElementById('status-filter').value = 'all';
    loadRegistrations(1);
}

function renderRegistrations(data) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render registrations table and pagination
    let html = '';

    if (data.registrations.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No registrations found.</p></div>';
    } else {
        html = renderRegistrationsTable(data.registrations, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update registration count display
    document.getElementById('registration-count-display').textContent = data.pagination.total_registrations.toLocaleString();
}

function renderRegistrationsTable(registrations, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Show</th><th>Vehicle</th><th>Category</th><th>Date</th><th>Status</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    registrations.forEach(registration => {
        html += '<tr>';
        html += '<td><strong>' + registration.show_name + '</strong></td>';

        html += '<td>';
        html += '<strong>' + registration.year + ' ' + registration.make + ' ' + registration.model + '</strong>';
        if (registration.color) {
            html += '<br><small class="text-muted">' + registration.color + '</small>';
        }
        html += '</td>';

        html += '<td>' + (registration.category_name || 'N/A') + '</td>';

        html += '<td>';
        if (registration.start_date) {
            html += formatDate(registration.start_date);
        } else {
            html += 'N/A';
        }
        html += '</td>';

        html += '<td>' + getStatusBadge(registration) + '</td>';
        html += '<td>' + getRegistrationActions(registration) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderRegistrationPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_registrations.toLocaleString()} registrations`;
    html += '</div>';

    return html;
}

function getStatusBadge(registration) {
    const now = new Date();
    const startDate = new Date(registration.start_date);

    if (registration.payment_status === 'pending') {
        return '<span class="badge bg-warning text-dark">Pending Payment</span>';
    } else if (startDate > now) {
        return '<span class="badge bg-success">Upcoming</span>';
    } else {
        return '<span class="badge bg-secondary">Past</span>';
    }
}

function getRegistrationActions(registration) {
    let actions = `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/user/viewRegistration/${registration.id}" class="btn btn-info" title="View Details">
                <i class="fas fa-eye"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/user/editRegistration/${registration.id}" class="btn btn-primary" title="Edit Registration">
                <i class="fas fa-edit"></i>
            </a>
    `;

    // Add payment button for pending payments
    if (registration.payment_status === 'pending' || registration.payment_status === 'unpaid') {
        actions += `
            <a href="<?php echo BASE_URL; ?>/payment/process/${registration.id}" class="btn btn-warning" title="Complete Payment">
                <i class="fas fa-credit-card"></i>
            </a>
        `;
    }

    // Add print button for paid registrations with QR codes
    if ((registration.payment_status === 'paid' || registration.payment_status === 'free') && registration.qr_code) {
        actions += `
            <a href="<?php echo BASE_URL; ?>/registration/printRegistration/${registration.id}" class="btn btn-success" target="_blank" title="Print Registration">
                <i class="fas fa-print"></i>
            </a>
        `;
    }

    // Add receipt button for completed payments
    if (registration.payment_status === 'paid') {
        actions += `
            <a href="<?php echo BASE_URL; ?>/user/receipt/${registration.id}" class="btn btn-outline-primary" target="_blank" title="View Receipt">
                <i class="fas fa-file-invoice-dollar"></i>
            </a>
        `;
    }

    // Add cancel button for pending registrations
    if (registration.status === 'pending' && registration.payment_status !== 'paid') {
        actions += `
            <button class="btn btn-danger" onclick="cancelRegistration(${registration.id})" title="Cancel Registration">
                <i class="fas fa-times"></i>
            </button>
        `;
    }

    actions += '</div>';
    return actions;
}

function renderRegistrationPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadRegistrations(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showRegistrationError(message) {
    const loadingDiv = document.getElementById('loading-registrations');
    const contentDiv = document.getElementById('registrations-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function exportRegistrations() {
    // Get current filter values
    const search = document.getElementById('search-registrations').value;
    const showFilter = document.getElementById('show-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        show_filter: showFilter,
        status_filter: statusFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/user/exportRegistrations?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}

function cancelRegistration(registrationId) {
    if (confirm('Are you sure you want to cancel this registration? This action cannot be undone.')) {
        // Disable the button to prevent double-clicks
        const button = event.target.closest('button');
        button.disabled = true;

        fetch(`<?php echo BASE_URL; ?>/user/cancelRegistration/${registrationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the registrations to reflect the change
                loadRegistrations(1);
                alert('Registration cancelled successfully.');
            } else {
                alert('Failed to cancel registration: ' + (data.error || 'Unknown error'));
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error cancelling registration:', error);
            alert('Network error occurred while cancelling registration.');
            button.disabled = false;
        });
    }
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
