<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Role Management</h1>
            <p class="text-muted mb-0">Optimized for managing thousands of users</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/users" class="btn btn-info me-2 d-none d-sm-inline">
                <i class="fas fa-users me-2"></i> Manage Users
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2 d-none d-sm-inline"></i> Back
            </a>
        </div>
    </div>

    <?php if (hasFlashMessage('user_success')) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_success')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (hasFlashMessage('user_error')) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_error')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Role Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Role Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php 
                        // Define badge colors for each role
                        $roleBadges = [
                            'admin' => 'danger',
                            'coordinator' => 'primary',
                            'judge' => 'success',
                            'staff' => 'warning',
                            'user' => 'info'
                        ];
                        
                        foreach ($roles as $role => $displayName) : 
                            $badgeColor = $roleBadges[$role] ?? 'secondary';
                            $userCount = $roleCounts[$role] ?? 0;
                        ?>
                            <div class="col-6 col-md-4 col-lg">
                                <div class="card h-100 border-<?php echo $badgeColor; ?> shadow-sm role-overview-card" 
                                     data-role="<?php echo $role; ?>" 
                                     style="cursor: <?php echo $userCount > 0 ? 'pointer' : 'default'; ?>;">
                                    <div class="card-body text-center p-3">
                                        <h6 class="card-title">
                                            <span class="badge bg-<?php echo $badgeColor; ?> mb-2">
                                                <?php echo $displayName; ?>
                                            </span>
                                        </h6>
                                        <div class="display-5 fw-bold my-2">
                                            <?php echo number_format($userCount); ?>
                                        </div>
                                        <p class="card-text text-muted small mb-0">
                                            <?php echo $userCount === 1 ? 'User' : 'Users'; ?>
                                        </p>
                                        <?php if ($userCount > 0): ?>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i>Click to view
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Details Sections (Lazy Loaded) -->
    <?php foreach ($roles as $role => $displayName) : 
        $badgeColor = $roleBadges[$role] ?? 'secondary';
        $userCount = $roleCounts[$role] ?? 0;
    ?>
        <div class="row mb-4 role-section" id="role-section-<?php echo $role; ?>" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-<?php echo $badgeColor; ?> bg-opacity-25">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <span class="badge bg-<?php echo $badgeColor; ?> me-2"><?php echo $displayName; ?></span>
                                <span class="badge bg-secondary" id="role-count-<?php echo $role; ?>"><?php echo number_format($userCount); ?></span>
                            </h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-outline-secondary" onclick="closeRoleSection('<?php echo $role; ?>')">
                                    <i class="fas fa-times"></i> Close
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Search and Filter Controls -->
                    <div class="card-body border-bottom">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search-<?php echo $role; ?>" class="form-label">Search Users</label>
                                <input type="text" class="form-control" id="search-<?php echo $role; ?>" 
                                       placeholder="Search by name or email..." 
                                       data-role="<?php echo $role; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="status-<?php echo $role; ?>" class="form-label">Status</label>
                                <select class="form-select" id="status-<?php echo $role; ?>" data-role="<?php echo $role; ?>">
                                    <option value="">All Statuses</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="pending">Pending</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="per-page-<?php echo $role; ?>" class="form-label">Per Page</label>
                                <select class="form-select" id="per-page-<?php echo $role; ?>" data-role="<?php echo $role; ?>">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="50">50</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="searchRoleUsers('<?php echo $role; ?>')">
                                        <i class="fas fa-search me-1"></i>Search
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearRoleSearch('<?php echo $role; ?>')">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Loading Indicator -->
                    <div class="card-body text-center" id="loading-<?php echo $role; ?>">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading <?php echo strtolower($displayName); ?> users...</p>
                    </div>
                    
                    <!-- Users Content (Will be populated via AJAX) -->
                    <div id="users-content-<?php echo $role; ?>" style="display: none;">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Role overview card click handlers
    document.querySelectorAll('.role-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const role = this.dataset.role;
            const userCount = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (userCount > 0) {
                loadRoleSection(role);
            }
        });
    });

    // Search input handlers
    document.querySelectorAll('input[id^="search-"]').forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const role = this.dataset.role;
                searchRoleUsers(role);
            }
        });
    });

    // Filter change handlers
    document.querySelectorAll('select[data-role]').forEach(select => {
        select.addEventListener('change', function() {
            const role = this.dataset.role;
            searchRoleUsers(role);
        });
    });
});

function loadRoleSection(role) {
    // Show the role section
    const section = document.getElementById('role-section-' + role);
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Load users for this role
    loadRoleUsers(role, 1);
}

function closeRoleSection(role) {
    const section = document.getElementById('role-section-' + role);
    section.style.display = 'none';
}

function loadRoleUsers(role, page = 1) {
    const loadingDiv = document.getElementById('loading-' + role);
    const contentDiv = document.getElementById('users-content-' + role);

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-' + role).value;
    const status = document.getElementById('status-' + role).value;
    const perPage = document.getElementById('per-page-' + role).value;

    // Build URL parameters
    const params = new URLSearchParams({
        role: role,
        page: page,
        per_page: perPage,
        search: search,
        status: status
    });

    // Make AJAX request
    fetch('<?php echo BASE_URL; ?>/admin/loadRoleUsers?' + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderRoleUsers(role, data);
        } else {
            showError(role, data.error || 'Failed to load users');
        }
    })
    .catch(error => {
        console.error('Error loading role users:', error);
        showError(role, 'Network error occurred');
    });
}

function searchRoleUsers(role) {
    loadRoleUsers(role, 1);
}

function clearRoleSearch(role) {
    document.getElementById('search-' + role).value = '';
    document.getElementById('status-' + role).value = '';
    loadRoleUsers(role, 1);
}

function renderRoleUsers(role, data) {
    const loadingDiv = document.getElementById('loading-' + role);
    const contentDiv = document.getElementById('users-content-' + role);

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render users table and pagination
    let html = '';

    if (data.users.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No users found.</p></div>';
    } else {
        html = renderUsersTable(role, data.users, data.pagination, data.filters);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update role count badge
    document.getElementById('role-count-' + role).textContent = data.pagination.total_users.toLocaleString();
}

function renderUsersTable(role, users, pagination, filters) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Name</th><th>Email</th><th>Status</th><th>Created</th><th>Last Login</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    users.forEach(user => {
        html += '<tr>';
        html += '<td><div class="d-flex align-items-center">';
        if (user.profile_image) {
            html += `<img src="<?php echo BASE_URL; ?>/${user.profile_image}" alt="Profile" class="rounded-circle me-2" width="30" height="30">`;
        } else {
            html += '<i class="fas fa-user-circle me-2 text-secondary" style="font-size: 1.5rem;"></i>';
        }
        html += user.name + '</div></td>';
        html += '<td>' + user.email + '</td>';
        html += '<td>' + getStatusBadge(user.status) + '</td>';
        html += '<td>' + formatDate(user.created_at) + '</td>';
        html += '<td>' + (user.last_login ? formatDate(user.last_login) : 'Never') + '</td>';
        html += '<td>' + getUserActions(user.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderPagination(role, pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_users.toLocaleString()} users`;
    html += '</div>';

    return html;
}

function getStatusBadge(status) {
    const badges = {
        'active': '<span class="badge bg-success">Active</span>',
        'inactive': '<span class="badge bg-danger">Inactive</span>',
        'pending': '<span class="badge bg-warning text-dark">Pending</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function getUserActions(userId) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/admin/editUser/${userId}" class="btn btn-primary">
                <i class="fas fa-edit"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/assignRole/${userId}" class="btn btn-info">
                <i class="fas fa-user-tag"></i>
            </a>
        </div>
    `;
}

function renderPagination(role, pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRoleUsers('${role}', ${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadRoleUsers('${role}', ${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadRoleUsers('${role}', ${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function showError(role, message) {
    const loadingDiv = document.getElementById('loading-' + role);
    const contentDiv = document.getElementById('users-content-' + role);

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
