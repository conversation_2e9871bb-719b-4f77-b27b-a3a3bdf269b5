<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Pending Payments</h1>
            <p class="text-muted mb-0">Review and process pending payment transactions</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/payment/admin" class="btn btn-outline-primary me-2">
                <i class="fas fa-tachometer-alt me-2 d-none d-sm-inline"></i> Payment Dashboard
            </a>
            <a href="<?php echo BASE_URL; ?>/payment/settings" class="btn btn-outline-secondary">
                <i class="fas fa-cog me-2 d-none d-sm-inline"></i> Settings
            </a>
        </div>
    </div>

    <?php flash('payment_message'); ?>

    <!-- Pending Payment Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Pending Payment Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm pending-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Total Pending</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($pending_counts['total_pending'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Need Review</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-danger shadow-sm pending-overview-card" 
                                 data-filter="urgent" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-danger mb-2">Urgent</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($pending_counts['urgent_pending'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Over 24 Hours</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm pending-overview-card" 
                                 data-filter="today" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Today</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($pending_counts['today_pending'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Submitted Today</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm pending-overview-card" 
                                 data-filter="high_value" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">High Value</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($pending_counts['high_value_pending'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Over $100</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Payment Summary -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>Pending Value
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-warning">$<?php echo number_format($pending_stats['total_pending_value'] ?? 0, 2); ?></h4>
                            <small class="text-muted">Total Pending</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger">$<?php echo number_format($pending_stats['urgent_pending_value'] ?? 0, 2); ?></h4>
                            <small class="text-muted">Urgent Value</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Processing Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary"><?php echo number_format($pending_stats['avg_processing_time'] ?? 0, 1); ?>h</h4>
                            <small class="text-muted">Avg Processing</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo number_format($pending_stats['processed_today'] ?? 0); ?></h4>
                            <small class="text-muted">Processed Today</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <button onclick="approveAllVisible()" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <span>Approve All Visible</span>
                                <small class="text-muted mt-1">Bulk approve current page</small>
                            </button>
                        </div>
                        <div class="col-6 col-md-3">
                            <button onclick="exportPending()" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-download fa-2x mb-2"></i>
                                <span>Export Pending</span>
                                <small class="text-muted mt-1">Download CSV report</small>
                            </button>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/payment/reports" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>Payment Reports</span>
                                <small class="text-muted mt-1">View detailed reports</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/payment/settings" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-cog fa-2x mb-2"></i>
                                <span>Payment Settings</span>
                                <small class="text-muted mt-1">Configure processing</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Payment Details Section (Lazy Loaded) -->
    <div class="pending-section" id="pending-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-warning bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-warning text-dark me-2">Pending Payment Details</span>
                        <span class="badge bg-secondary" id="pending-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closePendingSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-pending" class="form-label">Search Payments</label>
                        <input type="text" class="form-control" id="search-pending" 
                               placeholder="Search by user, amount, or reference...">
                    </div>
                    <div class="col-md-2">
                        <label for="urgency-filter" class="form-label">Urgency</label>
                        <select class="form-select" id="urgency-filter">
                            <option value="all">All Urgency</option>
                            <option value="urgent">Urgent (24h+)</option>
                            <option value="today">Today</option>
                            <option value="high_value">High Value</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="amount-filter" class="form-label">Amount Range</label>
                        <select class="form-select" id="amount-filter">
                            <option value="">All Amounts</option>
                            <option value="under_50">Under $50</option>
                            <option value="50_100">$50 - $100</option>
                            <option value="over_100">Over $100</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-pending" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-pending">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchPending()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearPendingSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportPending()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-pending">
                <div class="spinner-border text-warning" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading pending payments...</p>
            </div>
            
            <!-- Pending Payments Content (Will be populated via AJAX) -->
            <div id="pending-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pending overview card click handlers
    document.querySelectorAll('.pending-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadPendingSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-pending').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchPending();
        }
    });

    // Filter change handlers
    document.getElementById('urgency-filter').addEventListener('change', searchPending);
    document.getElementById('amount-filter').addEventListener('change', searchPending);
    document.getElementById('per-page-pending').addEventListener('change', searchPending);
});

function loadPendingSection(filter = 'all') {
    // Show the pending section
    const section = document.getElementById('pending-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        document.getElementById('urgency-filter').value = filter;
    }

    // Load pending payments
    loadPending(1);
}

function closePendingSection() {
    const section = document.getElementById('pending-section');
    section.style.display = 'none';
}

function loadPending(page = 1) {
    const loadingDiv = document.getElementById('loading-pending');
    const contentDiv = document.getElementById('pending-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-pending').value;
    const urgencyFilter = document.getElementById('urgency-filter').value;
    const amountFilter = document.getElementById('amount-filter').value;
    const perPage = document.getElementById('per-page-pending').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        urgency_filter: urgencyFilter,
        amount_filter: amountFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/payment/loadPending?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderPending(data);
        } else {
            showPendingError(data.error || 'Failed to load pending payments');
        }
    })
    .catch(error => {
        console.error('Error loading pending payments:', error);
        showPendingError('Network error occurred');
    });
}

function searchPending() {
    loadPending(1);
}

function clearPendingSearch() {
    document.getElementById('search-pending').value = '';
    document.getElementById('urgency-filter').value = 'all';
    document.getElementById('amount-filter').value = '';
    loadPending(1);
}

function renderPending(data) {
    const loadingDiv = document.getElementById('loading-pending');
    const contentDiv = document.getElementById('pending-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render pending payments table and pagination
    let html = '';

    if (data.payments.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No pending payments found.</p></div>';
    } else {
        html = renderPendingTable(data.payments, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update pending count display
    document.getElementById('pending-count-display').textContent = data.pagination.total_payments.toLocaleString();
}

function renderPendingTable(payments, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th><input type="checkbox" id="select-all-pending" onchange="toggleAllPending()"></th>';
    html += '<th>ID</th><th>Date</th><th>User</th><th>Type</th><th>Amount</th><th>Age</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    payments.forEach(payment => {
        const urgencyClass = getUrgencyClass(payment.hours_pending);
        html += `<tr class="${urgencyClass}">`;
        html += `<td><input type="checkbox" class="pending-checkbox" value="${payment.id}"></td>`;
        html += '<td><strong>#' + payment.id + '</strong></td>';
        html += '<td>' + formatDateTime(payment.created_at) + '</td>';
        html += '<td>' + (payment.user_name || 'Unknown') + '</td>';
        html += '<td>' + (payment.payment_type || 'N/A') + '</td>';
        html += '<td><strong>$' + parseFloat(payment.amount).toFixed(2) + '</strong></td>';
        html += '<td>' + getAgeDisplay(payment.hours_pending) + '</td>';
        html += '<td>' + getPendingActions(payment.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderPendingPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_payments.toLocaleString()} pending payments`;
    html += '</div>';

    return html;
}

function getUrgencyClass(hoursPending) {
    if (hoursPending >= 24) {
        return 'table-danger';
    } else if (hoursPending >= 12) {
        return 'table-warning';
    }
    return '';
}

function getAgeDisplay(hoursPending) {
    if (hoursPending >= 24) {
        const days = Math.floor(hoursPending / 24);
        return `<span class="badge bg-danger">${days}d ${Math.floor(hoursPending % 24)}h</span>`;
    } else if (hoursPending >= 12) {
        return `<span class="badge bg-warning text-dark">${Math.floor(hoursPending)}h</span>`;
    } else {
        return `<span class="badge bg-info">${Math.floor(hoursPending)}h</span>`;
    }
}

function getPendingActions(paymentId) {
    return `
        <div class="btn-group btn-group-sm">
            <button onclick="approvePayment(${paymentId})" class="btn btn-success" title="Approve">
                <i class="fas fa-check"></i>
            </button>
            <button onclick="rejectPayment(${paymentId})" class="btn btn-danger" title="Reject">
                <i class="fas fa-times"></i>
            </button>
            <a href="<?php echo BASE_URL; ?>/payment/details/${paymentId}" class="btn btn-info" title="View Details">
                <i class="fas fa-eye"></i>
            </a>
        </div>
    `;
}

function renderPendingPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPending(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadPending(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPending(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showPendingError(message) {
    const loadingDiv = document.getElementById('loading-pending');
    const contentDiv = document.getElementById('pending-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function toggleAllPending() {
    const selectAll = document.getElementById('select-all-pending');
    const checkboxes = document.querySelectorAll('.pending-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function approveAllVisible() {
    const checkboxes = document.querySelectorAll('.pending-checkbox:checked');
    const paymentIds = Array.from(checkboxes).map(cb => cb.value);

    if (paymentIds.length === 0) {
        alert('Please select payments to approve');
        return;
    }

    if (confirm(`Are you sure you want to approve ${paymentIds.length} payments?`)) {
        bulkApprovePayments(paymentIds);
    }
}

function approvePayment(paymentId) {
    if (confirm('Are you sure you want to approve this payment?')) {
        processPaymentAction(paymentId, 'approve');
    }
}

function rejectPayment(paymentId) {
    if (confirm('Are you sure you want to reject this payment?')) {
        processPaymentAction(paymentId, 'reject');
    }
}

function processPaymentAction(paymentId, action) {
    fetch(`<?php echo BASE_URL; ?>/payment/${action}/${paymentId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload current page
            loadPending(getCurrentPage());
        } else {
            alert(data.error || `Failed to ${action} payment`);
        }
    })
    .catch(error => {
        console.error(`Error ${action}ing payment:`, error);
        alert(`Network error occurred while ${action}ing payment`);
    });
}

function bulkApprovePayments(paymentIds) {
    fetch(`<?php echo BASE_URL; ?>/payment/bulkApprove`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ payment_ids: paymentIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload current page
            loadPending(getCurrentPage());
        } else {
            alert(data.error || 'Failed to approve payments');
        }
    })
    .catch(error => {
        console.error('Error bulk approving payments:', error);
        alert('Network error occurred while approving payments');
    });
}

function getCurrentPage() {
    // Get current page from pagination or default to 1
    const activePage = document.querySelector('.pagination .page-item.active .page-link');
    return activePage ? parseInt(activePage.textContent) : 1;
}

function exportPending() {
    // Get current filter values
    const search = document.getElementById('search-pending').value;
    const urgencyFilter = document.getElementById('urgency-filter').value;
    const amountFilter = document.getElementById('amount-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        urgency_filter: urgencyFilter,
        amount_filter: amountFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/payment/exportPending?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
