# Events and Shows Management System v3.63.7

A comprehensive web application for managing car shows, events, judging, registrations, and vehicle displays.

## New in v3.63.7: Calendar Guest Access and Privacy System Update

### Calendar Guest Access
- **Guest Calendar Access**: Calendar index page (/calendar) now accessible to guests without login
- **Guest Event Access**: Individual event pages (/calendar/event/) accessible to guests
- **Public Events Only**: Guests can only view public events and calendars
- **Notification Button Hidden**: Notification button hidden from guest users on event pages
- **Export Calendar Restricted**: Export calendar functionality remains login-required

### Simplified Privacy System
- **Two Privacy Levels**: Events now use only "Public" and "Draft" privacy levels
- **Public Events**: Visible to everyone including guests
- **Draft Events**: Hidden from public view, only visible to event creator
- **Draft Notification Restriction**: Draft events cannot use the notification system
- **Privacy Form Updates**: Create and edit event forms updated with new privacy options

### Technical Implementation
- **CalendarController**: Added guest methods (index, event, getEvents, getUpcomingEvents)
- **CalendarModel**: Added getPublicCalendars() method for guest users
- **Privacy Filtering**: Updated event filtering to handle Public/Draft system
- **Guest User Handling**: Proper timezone and user ID handling for guest users
- **Event View Updates**: Conditional display of notification and export features

## Previous in v3.63.6: Camera Banner API Routing Fix

### API Routing System Enhancement
- **Fixed 404 API Error**: Resolved "API endpoint not found" error for camera banners
- **Proper API Registration**: Added cameraBanners endpoint to core routing system
- **Enhanced Debug Logging**: Comprehensive API call tracking and error logging
- **Mobile Debug Tools**: Updated debug page with old/new API endpoint testing
- **Complete API Integration**: Full integration with existing controller architecture
- **Form Field Fixes**: All datetime-local inputs now display correct user timezone without helper text
- **Date Comparison Logic**: All date comparisons now use timezone-aware logic for accurate status determination

### Technical Implementation
- **CustomFieldRetriever.php**: Modified to skip date fields that need timezone conversion, preserving controller values
- **View Files Updated**: Admin, coordinator, user, and public views all updated with proper timezone functions
- **Date Logic Fixes**: Show status logic (upcoming/today/past) now uses user timezone for accurate determination
- **Complete Elimination**: Removed all instances of raw date() function calls in view templates

## Previous in v3.62.2: PHP Event Display Timezone Fix

### Event Display Timezone Features
- **User Timezone Display**: Event detail pages now show times in user's selected timezone
- **Site-wide Timezone Helper**: Timezone helper functions now available throughout the application
- **Timezone Indicators**: Logged-in users see timezone abbreviations with event times
- **All-day Event Handling**: Proper handling of all-day vs timed events
- **Guest User Support**: Non-logged-in users see events in default timezone
- **Meta Tag Fixes**: Open Graph social sharing tags use timezone-aware formatting

### Technical Implementation
- **Global Timezone Helper**: Added to Database.php constructor for site-wide availability
- **Enhanced Event View**: Updated `/calendar/event/{id}` to use `formatDateTimeForUser()` function
- **Smart Date Parsing**: Proper handling of multi-day events and time ranges
- **Fallback Support**: Works correctly for both logged-in and guest users

## Previous in v3.62.1: Complete JavaScript Timezone Implementation

### JavaScript Timezone Features
- **Complete Timezone Implementation**: All JavaScript files now properly handle timezone conversions
- **TimezoneHelper Integration**: All calendar and event JavaScript uses TimezoneHelper for consistent date/time handling
- **Manual Date Formatting**: Replaced all browser locale-dependent functions with manual formatting
- **Debug System Enhancement**: All debug files now create timezone-aware test events
- **Fallback Mechanisms**: Proper fallbacks when TimezoneHelper is not available
- **Script Loading Order**: Proper loading order ensures TimezoneHelper is available before other calendar scripts

### Technical Implementation
- **monthly-event-chart.js v3.48.1**: Complete timezone implementation with TimezoneHelper integration
- **Debug Files Updated**: All debug files (monthly-event-debug.js, custom-calendar-debug.js, debug_week2_issue.js) enhanced
- **View Files Updated**: PHP view files now include timezone-helper.js before other calendar scripts
- **Comprehensive Testing**: Browser console verification messages for all timezone implementations

## Previous in v3.61.0: User Timezone Management

### User Timezone Features
- **USA Timezone Selection**: Users can select their preferred timezone from a comprehensive list of USA timezones
- **Profile Integration**: Timezone selection integrated into user profile management
- **Timezone Validation**: Server-side validation ensures only valid USA timezones are accepted
- **Helper Functions**: Comprehensive timezone helper functions for date/time conversion
- **Current Time Display**: Shows current time in user's selected timezone on profile page
- **Default Timezone**: New users default to Eastern Time (America/New_York)

### Technical Implementation
- **Database Schema**: Added timezone column to users table with proper defaults
- **Timezone Helper**: New timezone_helper.php with conversion and validation functions
- **Form Enhancement**: Enhanced profile form with timezone dropdown and visual indicators
- **Error Handling**: Proper error handling and fallback to default timezone
- **Security**: CSRF protection maintained with proper input validation

## Previous in v3.60.1: Coordinator Edit Show Restrictions

### Enhanced Coordinator Permissions
- **Listing Fee Field Removal**: Coordinators can no longer view or modify listing fee fields in any form
- **Coordinator Dropdown Removal**: Coordinator selection dropdown removed - coordinators can only edit shows they coordinate
- **Status Field Restrictions**: Status field is disabled until the show's listing fee has been paid
- **Read-Only Coordinator Display**: Shows coordinator information as read-only text instead of editable dropdown
- **Payment-Based Status Control**: Status changes are restricted based on listing_paid database field

### Security Improvements
- **Server-Side Enforcement**: All restrictions enforced at the server level, not just client-side
- **Data Integrity**: Hidden fields maintain necessary data while preventing unauthorized modifications
- **Permission Validation**: Proper validation of coordinator permissions before allowing edits

## Previous in v3.60.0: Local Venue Search Fix

### Venue Search Improvements
- **Fixed Local Venue Search**: Form venue search now properly searches the local database instead of external APIs
- **Separated Search Functions**: Distinguished between local venue search (for forms) and API venue search (for creating new venues)
- **Enhanced CSRF Handling**: Fixed CSRF token validation for AJAX requests using header-based tokens
- **Improved Search Logic**: Local venue search now returns existing venues from the database with proper formatting
- **Better Error Handling**: Enhanced error handling for both local and API venue searches
- **Automatic Address Population**: When selecting a venue, address fields are automatically populated with venue data
- **Single Venue Selection**: Users can only select one venue per form, with clear selection indicators
- **Address Field Clearing**: Address fields are cleared when venue selection is removed

### Previous Updates in v3.59.0: Google Places Venue Integration Fixes

### Google Places API Improvements
- **Fixed Address Parsing**: Google Places API now properly separates formatted addresses into individual components (address, city, state, zip)
- **Enhanced Search Results**: Venue search results now display full addresses to help users identify the correct venue
- **Place Details Integration**: Added Google Place Details API calls to retrieve phone numbers, websites, and email addresses when available
- **Improved Address Extraction**: Smart parsing of formatted addresses to extract street address from full location strings
- **Better Error Handling**: Enhanced error handling for Place Details API calls with graceful fallbacks

### Venue Modal Enhancements
- **Address Display**: Search results now show complete addresses for better venue identification
- **Contact Information**: Phone, email, and website information is now retrieved and displayed in search results
- **Visual Improvements**: Enhanced search result cards with better formatting and contact details
- **Smart Field Population**: Improved form field population with proper address component separation
- **Responsive Design**: Better mobile display of search results and venue information

### Multi-Provider Address Parsing
- **Consistent Parsing**: All map providers (Google, Mapbox, HERE, OpenStreetMap) now properly parse addresses into components
- **Formatted Address Support**: Added formatted_address field for better display consistency
- **Fallback Mechanisms**: Smart fallbacks when address components aren't available from APIs
- **Enhanced OpenStreetMap**: Improved OpenStreetMap venue search with better name extraction and contact details

### Testing and Debugging
- **Comprehensive Test Suite**: Added test file for verifying Google Places integration
- **Debug Mode Support**: Enhanced debug logging for venue search operations
- **Address Parsing Tests**: Included tests for address extraction functionality
- **API Response Validation**: Better validation and error reporting for API responses

## Previous in v3.58.0: Google Maps Modernization & Map Usability

### Google Maps API Updates
- **Deprecated API Migration**: Updated from deprecated `google.maps.Marker` to modern `google.maps.marker.AdvancedMarkerElement`
- **Marker Settings Consistency**: Google Maps now respects all marker customization options from `/calendar/mapSettings`
- **Enhanced Marker Types**: Full support for circle, pin, and custom image markers with Google Maps
- **Visual Consistency**: Google Maps markers now match OpenStreetMap implementation styling
- **Future-Proof**: Eliminates deprecation warnings and ensures long-term compatibility

### Map Navigation Improvements
- **"Fit to Events" Button**: Added smart reset button that fits map to current filtered events
- **Mouse Wheel Zoom**: Enabled scroll wheel zoom across all map providers (Google Maps, OpenStreetMap, Mapbox, HERE)
- **Smart View Tracking**: Reset button uses current filtered view instead of system defaults
- **Enhanced Zoom Controls**: Full support for double-click, touch, box, and keyboard zoom interactions
- **Clean Reset Behavior**: Automatically removes search markers and closes info windows when resetting

### Map Settings Integration
- **Marker Type Selection**: Choose between default circles, pins, or custom images
- **Size Configuration**: Adjustable marker sizes (8-48 pixels)
- **Color Management**: Use calendar colors or set default marker color
- **Border Styling**: Optional borders with customizable color and width
- **Custom Images**: Support for custom marker images via URL

## Previous in v3.56.0: Calendar Event Venue Modal Integration

### Enhanced Venue Creation Experience
- **Venue Modal Integration**: Extended venue creation modal to calendar event pages (create and edit)
- **Online Venue Search**: Added "Search Online" button next to venue name field for automatic venue discovery
- **Multi-Provider Support**: Integrated with configured map provider (Google Places, Radar, Mapbox, HERE, OpenStreetMap)
- **Smart Auto-Population**: Search results automatically populate all venue fields with available data
- **Dropdown Selection**: Users can select from multiple search results with detailed venue information
- **Radar API Integration**: Added Radar as a new map provider option in admin settings
- **Seamless Workflow**: Replaced "Create New Venue" links that opened new tabs with inline modal popups
- **Consistent Experience**: Unified venue creation experience across all forms (shows and calendar events)

### Technical Enhancements
- **Map Provider Expansion**: Added Radar map initialization and geocoding support
- **API Integration**: Implemented venue search endpoints for all supported map providers
- **Enhanced Modal**: Improved venue modal with search functionality and result selection
- **Address Parsing**: Smart parsing of venue data from different API providers
- **Error Handling**: Robust error handling for API failures and network issues

## Previous in v3.55.0: Venue Creation Modal System
- **Hidden Listing Fee Field**: Made the listing fee field hidden in the coordinator edit show form
- **Improved Form Layout**: Registration fee field now spans full width for better visual balance
- **Preserved Functionality**: Listing fee value is still submitted with the form but not visible to coordinators
- **Enhanced User Experience**: Simplified form interface for coordinators while maintaining data integrity
- **Fixed Form Template Error**: Resolved "Unknown field type: hidden" error by adding hidden field support

## Previous in v3.53.1: Image Viewer Event Listener Fix
- **Fixed Browser Console Warnings**: Resolved non-passive event listener warnings for wheel and touch events
- **Improved Performance**: Explicitly marked event listeners as non-passive where preventDefault() is used
- **Enhanced User Experience**: Eliminated browser console violations while maintaining full functionality
- **Touch Device Optimization**: Proper event handling for touch-based zoom and drag operations

## Previous in v3.52.1: Payment Admin DataTable Fix
- **Fixed DataTable Error**: Resolved JavaScript error "DataTable is not a function" on payment admin dashboard
- **Added DataTables Library**: Included required DataTables CSS and JavaScript libraries
- **Enhanced Export Features**: Added full export functionality (Excel, PDF, CSV, Print) with proper dependencies
- **Improved Table Functionality**: Enabled sorting, searching, and pagination on payment tables
- **Mobile Responsive Tables**: DataTables configured for mobile-first responsive design

## Previous in v3.52.0: Admin Payment Management Dashboard
- **Payment Management Dashboard**: New comprehensive admin dashboard for managing all payments at `/payment/admin`
- **Payment Statistics**: Real-time statistics showing total, completed, pending, and rejected payments
- **Payment Type Breakdown**: Visual breakdown of registration vs show listing payments
- **Advanced Payment Table**: Sortable, filterable table with payment details and user information
- **Payment Details View**: Detailed payment information with user details and related records
- **Quick Actions**: Direct approve/reject functionality from dashboard
- **Export Functionality**: Export payment data to Excel, CSV, and PDF formats
- **Admin Settings Integration**: Payment dashboard integrated into admin settings panel
- **Mobile Responsive**: Full mobile-first responsive design for all payment management interfaces

## Previous in v3.51.1: Impersonation Fix
- **Fixed Admin Impersonation**: Fixed issue where impersonation banner showed "Unknown User" instead of the actual user's name
- **Session Management**: Improved session variable handling during user impersonation
- **Admin Experience**: Impersonation banner now correctly displays impersonated user's name and role

## New in v3.51.0: User Show Creation System

### Major Feature Addition
- **User Show Creation**: Regular users can now create their own shows and automatically become coordinators
- **Template System Integration**: Uses the same template system as admin show creation for consistency
- **Automatic Role Promotion**: Users are automatically promoted to coordinator role when they create a show
- **Calendar Integration**: Added "Create Show" button to calendar page dropdown menu
- **Dashboard Integration**: Added "Create Show" button to user dashboard for easy access
- **Automatic Status Management**: Show status is automatically set based on payment requirements

### User Experience Enhancements
- **Seamless Workflow**: Users can create shows directly from calendar or dashboard
- **Template Consistency**: Same form fields and validation as admin show creation
- **Automatic Notifications**: Admins are notified when users create new shows
- **Role Management**: Automatic promotion from user to coordinator role
- **Smart Status Flow**: Status automatically set to "payment_pending" or "draft" based on listing fees
- **Payment Integration**: Complete integration with existing payment system

### Technical Implementation
- **UserController Enhancement**: Added createShow method with full template support
- **View Creation**: New user/create_show.php view with responsive design
- **Role System Integration**: Automatic role updates and session management
- **Email Notifications**: Admin notification system for new user-created shows
- **Template Processing**: Full support for custom fields and validation
- **Status Automation**: Automatic status management based on payment requirements
- **Payment Integration**: Complete payment workflow with status transitions

## Previous in v3.50.6: Mobile-First Responsive Navigation and UI Enhancements

### UI/UX Improvements
- **Fixed Header Spacing**: Resolved overlapping issue between event count indicator and "Show all pins" toggle switch
- **Dual Pagination Controls**: Added pagination controls at both top and bottom of event list for better accessibility
- **Text Readability Fix**: Fixed white text on white background issue when events are selected/highlighted
- **Page Size Persistence**: Implemented localStorage to remember user's preferred page size setting between sessions
- **Enhanced Visual Feedback**: Improved event highlighting with proper color contrast for better readability
- **Pin Numbering Fix**: Corrected pin numbering to match event list numbers when "Show all pins" is unchecked
- **Mobile-First Navigation**: Redesigned top navigation buttons with responsive layout for optimal mobile and desktop experience

### Technical Enhancements
- **Synchronized Controls**: Both top and bottom pagination controls stay in sync automatically
- **Local Storage Integration**: User preferences are saved and restored across page visits
- **Improved CSS Styling**: Better spacing, contrast, and visual hierarchy throughout the map view
- **Mobile-First Design**: Responsive layout improvements for better mobile experience
- **Performance Optimization**: Efficient DOM manipulation for pagination controls

### Technical Implementation
- **Promise-Based Loading**: Enhanced event loading with proper promise handling for smooth page transitions
- **Event Tracking**: Each map marker tracks its corresponding event for accurate page navigation
- **State Management**: Proper pagination state management across different viewing modes
- **Debug Support**: Comprehensive debug logging for troubleshooting pagination operations
- **Cross-Provider Support**: Pagination works with all map providers (Google Maps, OpenStreetMap, Mapbox, HERE Maps)

## Previous in v3.50.2: Notification Modal Registration Deadline Alert Styling

### Layout Improvements
- **Registration Deadline Container**: Expanded registration deadline toggle container to match alert styling
- **Alert-Style Formatting**: Applied Bootstrap alert styling with consistent width, padding, and visual appearance
- **Visual Consistency**: Registration deadline container now matches the `.alert.alert-info` styling throughout the modal
- **Professional Appearance**: Enhanced visual polish with proper alert-style background, border, and spacing

### User Experience Enhancements
- **Consistent Interface**: Registration deadline toggle now appears in a styled container similar to other alerts
- **Better Visual Hierarchy**: Clear visual organization with alert-style formatting and proper toggle positioning
- **Improved Readability**: Enhanced contrast and spacing with alert background styling
- **Toggle Positioning**: Toggle switch remains in its original position while container expands to full width
- **Professional Layout**: Uniform styling throughout the notification modal interface

## Previous in v3.50.1: Notification Modal Registration Deadline Positioning Fix

### Bug Fixes
- **Notification Modal JavaScript Errors**: Fixed "originalText is not defined" error when clicking notify me button
- **Missing Global Functions**: Fixed "subscribeToEventModal is not defined" error when clicking subscribe button in modal
- **Variable Scope Issues**: Moved variable declarations outside try blocks to ensure proper scope
- **Modal Function Availability**: Added global modal functions setup during NotificationManager initialization
- **Dynamic Content Execution**: Improved script execution for dynamically loaded modal content

## Previous in v3.49.4: Toast Notification Persistence Fix

### Bug Fixes
- **Toast Notification Persistence**: Fixed toast notifications displaying repeatedly on every page load
- **Notification Marking**: Fixed JSON decoding issue preventing toast notifications from being marked as read
- **Database Consistency**: Added missing `is_read` column to notification INSERT statements
- **Undefined Variable Error**: Fixed undefined `$notifications` variable in notification queue view
- **Test Notifications Fix**: Fixed undefined `$users` variable in test notifications view
- **Class Loading Error**: Fixed "Class UserModel not found" errors in NotificationService, NotificationController, and header view
- **Missing Methods**: Added missing test notification methods (sendTestEmail, sendTestSms, etc.)
- **Data Key Mismatch**: Corrected controller data key from `queueItems` to `notifications`
- **Filter Support**: Added proper status and limit filtering for notification queue
- **CSRF Protection**: Enhanced CSRF token handling for notification operations
- **URL Routing**: Fixed form action URLs to match application routing structure

### Notification System Improvements
- **Toast Notification Management**: Added ability to clear persistent toast notifications
- **Queue Management**: Fully functional notification queue interface
- **Test Notifications**: Complete testing suite for all notification types with clear functionality
- **Failed Notification Cleanup**: Added ability to clear failed notifications
- **Filter Parameters**: Support for filtering by status (all, pending, sent, failed, cancelled)
- **Pagination**: Configurable limits (25, 50, 100, 200 items)
- **User Feedback**: Proper flash messages for operations
- **Service Architecture**: Improved lazy loading and error handling
- **Database Integrity**: Proper notification read/unread state management

## Previous in v3.47.7: Numbered Event Badges in Map View

### Event Organization Improvements
- **Sequential Numbering**: Events in the list are numbered 1, 2, 3, etc. for easy reference
- **Date Sorting**: Events automatically sorted by start date with soonest events first
- **Map Marker Numbers**: Corresponding map markers display the same numbers as the list
- **Cross-Reference**: Easy matching between event list items and map markers

### User Experience Enhancements
- **Visual Clarity**: Numbered badges make it easy to locate specific events on the map
- **Consistent Ordering**: Events maintain the same order in both list and map markers
- **All Map Providers**: Works with Google Maps, OpenStreetMap, Mapbox, and HERE Maps
- **Responsive Design**: Badges scale appropriately on different screen sizes

## Previous in v3.47.6: Enhanced Map View Layout

### Map View Improvements
- **Consistent Layout**: Advanced filters moved above the map for consistency with event view
- **Full-Width Design**: Filters now use full width for better visibility and usability
- **Mobile Responsive**: Improved mobile experience with better filter accessibility
- **User Experience**: Streamlined interface matching the event view layout pattern

### Layout Changes
- **Filter Position**: Advanced filters relocated from sidebar to above map container
- **Space Utilization**: Better use of screen real estate with full-width layout
- **Visual Consistency**: Unified design language across event and map views
- **Accessibility**: Improved filter visibility and interaction on all device sizes

## Previous in v3.47.5: Enhanced Facebook Sharing with Open Graph Tags

### Social Media Integration Improvements
- **Open Graph Meta Tags**: Proper Facebook sharing with event title, description, and details
- **Twitter Cards**: Enhanced Twitter sharing with rich media previews
- **LinkedIn Integration**: Professional sharing with proper meta tag support
- **SEO Enhancement**: Added meta descriptions and canonical URLs for better search engine optimization

### Facebook Sharing Features
- **Event-Specific Content**: Facebook now displays event title, description, date, and location
- **Rich Previews**: Proper Open Graph tags ensure rich media previews on social platforms
- **Popup Window**: Facebook sharing opens in a properly sized popup window
- **Debug Integration**: Debug mode shows Open Graph tag information for troubleshooting

### Technical Implementation
- **Dynamic Meta Tags**: Page-specific meta tags generated based on event data
- **Cross-Platform Support**: Optimized for Facebook, Twitter, and LinkedIn sharing
- **SEO-Friendly**: Proper canonical URLs and meta descriptions for search engines
- **Backward Compatible**: All existing functionality preserved

## Previous in v3.47.4: Guest Access to Public Events

### Public Event Viewing
- **Guest Access**: Public events can now be viewed without requiring login
- **Privacy-Aware**: Different access levels based on event privacy settings
- **Smart Redirects**: Appropriate redirects and messaging for different user states
- **Login Prompts**: Clear calls-to-action for guests to access additional features

### Privacy Levels
- **Public Events**: Accessible to everyone (guests and logged-in users)
- **Member Events**: Require login to view (redirects guests to login page)
- **Private Events**: Require login and ownership to view
- **Social Sharing**: Available to guests for public events only

### User Experience Improvements
- **Contextual Navigation**: Back button adapts based on user login status
- **Feature Access**: Login prompts replace restricted features for guests
- **Clear Messaging**: Informative messages about event privacy and access requirements

## Previous in v3.47.3: Social Sharing for Calendar Events

### Social Media Integration
- **Facebook Sharing**: Share events directly to Facebook with event details and location
- **Twitter Sharing**: Tweet about events with hashtags and event information
- **LinkedIn Sharing**: Professional sharing with event title and description
- **Email Sharing**: Share via email with formatted event details and link
- **Copy Link**: One-click link copying with visual feedback

### Features
- **Mobile-First Design**: Responsive social buttons that adapt to screen size
- **Privacy-Aware**: Social sharing only available for public events
- **Smart Content**: Auto-generated sharing text with event title, date, and location
- **Visual Feedback**: Hover effects and success/error states for all buttons
- **Debug Integration**: Debug mode logging for troubleshooting

### Technical Implementation
- **Modern Clipboard API**: Uses latest browser APIs with fallback support
- **Bootstrap Integration**: Tooltips and responsive design using Bootstrap 5
- **SEO-Friendly URLs**: Proper URL encoding for social media platforms
- **Cross-Browser Support**: Works on all modern browsers with graceful degradation

## Previous in v3.46.5: Multi-Day Event Popup Enhancement

### Event Details Improvement
- **Complete Date Ranges**: Both hover and click popups now show both start and end dates for multi-day events
- **Enhanced Formatting**: Multi-day events display date ranges and time ranges separately for clarity
- **Better Readability**: Improved date formatting with month abbreviations for clearer information
- **Dual Popup Fix**: Fixed both hover tooltips and click popups to show complete event duration
- **Maintained Compatibility**: Single-day events continue to show "Start Time - End Time" format

### Format Examples
- **Single Day**: "9:00 AM - 5:00 PM"
- **Multi-Day**: "Dec 15, 2024 9:00 AM - Dec 17, 2024 5:00 PM"
- **All Day Events**: "All Day"

## Previous in v3.46.4: Mobile Hover Optimization

### Mobile UX Enhancement
- **Disabled AJAX Mouseovers**: Hover popups no longer display on mobile devices for better touch experience
- **Comprehensive Mobile Detection**: Multi-method detection including user agent, touch capability, screen size, and orientation
- **Preserved Desktop Functionality**: Hover functionality remains unchanged on desktop devices
- **Enhanced Touch Support**: Touch events preserved for mobile interaction while removing hover interference

### Technical Implementation
- **isMobileDevice() Method**: Added robust mobile detection with multiple fallback methods
- **Conditional Event Listeners**: All hover event listeners now check for mobile devices
- **Debug Logging**: Enhanced debugging for mobile detection and hover functionality
- **Backward Compatibility**: No changes to existing desktop functionality

## Previous in v3.46.0: Filter System Integration with Monthly Event Chart

### Major Enhancement
- **Complete Filter Integration**: Advanced filter system now works seamlessly with Monthly Event Chart
- **All Filter Types Supported**: Calendar selection, date ranges, locations, categories, tags, and price ranges
- **Real-Time Filtering**: Events update instantly when filters are applied or changed
- **Infinite Loop Prevention**: Fixed circular dependency between filter system and Event chart

### Critical Fix
- **Event Loading Issue**: Resolved issue where no events were loading in Event chart
- **Filter Communication**: Fixed communication between advanced filter system and Event chart
- **Compatibility Methods**: Added refetchEvents(), clearEvents(), getEvents(), and loadEventsFromAPI()

### Benefits
- **Unified Experience**: Same filtering capabilities across all calendar views
- **Better Performance**: Optimized event loading with proper error handling
- **Enhanced Debugging**: Comprehensive logging for troubleshooting filter issues

## Previous in v3.43.0: Event Chart Spanner Bar Fix

### Spanner Bar Improvements
- **Proper Day Width**: Single day events now fill entire day column width consistently
- **Multi-day Spanning**: Multi-day events properly span across multiple day columns
- **Clean Badge-Only Bars**: Removed event titles from spanner bars, showing only numbered badges
- **CSS Grid Integration**: Fixed event bars to use CSS Grid instead of absolute positioning
- **Enhanced Badge Styling**: Increased badge size and improved styling for better visibility
- **Cleaner Design**: Removed alternating row colors for a more professional appearance

## Previous Updates (v3.37.0): Comprehensive Calendar Improvements

This version introduces major enhancements to the calendar system:

### Event Display Enhancements
- **Fixed Event Bar Spacing**: Added proper spacing between time and event title with `&nbsp;&nbsp;`
- **Improved Scrolling Animation**: Enhanced right-to-left continuous loop scrolling for long event titles
- **Equal Day Cell Sizing**: Fixed calendar day cells to maintain consistent 120px height for better layout

### All-Day Event Positioning
- **Top Positioning**: All-day events now appear at the top of each day cell with full width
- **Separate Sections**: All-day and timed events are organized in distinct sections within day cells
- **Visual Distinction**: All-day events have special styling and positioning

### Time-Based Event Positioning
- **Smart Positioning**: Timed events positioned based on their start/end times within day blocks
- **Percentage-Based Layout**: Events positioned using percentage of day for accurate time representation
- **Layered Display**: Multiple events on same day are properly layered and positioned

### Event Table Below Calendar
- **Comprehensive Listing**: Complete event table with Title, City, State, Start Date/Time, End Date/Time, and Venue
- **Automatic Updates**: Table updates automatically when calendar view changes
- **Sortable Display**: Events sorted chronologically for easy browsing
- **Responsive Design**: Mobile-friendly table layout with proper column sizing

### AJAX Hover Popup
- **Event Preview**: Hover over any event to see detailed popup with event information
- **Timed Display**: 500ms delay before showing, 300ms delay before hiding
- **Rich Information**: Shows title, date, time, location, venue, and description
- **Smart Positioning**: Popup positioned relative to hovered element

### Technical Improvements
- **Enhanced CSS Grid**: Better grid-based event positioning and spanning
- **Improved Event Rendering**: Separate rendering logic for all-day vs timed events
- **Better Mobile Support**: Enhanced responsive design for all screen sizes
- **Performance Optimizations**: Reduced DOM manipulation and improved rendering efficiency

## Previous in v3.35.60: Calendar Keyword Search Parameter Duplication Fix

This version resolves the duplicate parameter issue in keyword search:

- **Fixed Duplicate Parameter Names**: Resolved PDO error caused by using the same parameter name twice in SQL query
- **Separate Title and Description Parameters**: Now uses `:keyword_title` and `:keyword_desc` for unique parameter binding
- **Improved SQL Query Stability**: Better parameter handling for complex search queries
- **Enhanced Database Compatibility**: More reliable parameter binding across different PDO configurations
- **Keyword Search Fully Functional**: Complete resolution of keyword search functionality

## Previous in v3.35.59: Calendar Keyword Search SQL Parameter Fix

## Previous in v3.35.57: Calendar Keyword Search Filter Fix

## Previous in v3.35.56: Advanced Calendar Duplicate Prevention

This version implements comprehensive duplicate event prevention and debugging:

- **Eliminated Initial Event Sources**: Calendar now starts with no event sources to prevent conflicts
- **Automatic Filter Application**: Filter system automatically applies initial filters on page load
- **Duplicate Detection System**: Added real-time duplicate event detection and prevention
- **Enhanced Debugging**: Comprehensive logging to track event sources and loading processes
- **Event Source Management**: Improved event source replacement to prevent accumulation
- **Unique Event Tracking**: Each event is tracked with unique identifiers to prevent duplicates

## Previous in v3.35.49: Map Marker Customization

This version adds customizable map markers for the event map view:

- **Marker Customization**: Choose between different marker types (circle, pin, or custom image)
- **Visual Styling**: Customize marker size, color, and border options
- **Calendar Integration**: Option to use calendar colors for markers
- **Live Preview**: See how markers will appear on the map while configuring
- **Multiple Provider Support**: Works with all supported map providers
- **Mobile-Friendly**: Responsive design for all screen sizes

## Previous in v3.35.46: Automatic Address Geocoding

This version adds automatic geocoding for event addresses:

- **Server-Side Geocoding**: Automatically converts addresses to latitude/longitude coordinates
- **Multiple Provider Support**: Works with Google Maps, Mapbox, HERE Maps, and OpenStreetMap
- **Improved Map Functionality**: Better event placement on maps with accurate coordinates
- **Reduced Client-Side Processing**: Moved geocoding from browser to server for better reliability
- **Configurable System**: Uses the map provider settings from the admin panel
- **Fallback Mechanism**: Gracefully handles geocoding failures with appropriate error logging
- **Debug Mode Support**: Comprehensive debug output when DEBUG_MODE is enabled

## Previous in v3.35.16: Facebook Profile Image Synchronization

This version adds the ability to synchronize user profile images with Facebook:

- **Facebook Profile Image Sync**: Added ability to use Facebook profile image as user profile image
- **One-Click Synchronization**: Simple button to fetch and use the latest Facebook profile image
- **Automatic Image Processing**: Handles downloading, processing, and storing Facebook profile images
- **Enhanced User Experience**: Simplified profile image management for Facebook-connected users
- **Database Support**: Added facebook_token column to users table for API access
- **Secure Implementation**: Uses proper authentication and error handling for Facebook API calls

## Previous in v3.35.15: Privacy Policy, Terms of Service, and Data Deletion

This version adds comprehensive privacy and data management features:

- **Privacy Policy**: Added detailed privacy policy page explaining data collection and usage
- **Terms of Service**: Added comprehensive terms of service document
- **Data Deletion**: Implemented user data deletion functionality with email confirmation
- **Facebook Data Deletion**: Added support for Facebook's Data Deletion Callback API
- **User Control**: Enhanced user control over their personal data
- **GDPR Compliance**: Improved compliance with privacy regulations
- **Database Support**: Added deletion_requests table to track and manage deletion requests

## Previous in v3.35.14: Facebook Login Integration

This version added Facebook Login functionality to the system:

- **Social Login**: Added ability to register and login using Facebook accounts
- **Account Linking**: Automatically links Facebook accounts with existing user accounts by email
- **Enhanced User Experience**: Simplified registration and login process for users
- **Secure Authentication**: Implemented OAuth 2.0 flow with CSRF protection
- **Database Support**: Added facebook_id column to users table
- **Comprehensive Documentation**: Created detailed setup guide in docs/facebook_login_setup.md
- **Configuration Options**: Added Facebook App ID and Secret configuration in config.php

## Previous in v3.35.13: Enhanced Registration Search Functionality

This version significantly improves the registration search functionality in the staff view:

- **Advanced Search Capabilities**: Added ability to search by email, phone, license plate, make, model, and year
- **Hidden Data Search**: Implemented hidden column technique to search non-displayed data
- **Improved DataTables Integration**: Fixed DataTables loading and configuration issues
- **Enhanced Error Handling**: Added robust error handling for search functionality
- **Mobile-Responsive Design**: Ensured search works properly on all device sizes
- **Debug Mode Support**: Added comprehensive debug output for troubleshooting
- **Performance Optimization**: Improved search performance with DataTables' built-in functionality
- **Backup Creation**: Created backups of modified files in autobackup/registration_search_enhanced directory

## Previous in v3.35.12: Added Live Search to Registrations

This version added live search functionality to the registrations table in staff view:

- **Real-Time Filtering**: Implemented filtering that updates as users type in the search box
- **Comprehensive Search**: Added ability to search across all registration fields
- **Clear Button**: Added one-click search reset functionality
- **Enhanced Data Access**: Updated RegistrationModel to include additional searchable fields
- **Code Organization**: Created dedicated registration-search.js file for better maintainability

## Previous in v3.35.7: Fixed SQL Parameter Binding in Payment Processing

This version fixes a critical SQL error in the registration payment processing:

- **Fixed SQL Parameter Error**: Resolved "SQLSTATE[HY093]: Invalid parameter number" error in updatePayment method
- **Improved Parameter Binding**: Added unique parameter name for duplicate parameter in SQL query
- **Enhanced SQL Query**: Modified CASE statement to use a distinct parameter name for the condition
- **Consistent Data Handling**: Maintained the same business logic while fixing the parameter binding issue
- **Backup Creation**: Created backup of original RegistrationModel.php in autobackup directory

## Previous in v3.35.6: Fixed Staff Payment Processing Method

This version fixes a critical error in the staff payment processing functionality:

- **Fixed Method Call Error**: Resolved "Call to undefined method RegistrationModel::updatePaymentStatus()" error
- **Correct Method Implementation**: Updated StaffController to use the existing updatePayment() method
- **Enhanced Data Passing**: Added proper parameter structure for the updatePayment method
- **Consistent Payment Status**: Ensured payment status is correctly updated after manual payment processing
- **Backup Creation**: Created backup of original file in autobackup directory

## Previous in v3.35.5: Fixed Undefined Array Key Warnings in Payment Processing

This version fixes undefined array key warnings in the payment processing system:

- **Fixed Array Key Warnings**: Resolved "Undefined array key" warnings for user_id, payment_type, and related_id in PaymentModel.php
- **Enhanced Data Validation**: Added null coalescing operators to safely handle missing array keys
- **Improved StaffController**: Updated StaffController to properly set all required payment data fields
- **Backward Compatibility**: Maintained compatibility with existing payment processing workflows
- **Backup Creation**: Created backups of original files in autobackup directory

## Previous in v3.35.4: Fixed Vehicle Display in Staff Payment Processing

This version fixes property warnings in the staff payment processing view:

- **Fixed Property Warnings**: Resolved "Undefined property" warnings for vehicle_year, vehicle_make, and vehicle_model
- **Property Name Alignment**: Updated property names to match database field names (year, make, model)
- **Improved Data Consistency**: Ensured view template uses the correct property names from the database query
- **Backup Creation**: Created backup of original process_payment.php in autobackup directory
- **Fixed CSRF Issue**: Resolved "Invalid request" error when clicking process payment button

## Previous in v3.35.3: Fixed Vehicle Display in Staff Registrations

This version fixes property warnings in the staff payment processing view:

- **Fixed Property Warnings**: Resolved "Undefined property" warnings for vehicle_year, vehicle_make, and vehicle_model
- **Property Name Alignment**: Updated property names to match database field names (year, make, model)
- **Improved Data Consistency**: Ensured view template uses the correct property names from the database query
- **Backup Creation**: Created backup of original process_payment.php in autobackup directory

## Previous in v3.35.3: Fixed Vehicle Display in Staff Registrations

This version fixes property warnings in the staff registrations view:

- **Fixed Property Warnings**: Resolved "Undefined property" warnings for vehicle_year, vehicle_make, and vehicle_model
- **Property Name Alignment**: Updated property names to match database field names (year, make, model)
- **Improved Data Consistency**: Ensured view template uses the correct property names from the database query
- **Backup Creation**: Created backup of original registrations.php in autobackup directory

## Previous in v3.35.2: Fixed Staff Registrations View

This version fixes a critical error in the staff registrations view:

- **Fixed Fatal Error**: Resolved "Call to undefined method RegistrationModel::getRegistrationsByShow()" error
- **Method Name Correction**: Updated method call to use the correct method name getShowRegistrations()
- **Improved Code Consistency**: Aligned controller method calls with model method names
- **Backup Creation**: Created backup of original StaffController.php in autobackup directory

## Previous in v3.35.1: Fixed Staff Show View

This version fixes an issue in the staff show view:

- **Fixed PHP Warning**: Resolved "Undefined property: stdClass::$category_id" warning in staff/show.php
- **Enhanced Database Queries**: Updated SQL queries to include category_id in the result set
- **Improved Data Consistency**: Ensured consistent property naming across all database result objects
- **Backup Creation**: Created backup of original RegistrationModel.php in autobackup directory

## Previous in v3.35.0: Added Show Staff Role

This version adds a new "Show Staff" role to the system:

- **New Role**: Added "Staff" role to the role hierarchy system
- **Assignment System**: Created staff assignment system to link staff to specific shows
- **Enhanced Permissions**: Staff can create/edit registrations, process payments, and check-in vehicles
- **Coordinator Control**: Coordinators can assign staff to their shows
- **Admin Management**: Admins can promote users to staff and manage all staff assignments
- **Improved Workflow**: Staff members can only access shows they are assigned to
- **Database Support**: Added new database table for staff assignments
- **Documentation**: Updated documentation to reflect the new role system

## Previous in v3.34.52: Fixed PHP Deprecation Warning

This version fixes a PHP deprecation warning in the financial report:

- **Fixed Deprecation Warning**: Added null checks to number_format() calls to prevent warnings
- **Improved Data Handling**: Added null coalescing operator (??) to handle null values in revenue data
- **Enhanced Stability**: Ensured all number formatting functions handle null values gracefully
- **Backward Compatibility**: Maintained compatibility with newer PHP versions

## Previous in v3.34.51: Fixed Manual Payment Link Visibility

This version ensures the manual payment link is visible in the actions dropdown:

- **Enhanced Visibility**: Removed the is_free condition to ensure the manual payment option is always available
- **Diagnostic Information**: Added debug output to help troubleshoot the is_free value
- **Improved Accessibility**: Ensured the manual payment option appears for all pending/unpaid registrations
- **Consistent Placement**: Maintained the placement in the actions dropdown menu for better user experience

## Previous in v3.34.50: Improved Manual Payment Access

This version improves the placement of the manual payment option:

- **Better Placement**: Moved "Manual Payment" link to the actions dropdown menu in the registrations table
- **Streamlined Interface**: Placed the manual payment option alongside other payment-related actions
- **Consistent Conditions**: Maintained the same conditional logic for displaying the manual payment option
- **Cleaner Code**: Removed debug output that was added for troubleshooting

## Previous in v3.34.49: Fixed Manual Payment Button Display

This version fixes issues with the manual payment button display:

- **Improved Visibility**: Fixed "Manual Payment" button not showing by adding debug output and correcting conditions
- **Enhanced Status Handling**: Now checks for both 'pending' and 'unpaid' payment statuses
- **Corrected Type Comparison**: Fixed comparison for is_free value (integer vs string)
- **Diagnostic Information**: Added debug output to help troubleshoot display conditions

## Previous in v3.34.48: Refined Manual Payment Button Logic

This version refines the display logic for the manual payment button:

- **Updated Display Condition**: Modified the "Manual Payment" button to only appear for free shows
- **Precise Logic**: Now checks specifically for is_free='1' in the shows table
- **Business Rule Alignment**: Better alignment with business requirements for payment processing
- **Improved Conditional Logic**: Enhanced conditional display for better user experience

## Previous in v3.34.47: Improved Manual Payment Access

This version enhances the accessibility of manual payment processing:

- **Direct Manual Payment Access**: Added "Manual Payment" button to admin registration view for unpaid registrations
- **Streamlined Payment Workflow**: Administrators can now directly access manual payment processing from the registration view
- **Conditional Display**: Button only appears for shows with pending payment status
- **Improved User Experience**: Enhanced interface makes manual payment processing more accessible

## Previous in v3.34.46: Enhanced Project Documentation

This version adds comprehensive documentation to improve project portability and understanding:

- **Project Structure Documentation**: Added structure.md with detailed project structure information
- **Feature Documentation**: Added features.md listing all implemented features and their status
- **Future Development Roadmap**: Added task_pending.md with features planned for future development
- **Manual Payment System Documentation**: Added detailed documentation of the manual payment system
- **Improved Project Portability**: Enhanced documentation makes the project easier to understand and maintain

## Previous in v3.34.45: Fixed CSRF Function Error

This version fixes a critical error with CSRF protection in admin listing views:

- **Added Missing Function**: Added the missing generateCsrfInput() function to csrf_helper.php
- **Fixed Fatal Error**: Resolved "Call to undefined function generateCsrfInput()" error
- **Consistent CSRF Protection**: Ensured consistent CSRF protection across all forms
- **Backup Creation**: Created backup of original csrf_helper.php in autobackup directory
- **Version Update**: Updated version number in all relevant files

## Previous in v3.34.44: Improved Admin Next Steps Section

This version enhances the admin Next Steps section:

- **Improved UI Clarity**: Replaced "Customize Registration Form" card with "Configure Show Settings" card
- **Role-Based Visibility**: Added role-based visibility for the Next Steps section
- **Enhanced User Experience**: Improved UI clarity for different user roles
- **Admin-Only Features**: Next Steps section now only visible to administrators, hidden for coordinators

## Previous in v3.34.43: Fixed ShowModel System Fields

This version fixes a critical error in the ShowModel class:

- **Fixed Fatal Error**: Resolved "Cannot redeclare ShowModel::$systemFields" error
- **Removed Duplicates**: Eliminated duplicate declarations of properties
- **Merged Fields**: Combined system fields from both declarations to maintain functionality
- **Backup Creation**: Created backup of original file in autobackup directory

## Previous in v3.34.41: Fixed Database Configuration Issue

This version fixed a critical configuration issue in the listing fee tables creation script:

- **Added Config Include**: Added missing require_once for config.php
- **Database Constants**: Ensured DB_HOST, DB_USER, DB_PASS, and DB_NAME constants are defined
- **Improved Error Handling**: Enhanced script reliability with proper configuration loading
- **Backup Creation**: Added backup of the fixed script for reference
- **Version Update**: Updated version number in all relevant files

## Previous in v3.34.40: Fixed Database Path Issue

This version fixed a critical path issue in the listing fee tables creation script:

- **Fixed Database Path**: Corrected the path to Database.php in create_listing_fee_tables.php
- **Improved Script Reliability**: Ensured the script can properly connect to the database
- **Backup Creation**: Added backup of the original script for reference
- **Version Update**: Updated version number in all relevant files

## Previous in v3.34.39: Enhanced Coordinator Show Creation

This version enhances the coordinator's show creation process with a template-based system:

- **Template-Based Forms**: Implemented dynamic form system for show creation
- **Listing Fee Integration**: Added listing fee display and payment integration
- **Automatic Coordinator Assignment**: Shows are automatically assigned to the creating coordinator
- **Mobile-First Design**: Enhanced responsive design for all screen sizes
- **Improved Date Handling**: Added automatic date suggestions and validation
- **Next Steps Guidance**: Added guidance section after show creation
- **Custom Fields Support**: Added support for custom fields in show creation
- **Fixed Field Naming**: Resolved inconsistencies between registration date field names
- **Enhanced Error Handling**: Improved form validation and error display
- **Streamlined Payment Flow**: Integrated with payment system for listing fees

## Previous in v3.34.38: Fixed Coordinator Reports Functionality

This version fixes issues with the coordinator reports section and enhances user profiles:

- **Fixed Database Table References**: Updated all queries to use show_categories instead of categories
- **Fixed User Name Field**: Updated all queries to use the single name column instead of first_name/last_name
- **Fixed Registration Report**: Implemented working registration statistics with proper data
- **Fixed Financial Report**: Added revenue tracking by category and payment method
- **Fixed Judging Report**: Added winners display and judge performance metrics
- **Fixed Show Report**: Added comprehensive show statistics and recent registrations
- **Fixed Data Export**: Implemented CSV export functionality for all reports
- **Mobile-First Design**: Maintained responsive design across all report interfaces
- **Added Database Check**: Created SQL script to ensure required tables exist
- **Enhanced User Profiles**: Added phone and address fields to user profiles
- **Updated Registration Forms**: Added contact information fields to registration and profile forms
- **Fixed Export Error**: Resolved "Unknown column 'u.phone'" error in export registrations functionality

## Previous in v3.34.36: Separated Admin and Coordinator Payment Settings

This version separates admin and coordinator payment settings:

- **Separate Payment Credentials**: Coordinators now have their own payment credentials for receiving registration fees
- **Admin-Only Settings**: Admin payment settings are used exclusively for receiving listing fees from coordinators
- **Show-Specific Settings**: Each show can have its own payment settings configured by the coordinator
- **Enhanced Database Structure**: Added is_admin flag to payment settings tables
- **Improved Coordinator Interface**: Updated coordinator payment settings interface
- **Clear Separation of Concerns**: Admin settings for listing fees, coordinator settings for registration fees
- **Mobile-First Design**: Maintained responsive design across all payment interfaces

## Previous in v3.34.32: Fixed Judge Dashboard Duplicate Shows

This version fixes an issue with duplicate shows appearing in the judge dashboard:

- **Eliminated Duplicate Shows**: Fixed issue where shows would appear multiple times in the judge dashboard
- **DISTINCT Query**: Modified the getShowsByJudge method to use DISTINCT in SQL query
- **Improved Judge Experience**: Judges now see a clean list of unique shows they are assigned to
- **Multiple Category Support**: Properly handles cases where judges are assigned to multiple categories in the same show

## Previous in v3.34.31: Enhanced Vehicle Visualization in Registrations

This version improves the visual representation of vehicles in both user and admin registration pages:

- **Vehicle Primary Images**: Replaced generic fa-car icons with actual vehicle primary images
- **Consistent Experience**: Implemented the same image display logic across all interfaces
- **Multi-Source Retrieval**: Added support for retrieving images from both direct vehicle_image and database images table
- **Primary Image Priority**: First checks for primary images, then falls back to most recent images
- **Performance Optimization**: Prioritized thumbnail images for faster page loading
- **Path Handling**: Added proper path handling for image URLs with leading slash management
- **File Verification**: Added checks to ensure image files exist before attempting to display them
- **Fallback Mechanism**: Automatically falls back to fa-car icon when no vehicle image is available
- **Improved Visual Experience**: Enhanced visual representation of vehicles in all registration lists
- **Debug Mode Support**: Added debug information for troubleshooting image display issues
- **Mobile-First Design**: Fully responsive interface works on all device sizes

## Previous in v3.34.30: Enhanced User Profile Image Management

This version adds comprehensive profile image management for users:

- **Profile Image Upload**: Users can now upload and manage their profile image
- **Image Deletion**: Added ability to delete profile image with confirmation
- **Advanced Image Management**: Full integration with the Image Manager for advanced editing
- **Consistent Image System**: Uses the existing images table with entity_type='user'
- **One Image Policy**: Enforces one profile image per user for consistency
- **UI Integration**: Profile image appears in navigation, profile page, and dashboard
- **Mobile-First Design**: Fully responsive interface works on all device sizes
- **Secure File Handling**: Improved security for uploaded profile images
- **User Experience**: Streamlined workflow for managing profile images

## Previous in v3.34.27: Permission-Based Image Browsing

This version enhances the image browser with permission-based filtering:

- **Entity-Specific Image Browsing**: Added ability to browse images specific to a show or vehicle
- **Permission-Based Access**: Images are filtered based on user permissions and entity ownership
- **Improved Navigation**: Added direct links from show and vehicle pages to their specific image collections
- **Enhanced Security**: Users can only see images they have permission to access
- **Streamlined Workflow**: Simplified image management for show coordinators and vehicle owners

## Previous in v3.34.26: UI Improvements

This version includes UI improvements for better mobile responsiveness:

- **Streamlined Edit Show Template**: Removed the Show Management table from the template-based edit show page (edit_with_template.php) for a cleaner, more mobile-friendly interface
- **Improved Mobile Experience**: Enhanced layout for better usability on smaller screens
- **Simplified Navigation**: Reduced redundant UI elements for a more focused user experience

## Previous in v3.34.25: Admin Interface Improvements

This version includes important fixes for the admin interface:

- **Fan Votes Page**: Fixed "Too few arguments" error when accessing the fan votes page
- **Improved Error Handling**: Better handling when accessing pages without required parameters
- **Graceful Redirection**: Users are now redirected to appropriate pages when required parameters are missing

## Previous in v3.34.24: Universal Background Execution

This version ensures background execution works for all controllers:

- **Coordinator Interface**: Background execution now works when coordinators mark shows as completed
- **Admin Interface**: Background execution works when admins mark shows as completed
- **Universal Compatibility**: The same background execution method is used across all interfaces
- **Consistent Behavior**: Scoring scripts run in the background regardless of how a show is marked as completed

## Previous in v3.34.23: Background Scoring Execution

This version implements true background execution of scoring scripts:

- **Background Execution**: Scoring scripts now run in the background using shell commands
- **No UI Disruption**: Scripts run completely independently of the web UI
- **Improved Task Status**: Task status is properly reset after execution
- **Multiple Shell Methods**: Supports exec, shell_exec, system, and passthru for maximum compatibility

## Previous in v3.34.22: Silent Scoring Execution

This version fixes issues with scoring script output:

- **Output Buffering**: Added output buffering to capture script output instead of displaying it
- **Fixed Constant Redefinition**: Fixed APPROOT constant redefinition warning
- **Improved User Experience**: Scoring now runs silently in the background without disrupting the user interface
- **Enhanced Logging**: Script output is now logged to the error log for debugging

## Previous in v3.34.21: Direct Scoring Execution

This version implements direct execution of scoring scripts when a show is marked as completed:

- **Direct Script Execution**: Always runs the scoring script directly when a show is marked as completed
- **Bypass Task System**: Bypasses the scheduled task system for maximum reliability
- **Multiple Execution Points**: Ensures scoring runs from all places where show status can be changed
- **Improved Error Handling**: Enhanced error handling and logging for script execution

## Previous in v3.34.20: Automatic Task Recovery

This version implements automatic task recovery and timeout handling:

- **Timeout Handling**: Added timeout handling for scheduled tasks
- **Auto-Reset Script**: Added script to automatically reset stuck tasks
- **Execution Time Tracking**: Added tracking of task execution time
- **Process Identification**: Improved process identification for running tasks
- **Permanent Solution**: Implemented permanent solution for stuck tasks

## Previous in v3.34.19: Direct Script Execution

This version implements direct script execution for show completion tasks:

- **Direct Script Inclusion**: Now directly includes and executes the task script
- **Simplified Execution Flow**: Removed multiple execution methods in favor of direct inclusion
- **Verified Working Method**: Based on successful test_cli.php results
- **Enhanced Status Management**: Properly updates task status after execution

## Previous in v3.34.18: Enhanced Diagnostics for LightSpeed

This version adds enhanced diagnostics specifically for LightSpeed webserver:

- **Permission Checking**: Added script to check file and directory permissions
- **Detailed Task Analysis**: Added script for detailed analysis of scheduled tasks
- **Direct Task Execution**: Improved direct task execution script
- **Comprehensive Logging**: Enhanced logging for better troubleshooting

## Previous in v3.34.17: Advanced Diagnostics and Date Correction

This version adds advanced diagnostics and date correction tools:

- **CLI Testing Tools**: Added scripts to test PHP CLI functionality
- **Direct Task Execution**: Added script for direct execution of scheduled tasks
- **Date Correction**: Added script to fix future dates in scheduled tasks
- **Detailed Logging**: Enhanced logging for better troubleshooting
- **Task Status Reset**: Automatic reset of stuck tasks

## Previous in v3.34.16: Optimized for LightSpeed Webserver

This version optimizes scheduled task execution specifically for LightSpeed webserver:

- **Multiple Linux Execution Methods**: Implemented multiple Linux-specific methods to ensure tasks run
- **Nohup Support**: Added nohup for reliable background execution
- **Fallback Mechanisms**: Implemented multiple fallback execution methods
- **Direct Execution Option**: Added direct execution option for critical tasks

## Previous in v3.34.15: Enhanced Compatibility for Scheduled Tasks

This version significantly improves the execution of scheduled tasks on Windows systems:

- **Multiple Execution Methods**: Implemented multiple methods to ensure tasks run on Windows
- **Direct Script Inclusion**: Added direct script inclusion as a fallback method
- **Improved Command Execution**: Enhanced Windows command execution with popen
- **Global Variable Support**: Added support for passing task IDs via global variables

## Previous in v3.34.14: Refined Scheduled Tasks Execution

This version refines the scheduled tasks execution system:

- **Removed Direct Script Fallback**: Removed the fallback to directly running calculate_show_winners.php
- **Task-Based Execution Only**: Now only executes tasks that are properly configured in the scheduled_tasks table
- **Improved Task Management**: Better handling of task execution and status updates

## Previous in v3.34.13: Fixed Scheduled Tasks Execution

This version fixes issues with scheduled tasks not being executed when a show's status is changed to "completed":

- **Direct Script Execution**: Added direct script execution for show completion tasks
- **Enhanced Status Change Detection**: Improved detection of show status changes
- **Detailed Logging**: Added comprehensive logging for task execution
- **Verification System**: Added verification of task status updates
- **Fallback Mechanisms**: Implemented multiple fallback mechanisms for task execution

## Previous in v3.34.12: Fixed Modal Dialog Issues

This version fixes issues with modal dialogs in the scheduled tasks interface:

- **Fixed Modal Backdrop Issue**: Resolved problem where the page remains locked/greyed out after closing a modal
- **Enhanced Modal Cleanup**: Added comprehensive cleanup for modal artifacts when dialogs are closed
- **Improved Cancel Button Handling**: Added explicit event handlers for cancel buttons to ensure proper modal dismissal
- **Global Escape Key Handler**: Added support for the Escape key to properly close modals and clean up artifacts
- **Mobile-First Design**: All interfaces are fully responsive for all device sizes

## Previous in v3.34.10: Enhanced Case-Insensitive Event Handling

This version further improves the scheduled tasks system with case-insensitive event handling:

- **Case-Insensitive Event Matching**: Modified EventTrigger to match event names case-insensitively
- **Multiple Event Name Formats**: Added support for both "show_completed" and "Show Completed" event name formats
- **Improved Status Detection**: Enhanced status change detection with case-insensitive comparisons
- **Detailed Logging**: Added comprehensive logging for event triggering and task execution
- **SQL Updates**: Added SQL commands to ensure existing tasks work with the new system
- **Mobile-First Design**: All interfaces are fully responsive for all device sizes

## Previous in v3.34.9: Fixed Event Triggering for Scheduled Tasks

This version fixes issues with event triggering for scheduled tasks:

- **Fixed Show Status Change Events**: Resolved issue where changing a show's status to "Completed" in the admin interface didn't trigger scheduled tasks
- **Enhanced Event Triggering**: Modified ShowModel to properly trigger events when a show's status is changed through the edit form
- **Improved Logging**: Added detailed logging for event triggering to help diagnose issues
- **Updated Database Script**: Created SQL update script to ensure all required tables exist and have the correct structure
- **Mobile-First Design**: All interfaces are fully responsive for all device sizes

## Previous in v3.34.8: Enhanced Scheduled Tasks System

This version further improves the scheduled tasks system:

- **Fixed Save Changes Button**: Resolved issue with the "Save Changes" button not working when editing a task
- **Enhanced JavaScript Handling**: Added explicit form submission handling for the edit task form
- **Improved Error Handling**: Added detailed error logging and debugging for task operations
- **Updated Database Script**: Created SQL update script to ensure scheduled_tasks table is properly set up
- **Mobile-First Design**: All interfaces are fully responsive for all device sizes

## Previous in v3.34.7: Fixed Scheduled Tasks System

This version fixes issues with the scheduled tasks system:

- **Fixed Edit Task Functionality**: Resolved issue with the "Save Changes" button not working when editing a task
- **Improved Database Initialization**: Added proper database initialization in the getTask method
- **Enhanced Form Submission**: Updated form action URLs to ensure proper submission
- **Added Database Update Script**: Created SQL update script to ensure scheduled_tasks table is properly set up
- **Mobile-First Design**: All interfaces are fully responsive for all device sizes

## Previous in v3.34.6: Enhanced Task Automation & PHP 8.4 Compatibility

This version enhances the scheduled tasks system and ensures PHP 8.4 compatibility:

- **Dynamic Task Parameters**: Added support for passing event parameters to scheduled tasks
  - Use `{event_parameter}` placeholder in task parameters to receive event-specific data
  - For example, `--show_id={event_parameter}` will receive the show ID when a show is completed
  - Automatically passes relevant context from events to scheduled tasks
- **PHP 8.4 Compatibility**: Fixed deprecation warnings for dynamic properties
  - Added proper property declarations in ShowModel class
  - Ensures smooth operation with PHP 8.4 and later versions
- **Database Updates**: Added SQL update script (sql/update_scheduled_tasks_v2.sql) for new features

## Previous in v3.34.9: Centralized Scoring System & Task Automation

This version simplifies and improves the scoring system architecture and adds task automation:

- **Centralized Scoring Logic**: Updated scripts to use VehicleScoringModel for all scoring operations
- **Simplified Workflow**: Updated run_all_scoring.php to execute all scripts in sequence
- **Improved Maintainability**: Reduced code duplication and enhanced error handling
- **Consistent Scoring**: Ensured all scoring operations use the same logic and database tables
- **Database Updates**: Added SQL update script (sql/update_3.34.9.sql) for table compatibility
- **Internal Cron System**: Added scheduled tasks system for automating operations:
  - Admin interface for managing scheduled tasks
  - Support for event-triggered, time-based, and interval-based tasks
  - Automatic scoring when show status changes to "completed"
  - Daily and interval-based maintenance tasks

## Previous in v3.34.8: Improved Scoring Fairness

This version enhanced the fairness of the scoring system:

- **Fair Perfect Scores**: Modified scoring to only allow a perfect 100 score for vehicles with perfect scores on all metrics
- **Improved Differentiation**: Enhanced the scoring system to better differentiate between excellent and perfect entries
- **Consistent Implementation**: Updated both scoring methods to use the same improved logic
- **Enhanced Testing**: Added detailed vehicle score examination capabilities to the testing script

## Previous in v3.34.7: Fixed Inconsistent Scoring

This version fixed inconsistencies between scoring implementations:

- **Consistent Scoring**: Fixed inconsistencies between VehicleScoringModel.php and generate_vehicle_scores.php
- **Single Source of Truth**: Modified generate_vehicle_scores.php to directly use VehicleScoringModel for calculations
- **Maintained Functionality**: Preserved detailed database updates while ensuring consistent scoring
- **Improved Code Organization**: Reduced duplication and maintenance burden

## Previous in v3.34.6: Database Table Reference Fixes

This version fixed issues with database table references in scoring scripts:

- **Fixed Database References**: Updated scripts to use the correct judge_metric_scores and judge_total_scores tables
- **PHP 8.4.6 Compatibility**: Fixed deprecated function calls and improved code for PHP 8.4.6
- **Improved Error Handling**: Enhanced error handling for database operations
- **Consistent Table Names**: Ensured consistent table name usage across all scripts

## Previous in v3.34.5: Fixed Score Normalization

This version fixes issues with score normalization and improves the scoring system:

- **Fixed Score Normalization**: Corrected the normalization logic to properly respect the normalize_scores setting
- **Consistent Scoring**: Ensured consistent scoring behavior between web application and scripts
- **Score Capping**: Scores are now properly capped at 100 points when normalize_scores is enabled
- **Improved Documentation**: Added comprehensive documentation for the scoring system
- **Scoring Consistency Test**: Added a test script to verify consistent scoring between implementations
- **Long-term Maintainability**: Enhanced code structure for easier maintenance and future updates

## Previous in v3.34.0: Judge Score Editing

This version adds the ability to edit judge scores from the admin interface:

- **Judge Score Editing**: Administrators can now edit individual judge scores from the manage scores page
- **Metric-Level Editing**: Edit individual metric scores for each judge's evaluation
- **Automatic Recalculation**: Vehicle total scores are automatically recalculated after editing judge scores
- **Enhanced Score Management**: Improved workflow for correcting judging errors
- **Mobile-First Design**: Score editing interface is fully responsive for all device sizes

## Previous in v3.33.0: Improved Scoring Management System

This version enhances the scoring system with better management tools and normalized scores:

- **Normalized Score Scale**: Fixed score normalization to ensure all scores are properly scaled to 1-100
- **Admin Scoring Interface**: Added dedicated admin pages for managing scoring settings and viewing scores
- **Centralized Score Management**: New interface to view judge scores, vehicle scores, and category winners
- **Automated Calculation Sequence**: Added master script to run all scoring calculations in the correct order
- **Improved Winner Calculation**: Fixed category winner calculation to properly update when scores change
- **Enhanced Score Visibility**: Better display of raw, weighted, normalized, and final scores
- **Score Clearing Tools**: Added ability to clear specific score types for a show
- **Mobile-First Design**: All new interfaces are fully responsive for all device sizes

## Previous in v3.32.0: Enhanced Role Hierarchy Implementation

This version improves the hierarchical role system implementation:

- **Inclusive Judge Selection**: Now includes administrators and coordinators in the judge selection pool
- **Expanded Judging Capabilities**: Allows admins and coordinators to serve as judges for events
- **Consistent Auth Usage**: Updated ShowController to use Auth class for role checking
- **Improved Access Control**: Enhanced unpublished show access to respect hierarchical roles
- **No Database Changes**: Implemented through code changes without requiring database updates

## Previous in v3.31.0: Hierarchical Role Access System

This version implements a hierarchical role access system:

- **Role Inheritance**: Higher-level roles automatically inherit access to all functions of lower roles
- **Administrator Access**: Administrators can access all coordinator, judge, and user features
- **Coordinator Access**: Coordinators can access all judge and user features
- **Judge Access**: Judges can access all regular user features
- **Seamless Integration**: Works site-wide with existing role-based access controls
- **No Database Changes**: Implemented through code changes without requiring database updates

## Previous in v3.30.0: Improved Show Winners Calculation

This version enhances the show winners calculation system:

- **Direct Score Retrieval**: Now uses the total_score column from vehicle_total_scores table to determine winners
- **Raw Score Display**: Shows actual score values instead of percentages for more accurate representation
- **Simplified Winner Determination**: Streamlined process for determining category winners
- **Enhanced Database Integration**: Improved database queries for better performance
- **SQL Update Script**: Added update_show_winners_calculation.sql to ensure proper database structure
- **Removed Dependency**: No longer requires VehicleScoringModel for winner calculation

## Previous in v3.29.0: Normalized Score Display

This version improves the score display system:

- **Normalized Percentage Scores**: All scores are now displayed as normalized percentages
- **Consistent Score Format**: Scores are shown with a percentage sign (%) for clarity
- **Enhanced Winner Cards**: Added score display to category winner cards
- **Improved Score Calculation**: Modified VehicleScoringModel to always normalize scores
- **Database Updates**: Updated show_scoring_settings to use normalized scores by default

## Previous in v3.28.0: Judge-Specific Score Display

This version adds a new script for displaying scores by judge:

- **Judge-Specific Tabs**: New script to display scores organized by judge with a tabbed interface
- **Average Score Calculation**: Automatically calculates and displays average scores across all judges
- **Detailed Metric Breakdown**: Shows detailed metric scores for each judge
- **Direct Database Access**: Uses the scores table directly instead of vehicle_total_scores
- **Mobile-First Design**: Fully responsive design for all device sizes
- **Consistent Calculation**: Uses the same calculation method as the judge/viewScores page

## Previous in v3.27.0: Enhanced Vehicle Score Display

This version improves the vehicle score viewing experience:

- **Judge-Specific Score Tabs**: Added tabbed interface to display detailed scores by each judge
- **Improved Score Organization**: Scores are now grouped by judge for better readability
- **Database Compatibility Fix**: Updated references from scoring_settings to show_scoring_settings
- **Automatic Table Creation**: Added SQL script to ensure required tables exist

## Previous in v3.26.0: Improved Score Retrieval System

This version enhances the score viewing experience:

- **Database-Driven Scoring Display**: Modified the judge/viewScores page to retrieve scores directly from database tables
- **Performance Optimization**: Eliminated redundant calculations by using pre-calculated scores stored in the database
- **Comprehensive Score Data**: Now retrieves all score components (raw scores, weights, weighted scores, age weighted scores, final normalized scores) from vehicle_total_scores and vehicle_metric_scores tables
- **Backward Compatibility**: Maintained fallback calculation if stored scores are not available

## Previous in v3.25.0: Fixed Information Display in Judge Scoring View

This version fixes important issues with the judging system:

- **Persistent Information Display**: Fixed issue where important information boxes would disappear after 5 seconds on the judge scoring view
- **Enhanced Judge Experience**: All scoring information now remains visible throughout the entire scoring review process
- **Improved UI Consistency**: Judges can always see:
  - Formula details and name
  - Understanding Weighted Scores explanation
  - Age Weight Applied information
  - Any warning messages

Previous version (3.24.0) included:
- Fixed judging completion status for vehicles with only draft scores
- Resolved image display errors in the judging interface
- Added robust fallbacks for different image property naming conventions

## Previous in v3.23.0: Fixed Registration Viewing Error

This version fixed a critical error in the registration viewing functionality:

- **Fixed Fatal Error**: Resolved error in UserController when viewing registration details
- **Added Missing Methods**: Implemented missing methods in JudgingModel:
  - Added getJudgingResultsByRegistration() method to retrieve judging results
  - Added getAwardsByRegistration() method to retrieve awards for a registration
- **Improved Error Handling**: Added proper error handling with fallbacks for missing tables
- **Enhanced Stability**: Ensured registration details can be viewed without errors

## Previous in v3.22.0: Improved Score Generation and Bug Fixes

This version includes important fixes and improvements to the vehicle score generation system:

- **Fixed Function Name Conflict**: Resolved conflict between generate_vehicle_scores.php and calculate_show_winners.php
- **Improved Duplicate Prevention**: Enhanced checks to prevent duplicate entries in vehicle_metric_scores table
- **Better Error Handling**: Added try-catch blocks for more robust error handling during metric score insertion
- **Optimized Database Operations**: Improved database operations for better performance and reliability

## New in v3.21.0: Automated Vehicle Score Generation

This version adds a new script for automated vehicle score generation:

- **Batch Score Generation**: New script to generate scores for all vehicles in a show with a single operation
- **Enhanced Score Storage**: Added additional columns to vehicle_total_scores table for more detailed score tracking
- **Comprehensive Score Data**: Now stores raw scores, weighted scores, and normalized scores for better analysis
- **Formula Tracking**: Records which formula was used to calculate each vehicle's score
- **Metric-Level Scoring**: New vehicle_metric_scores table stores detailed scores for each judging metric
- **Display Number Tracking**: Records vehicle display numbers for easier identification at events
- **Weight Multiplier Storage**: Tracks the weight multiplier used in score calculations
- **Automatic Winner Calculation**: Automatically executes the show winners calculation after generating scores
- **Mobile-First Design**: Score generation script is fully responsive for all devices
- **Detailed Reporting**: Provides comprehensive summary of processed vehicles and their scores
- **Error Handling**: Robust error checking to ensure all vehicles are properly processed
- **Database Updates**: Added SQL script to update the vehicle_total_scores table structure and create the vehicle_metric_scores table

## Previous in v3.20.0: Fan Voting System Fix and PHP 8.4 Compatibility

This version fixes issues with the fan voting system and improves PHP 8.4 compatibility:

- **Fixed Fan Voting Page**: Resolved issue with fan voting page not displaying properly
- **Fixed Show Results Page**: Resolved issue with show results page not displaying due to missing table
- **Updated Image Retrieval**: Fixed VehicleModel to use the images table instead of the non-existent vehicle_images table
- **Full-Size Images**: Updated to use original images instead of thumbnails for better quality
- **Responsive Image Scaling**: Images now properly scale to fit container width and viewport size
- **Display Number Support**: Updated vote pages to show display numbers instead of registration numbers
- **Enhanced UI**: Improved the voting UI with prominent display number badges and centered voting options
- **Larger Vehicle Images**: Increased image size and improved image container styling for better visual appeal
- **Modern Card Design**: Added shadow effects, rounded corners, and improved spacing for a more attractive layout
- **Improved Button Styling**: Enhanced buttons with modern styling, shadows, and better spacing
- **Viewport-Aware Layout**: Redesigned pages to automatically adjust to browser viewport size
- **Fluid Container Design**: Implemented fluid containers for better space utilization on all devices
- **Fixed Path Duplication**: Fixed issue with duplicate path in image URLs
- **Automatic Table Creation**: Added automatic creation of fan_votes and category_winners tables if they don't exist
- **Direct Database Access**: Updated code to use category_winners table directly without intermediate views
- **Improved Error Handling**: Enhanced error handling for fan voting and show results operations
- **Database Updates**: Added SQL scripts to create required tables and views
- **Mobile-First Design**: Ensured fan voting pages are fully responsive for mobile devices
- **PHP 8.4 Compatibility**: Fixed deprecated dynamic property creation in UserController

## Previous in v3.19.0: Vehicle Owner Score Viewing and Show Results

This version adds new features for vehicle owners to view their scores and for the public to view show results:

- **Vehicle Owner Score Page**: Owners can now view their judging scores and comments once judging is completed
- **Public Results Page**: Shows marked as "Completed" now display category winners with 1st, 2nd, and 3rd place
- **Automatic Winner Calculation**: Added cron script to calculate winners when shows are marked as completed
- **Trophy Display**: Visual trophy indicators for 1st, 2nd, and 3rd place winners
- **Mobile-First Design**: All new pages are fully responsive for mobile devices
- **Database Updates**: Added category_winners table to store show results

## Previous in v3.18.0: Age Weight Scoring Fix

This version fixes issues with age weights not properly affecting vehicle scores:

- **Fixed Age Weight Application**: Corrected the application of age weights to vehicle scores
- **Enhanced Score Display**: Improved the display of age weights and weighted scores in the judging interface
- **Fixed Weight Calculation**: Corrected weight calculation to use weights exactly as stored in the database
- **Improved Weight Display**: Updated to show the actual weight values from the judging_metrics table
- **Formula Compliance**: Ensured scoring formulas work as configured in the admin interface
- **Better Error Logging**: Added detailed logging for score calculations to aid in troubleshooting
- **Database Updates**: Added SQL script to ensure age_weights table has the description column
- **Improved Age Weight Retrieval**: Enhanced the retrieval of age weights based on vehicle year
- **Fixed Weight Calculation**: Corrected the formula for applying weights to scores
- **Fixed Normalization**: Corrected the normalization calculation to properly apply age weights
- **Consistent Scoring**: Ensured consistent scoring across all views and calculations

## Previous in v3.17.0: Configurable Scoring System

This version adds a fully configurable scoring system with customizable formulas:

- **Customizable Scoring Formulas**: Create and manage different scoring formulas through the admin interface
- **Show-Specific Settings**: Configure scoring settings individually for each show
- **Formula Variables**: Use variables like rawScore, metricWeight, ageWeight in custom formulas
- **Math Functions**: Support for mathematical functions like Math.pow(), Math.sqrt(), etc.
- **Score Normalization**: Option to normalize scores to a 0-100 scale
- **Weight Multipliers**: Adjust the impact of metric weights and age weights
- **Live Preview**: See how your formula changes affect scores in real-time
- **Database Updates**: Added scoring_formulas and show_scoring_settings tables

## Previous in v3.11.0: Fixed Default Age Weights

This version fixes issues with default age weights not being correctly added to shows:

- **Improved Age Weight Handling**: Modified DefaultAgeWeightModel to directly use year values without conversion
- **Enhanced Compatibility**: Added compatibility check for description column in age_weights table
- **Database Update**: Added SQL script to add description column to age_weights table
- **Better Error Handling**: Improved error handling for age weight operations

## Previous in v3.10.0: Vehicle Scoring System with Age Weights

This version adds a comprehensive vehicle scoring system that calculates total scores based on judging metrics and age weights:

- **Vehicle Score Calculation**: Automatically calculates vehicle scores by combining judging metrics with age weight multipliers
- **Age Weight Support**: Applies age-based multipliers to vehicle scores based on vehicle year
- **Score Caching**: Stores calculated scores for improved performance
- **Show-wide Scoring**: Ability to retrieve and recalculate all vehicle scores for a show
- **New Database Table**: Added `vehicle_total_scores` table to store calculated scores

## Previous in v3.9.0: Fixed Judge View Scores Page

This version fixes PHP warnings and image display issues in the judge view scores page:

- **Fixed PHP Warnings**: Resolved undefined property warnings in judge/view_scores.php
- **Improved Image Handling**: Added proper checks for image->filename property before accessing it
- **Enhanced Score Display**: Updated JudgingModel to include metric_description field for proper display
- **Better Error Handling**: Improved error handling in vehicle image display to prevent PHP warnings
- **Fixed Image Display**: Resolved issues with vehicle images not displaying correctly:
  - Added support for multiple image property formats (filename, file_name, image_path)
  - Updated image paths to use BASE_URL instead of URLROOT for consistency
  - Added fallback handling for different image property structures

## Previous in v3.8.0: Judge Assignment Fix

This version fixes issues with judge assignments and table references:

- **Fixed Judge Assignments**: Resolved issue with judges not seeing vehicles to judge despite assignments
- **Database Schema Fix**: Updated judge_assignments table to allow NULL values for category_id
- **Fixed Table References**: Updated incorrect table references in RegistrationModel:
  - Changed vehicle_images to images table with correct entity_type filtering
  - Fixed scores table queries to use the correct columns
- **Improved Error Handling**: Enhanced error logging for judge assignments
- **SQL Update Script**: Added SQL script to fix existing judge_assignments table structure

## Previous in v3.7.0: Enhanced Radio Field Handling

This version improves radio field handling with robust support for different option formats:

- **Radio Field Display Fix**: Resolved issue with radio type custom fields not displaying in the `/admin/editShow/` section
- **Flexible Option Formats**: Enhanced radio field handling to support various option formats (arrays, strings, and JSON strings)
- **Debug Tool Fix**: Fixed debug/radio_fields.php to properly handle non-array options in radio fields
- **Detailed Error Logging**: Added detailed error logging for radio field processing to aid in troubleshooting
- **Default Options**: Improved radio field display with fallback to default Yes/No options when no options are defined
- **Database Updates**: Added SQL update script to ensure proper database structure for radio fields
- **Form Designer Improvements**: Enhanced form designer interface with comprehensive guidance for all field types
- **Field Type Guidance**: Added detailed help text with examples and tips for each field type
- **Automatic JSON Formatting**: Added automatic JSON formatting and conversion for field options
- **Dynamic Field UI**: Improved field type handling to show/hide relevant fields based on selected type

## Previous in v3.6.0: Fixed Radio Type Custom Fields

This version fixed issues with radio type custom fields:

- **Enhanced Field Retrieval**: Improved CustomFieldRetriever to properly retrieve and map radio field values
- **Better Field Type Detection**: Enhanced detectFieldType method to properly identify radio type fields
- **Database-Aware Type Detection**: Added field type detection from field_mappings table for more accurate identification
- **Improved Error Logging**: Enhanced error logging for custom field operations to aid in troubleshooting

## Previous in v3.5.0: Modern Custom Fields System

This version modernizes the custom fields system:

- **Unlimited Custom Fields**: Now using only the `custom_field_values` table for unlimited custom fields
- **Simplified Database Structure**: Completely removed legacy custom field columns from the database
- **Data Migration**: Automatically migrates any existing custom field data to the new system
- **Enhanced Form Designer Integration**: All form designer fields now use the modern approach
- **Fixed Custom Field Updates**: Resolved issue with custom fields not updating in the `/admin/editShow/` section
- **Improved Error Handling**: Added comprehensive error logging for custom field operations
- **Null Value Handling**: Improved handling of null and empty values in custom fields
- **Transaction Management**: Enhanced transaction handling for custom field value updates

## Previous in v3.4.0: Form Designer Fix

This version fixes an issue with the form designer:

- **Fixed Add New Field Functionality**: Corrected the "Add New Field" popup in form designer that incorrectly disabled all fields and showed system field warning
- **Improved Form Field Modal**: Fixed form field modal not properly resetting disabled state when adding new fields

## Previous in v3.2.9: Navigation Improvements

This version includes an important navigation fix:

- **Fixed Browse Shows Button**: Corrected the "Browse Available Shows" button in user/payments and user/registrations pages to point to the correct URL
- **Improved User Experience**: Ensures users can properly navigate from payment history and registrations to the shows listing

## Previous in v3.2.8: Image Gallery Improvements

This version includes important fixes for the vehicle image galleries:

- **Fixed Duplicate Thumbnails**: Resolved issue with duplicate thumbnails appearing in vehicle galleries
- **Improved Image Tracking**: Enhanced the way images are tracked to prevent duplicates
- **Consistent Thumbnail Display**: Ensured consistent thumbnail display across all vehicle views

## Previous in v3.2.7: Navigation Improvements

This version includes a small but important navigation fix:

- **Fixed Show Details Link**: Corrected the "View Complete Show Details" button link in registration view to point to the correct URL
- **Improved User Experience**: Ensures users can properly navigate from registration details to show details

## Previous in v3.2.6: Payment Receipt Functionality

This version adds a dedicated Payment Receipt page for user registrations:

- **Payment Receipt View**: Added a new page for users to view and print payment receipts
- **Printable Layout**: Designed with print-friendly styling for easy record-keeping
- **Complete Payment Details**: Displays comprehensive payment information including transaction IDs and payment methods
- **Registration Summary**: Shows all relevant registration and vehicle details
- **Mobile-First Design**: Fully responsive layout works seamlessly on all devices
- **Fixed Receipt Button**: The "View Receipt" button in registration payment tab now works correctly

## Previous in v3.2.5: Payment History for Users

This version adds a dedicated Payment History page for users:

- **Payment History Page**: Added a new page for users to view their complete payment records
- **Consistent Navigation**: Integrated with the existing user navigation sidebar
- **Fixed Payment History Link**: The Payment History link in user/registrations now works correctly
- **Comprehensive Payment Details**: Users can view all payment information including dates, amounts, methods, and statuses
- **Mobile-First Design**: Fully responsive layout works seamlessly on all devices

## Previous in v3.2.4: Navigation Improvement

This version improves the navigation in the admin section:

- **Fixed Show Details Link**: Updated the "Show Details" link on admin/registrations page to point to the correct show view page
- **Improved User Experience**: Makes it easier to navigate between admin and public show views

## Previous in v3.2.3: Fan Votes Display Enhancement

This version improves the fan votes display in the admin section:

- **Enhanced Fan Vote Rankings**: Updated to show vehicle display number instead of registration number
- **Improved User Experience**: Makes it easier to match vehicles with their display numbers at events

## Previous in v3.2.2: Fan Votes Display Fix

This version fixed issues with the fan votes display in the admin section:

- **Fixed Fan Votes View**: Resolved "Table vehicle_images doesn't exist" error in admin/shows view votes
- **Updated Database Queries**: Modified all queries to use the consolidated images table
- **Improved Admin & Coordinator Controllers**: Updated vehicle image retrieval in both controllers
- **Enhanced Judging Model**: Fixed all methods to use the new image system consistently

## Previous in v3.2.1: Image System Compatibility Fix

This version fixed compatibility issues with the image system consolidation:

- **Fixed Vehicle Display**: Restored vehicle display in user dashboard and vehicles page
- **Updated Image Retrieval**: Fixed methods to properly use the unified image system
- **Backward Compatibility**: Ensured compatibility with existing image references
- **Improved Error Handling**: Added better fallbacks for missing image data
- **Fixed Fatal Error**: Resolved "Class ImageEditorModel not found" error in VehicleModel

## Previous in v3.2.0: Image System Consolidation

This version consolidates the image management system:

- **Unified Image Storage**: All images now use the centralized `images` table
- **Improved Image Deletion**: Fixed image deletion to properly remove both original and thumbnail files
- **Database Cleanup**: Removed deprecated image tables for a cleaner database structure
- **Enhanced Image Management**: Updated all image-related code to use the ImageEditorModel
- **Backward Compatibility**: Maintained support for existing image references

## Previous in v3.1.0: Vehicle Display Page Improvements

This version enhances the vehicle display page with two key improvements:

- **Improved Navigation**: "Back to Show" button replaced with "Return to Previous Page" for better user flow
- **Enhanced Image Gallery**: Added thumbnail gallery below the main vehicle image with the ability to switch between images
- **Comprehensive Image Support**: Improved image retrieval to display all vehicle images from both old and new systems

## Previous in v3.0.0: Public Vehicle View Page

This version adds a public-facing vehicle view page for enhanced vehicle showcase:

- **Public Vehicle Details**: Added dedicated page to view vehicle details for both logged-in and public users
- **Vehicle Gallery**: Displays vehicle images with lightbox functionality for better viewing
- **Show Appearances**: Lists all shows where the vehicle has been registered
- **Owner Information**: Displays vehicle owner details for public recognition
- **Mobile-First Design**: Fully responsive layout works seamlessly on all devices
- **Enhanced Navigation**: Intuitive breadcrumb navigation for easy context awareness
- **Owner Controls**: Vehicle owners see additional management options when viewing their vehicles
- **Current Shows Section**: Sidebar displays current and upcoming shows featuring the vehicle
- **Integrated Dashboard**: User dashboard now links vehicles directly to their public view pages
- **Enhanced Vehicle Cards**: Vehicle cards in user/vehicles page now link to public view
- **Streamlined Management**: Added "View" button to vehicle management interface for quick access

## Previous in v2.99.0: Multi-Select and Alignment Tools for Template Designer

This version adds powerful multi-select and alignment capabilities to the template designer:

- **Multi-Select**: Hold the Shift or Ctrl key to select multiple elements at once
- **Selection Box**: Click and drag with Shift or Ctrl key to select multiple elements in an area
- **Alignment Tools**: Align selected elements left, center, right, top, middle, or bottom
- **Distribution Tools**: Evenly space selected elements horizontally or vertically with perfect spacing
- **Customizable Spacing**: Control the exact spacing between distributed elements
- **Visual Indicators**: Clear visual feedback for multi-selected elements

## Previous in v2.98.0: Fixed Text Alignment in Printable Templates

This version fixes issues with text alignment in the printable template editor:

- **Fixed Text Alignment**: Corrected the left, center, and right alignment functionality in the template editor
- **Improved Alignment Handling**: Enhanced the code to properly update both text-align and justify-content properties
- **Better Text Positioning**: Text elements now correctly respect the selected alignment option
- **Comprehensive Fix**: Corrected multiple instances where text alignment was being forced to center
- **Consistent Alignment**: Ensured text alignment is preserved during element resizing and dragging
- **Default Alignment**: Set appropriate default text alignment for new text elements
- **Template Loading**: Fixed template loading to properly respect saved text alignment settings
- **Priority Styling**: Added !important flags to text alignment styles to prevent overrides
- **Enhanced Selectors**: Added data attributes to help CSS selectors target elements with specific alignments
- **Robust CSS Rules**: Added explicit CSS rules to enforce text alignment based on style attributes
- **Backward Compatibility**: Added support for class-based and data-attribute-based alignment
- **Template Fixes**: Fixed conflicting alignment settings in existing templates
- **Database Update**: Included comprehensive SQL script to fix alignment issues in all templates
- **Alignment Consistency**: Ensured consistency between class names, data attributes, and inline styles
- **Multiple Fixes**: Added SQL queries to handle various alignment conflict scenarios
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.97.9: Display Number Badge for Printable Templates

This version adds a new Display Number Badge element to the printable template editor:

- **Display Number Badge Element**: Added a new element type for displaying vehicle numbers in printable templates
- **Customizable Badge Appearance**: Badge color and text color can be customized in the properties panel
- **Resizable Badge**: Display number badge can be resized to fit different template designs
- **{{DN}} Placeholder Support**: Added support for the {{DN}} placeholder in templates
- **Mobile-First Design**: Responsive design ensures badges display properly on all devices

## Previous in v2.97.8: Category Filtering for Judge Pages

This version adds category filtering to the judge interface:

- **Category Dropdown Filter**: Added dropdown menu to filter vehicles by category on the judge show page
- **Category Prefix Display**: Shows category prefixes alongside category names for easier identification
- **Dynamic Filtering**: Real-time filtering of vehicles without page reload
- **Filter Status Indicator**: Shows count of filtered vehicles and current filter selection
- **Mobile-Friendly Design**: Filter works seamlessly on both mobile and desktop views

## Previous in v2.97.7: Enhanced Mobile Responsiveness for Judge Pages

This version improves the mobile experience for judges:

- **Mobile-First Judge Show Page**: Completely redesigned `/judge/show/` page for mobile devices
- **Eliminated Horizontal Scrolling**: Fixed table layouts to prevent horizontal scrolling on small screens
- **Card-Based Mobile View**: Implemented card-based layouts for vehicle listings on mobile
- **Responsive Typography**: Adjusted text sizes and spacing for better readability on small screens
- **Improved Touch Targets**: Enlarged buttons and interactive elements for easier touch interaction

## Previous in v2.97.6: Auto-Save for Judge Scoring

This version adds automatic saving for judge scoring to prevent data loss:

- **Auto-Save Functionality**: Automatically saves judge scores every 30 seconds
- **Crash Recovery**: Restores auto-saved scores if the browser crashes during judging
- **Visual Indicators**: Shows auto-save status and last save time
- **Permission Control**: Only admins and coordinators can modify finalized scores
- **Mobile-First Design**: Fully responsive on all devices

## Previous in v2.96.0: Improved Judging Progress Accuracy

This version fixes issues with the judging progress display:

- **Accurate Progress Tracking**: Fixed progress bar incorrectly showing 100% complete when judging hasn't started
- **Default Score Correction**: Changed default score from 1 to 0 to accurately reflect unjudged status
- **Improved Calculation**: Progress now only counts metrics that have been actively judged by the user
- **Enhanced User Experience**: Better visual feedback on judging completion status
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.95.0: Fixed Judge Controller Database Connection

This version fixes critical issues with the judge controller:

- **Fixed Database Connection**: Resolved "Undefined property: JudgeController::$db" error
- **Improved Database Handling**: Database connection now initialized in the constructor
- **Enhanced Admin Permissions**: Fixed permission checks for admins in vehicle check-in functionality
- **Role-Based Access Control**: Added admin role bypass for judge assignment checks
- **Robust Error Handling**: Enhanced error handling for database operations

## Previous in v2.94.0: Judge Interface Improvements

This version fixes issues with the judge interface:

- **Fixed Judge Button**: Resolved issue with Judge button not working on the judge/show page
- **Enhanced Mobile Experience**: Improved button styling for better mobile responsiveness
- **Fixed Property Error**: Resolved "Undefined property: stdClass::$id" error in judge/show.php

## Previous in v2.93.0: Fixed Judge Assignment System

This version fixes issues with the judge assignment system:

- **Synchronized Judge Assignments**: Fixed issue where judges assigned by admins weren't appearing in the judge dashboard
- **Improved Database Consistency**: Updated JudgingModel to properly sync judge assignments between tables
- **Streamlined Admin Experience**: Eliminated the need to run fix scripts after assigning judges
- **Enhanced Reliability**: Ensured that admin-assigned judges are properly recorded in all necessary tables

## Previous in v2.92.0: Database Reliability Improvements

This version enhanced database operations and error handling:

- **Improved Error Handling**: Added better fallback mechanisms for database table existence checks
- **Enhanced Compatibility**: Standardized database query methods across models
- **Fixed SQL Syntax**: Corrected syntax errors in JudgingModel when checking if tables exist

## Previous in v2.89.0: Enhanced User Registration View

This version improves the user experience in the registration view:

- **Consistent Registration Printing**: Updated "Print" button in Check-in QR Code section to "Print Registration"
- **Unified Print Experience**: All print buttons now use the same comprehensive registration print page
- **Fixed QR Code Download**: Corrected the download functionality for QR codes in user registration view
- **Improved User Interface**: Enhanced clarity and consistency across registration management screens

## Previous in v2.88.0: Improved Registration Printing Consistency

This version improves the user experience when printing registrations:

- **Consistent Button Naming**: Renamed "Print QR Code" to "Print Registration" in the Smart QR Code section
- **Streamlined Printing**: Updated the button to link directly to the registration print page
- **Improved User Experience**: Ensured consistent behavior between all registration printing options

## Previous in v2.87.0: Fixed Custom Field Warnings in Registration Printing

This version fixes PHP warnings that occurred when printing registrations:

- **Fixed PHP Warnings**: Resolved "Attempt to read property on array" warnings in printRegistration method
- **Improved Error Handling**: Enhanced handling of custom field values to prevent PHP warnings
- **Corrected Data Structure**: Updated code to properly handle the array structure of custom field values
- **More Reliable Printing**: Registration printing now works correctly with all types of custom fields

## Previous in v2.86.0: Improved Registration Form Navigation

This version enhances the user experience when editing registrations:

- **Return to Previous Page**: Registration edit form now returns to the previous page after submission
- **Improved Navigation**: Added "Back" functionality to the Cancel button using JavaScript history
- **Context Preservation**: Maintains user navigation context throughout the registration editing process

## Previous in v2.85.0: Registration Status Update Fix & Database Compatibility

This version fixes critical issues with registration status updates and improves database compatibility:

- **Fixed Status Updates**: Fixed critical issue with registration status not updating in the database
- **Database Compatibility**: Corrected form dropdown values to match database enum constraints
- **Improved Error Logging**: Drastically reduced excessive logging to make error logs more readable
- **Enhanced Debugging**: Added targeted debug logging for registration updates to help troubleshoot issues

## Previous in v2.83.0: System-Managed Display Numbers

This version improves the display number management system:

- **System-Managed Display Numbers**: Display numbers are now fully managed by the system
- **Removed Manual Editing**: Removed the ability to manually edit display numbers from admin/editRegistration
- **Consistent Display Numbers**: Ensures display numbers remain consistent throughout the event
- **Automatic Assignment**: Display numbers continue to be automatically assigned based on category

## Previous in v2.82.0: Enhanced QR Code Security & Display Numbers

This version implements enhanced security for QR codes and a new display number system:

- **Secure QR Codes**: Added registration number as a security token in QR code URLs
- **Token Validation**: System validates both registration ID and registration number token
- **Security Logging**: Added logging for invalid QR code token attempts
- **Category-Based Display Numbers**: Display numbers now include category prefix (e.g., "C-123" for Classics)
- **Automatic Assignment**: Display numbers with category prefixes are automatically assigned when creating registrations
- **Prefix Selection**: Admins and coordinators can select from category-based prefixes when editing display numbers
- **Manual Override**: Full control over display number format while maintaining category organization
- **Print Integration**: Display numbers are prominently shown on printed registration cards

## Previous in v2.81.0: Vehicle Check-in System

This version implements a comprehensive vehicle check-in system:

- **Complete Check-in Workflow**: Admins, coordinators, and judges can now check in vehicles at events
- **Check-in Status Tracking**: System tracks check-in status and timestamp for each registration
- **Role-Based Access Control**: Different user roles have appropriate check-in permissions
- **QR Code Integration**: Check-in works seamlessly with the existing QR code system
- **Mobile-Friendly Interface**: Responsive design for check-in functionality on all devices
- **Undo Capability**: Option to undo check-ins if needed
- **Visual Status Indicators**: Clear badges show check-in status on registration pages

## Previous in v2.80.0: QR Code Functionality Fixes

This version fixes issues with QR code functionality:

- **Fixed Check-in QR Code Display**: Corrected the path for QR code images in user registration views
- **Added QR Code Download**: Implemented downloadQrCode method in UserController
- **Path Corrections**: Updated all QR code image paths to use the correct uploads directory
- **Mobile-Friendly Design**: Maintained responsive design for QR code display on all screen sizes

## Previous in v2.79.0: Improved Navigation

This version enhances the system's navigation for better user experience:

- **Back to Home Button**: Added a convenient "Back to Home" button on the entries page
- **Intuitive Navigation**: Improved user flow between pages with clear navigation options
- **Visual Cues**: Added directional icon (arrow) for better usability
- **Mobile-Friendly Design**: Maintained responsive design for all screen sizes
- **Consistent Styling**: Used existing design language for seamless integration

## Previous in v2.78.0: Social Media Integration

This version enhances the system with social media integration:

- **Facebook Integration**: Added direct link to the club's Facebook group in the navigation menu
- **Consistent Social Presence**: Facebook link added to all pages (index, entries, admin)
- **Improved Club Visibility**: Enhanced promotion of the club's online community
- **Mobile-Friendly Design**: Social media links optimized for all device sizes
- **Visual Enhancement**: Added Facebook icon for better recognition

## Previous in v2.77.0: Improved Cache Management

This version enhances the system's cache management for more reliable data display:

- **Comprehensive Cache Prevention**: Added HTTP headers to prevent browsers from caching pages
- **Real-time Data Display**: Ensured all dynamic content is always up-to-date
- **Improved User Experience**: Fixed issues with data not refreshing on page reload
- **Enhanced Admin Workflow**: Administrators now see the most current data at all times
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.76.0: Enhanced Entry Display Management

This version improves the entry display management system with more intuitive controls:

- **Automatic Display Approval**: Payment confirmation now automatically approves entries for display
- **Entry Hiding**: Admins can now hide entries from public view with a dedicated button
- **Improved Cache Management**: Fixed cache busting for CSS and JavaScript files
- **Better User Feedback**: Enhanced confirmation messages for all admin actions
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.75.0: Improved Entry Management System

This version enhances the entry management system with more flexible approval options:

- **Display Approval**: Admins can now approve entries for display without confirming payment
- **Separate Status Tracking**: Payment status and display status are now tracked separately
- **Enhanced Visual Indicators**: Different colors for payment status in the public entry list
- **Improved Entry Filtering**: Public entry list now shows only display-approved entries
- **Better Status Reporting**: Added display approval count to the public entry list
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.74.0: Enhanced Admin Controls & Improved User Experience

This version adds several improvements to the admin interface and user experience:

- **Entry Editing**: Admins can now edit all fields of parade entries directly from the admin panel
- **Enhanced Delete Confirmation**: Added detailed confirmation prompts when deleting entries
- **Improved Visitor Stats**: Enhanced visitor count display in the admin dashboard
- **Welcoming Messages**: Added prominent messages on the front page to welcome all vehicle types and clubs
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.73.0: Unique Visitor Tracking System

This version adds a unique visitor tracking system to the July 4th event page:

- **Invisible Visitor Counter**: Added an invisible unique visitor counter to the main page
- **Admin Dashboard Integration**: Visitor count is displayed in the admin dashboard
- **Unique Visitor Identification**: Uses IP address and user agent to identify unique visitors
- **Secure Data Storage**: Visitor data is stored securely in a JSON file
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.72.0: Standardized QR Code System & Fixed Registration Printing

This version standardizes the QR code system and fixes registration printing issues:

- **Consistent QR Codes**: Fixed inconsistency between QR codes in admin view and test view
- **Standardized URL Format**: All QR codes now consistently point to /qr/scan/registration/{id}
- **Simplified QR Generation**: Removed on-the-fly QR code generation to ensure consistency
- **Improved User Interface**: Updated QR code management interface to prevent confusion
- **Enhanced User Experience**: QR code display now only shows generated codes
- **Fixed Registration Printing**: Resolved issues with registration data not displaying in printable view
- **Template System Integration**: Fixed integration with the printable templates system
- **Default Template Support**: Registration printing now uses the default template from the database
- **Enhanced QR Code Display**: Added dedicated controller method to serve QR codes directly
- **Improved QR Code Security**: QR codes are now served with proper access control and validation
- **Robust QR Code Handling**: Added content type detection and file validation for QR codes
- **Fallback System**: Added fallback to a dedicated view when template rendering fails
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.71.0: QR Code System Improvements

This version enhances the QR code system with important fixes and improvements:

- **Fixed QR Code Storage**: Corrected QR code storage path to use /uploads/qrcodes/ instead of /public/uploads/qrcodes/
- **Persistent QR Codes**: QR codes are now stored permanently and linked to registrations in the database
- **Automatic Cleanup**: Added system to automatically clean up QR codes 1 month after show end date
- **Enhanced QR Display**: Improved QR code display with clear indicators for permanent vs. temporary codes
- **Optimized Storage**: Reduced server storage usage by cleaning up old QR code files
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.71.0: Improved Registration Number System

This version enhances the registration number system:

- **New Registration Number Format**: Changed from "CS-#####" to "RER-##########" (10 digits)
- **Sequential Numbering**: Improved registration number generation to ensure proper sequential numbering
- **Fixed Scientific Notation Issue**: Resolved issue where registration numbers sometimes appeared in scientific notation
- **Enhanced Error Handling**: Added robust error handling in registration number generation
- **Database Update Script**: Included script to update existing registration numbers to the new format
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.70.0: Registration Display Number Support

This version adds support for vehicle display numbers in registrations:

- **Display Number Field**: Added display number field to registration forms and database
- **Admin Management**: Admins can now assign and edit display numbers for vehicles
- **Database Update**: Added display_number column to registrations table
- **Fixed CSRF Issues**: Resolved CSRF token validation issues in forms
- **Improved Form Security**: Enhanced form security with proper CSRF token handling
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.69.0: Printable Registration Sheets

This version adds comprehensive registration printing functionality:

- **Printable Registrations**: Users can now print their registration sheets with QR codes
- **QR Code Integration**: Registration sheets require QR code generation before printing
- **Admin QR Generation**: Admins can generate QR codes and print in one step
- **Template-Based Printing**: Uses the template designer system for customizable registration sheets
- **Multiple Access Points**: Print buttons added throughout the user and admin interfaces
- **Dynamic Data**: Registration sheets include vehicle, owner, and show information
- **Clear User Guidance**: Users are informed about the QR code generation process
- **Fallback Templates**: System provides a default template if no custom template is available
- **Mobile-Friendly**: All printed sheets are responsive and mobile-friendly

## Previous in v2.68.0: Template Designer Modal Fixes

This version fixes z-index issues with the template designer:

- **Fixed Modal Layering**: Resolved issue with resize handles appearing on top of image selection modal
- **Improved Modal Interaction**: Temporarily hide resize handles when image selection modal is open
- **Enhanced Z-index Management**: Properly configured z-index values for all UI elements
- **Fixed Modal Controls**: Ensured all controls in the image selection modal remain active
- **Improved User Experience**: Smoother workflow when selecting images for templates

## Previous in v2.56.0: Template Designer Fixes and Enhancements

This version fixes several issues with the template designer:

- **Fixed Resize Handles**: Resize handles now appear correctly on all elements, including newly added fields
- **Fixed Template Editing**: Templates now load and save correctly without showing HTML code
- **Enhanced Media Library**: Fixed image upload and selection in the media library
- **Improved UI**: Enhanced resize handle visibility for better usability
- **Robust Template Parsing**: Completely rewrote HTML parsing to prevent code display in the visual editor
- **Element Preservation**: Fixed element positioning and styling when saving and loading templates
- **Advanced Serialization**: Added robust template serialization for reliable template restoration
- **Debugging Tools**: Added comprehensive debugging to track and resolve template loading issues
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.55.0: Enhanced Media Library Integration

This version enhances the template designer with media library integration:

- **Media Library**: Added integrated media library for image selection
- **Field Styling**: Added font options for field elements (font family, weight, alignment)
- **Media Upload**: Added media upload functionality for template images
- **Improved UI**: Enhanced image selection modal with preview thumbnails

## Previous in v2.54.0: Improved Template Designer Caching

This version improves the template designer with better cache handling:

- **Cache Busting**: Added cache-busting parameters to prevent stale resources
- **Refresh Button**: Added a convenient refresh button to the template designer
- **No-Cache Headers**: Implemented proper cache control headers
- **Improved Refreshing**: Fixed issues with the template designer not refreshing properly
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.53.0: Fixed Field Element Handling

This version fixes issues with field element handling in the template designer:

- **Field Label Fix**: Fixed field properties showing partial HTML code in field label
- **Field Selection Fix**: Fixed field name dropdown not affecting the canvas element
- **Content Handling**: Fixed field element content being parsed as HTML instead of text
- **Improved Editing**: Enhanced field element creation and editing process
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.52.0: Advanced Template Designer Controls

This version adds advanced controls to the template designer:

- **Element Resizing**: Added ability to resize elements by dragging corners/edges
- **Font Controls**: Added font selection, weight, and alignment options for text elements
- **Image Selection**: Added ability to select images from the media library
- **Aspect Ratio Control**: Added option to maintain aspect ratio for images and QR codes
- **Line Resizing**: Improved line element resizing for better control
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.51.0: Enhanced Template Designer Field Selection

This version enhances the template designer with improved field selection capabilities:

- **Field Selection Dropdown**: Added dropdown menu to select from available fields
- **Field Label Editing**: Added ability to customize field labels
- **Enhanced Field Properties**: Improved field element property panel
- **Better Field Rendering**: Fixed field element rendering with proper labels
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.50.0: Fixed Template Designer JavaScript

This version fixes issues with JavaScript code appearing in the template designer:

- **Fixed Jumbled JavaScript**: Resolved issue with raw JavaScript code appearing at the bottom of the template designer
- **Proper Code Separation**: Ensured JavaScript code is properly loaded from external files
- **Clean Interface**: Removed inline JavaScript that was causing display issues
- **Enhanced User Experience**: Restored proper functionality to the visual template designer
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.49.0: Fixed Visual Template Designer

This version fixes issues with the visual template designer:

- **Fixed Jumbled Elements**: Resolved issue with jumbled elements at the bottom of the template designer
- **Added Template Data Support**: Added missing template_data field to properly store template elements
- **Database Enhancement**: Added template_data column to printable_templates table
- **Improved Error Handling**: Better handling of cases where template_data column doesn't exist
- **Enhanced Element Selection**: Fixed template element selection and property display
- **Proper Element Rendering**: Ensured proper rendering of template elements with default values
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.48.0: Development Tools Enhancement

This version adds development tools to help with testing and debugging:

- **Admin Authentication Bypass**: Added a development bypass for admin authentication
- **Developer Settings**: Added a new toggle in admin settings under the development category
- **Security Warnings**: Implemented proper warning messages for development features
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.47.0: Fixed Visual Template Designer

This version fixes issues with the visual template designer:

- **Fixed JavaScript Errors**: Resolved template string interpolation errors in the designer
- **Improved Form Field Initialization**: Enhanced property panel to properly display element properties
- **Fixed Value Parsing Errors**: Resolved "value cannot be parsed" errors in the template designer
- **Enhanced Color Input Handling**: Fixed color format validation errors in the designer
- **Proper Element Rendering**: Ensured proper rendering of template elements with default values
- **Improved Property Updates**: Fixed issues with applying property changes to template elements
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.46.0: Redesigned Printable Templates System

This version completely redesigns the printable templates system:

- **HTML/CSS-Based Templates**: Replaced JSON-based templates with direct HTML/CSS
- **Visual Drag-and-Drop Editor**: Intuitive interface for designing templates without JSON
- **Fixed Alignment Issues**: Solved alignment problems when printing templates
- **Live Preview**: Real-time preview of template changes
- **Source Code Editor**: Advanced HTML/CSS editor for direct template customization
- **Improved Validation**: Removed problematic JSON validation that prevented saving templates
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.45.0: Fixed QR Code Generator

This version fixed an issue with the QR Code Generator functionality:

- **Fixed QR Generation**: Resolved issue where clicking "Generate QR Code" button did nothing
- **Enhanced AJAX Handling**: Improved entity loading and error handling
- **Better User Experience**: Added loading indicators and improved error messages
- **Robust Error Handling**: Better handling of API failures and network issues
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.44.0: Fixed QR Code Management Page

This version fixed a critical error in the QR Code Management page:

- **Fixed Fatal Error**: Resolved "Call to undefined method ShowModel::getActiveShows()" error
- **Alternative Implementation**: Used getShows() with filtering instead of getActiveShows()
- **Enhanced Filtering**: Added client-side filtering for active shows based on end date
- **Improved Error Handling**: Better handling of show retrieval failures
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.39.0: Redesigned Admin Settings Interface

This version introduces a completely redesigned admin settings interface:

- **Modern Card-Based Layout**: Replaced the traditional sidebar with an intuitive card-based dashboard
- **Mobile-First Responsive Design**: Fully responsive interface that works seamlessly on all devices
- **Improved Visual Organization**: Logical grouping of settings with clear visual cues and icons
- **Enhanced User Experience**: Hover effects, visual feedback, and improved typography
- **Better Form Layouts**: Improved spacing, alignment, and visual hierarchy in settings forms
- **Enhanced Modals**: More descriptive content and better visual design for confirmation dialogs
- **Password Visibility Toggle**: Added ability to show/hide sensitive password fields
- **Improved Maintenance Section**: Larger, more accessible buttons with clear visual indicators
- **Streamlined Navigation**: Direct access to all settings pages from a single dashboard

## Previous in v2.37.0: Fixed PHP 8.4 Compatibility in QR Controller

This version fixes a deprecated warning in the QR code controller:

- **Fixed Deprecated Warning**: Resolved "Creation of dynamic property QrController::$db is deprecated" warning
- **Improved PHP 8.4 Compatibility**: Updated code to follow best practices for PHP 8.4.6
- **Enhanced Code Quality**: Properly declared properties and initialized them in the constructor
- **Optimized Database Access**: Removed redundant database instantiations throughout the controller
- **Mobile-First Design**: Preserved responsive design principles throughout

## Previous in v2.36.0: Fixed Admin QR Entity Generator

This version fixes a critical error in the QR entity generator:

- **Fixed Fatal Error**: Resolved "Call to undefined method AdminController::isAdmin()" error in AdminController.php:4276
- **Improved Admin Verification**: Updated getEntities() method to use the correct auth->hasRole('admin') method
- **Enhanced Consistency**: Standardized admin access control methods across the application
- **Maintained Security**: Ensured proper access control for admin-only features
- **Mobile-First Design**: Preserved responsive design principles throughout

## Previous in v2.35.0: Fixed Admin QR Code Generator

This version fixes a critical error in the QR code generator:

- **Fixed Fatal Error**: Resolved "Call to undefined method AdminController::isAdmin()" error in AdminController.php
- **Improved Admin Verification**: Updated qrGenerator() method to use the correct auth->hasRole('admin') method
- **Enhanced Consistency**: Standardized admin access control methods across the application
- **Maintained Security**: Ensured proper access control for admin-only features
- **Mobile-First Design**: Preserved responsive design principles throughout

## Previous in v2.34.0: Unified QR Code System

This version introduces a comprehensive smart QR code system that adapts to different user roles:

- **Intelligent Redirection**: The same QR code directs users to different interfaces based on their role
- **Role-Based Access**: Judges see judging interface, voters see voting interface, admins see admin tools
- **Universal QR Codes**: Single QR code works for everyone - judges, voters, coordinators, and admins
- **QR Code Analytics**: Track QR code usage with detailed analytics by user role and entity type
- **QR Code Generator**: Admin utility to create QR codes for any entity in the system
- **QR Code Testing**: Dedicated test page to verify QR code functionality
- **Enhanced Registration Details**: Improved QR code display with testing and printing options
- **Mobile-Friendly Design**: Optimized for smartphone scanning with responsive interfaces
- **Printable Templates**: Dedicated page for printing QR codes with clear instructions
- **Enhanced Show Management**: Improved tools for organizing judging and voting at events

## Previous in v2.33.0: Enhanced Vehicle Registration Display

This version improves the vehicle registration experience:

- **Vehicle Registration Cards**: Show pages now display cards for each of the user's registered vehicles
- **Registration Status**: Users can see their registered vehicles directly on the show page
- **Clickable Cards**: Vehicle cards are fully clickable for quick access to registration details
- **Direct Access**: Links to registration details and QR codes for each registered vehicle
- **Multiple Vehicle Support**: "Register Another Vehicle" button appears when user has more vehicles to register
- **Improved User Experience**: Clearer registration status and easier access to registration details
- **Mobile-First Design**: Responsive vehicle cards that work great on all devices

## Previous in v2.32.0: Enhanced Image Management and Thumbnail Support

This version improves image handling throughout the application:

- **Thumbnail Display**: All cards and small image areas now display thumbnails when available
- **Improved Performance**: Reduced page load times by using smaller thumbnail images
- **Automatic Fallback**: System automatically falls back to original images when thumbnails aren't available
- **Image Helper Functions**: Added new helper functions for consistent thumbnail handling
- **Thumbnail Cleanup**: Fixed issue where thumbnails weren't being deleted when images were removed
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.31.0: Fixed Primary Image Display in Vehicle Editor

This version fixes issues with primary image display in the vehicle image editor:

- **Fixed Image Path**: Corrected the image path in vehicle card (was using `/image_editor/vehicle/uploads/vehicles/` instead of `/uploads/vehicles/`)
- **Simplified Image Display**: Streamlined the primary image display logic to use the correct BASE_URL path
- **Robust Database Updates**: Added transaction support for primary image updates
- **Improved Error Handling**: Enhanced error handling for image operations
- **Database Synchronization**: Ensured proper synchronization between images table and vehicles table
- **SQL Update Script**: Added script to ensure all vehicles have their primary_image field correctly set
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.30.0: Fixed Image Controller Parameter Handling and Database Issues

This version fixed critical errors in the ImageEditorController and VehicleModel:

- **Fixed Fatal Error**: Resolved "Too few arguments to function ImageEditorController::setPrimary()" error
- **Enhanced Parameter Handling**: Modified setPrimary() and setBanner() methods to handle missing parameters
- **Improved Error Recovery**: Added fallback to retrieve entity information from the image when parameters are missing
- **Fixed Database Error**: Resolved "Unknown column 'primary_image' in 'field list'" error in VehicleModel
- **Dynamic Column Creation**: Added automatic creation of primary_image column if it doesn't exist
- **Enhanced Error Handling**: Improved error handling for database operations
- **Maintained Functionality**: Preserved all existing image management functionality
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.29.0: Enhanced Image Manager

This version introduces significant improvements to the Image Manager:

- **User-Specific Images**: Users now only see and manage their own uploaded images
- **Automatic Image Optimization**: Images are automatically optimized based on settings in /admin/imageSettings
- **Thumbnail Generation**: Thumbnails are automatically created for all uploaded images
- **Image Resizing**: Large images can be automatically resized based on configurable maximum dimensions
- **Improved Settings**: Added settings for maximum image dimensions, quality, and resizing options
- **File System Cleanup**: Deleted images are now properly removed from the file system
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.28.3: Fixed Image Upload Buttons

This version fixed issues with image upload buttons throughout the application:

- **Fixed Navigation**: Corrected the "Upload Images" buttons to properly redirect to the image upload page
- **Fixed Vehicle Images Page**: Repaired broken upload and edit buttons on the vehicle images page
- **Enhanced Image Management**: Added both "Upload Images" and "Manage Images" buttons to vehicle information card

## Previous in v2.28.2: Fixed .htaccess for Image Access

This version fixed issues with .htaccess configuration that was preventing image display:

- **Improved URL Rewriting**: Updated main .htaccess to explicitly exclude the uploads directory from URL rewriting
- **Enhanced Image Access**: Fixed .htaccess in uploads/vehicles directory to properly allow image access
- **Thumbnail Support**: Added .htaccess to uploads/vehicles/thumbnails directory to ensure thumbnail access
- **Apache Compatibility**: Ensured compatibility with both Apache 2.2 and 2.4 using IfModule directives
- **Maintained Security**: Preserved security by continuing to block PHP file access in uploads directories
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.28.1: Corrected Image Paths

This version fixes issues with image paths:

- **Corrected Directory Structure**: Updated image paths to use the correct uploads directory (/uploads instead of /public/uploads)
- **Fixed Broken Images**: Resolved broken vehicle images by using the proper server directory structure
- **Maintained Compatibility**: Ensured compatibility with both old and new image storage systems
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.28.0: Fixed Broken Vehicle Images

This version fixes issues with broken vehicle images:

- **Fixed Broken Images**: Resolved broken image icons in the vehicle images display page
- **Enhanced Path Handling**: Improved image path resolution to support multiple storage locations
- **Dual System Support**: Added comprehensive support for both old and new image storage systems
- **Thumbnail Handling**: Improved thumbnail handling with fallback to original images when needed
- **Admin Compatibility**: Fixed compatibility between admin image manager and user vehicle images
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.27.0: Fixed Vehicle Image Display Issues

This version fixes issues with vehicle image display:

- **Fixed PHP Warnings**: Resolved "Undefined property: stdClass::$filename" warnings in vehicle images display
- **Enhanced Compatibility**: Updated images.php to handle both old and new image storage systems
- **Dual Database Support**: Added compatibility for both vehicle_images table (image_path) and images table (file_name)
- **Fallback Images**: Added fallback to default image when neither filename nor file_name is available
- **Improved Error Handling**: Enhanced error handling for image display
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.26.0: Enhanced Vehicle Registration with Filtering and Sorting

This version adds powerful filtering and sorting capabilities to the vehicle registration pages:

- **Filter Buttons**: Added "All Shows", "Published", and "Draft" filter buttons to quickly narrow down the list
- **Sortable Columns**: All columns (name, location, dates, registration, coordinator, status, entries) can be sorted by clicking
- **Clickable Show Names**: Show names now link directly to the view show page for quick access
- **Excluded Completed/Cancelled**: Completed and cancelled shows are automatically excluded from the list
- **Visual Sort Indicators**: Clear visual indicators show the current sort direction
- **Mobile-First Design**: All new features are fully responsive and work great on mobile devices

## Previous in v2.25.0: Fixed Method Call in CoordinatorController

This version fixes a critical error in the CoordinatorController:

- **Fixed Fatal Error**: Resolved "Call to undefined method ShowModel::getCoordinatorShows()" error
- **Method Name Correction**: Updated CoordinatorController to use the correct ShowModel::getShowsByCoordinator() method
- **Enhanced Reliability**: Ensured consistent method naming across the application
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.24.0: Improved Registration Page UI

This version enhances the user interface of the registration management page:

- **Enhanced Button Layout**: Improved alignment and spacing of action buttons on the registrations page
- **Better Mobile Experience**: Optimized button layout for small screens with responsive stacking
- **Consistent Spacing**: Added proper spacing between buttons using gap utility instead of margins
- **Improved Vertical Alignment**: Better alignment between heading and buttons
- **Mobile-First Design**: Enhanced responsive design principles throughout

## Previous in v2.23.0: Fixed Registration Category Display

This version fixes a critical issue in the registration category display:

- **Fixed PHP Warning**: Resolved "Undefined property: stdClass::$name" warning in registrations.php
- **Property Name Correction**: Updated view to use the correct property name (category_name instead of name)
- **Enhanced Reliability**: Improved consistency between database query results and view templates
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.22.0: Fixed Header and Footer Includes

This version fixes critical path issues in the view files:

- **Fixed Include Paths**: Updated header and footer includes to use the correct directory path
- **Consistent View Structure**: Ensured all views use the same include path structure
- **Error Resolution**: Fixed "Failed to open stream" errors in multiple view files
- **Enhanced Reliability**: Improved system stability by fixing file inclusion issues
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.21.0: Admin and Coordinator Registration Override

This version added the ability for admins and coordinators to register vehicles after the registration end date:

- **Admin Registration Override**: Admins can now register vehicles on behalf of users after the registration deadline
- **Coordinator Registration Override**: Show coordinators can register vehicles after the registration deadline
- **Improved User Interface**: Added "Register Vehicle" buttons to admin and coordinator registration management pages
- **Enhanced Registration Logic**: Modified registration system to check for admin/coordinator roles
- **Quick Access Menu Items**: Added "Register Vehicle for User" option to admin and coordinator dropdown menus
- **Show Selection Interface**: Added show selection page for vehicle registration
- **Mobile-First Design**: Maintained responsive design principles throughout the new features

## Previous in v2.20.0: Fixed Method Access Error in ShowController

This version fixes a critical error in the ShowController:

- **Fixed Fatal Error**: Resolved "Cannot access protected method ShowController::view()" error
- **Improved Method Access**: Made ShowController::view() method public to allow proper routing
- **Enhanced Compatibility**: Ensured compatibility with App.php routing mechanism
- **Simplified Implementation**: Streamlined ShowController while maintaining APPROOT availability
- **Maintained Fail-Safe Approach**: Preserved fail-safe APPROOT definition in view files
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.99: Fixed Fatal Error in Show Views

This version fixes a critical error in the show views:

- **Fixed Fatal Error**: Resolved "Undefined constant APPROOT" fatal error in show views
- **Fail-Safe APPROOT Access**: Added APPROOT definition directly in show view files
- **Enhanced Controller Logic**: Implemented robust APPROOT handling in ShowController
- **Multiple Fallbacks**: Created multiple fallback mechanisms for APPROOT definition
- **Consistent Variable Access**: Ensured consistent access to global variables across all views
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.98: Fixed APPROOT Access in Show Views

This version fixes issues with APPROOT access in show views:

- **Fixed Global Variable Access**: Resolved issues with APPROOT access in show views
- **Enhanced Controller Logic**: Modified ShowController to ensure APPROOT is available in the global scope
- **Consistent Variable Access**: Ensured consistent access to global variables across all views
- **Improved View Loading**: Enhanced view loading mechanism in ShowController
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.97: Fixed Fan Votes Display

This version fixes critical issues with the fan votes display:

- **Fixed Fatal Error**: Resolved "Failed opening required 'admin_header.php'" error in fan_votes.php
- **Removed Non-existent Files**: Removed references to non-existent admin_header.php and admin_sidebar.php files
- **Updated Templates**: Updated admin and coordinator fan_votes.php files to use the existing admin_settings_sidebar.php
- **Improved Layout**: Fixed layout issues with fan_votes.php and qr_codes.php pages
- **Enhanced UI**: Improved UI consistency with other admin pages
- **Mobile-First Design**: Enhanced responsive design principles throughout the fix

## Previous in v2.19.96: Enhanced Fan Favorite Voting with QR Codes

This version adds powerful new features to the fan favorite voting system:

- **QR Code Integration**: Generate QR codes for each vehicle that link directly to voting pages
- **Dual Authentication**: Support for both IP-based voting and Facebook login authentication
- **Mobile-First Design**: Optimized voting interface for smartphones and tablets
- **Admin Monitoring**: Comprehensive dashboard for admins to track voting statistics
- **Coordinator Tools**: Dedicated interface for show coordinators to monitor votes
- **Export Capability**: Export voting data to CSV for further analysis
- **Vote Statistics**: Track unique IP addresses, Facebook votes, and voting patterns
- **Printable QR Codes**: Print sheets of QR codes to place next to vehicles at shows
- **Enhanced Security**: Improved validation and security for the voting process

## Previous in v2.19.95: Fixed Duplicate Method Declaration

This version fixes a critical error with duplicate method declaration:

- **Fixed Fatal Error**: Resolved "Cannot redeclare ShowModel::tableExists()" in ShowModel.php
- **Removed Duplicate Method**: Eliminated duplicate tableExists() method declaration at the end of ShowModel class
- **Maintained Functionality**: Ensured consistent database table existence checking functionality
- **Mobile-Friendly**: All functionality continues to work seamlessly on mobile devices

## Previous in v2.19.90: Fixed Age Weights Functionality

This version fixes critical issues with the age weights management functionality:

- **Fixed Fatal Error**: Resolved "Call to undefined method ShowModel::createAgeWeight()" error
- **Added Missing Methods**: Implemented all required age weights management methods in ShowModel
- **Database Support**: Added automatic table creation for age_weights if it doesn't exist
- **Complete CRUD Operations**: Added full create, read, update, and delete functionality for age weights
- **Enhanced Error Handling**: Improved error handling for all age weight operations
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.19.89: Fixed Age Weights Interface

This version fixes issues with the age weights management interface:

- **Fixed PHP Warnings**: Resolved "Undefined array key" warnings in age_weights view files
- **Updated Field Names**: Changed field names in views to match database structure (min_age/max_age instead of min_year/max_year)
- **Removed Non-existent Fields**: Removed name field that doesn't exist in the database
- **Improved Interface**: Enhanced age_weights interface with better descriptions and simplified table layout
- **Enhanced Error Handling**: Improved error handling for age weight management
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.19.88: Fixed Metric Creation Issues

This version fixes critical issues with the metric creation functionality:

- **Fixed Database Error**: Resolved "Unknown column 'is_active' in 'field list'" error in createMetric method
- **Enhanced Column Detection**: Added dynamic column detection for judging_metrics table to handle different database structures
- **Fixed PHP Warning**: Resolved "Undefined array key 'display_order'" warning in CoordinatorController
- **Improved Error Handling**: Enhanced error handling for database operations in metric management
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.19.87: Fixed Category and Metric Management

This version fixes critical issues with category and metric management:

- **Fixed Fatal Error**: Added missing createCategory method to ShowModel to fix "Call to undefined method" error
- **Fixed CSRF Token Handling**: Updated metrics add, edit, and delete forms to use csrfTokenField() instead of generateCsrfToken()
- **Enhanced Security**: Fixed "CSRF token validation failed: Token missing" error when submitting forms
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.19.86: Fixed CSRF Token Validation in Category Management

This version fixes CSRF token validation issues in the category management forms:

- **Fixed CSRF Token Handling**: Updated category add, edit, and delete forms to use csrfTokenField() instead of generateCsrfToken()
- **Improved Form Submission**: Ensured proper CSRF token handling in all category management operations
- **Enhanced Security**: Fixed "CSRF token validation failed: Token missing" error when submitting forms
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.23.5: Fixed Category Deletion

This version fixes the "Delete All Categories" functionality in the coordinator's edit show page:

- **Database Fix**: Corrected table name from 'categories' to 'show_categories'
- **Error Prevention**: Eliminated SQL error when trying to delete categories
- **Improved Reliability**: Enhanced error handling for database operations
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.23.4: Fixed Form Submission Issues

This version fixed form submission issues in the coordinator's edit show page:

- **Form Structure Fix**: Restructured edit_show.php to prevent nested forms
- **Button Functionality**: Fixed "Add Default Categories" button submitting the main form
- **UI Improvement**: Maintained clean separation between show editing and category management
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.23.3: Fixed Database Transaction Issue

This version fixed a case sensitivity issue with database transactions when adding default categories:

- **Fixed Method Case**: Corrected method name from rollback() to rollBack() to match Database class
- **Transaction Reliability**: Ensured database transactions work correctly when adding default categories
- **Error Handling**: Improved error logging for transaction failures
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.23.2: Fixed "Add Default Categories" Button Functionality

This version fixed the "Add Default Categories" button functionality in the coordinator's edit show page:

- **Fixed Syntax Error**: Corrected syntax error in addDefaultCategoriesToShow method
- **Removed Duplicate Code**: Cleaned up duplicate code that was causing issues
- **Improved Flash Messages**: Enhanced user feedback with proper flash messages
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.23.1: Fixed "Add Default Categories" Button Redirect

This version fixed the "Add Default Categories" button redirect issue in the coordinator's edit show page:

- **Fixed Redirect Issue**: Fixed "Add Default Categories" button redirecting to home page
- **Restructured Methods**: Improved all "Add Default" methods for better reliability
- **Enhanced Validation**: Added better validation and error handling
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.23.0: Fixed "Add Default" Buttons in Coordinator Interface

This version fixes the non-working "Add Default" buttons in the coordinator's edit show page:

- **Fixed Default Buttons**: All "Add Default" buttons now work correctly in the coordinator interface
- **Model-Based Implementation**: Updated to use DefaultCategoryModel, DefaultMetricModel, and DefaultAgeWeightModel
- **Duplicate Prevention**: Added checks to prevent duplicate items when using "Add Default" buttons
- **Clear Warnings**: Added user-friendly warnings when items already exist
- **Improved Error Handling**: Better error messages and validation
- **Mobile-Friendly**: All functionality works seamlessly on mobile devices

## Previous in v2.22.0: Administrator User Impersonation

This version adds a powerful user impersonation feature for administrators:

- **User Impersonation**: Administrators can temporarily login as any user (judge, coordinator, or regular user)
- **Testing Tool**: Easily test and verify the user experience for different user roles
- **One-Click Return**: A prominent "Return to Admin" button allows quick return to the admin account
- **Secure Implementation**: All impersonation sessions are tracked and secured
- **Mobile-Friendly**: The impersonation interface works seamlessly on all devices

## Previous in v2.21.0: Fixed Default Metrics Addition

This version fixes a critical error in the default metrics functionality:

- **Fixed Fatal Error**: Resolved "Call to undefined method ShowModel::addMetric()" error in CoordinatorController
- **Method Name Correction**: Updated addDefaultMetricsToShow method to use createMetric instead of non-existent addMetric method
- **Enhanced Parameter Handling**: Added required parameters (max_score, display_order, is_active) to metric creation
- **Improved Error Handling**: Enhanced error handling for metric creation process
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.20.0: Completed Coordinator Show Management

This version completes the coordinator's show management functionality:

- **Fixed Redirect Issues**: Resolved issues with delete buttons and "Add Default" buttons not redirecting back to the edit show page
- **Added Bulk Operations**: Implemented missing controller methods for deleting all categories, metrics, and age weights
- **Added Default Items**: Implemented missing controller methods for adding default categories, metrics, and age weights
- **Improved User Feedback**: Enhanced flash messages for all operations
- **Consistent Navigation**: Ensured all actions redirect back to the edit show page
- **Enhanced Error Handling**: Improved error handling for all operations
- **Complete Feature Parity**: Achieved full feature parity with admin interface (except coordinator selection)

## Previous in v2.19.99: Enhanced Coordinator Show Editing Interface

This version enhances the coordinator's show editing interface to match the admin functionality:

- **Complete Admin-Like Interface**: Updated coordinator's edit show page to fully match admin functionality
- **Enhanced Visual Design**: Added table colors and styling to match admin interface
- **Advanced Management Tools**: Added buttons for inserting defaults and deleting items
- **Confirmation Modals**: Added delete confirmation modals for categories, metrics, and age weights
- **Bulk Operations**: Added support for deleting all categories, metrics, or age weights at once
- **Visual Consistency**: Improved visual consistency with admin interface
- **Interactive Elements**: Enhanced JavaScript functionality for interactive elements
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.19.98: Fixed Coordinator Show Editing Functionality

This version fixes critical issues with the coordinator's show editing functionality:

- **Fixed Fatal Error**: Resolved PHP fatal error "Call to undefined method CoordinatorController::generateCsrfToken()"
- **Improved CSRF Handling**: Updated CSRF token handling to use the proper helper function
- **Enhanced Form Field Processing**: Added FormFieldSynchronizer to ensure form fields match database columns
- **Added Template Fallbacks**: Implemented fallback mechanisms when no form template is found
- **Improved Field Validation**: Added validation for form fields to prevent errors with invalid templates
- **Enhanced Error Handling**: Improved error handling throughout the form template processing
- **Improved Template Management**: Enhanced template creation and field synchronization

## Previous in v2.19.97: Enhanced Coordinator Show Editing Functionality

This version enhances the coordinator's show editing functionality:

- **Fixed Empty Edit Show Page**: Resolved issue where /coordinator/editShow/4 was empty with just a table heading
- **Enhanced Coordinator Interface**: Updated coordinator's edit show page to match admin functionality
- **Maintained Role Restrictions**: Kept coordinator selection disabled for coordinator role
- **Added Custom Form Fields**: Added support for dynamic form fields from templates
- **Added Related Data Sections**: Added sections for categories, metrics, and age weights
- **Improved User Experience**: Added JavaScript to handle the is_free checkbox toggling the registration_fee field
- **Mobile-First Design**: Enhanced responsive design for better mobile experience
- **URL Consistency**: Fixed inconsistent URL usage between BASE_URL and URLROOT

## Previous in v2.19.96: Fixed Coordinator Registration Update Functionality

This version fixes issues with the coordinator registration update functionality:

- **Fixed Update Buttons**: Resolved non-working "Update Status" and "Update Payment" buttons on the registration details page
- **Fixed Persistent Database Issues**: Resolved stubborn issue where payment status updates were not being saved to the database
- **Fixed Payment Status Updates**: Resolved issue where only "refunded" payment status was being saved to the database
- **Fixed Database Error**: Resolved "Unknown column 'payment_date' in 'field list'" error in payment updates
- **Added Missing Methods**: Added update_registration and update_payment methods to CoordinatorController as URL-friendly aliases
- **Improved Payment Updates**: Fixed updatePayment method to properly handle form data and use the more flexible updateRegistration method
- **Enhanced Data Handling**: Added payment_reference field handling in updateRegistration method
- **Column Existence Check**: Added verification that payment_date column exists before attempting to update it
- **Fallback Update Mechanism**: Added direct SQL update as fallback when model-based updates fail
- **Update Verification**: Added verification steps to confirm updates are actually applied to the database
- **Column Diagnostics**: Added detailed column information logging to diagnose database structure issues
- **Database Compatibility**: Added more aggressive SQL script to ensure payment_status column is properly defined as VARCHAR(50)
- **Column Creation**: Added SQL script to ensure payment_reference and payment_date columns exist with correct data types
- **Enhanced SQL Logging**: Added detailed SQL execution logging for better troubleshooting
- **Detailed Status Tracking**: Added comprehensive payment status tracking in logs
- **Enhanced User Feedback**: Added success flash message for payment status updates
- **Simplified Validation**: Removed unnecessary validation to streamline the update process
- **Consistent URL Structure**: Maintained consistent URL structure for coordinator actions
- **Improved Error Handling**: Enhanced error handling for registration management operations
- **Mobile-First Design**: Maintained responsive design principles throughout the implementation

## Previous in v2.19.95: Fixed Coordinator Registration View and Delete Functionality

This version fixes issues with the coordinator registration management:

- **Fixed View Button**: Resolved non-working "view" button on /coordinator/registrations/4 page
- **Added Missing Method**: Added view_registration method to CoordinatorController as URL-friendly alias for viewRegistration
- **Verified Delete Functionality**: Ensured "delete" button works correctly in coordinator registration management
- **Consistent URL Structure**: Maintained consistent URL structure for coordinator actions
- **Improved Error Handling**: Enhanced error handling for registration management operations
- **Mobile-First Design**: Maintained responsive design principles throughout the implementation

## Previous in v2.19.94: Fixed Judge Assignments Management

This version fixes the missing view error when managing judge assignments:

- **Fixed Missing View Error**: Resolved "The requested view 'coordinator/judges/index' could not be found" error
- **Created Missing View**: Added the missing coordinator/judges/index.php view file
- **Fixed CSRF Function Error**: Resolved "Call to undefined function csrfField()" error in judge assignments view
- **Fixed PHP 8.4.6 Deprecation**: Resolved "Deprecated: Creation of dynamic property CoordinatorController::$db" warning
- **Fixed SQL Parameter Error**: Resolved "SQLSTATE[HY093]: Invalid parameter number" error in judge assignment functionality
- **Fixed Remove Functionality**: Fixed non-functioning "Remove" button in judge assignments management
- **Method Name Consistency**: Renamed removeJudge method to removeJudgeAssignment to match form action URL
- **Improved NULL Handling**: Modified SQL queries to properly handle NULL values in category_id
- **Added Missing Methods**: Implemented assignJudge and removeJudgeAssignment methods in ShowModel
- **Improved Code Quality**: Properly declared the $db property in CoordinatorController class
- **Proper CSRF Implementation**: Implemented correct CSRF token handling in the judge assignments interface
- **Complete Judge Management**: Implemented full judge assignment management interface
- **Consistent Navigation**: Added breadcrumb navigation for better user experience
- **Mobile-First Design**: Maintained responsive design principles throughout the implementation

## Previous in v2.19.93: Fixed Category Support in Metrics Management

This version fixes errors in the coordinator metrics management related to categories:

- **Fixed Array Key Errors**: Resolved "Undefined array key 'category_id'" and "Undefined array key 'categories'" errors
- **Fixed Foreach Error**: Resolved "foreach() argument must be of type array|object, null given" error in metrics views
- **Enhanced Database Structure**: Added category_id column to judging_metrics table
- **Improved Controller Logic**: Updated CoordinatorController to properly load categories for metric forms
- **Enhanced Model Methods**: Updated ShowModel methods to handle the category_id field
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.92: Fixed Metric Management Methods

This version fixes a critical error in the coordinator metrics management:

- **Fixed Fatal Error**: Resolved "Call to undefined method ShowModel::getMetricById()" error in CoordinatorController
- **Added Missing Methods**: Implemented getMetricById, createMetric, and updateMetric methods in ShowModel
- **Enhanced Error Handling**: Added comprehensive error handling in all metric management methods
- **Improved Model Structure**: Updated ShowModel version number to reflect new methods
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.91: Fixed Judging Metrics Property Error

This version fixes an error in the coordinator metrics view:

- **Fixed Undefined Property Error**: Resolved "Undefined property: stdClass::$max_points" error in coordinator metrics view
- **Updated Form Fields**: Changed form field names from max_points to max_score in add/edit metric forms
- **Improved Consistency**: Ensured consistency between database column names and view property names
- **Enhanced Error Prevention**: Added null coalescing operator for safer property access
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.91: Fixed Category Name Display in Coordinator View

This version fixes the remaining issue in the coordinator show view:

- **Fixed Category Name Display**: Resolved "Undefined property: stdClass::$category_name" error in registration counts display
- **Added Fallback Label**: Added 'Uncategorized' label for registrations without a category name
- **Enhanced Error Prevention**: Improved property existence checks for registration data
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.90: Fixed Coordinator Show View Errors

This version fixes multiple errors in the coordinator show view:

- **Fixed Undefined Property Errors**: Resolved "Undefined property: stdClass::$event_date", "stdClass::$is_active", "stdClass::$category_name", "stdClass::$category_id", and "stdClass::$has_judged" errors
- **Fixed Deprecated Function Warning**: Fixed "Passing null to parameter #1 ($datetime) of type string is deprecated" warning in strtotime()
- **Improved Property Access**: Added proper property existence checks before accessing object properties
- **Enhanced Error Prevention**: Added fallback display for missing data
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.89: Fixed Judge Assignments in Coordinator View

This version fixes a critical error in the coordinator show view:

- **Fixed Fatal Error**: Resolved "Call to undefined method ShowModel::getJudgeAssignments()" error
- **Added Missing Method**: Implemented getJudgeAssignments method in ShowModel
- **Improved Error Handling**: Added robust error handling and table existence checks
- **Enhanced Reliability**: Ensured coordinator show view works correctly even with partial database setup
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.88: Fixed Coordinator Dashboard Date Errors

This version fixes critical errors in the coordinator dashboard:

- **Fixed Undefined Property Error**: Resolved "Undefined property: stdClass::$event_date" error in coordinator dashboard
- **Fixed Deprecated Function Warning**: Fixed "Passing null to parameter #1 ($datetime) of type string is deprecated" warning in strtotime()
- **Fixed Status Display**: Resolved "Undefined property: stdClass::$is_active" error in coordinator dashboard
- **Improved Date Handling**: Added proper null checks before using date functions
- **Enhanced Error Prevention**: Added fallback display for missing dates
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.87: QR Code Logo Management and Logo Display Fix

This version adds the ability to delete QR code logos and fixes logo display in generated QR codes:

- **Delete QR Code Logo**: Added a button to remove existing QR code logos
- **User-Friendly Confirmation**: Includes confirmation dialog before deletion
- **Clean File Management**: Automatically removes logo files from the server
- **Fixed Logo Display**: Resolved issue with logos not appearing in generated QR codes
- **Improved Path Handling**: Enhanced logo file path detection with multiple fallbacks
- **Better Error Logging**: Added detailed logging for QR code logo operations
- **Mobile-First Design**: Maintains responsive design principles

## Previous in v2.19.86: QR Code Regeneration Feature and Complete QR Code System Overhaul

This version adds a QR code regeneration feature to the registration details page and completely replaces the QR code generation system:

- **Regenerate QR Code Button**: Added a button to regenerate QR codes on the registration details page
- **Uses QR Code Settings**: Regenerated QR codes use the latest settings from the QR Code Settings page
- **Completely New QR Code System**: Replaced the non-functional QR code generator with Google Chart API integration
- **Guaranteed Scannable QR Codes**: All generated QR codes are now fully scannable with any QR code reader
- **Multiple API Fallbacks**: Added cascading fallback to alternative QR code APIs if the primary one fails
- **Logo Support Maintained**: QR codes can still include custom logos from the QR Code Settings
- **Full Error Correction Support**: Implemented proper error correction level handling (L, M, Q, H)
- **Margin Control**: QR code margin settings are now properly applied
- **Improved User Experience**: Easily update QR codes without leaving the registration details page
- **Mobile-First Design**: Maintained responsive design principles throughout the implementation
- **Consistent UI**: Integrated the new button seamlessly with existing QR code actions

## Previous in v2.19.85: Fixed Registration Categories Display

This version fixes an issue with the registration categories display:

- **Fixed Undefined Variable Error**: Resolved "Undefined variable $categories" error in registrations.php
- **Improved Controller Logic**: Added categories loading to AdminController and CoordinatorController
- **Enhanced Error Handling**: Added fallback handling in views to prevent errors when categories are not available
- **Robust UI**: Improved error handling in category dropdown display
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.84: Enhanced Registration Management

This version enhances the registration management functionality:

- **All Registrations View**: Admins can now view all registrations across all shows in one place
- **Coordinator Access**: Coordinators can view all registrations for shows they manage
- **Show Filtering**: Filter registrations by specific show using a convenient dropdown
- **User-Specific View**: Maintain ability to view registrations for a specific user
- **Data Export**: Export registration data to CSV for further analysis
- **Print-Friendly View**: Generate print-optimized views of registration lists
- **Quick Access**: Direct links to create new registrations and navigate between views
- **Enhanced Search**: Improved search functionality across all registration data
- **Mobile-First Design**: Maintained responsive design principles throughout
- **Robust Error Handling**: Fixed undefined property errors and improved data validation
- **Fallback Mechanisms**: Added multiple fallback options for database queries

## Previous in v2.19.83: Fixed QR Code Printing

This version fixes QR code printing functionality:

- **Fixed Missing View**: Added the missing 'print_qr_code' view for admin and user interfaces
- **Fixed Property Access**: Corrected vehicle property access in QR code printing templates
- **Enhanced User Experience**: Added print-specific styling for clean QR code printouts
- **Improved Navigation**: Added proper return URL handling after QR code generation
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.19.82: Enhanced QR Code Customization

This version enhances QR code generation with advanced customization options:

- **Advanced QR Code Settings**: Added support for size, margin, and error correction level
- **Logo Support**: Enhanced QR code generation to properly support logos
- **Live Preview**: Added interactive preview of QR codes in the admin interface
- **Optimized Generation**: Improved QR code generation for better scannability
- **Mobile-First Design**: Maintained responsive design principles throughout
- **User Guidance**: Added tips for optimal QR code generation

## Previous in v2.19.81: Enhanced QR Code Support

This version added comprehensive QR code support with printable templates:

- **Printable Templates**: Added support for custom printable templates with QR codes
- **Default Registration Template**: Created a default vehicle registration template with QR code
- **Database Integration**: Added printable_templates table for storing custom templates
- **QR Code Storage**: Fixed QR code storage in the database for reliable retrieval
- **Web-Based Updates**: Added browser-accessible database update scripts
- **Mobile-First Design**: Maintained responsive design principles throughout

## Previous in v2.19.80: Fixed QR Code Generation

This version fixed QR code generation functionality:

- **Fixed QR Code Generation**: Resolved routing issue that prevented QR code generation from working
- **Enhanced QR Code Content**: QR codes now link directly to judging and fan voting pages
- **Added phpqrcode Library**: Implemented a simplified QR code generation library
- **Improved Routing**: Fixed routing for special methods like generateQrCode
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.79: Fixed Judge Dashboard Date Errors

This version fixes critical errors in the judge dashboard:

- **Fixed Undefined Property Error**: Resolved "Undefined property: stdClass::$event_date" error in judge dashboard
- **Fixed Deprecated Function Warning**: Fixed "Passing null to parameter #1 ($datetime) of type string is deprecated" warning in strtotime()
- **Improved Date Handling**: Added proper null checks before using date functions
- **Enhanced Error Prevention**: Added fallback display for missing dates
- **Mobile-First Design**: Maintained responsive design principles throughout the fix

## Previous in v2.19.77: Fixed Flash Message Function

This version fixes a critical error in the flash message system:

- **Fixed Fatal Error**: Resolved "Too few arguments to function flash()" error in judge dashboard
- **Enhanced Helper Function**: Improved flash() to work as both a setter and getter based on argument count
- **Better Documentation**: Updated function documentation to clarify dual functionality
- **Improved Error Handling**: Added robust error handling for flash message system

## Previous in v2.19.76: Fixed Judge Dashboard Functionality

This version fixed critical issues with the judge dashboard:

- **Fixed Fatal Error**: Resolved "Call to undefined method ShowModel::getShowsByJudge()" error
- **Added Missing Methods**: Implemented getShowsByJudge and getJudgeCategories methods in ShowModel
- **Improved Error Handling**: Added robust error handling and table existence checks
- **Enhanced Reliability**: Ensured judge dashboard works correctly even with partial database setup

## Previous in v2.19.75: Admin Access to All Features

This version enhances the admin role capabilities:

- **Unified Access**: Admin users now have access to all judging and coordinator features
- **Streamlined Navigation**: Added direct links to Judge and Coordinator dashboards in the admin menu
- **Enhanced Admin Sidebar**: Added dedicated section for judging and coordination in the admin settings sidebar
- **Improved Tools Menu**: Added judging and coordination section to the admin tools dropdown
- **Role Consolidation**: Admin users can now perform all functions without switching roles

## Previous in v2.19.74: Enhanced Judging System

This version adds comprehensive judging functionality:

- **Judge Assignment**: Easily assign judges to specific categories
- **Category Judging**: Score vehicles within each category
- **Judging Interface**: Intuitive interface for judging vehicles
- **Judging Status**: Track judging progress for each vehicle
- **Judge Management**: Add, remove, and manage judges for each show
- **Category Management**: Assign judges to specific categories

## Previous in v2.19.41: Award Management System

This version adds comprehensive award management functionality:

- **Award Creation**: Easily create awards for shows with different types (category, special, overall)
- **Award Assignment**: Assign awards to specific vehicles or categories
- **Award Management**: View and manage all awards for a show
- **Award Types**: Support for category awards, special awards, and overall awards
- **Database Integration**: Automatic creation of awards table if it doesn't exist
- **User Interface**: Intuitive interface for adding and managing awards

## Previous in v2.19.40: Improved Navigation and Judging System

This version enhances the judging system and fixes navigation issues:

- **Fixed Judging Navigation**: The link from admin/registrations to admin/judging now works correctly
- **Enhanced Judging Model**: Added comprehensive methods for tracking judged vehicles and judges
- **Improved Registration Model**: Added methods to count registrations by show and category
- **Automatic Table Creation**: System now automatically creates required tables for judging if they don't exist
- **Database Update Script**: Added update_judging_tables.sql to create all necessary judging-related tables
- **Robust Error Handling**: Added comprehensive error handling for all judging operations

## Previous in v2.19.39: Enhanced Payment Status Support

This version improves the payment status handling throughout the system:

- **Extended Payment Status Options**: Added 'free' and 'waived' options to payment_status enum
- **Database Update Script**: Included update_payment_status.sql to modify existing databases
- **Consistent Status Display**: Fixed inconsistency between 'pending' and 'unpaid' payment statuses
- **Improved User Dashboard**: Payment column now correctly displays all payment statuses
- **Better Free Registration Handling**: Free registrations now properly display as "Free" with a green badge

## Previous in v2.19.38: Dynamic Registration Forms

This version adds support for dynamic registration forms using templates:

- **Template-Based Forms**: Registration forms now use templates created in the form builder
- **Form Hierarchy**: System checks for show-specific templates first, then default templates, then falls back to system templates
- **Mobile-First Design**: All forms are now responsive with mobile-first design principles
- **Consistent UI**: Forms maintain consistent styling across the application
- **Custom Fields**: Support for all field types defined in templates

## Previous in v2.19.37: Default Categories and Metrics

This version adds the ability to quickly add pre-configured categories and metrics to shows:

- **Default Categories**: Create and manage a library of default show categories
- **Default Metrics**: Create and manage a library of default judging metrics
- **Quick Setup**: Add all default categories or metrics to a show with a single click
- **Admin Interface**: Dedicated admin pages for managing default categories and metrics
- **Time-Saving**: Streamline the process of setting up new shows with consistent categories and metrics
- **Customizable**: Default values can still be edited after adding them to a show

## Previous in v2.19.36: Fixed Form Submission for Event and Show Templates

This version fixes issues with form submission for "event" and "show" template types:

- **Proper Entity Type Validation**: Fixed validation for required fields based on correct entity type mapping
- **Improved Error Handling**: Better error messages when required fields are missing
- **Database Consistency**: Added SQL update script to ensure system_fields table has correct entity types
- **Enhanced Form Validation**: Properly validates required fields before form submission
- **Code Stability**: Fixed syntax error in SystemFieldManager.php by removing duplicate method declaration
- **Entity Type Mapping**: Fixed issue with event form templates showing "unknown" entity type
- **Form Field Validation**: Fixed issue with show templates using event fields instead of registration fields
- **Navigation Flow**: Fixed redirect issues after saving templates by rendering templates view directly

## Previous in v2.19.35: Enhanced Form Designer Reliability

This version further improves the form designer functionality:

- **Guaranteed System Field Detection**: Multiple layers of verification ensure system fields are properly identified
- **Automatic Database Maintenance**: System fields are automatically created if missing from the database
- **Improved Debugging**: Enhanced logging for easier troubleshooting of form designer issues
- **Consistent Vehicle Templates**: Padlock icons now reliably display for all system fields in vehicle templates
- **Entity Type Correction**: Fixed issue where system fields had incorrect entity types
- **Self-Healing Database**: System now automatically corrects entity type values in the database
- **Complete System Fields Support**: Added support for all system fields across event, vehicle, and registration entity types
- **Comprehensive Database Scripts**: New SQL scripts ensure system_fields table is properly set up

## Previous in v2.19.34: Form Designer Improvements

This version enhanced the form designer functionality:

- **Fixed Vehicle Templates**: Padlock icons now correctly display for vehicle type templates
- **Improved System Field Detection**: Better handling of system fields across all template types
- **Enhanced Database Integration**: Automatic verification of system fields in the database
- **Better Error Logging**: More detailed logging for troubleshooting form designer issues

## Previous in v2.19.33: Enhanced Admin Registration Management

This version improves the admin registration management capabilities:

- **Complete Registration Editing**: Admins can now fully edit registration details
- **Status Management**: Easily update registration and payment statuses
- **Vehicle Assignment**: Change vehicle assignments for registrations
- **Display Number Management**: Set and update display numbers for show vehicles

## Previous in v2.19.32: Dynamic Database Operations

This version introduces intelligent database operation handling:

- **Smart Query Building**: Dynamically constructs SQL queries based on available data
- **Partial Updates Support**: Updates only the fields that need to be changed
- **Improved Error Handling**: Better detection and reporting of database operation issues
- **Enhanced Logging**: More detailed logging for troubleshooting database operations

## Previous in v2.19.31: Advanced Database Compatibility

This version introduces sophisticated database compatibility features:

- **Schema-Aware Queries**: Automatically detects available database columns and adapts queries accordingly
- **Multi-Level Fallbacks**: Implements three levels of fallback mechanisms for maximum reliability
- **Fixed Critical Errors**: Resolved issues with missing columns that prevented registration management
- **Graceful Degradation**: System continues to function even with incomplete database schemas

## Previous in v2.19.30: UI Performance Improvements

This version enhances the user interface performance and reliability:

- **Fixed Modal Issues**: Resolved flickering and looping modals in the user registrations page
- **Optimized UI Performance**: Improved page responsiveness by reducing DOM elements
- **Enhanced User Experience**: Streamlined modal interactions for registration management
- **Improved JavaScript Handling**: Better event management for modal dialogs

## Previous in v2.19.29: Database Compatibility Fixes

This version addresses critical database compatibility issues:

- **Fixed Fatal Error**: Resolved "Unknown column 'u.phone' in 'field list'" error when viewing registrations
- **Dynamic SQL Queries**: Added intelligent query building that adapts to existing database columns
- **Robust Error Handling**: Implemented fallback queries to ensure system stability
- **Graceful Degradation**: System now handles missing database columns without crashing

## Previous in v2.19.28: PHP 8.4.6 Compatibility Fixes

This version addresses compatibility issues with PHP 8.4.6:

- **Fixed Undefined Properties**: Resolved warnings related to undefined properties in registration views
- **Fixed Deprecation Warnings**: Addressed deprecation warnings related to null values in number_format()
- **Enhanced Data Retrieval**: Improved registration data retrieval to include all necessary fields
- **Added Fallbacks**: Implemented proper fallback values for missing registration data

## Previous in v2.19.27: User Registration Management

This version adds the ability to view and manage all registrations for a specific user:

- **User Registrations View**: Added a dedicated page to view all registrations for a specific user across all shows
- **Quick Access**: Added "Registrations" button to the user management page for direct access
- **Advanced Filtering**: Filter user registrations by status (approved, pending, cancelled) and date (upcoming/past)
- **Streamlined Management**: Delete or cancel registrations directly from the user registrations page

## Previous in v2.19.26: Registration Management Improvements

This version enhances the registration management capabilities:

- **Registration Deletion**: Admins and coordinators can now delete user show registrations
- **Enhanced UI**: Added delete buttons to registration management pages
- **Confirmation Dialogs**: Added confirmation modals to prevent accidental deletion
- **Registration Statistics**: 
  - Added category breakdown statistics to registration management page
  - Added summary statistics (total, paid, pending, revenue)
- **Database Compatibility**: Added missing columns to database tables:
  - Added registration_number column to registrations table if missing
  - Added max_entries column to show_categories table if missing
- **Fixed SQL Errors**: 
  - Resolved "Unknown column 'r.registration_number'" error in registration listings
  - Fixed "Undefined variable $category_stats" warning in registration views
  - Fixed "Undefined variable $total_registrations" and related warnings
  - Fixed deprecated warning about passing null to number_format() function
- **Fallback Mechanisms**: 
  - Added alternative sorting method when registration_number column doesn't exist
  - Added fallback for max_entries column in category statistics
  - Added robust null checking for registration statistics
- **PHP 8.4.6 Compatibility**: Fixed deprecation warnings by properly declaring properties
- **Improved Error Handling**: Enhanced error handling in registration deletion process

## Previous in v2.19.25: Fixed ShowController Fatal Error

This version fixed a critical error in the ShowController:

- **Fixed Fatal Error**: Added missing isRegistrationOpen method to ShowModel
- **Improved Date Validation**: Added proper error handling for registration date validation
- **Enhanced Error Logging**: Added detailed error logging for registration status checks
- **PHP 8.4.6 Compatibility**: Ensured all code follows PHP 8.4.6 best practices

## Previous in v2.19.24: PHP 8.4.6 Compatibility Fix

This version fixed a PHP 8.4.6 deprecation warning:

- **Fixed Dynamic Property Warning**: Properly declared the systemFieldManager property in FormDesignerController
- **PHP 8.4.6 Compatibility**: Prevented dynamic property creation which is deprecated in PHP 8.4.6
- **Code Stability**: Improved code structure to follow PHP best practices

## Previous in v2.19.23: Fixed Form Designer System Fields Protection

This version fixes issues with system fields protection in the form designer:

- **System Fields Protection**: Fixed padlock icons for system fields to work with all template IDs
- **Consistent Field Identification**: Updated FormDesignerController to use SystemFieldManager for consistent system field identification
- **Enhanced Logging**: Added proper logging for system field detection to aid in troubleshooting
- **Universal Protection**: Ensured padlock icons appear for all system fields regardless of template type or ID
- **Smarter Form Validation**: Fixed form validation to only check for required system fields relevant to the current entity type
- **User-Friendly Warnings**: Added confirmation dialog when saving forms with missing system fields instead of blocking save
- **Robust CSRF Handling**: Fixed CSRF token validation to accept tokens from both headers and request body
- **Better Error Handling**: Improved error handling in form saving process with detailed logging
- **PHP 8.4.6 Compatibility**: Fixed deprecation warning by properly declaring class properties
- **Required Fields Distinction**: Added new method to distinguish between all system fields and required system fields
- **Entity-Specific Validation**: Updated JavaScript validation to only check for required system fields for the current entity type
- **Permanent Entity-Aware Validation**: Implemented permanent fix for entity-specific field validation in form designer
- **Form Type Awareness**: Fixed issue where vehicle fields were being validated in event forms
- **Code Optimization**: Removed duplicate fetch call in form save function
- **Database Integrity**: Added automatic database repair to fix incorrect entity_type values
- **Entity Type Enforcement**: Enhanced SystemFieldManager to ensure proper entity type filtering
- **Template Editor Fix**: Fixed edit_final.php to use required_system_fields instead of critical_fields for validation
- **Consistent Data Passing**: Updated FormDesignerController to pass required_system_fields to all form designer views
- **Client-Side Filtering**: Added client-side filtering of critical and required fields by entity type
- **Entity Type Detection**: Fixed entity type detection in edit_final.php template
- **Debugging Tools**: Added debug information to form designer to aid in troubleshooting
- **Forced Entity Type**: Implemented forced entity type detection based on template type
- **Submission Validation**: Added client-side filtering of required fields in form submission handler
- **Schema Compatibility**: Fixed entity type mapping to match database schema enum values
- **Enhanced Debugging**: Added detailed debug information to show actual template and entity types
- **Error Prevention**: Fixed undefined variable errors in edit_final.php
- **Code Stability**: Added proper variable initialization to prevent PHP warnings
- **Reliable Detection**: Improved JavaScript entity type detection for more reliable field filtering
- **Fallback Mechanism**: Added fallback entity type detection based on template type
- **Smart Detection**: Added template name-based entity type detection for better accuracy
- **Extended Support**: Added support for registration and user entity types
- **User-Friendly Validation**: Changed validation to warn instead of block when required fields are missing
- **Confirmation Dialog**: Added confirmation dialog for saving forms with missing recommended fields
- **Detailed Debugging**: Enhanced debug information to show system fields and template name
- **Special Handling**: Added special handling for registration forms to bypass validation
- **Vehicle Support**: Added special handling for vehicle forms to bypass validation
- **Simplified Logic**: Simplified field validation logic for better compatibility

## Previous in v2.19.22: Fixed User Registrations

This version fixed a critical error in the user registrations page:

- **Fixed Fatal Error**: Resolved the "Call to undefined method ShowModel::getUserCompletedShows()" error
- **Improved Error Handling**: Added proper error handling for awards and judging results tables
- **Database Compatibility**: Implemented table existence checks to prevent SQL errors when tables don't exist

## Previous in v2.19.21: Fixed Template Assignment

This version fixed issues with template assignment:

- **Default Templates**: Shows, registrations, and vehicles now correctly use default templates assigned in /admin/defaultTemplates
- **Template Hierarchy**: Fixed the template selection hierarchy to properly check entity-specific templates first, then default templates
- **Admin Interface**: Updated the admin interface to use the correct templates when editing shows

## Previous in v2.19.20: Fixed EntityTemplatesController

This version enhances the EntityTemplatesController and fixes critical errors:

### New Features
- **Entity Lookup**: Added ability to find and display entity details when assigning templates
- **Revert to Default**: Added functionality to revert entities to use default templates
- **Improved UI**: Enhanced the template assignment interface with entity details

### Bug Fixes
- **Fixed Constructor**: Removed call to non-existent parent constructor
- **Proper Inheritance**: Ensured proper inheritance from Controller base class
- **Fixed SQL Errors**: Improved column creation to check if columns exist before adding them
- **Database Compatibility**: Enhanced EntityTemplateManager to avoid "Column already exists" errors

## Previous in v2.19.19: Entity-Specific Templates

This version adds support for assigning specific templates to individual entities:

- **Entity-Specific Templates**: Ability to assign specific templates to individual shows, vehicles, and registrations
- **Template Override System**: Entity-specific templates override default templates when present
- **Database Structure**: Added template_id fields to shows, vehicles, and registrations tables
- **Improved Template Selection**: Enhanced template selection logic to respect entity-specific templates
- **Backward Compatibility**: Maintains compatibility with the default template system
- **Fixed View Loading**: Resolved "View does not exist" error when accessing /entityTemplates by extending the Controller base class
- **Code Simplification**: Removed redundant view loading code in EntityTemplatesController

## Previous in v2.19.18: Default Templates Management

This version adds a powerful new system for managing default templates:

- **Default Templates Manager**: New admin interface for setting default templates for shows, vehicles, and registrations
- **Entity-Specific Templates**: Shows now use 'event' type templates, vehicles use 'vehicle' type, and registrations use 'show' type
- **Template Priority System**: System now checks for entity-specific templates first, then default templates, then system templates
- **Automatic Database Setup**: System automatically creates the necessary database tables on first use
- **Improved Template Selection**: Enhanced template selection logic to respect user preferences
- **Menu Integration**: Added Default Templates to admin navigation menus for easy access

## Previous in v2.19.17: Fixed Admin Dashboard and Settings

This version fixed critical issues with the admin dashboard and settings:

- **Fixed Redirect Loop**: Resolved ERR_TOO_MANY_REDIRECTS issue when accessing the admin dashboard
- **Fixed Settings Access**: Added missing settings method to AdminController
- **Restored Dashboard Method**: Added back the missing dashboard method in AdminController
- **Added User Count Method**: Added getUserCount method to UserModel for dashboard statistics
- **Added Show Count Method**: Added getShowCount method to ShowModel for dashboard statistics
- **Enhanced Settings Model**: Added updateSettings method to SettingsModel
- **Fixed Duplicate Code**: Removed duplicate APPROOT definition and JudgingModel initialization

## Template Type Guidelines

- **Shows**: Always use templates of type "event"
- **Vehicles**: Always use templates of type "vehicle"
- **Registrations**: Always use templates of type "show"

## Previous in v2.19.15: Fixed Template Selection Logic

This version fixed an issue where the system was creating duplicate templates instead of using existing ones:

- **Improved Template Selection**: Enhanced the system to properly find and use custom templates
- **Template Type Priority**: Added a priority system that checks for show-specific templates first
- **Support for Custom Types**: Added support for 'event' type templates in the admin show editor
- **Respect User Templates**: System now respects user-created templates instead of defaulting to system templates
- **Duplicate Prevention**: Fixed issue where duplicate templates were being created for the same show

## Previous in v2.19.14: Fixed Template Processing Errors

This version fixes critical errors in the FormFieldSynchronizer that were causing PHP warnings:

- **Robust Error Handling**: Added comprehensive null checking to prevent PHP warnings
- **Template Validation**: Improved validation of template objects before processing
- **Fallback Templates**: Added fallback for missing templates to prevent synchronization failures
- **Error Prevention**: Fixed "Attempt to read property on false" errors throughout the system
- **Enhanced Logging**: Added detailed error logging to help diagnose template issues

## Previous in v2.19.13: Fixed Template Update Issue

This version fixes a critical issue where the form designer was creating new templates instead of updating existing ones:

- **Template Update Fix**: Fixed issue where form designer was creating new templates instead of updating existing ones
- **Template ID Tracking**: Added explicit template ID tracking to ensure templates are properly updated
- **Form Submission Enhancement**: Improved form submission to include all necessary template data
- **Error Logging**: Enhanced error logging for template update operations
- **Duplicate Prevention**: Added safeguards to prevent duplicate template creation

## Previous in v2.19.12: Fixed Custom Field Deletion

This version fixes an issue with custom fields not being properly deleted when removed from templates:

- **System Fields Integration**: Now uses the SystemFieldManager to properly identify system vs. custom fields
- **Improved Field Detection**: Correctly identifies custom fields using the system_fields database table
- **Fixed Field Cleanup**: Ensures all custom fields are properly cleaned up when removed from templates
- **Database Mapping Cleanup**: Properly removes database mappings for all deleted custom fields
- **Data Deletion**: Ensures all data associated with removed fields is deleted from both tables
- **Orphaned Data Cleanup**: Improved cleanup of orphaned custom field values
- **Database Update Script**: Added SQL script to ensure proper database structure including system_fields table
- **Form Designer Fix**: Fixed issue with template names being lost when editing templates
- **Multiple Safeguards**: Added several checks to ensure template names are preserved during form submission
- **Template-Type Detection**: Improved detection of template types to apply appropriate field requirements
- **Smart Field Requirements**: Only enforce critical fields for admin templates, not for regular templates
- **Template Saving Fix**: Fixed issue that prevented templates from being saved in the form designer

## Previous in v2.19.11: Dynamic Form Field System

This version introduces a completely dynamic form field system that works with all field types:

- **Dynamic Field Mapping**: Automatically maps form fields to appropriate database columns based on field type
- **Support for All Field Types**: Works with text, textarea, number, email, tel, url, date, time, datetime, select, radio, checkbox, file, color, range, richtext, signature, rating, and hidden fields
- **Improved Database Structure**: Ensures all necessary tables and columns exist
- **Enhanced Error Handling**: Better error recovery for form submissions
- **Diagnostic Tools**: Added debug_form_submission_helper.php for diagnosing and fixing form issues
- **MySQL 8.0.41 Compatibility**: Improved SQL scripts for better compatibility

## Previous in v2.19.8: Enhanced Custom Field Cleanup

This version enhances the custom field system with improved automatic cleanup:

- **Automatic Field Cleanup**: When fields are removed from form templates, their data is automatically deleted
- **Database Optimization**: Prevents accumulation of unused data in both custom_field_values table and custom_field_X columns
- **Improved Form Designer**: Form editor now properly manages the lifecycle of custom fields
- **Enhanced Data Integrity**: Ensures database only contains data for fields that are actually in use
- **Diagnostic Tools**: Added tools for troubleshooting field mapping issues
- **Explicit Field Detection**: Improved detection of removed fields in the form designer

## Previous in v2.19.5: Show Categories and Judging Metrics

The previous version introduced support for show categories and judging metrics:

- **Show Categories**: Automatically creates and manages show categories
- **Judging Metrics**: Adds support for custom judging metrics for each show
- **Automatic Table Creation**: Creates necessary database tables on-the-fly if they don't exist
- **Improved Error Handling**: Better error recovery for missing database tables and methods
- **Fixed Method Calls**: Resolved issues with missing methods in ShowModel class

## Features

- **User Management**: Registration, login, profile management, and role-based access control
- **Vehicle Management**: Add, edit, and manage vehicles with images
- **Show Management**: Create and manage car shows with categories and judging criteria
- **Registration System**: Register vehicles for shows with comprehensive payment processing
- **Payment Processing**: Support for PayPal, CashApp, Venmo, and free events
- **Show Listing Fees**: Charge for show listings based on user role permissions
- **Judging System**: Assign judges, score vehicles, and calculate results
- **Public Display**: Public pages for shows, vehicles, and results
- **Fan Voting**: Allow public voting for fan favorite vehicles
- **Dynamic Form Builder**: Create custom forms with support for all field types
- **Image Editor**: Edit, crop, resize, and enhance images for shows and vehicles
- **Advanced Image Viewer**: View images with zooming and panning capabilities in an AJAX popout
- **Featured Images**: Set featured images for shows to enhance visual appeal

## Form Field System

The system includes a powerful dynamic form field system that supports all common field types:

### Supported Field Types

- **Text Fields**: text, email, tel, url, hidden
- **Long Text**: textarea, richtext
- **Numbers**: number, range
- **Date/Time**: date, time, datetime
- **Selection**: select, radio, checkbox
- **Special**: file, color, signature, rating
- **Layout**: section, html (non-data fields)

### How It Works

1. **Field Mapping**: Each form field is automatically mapped to an appropriate database column based on its type
2. **Data Storage**: Data is stored in both the shows table (in custom_field_X columns) and the custom_field_values table
3. **Dynamic Columns**: The system automatically selects the best column type for each field
4. **Consistency**: The FormFieldSynchronizer ensures mappings are consistent across the application
5. **Automatic Cleanup**: When fields are removed from templates using the form designer, their data and mappings are automatically cleaned up

### Troubleshooting Form Issues

If you encounter issues with form submissions:

1. Use the debug_form_submission_helper.php tool to diagnose and fix issues
2. Use the cleanup_field_data.php tool to clean up orphaned mappings and data
3. Check field mappings in the field_mappings table
4. Ensure the custom_field_values table exists and has the correct structure
5. Verify that all custom_field_X columns exist in the shows table

If data remains in the database after removing fields from templates:

1. Run the cleanup_field_data.php script
2. Use the "Clean Up Field" option to clean up a specific field
3. Use the "Clean Up All Orphaned Mappings" option to clean up all orphaned mappings

## Requirements

- PHP 8.0 or higher (tested with PHP 8.4.6)
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)
- Composer (for dependency management)

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/events-and-shows.git
   ```

2. Navigate to the project directory:
   ```
   cd events-and-shows
   ```

3. Run the installer:
   ```
   php install.php
   ```

4. If you encounter database issues, run the database setup script:
   ```
   https://yourdomain.com/direct_create_tables.php
   ```
   
   Or open `install.php` in your browser.
   
5. If you encounter issues with form templates or custom fields not saving, run the form template diagnostic tool:
   ```
   https://yourdomain.com/form_template_diagnostic.php
   ```

6. If you need to manually create or recreate the field_mappings table, use the provided SQL script:
   ```
   database/manual/create_field_mappings_table.sql
   ```

4. Follow the on-screen instructions to:
   - Configure database connection
   - Set up admin account
   - Configure application settings

5. Make sure the uploads directory is writable:
   ```
   chmod 755 public/uploads
   ```

6. (Optional) Create test data for users and registrations:
   ```
   php create_minimal_tables.php
   ```
   
   Or for more comprehensive test data:
   ```
   php create_test_data.php
   ```
   ```
   chmod -R 777 uploads
   ```

6. After installation, remove the installer:
   ```
   rm install.php
   ```

## Configuration

The main configuration file is located at `config/config.php`. You can modify:

- Database settings
- Application name and URL
- File upload settings
- Facebook integration settings (if using Facebook login)

## User Roles

- **Admin**: Full access to all features
- **Coordinator**: Manage assigned shows, categories, and registrations
- **Judge**: Score vehicles in assigned categories
- **User**: Register vehicles and participate in shows

## Directory Structure

```
events-and-shows/
├── config/             # Configuration files
├── controllers/        # Controller classes
├── models/             # Model classes
├── views/              # View templates
├── lib/                # Core libraries and helpers
├── public/             # Publicly accessible files
│   ├── css/            # CSS files
│   ├── js/             # JavaScript files
│   ├── img/            # Image files
│   └── uploads/        # User uploaded files
├── install/            # Installation files
└── vendor/             # Composer dependencies
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a list of changes in each version.

## Support

For support, please open an issue on the GitHub repository or contact the maintainers directly.