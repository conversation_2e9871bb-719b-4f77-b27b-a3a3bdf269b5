<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Judge Dashboard</h1>
            <p class="text-muted mb-0">Manage your judging assignments and scoring</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/judge/scoreLink" class="btn btn-primary me-2 d-none d-sm-inline">
                <i class="fas fa-star me-2"></i> Quick Score
            </a>
            <a href="<?php echo BASE_URL; ?>/user/dashboard" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt me-2 d-none d-sm-inline"></i> User Dashboard
            </a>
        </div>
    </div>

    <?php flash('judge_message'); ?>

    <!-- Judge Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Judging Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm judge-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">Total Assignments</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($judge_counts['total_assignments'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">All Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm judge-overview-card" 
                                 data-filter="upcoming" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Upcoming Shows</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($judge_counts['upcoming_assignments'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Need Preparation</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm judge-overview-card" 
                                 data-filter="active" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Active Shows</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($judge_counts['active_assignments'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Currently Judging</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm judge-overview-card" 
                                 data-filter="completed" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Completed Shows</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($judge_counts['completed_assignments'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Past Events</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Judge Activity Summary -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Judging Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary"><?php echo number_format($judge_stats['total_scores'] ?? 0); ?></h4>
                            <small class="text-muted">Total Scores</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo number_format($judge_stats['this_month_scores'] ?? 0); ?></h4>
                            <small class="text-muted">This Month</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Next Assignment
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($judge_stats['next_show'])): ?>
                        <div class="text-center">
                            <h5 class="text-primary"><?php echo $judge_stats['next_show']['name']; ?></h5>
                            <p class="text-muted mb-1"><?php echo $judge_stats['next_show']['location']; ?></p>
                            <small class="text-warning">
                                <?php echo date('M j, Y', strtotime($judge_stats['next_show']['start_date'])); ?>
                            </small>
                            <div class="mt-2">
                                <span class="badge bg-info"><?php echo $judge_stats['next_show']['categories']; ?> categories</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-calendar-times fa-2x mb-2"></i>
                            <p>No upcoming assignments</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/judge/scoreLink" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <span>Quick Score</span>
                                <small class="text-muted mt-1">Score a vehicle</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/judge/schedule" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>View Schedule</span>
                                <small class="text-muted mt-1">Check assignments</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/judge/reports" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                                <span>Scoring Reports</span>
                                <small class="text-muted mt-1">View scoring history</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/judge/help" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-question-circle fa-2x mb-2"></i>
                                <span>Judging Guide</span>
                                <small class="text-muted mt-1">Get help & tips</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Assignment Details Section (Lazy Loaded) -->
    <div class="assignment-section" id="assignment-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">Assignment Details</span>
                        <span class="badge bg-secondary" id="assignment-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closeAssignmentSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-assignments" class="form-label">Search Assignments</label>
                        <input type="text" class="form-control" id="search-assignments" 
                               placeholder="Search by show name or location...">
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="all">All Status</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date-filter" class="form-label">Date Range</label>
                        <select class="form-select" id="date-filter">
                            <option value="">All Dates</option>
                            <option value="this_month">This Month</option>
                            <option value="next_month">Next Month</option>
                            <option value="this_year">This Year</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-assignments" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-assignments">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchAssignments()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearAssignmentSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportAssignments()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-assignments">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading assignments...</p>
            </div>
            
            <!-- Assignments Content (Will be populated via AJAX) -->
            <div id="assignments-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Judge overview card click handlers
    document.querySelectorAll('.judge-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadAssignmentSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-assignments').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchAssignments();
        }
    });

    // Filter change handlers
    document.getElementById('status-filter').addEventListener('change', searchAssignments);
    document.getElementById('date-filter').addEventListener('change', searchAssignments);
    document.getElementById('per-page-assignments').addEventListener('change', searchAssignments);
});

function loadAssignmentSection(filter = 'all') {
    // Show the assignment section
    const section = document.getElementById('assignment-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        document.getElementById('status-filter').value = filter;
    }

    // Load assignments
    loadAssignments(1);
}

function closeAssignmentSection() {
    const section = document.getElementById('assignment-section');
    section.style.display = 'none';
}

function loadAssignments(page = 1) {
    const loadingDiv = document.getElementById('loading-assignments');
    const contentDiv = document.getElementById('assignments-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-assignments').value;
    const statusFilter = document.getElementById('status-filter').value;
    const dateFilter = document.getElementById('date-filter').value;
    const perPage = document.getElementById('per-page-assignments').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        status_filter: statusFilter,
        date_filter: dateFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/judge/loadAssignments?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderAssignments(data);
        } else {
            showAssignmentError(data.error || 'Failed to load assignments');
        }
    })
    .catch(error => {
        console.error('Error loading assignments:', error);
        showAssignmentError('Network error occurred');
    });
}

function searchAssignments() {
    loadAssignments(1);
}

function clearAssignmentSearch() {
    document.getElementById('search-assignments').value = '';
    document.getElementById('status-filter').value = 'all';
    document.getElementById('date-filter').value = '';
    loadAssignments(1);
}

function renderAssignments(data) {
    const loadingDiv = document.getElementById('loading-assignments');
    const contentDiv = document.getElementById('assignments-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render assignments table and pagination
    let html = '';

    if (data.assignments.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No assignments found.</p></div>';
    } else {
        html = renderAssignmentsTable(data.assignments, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update assignment count display
    document.getElementById('assignment-count-display').textContent = data.pagination.total_assignments.toLocaleString();
}

function renderAssignmentsTable(assignments, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Show Name</th><th>Location</th><th>Date</th><th>Categories</th><th>Status</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    assignments.forEach(assignment => {
        html += '<tr>';
        html += '<td><strong>' + assignment.show_name + '</strong></td>';
        html += '<td>' + (assignment.location || 'TBD') + '</td>';
        html += '<td>' + formatDate(assignment.start_date) + '</td>';
        html += '<td><span class="badge bg-info">' + (assignment.category_count || 0) + ' categories</span></td>';
        html += '<td>' + getStatusBadge(assignment.status, assignment.start_date, assignment.end_date) + '</td>';
        html += '<td>' + getAssignmentActions(assignment.show_id, assignment.status) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderAssignmentPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_assignments.toLocaleString()} assignments`;
    html += '</div>';

    return html;
}

function getStatusBadge(status, startDate, endDate) {
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > now) {
        return '<span class="badge bg-success">Upcoming</span>';
    } else if (start <= now && end >= now) {
        return '<span class="badge bg-warning text-dark">Active</span>';
    } else {
        return '<span class="badge bg-info">Completed</span>';
    }
}

function getAssignmentActions(showId, status) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/judge/show/${showId}" class="btn btn-info" title="View Show">
                <i class="fas fa-eye"></i>
            </a>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" title="Judge Actions">
                    <i class="fas fa-gavel"></i>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/score/${showId}">
                        <i class="fas fa-star me-2"></i>Score Vehicles
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/categories/${showId}">
                        <i class="fas fa-tags me-2"></i>My Categories
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/scores/${showId}">
                        <i class="fas fa-chart-line me-2"></i>View Scores
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/reports/${showId}">
                        <i class="fas fa-file-alt me-2"></i>Judging Reports
                    </a></li>
                </ul>
            </div>
        </div>
    `;
}

function renderAssignmentPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadAssignments(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadAssignments(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadAssignments(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showAssignmentError(message) {
    const loadingDiv = document.getElementById('loading-assignments');
    const contentDiv = document.getElementById('assignments-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function exportAssignments() {
    // Get current filter values
    const search = document.getElementById('search-assignments').value;
    const statusFilter = document.getElementById('status-filter').value;
    const dateFilter = document.getElementById('date-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        status_filter: statusFilter,
        date_filter: dateFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/judge/exportAssignments?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
