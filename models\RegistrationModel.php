<?php
/**
 * Registration Model
 * 
 * This model handles all database operations related to vehicle registrations.
 */
class RegistrationModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Ensure all required fields are present in the registration object
     * 
     * @param object $registration Registration object to check
     * @return void
     */
    private function ensureRequiredFields(&$registration) {
        // Basic registration fields
        if (!isset($registration->id)) $registration->id = 0;
        if (!isset($registration->status)) $registration->status = 'unknown';
        
        // Vehicle fields
        if (!isset($registration->make)) $registration->make = 'Unknown Make';
        if (!isset($registration->model)) $registration->model = 'Unknown Model';
        if (!isset($registration->year)) $registration->year = '';
        if (!isset($registration->color)) $registration->color = '';
        if (!isset($registration->owner_id)) $registration->owner_id = 0;
        
        // User fields
        if (!isset($registration->owner_name)) $registration->owner_name = 'Unknown Owner';
        
        // Show fields
        if (!isset($registration->show_name)) $registration->show_name = 'Unknown Show';
        if (!isset($registration->start_date)) $registration->start_date = '';
        if (!isset($registration->end_date)) $registration->end_date = '';
        if (!isset($registration->show_location)) $registration->show_location = '';
        
        // Category fields
        if (!isset($registration->category_name)) $registration->category_name = 'Unknown Category';
        
        // Date fields
        if (!isset($registration->created_at)) {
            if (isset($registration->registration_date)) {
                $registration->created_at = $registration->registration_date;
            } else {
                $registration->created_at = gmdate('Y-m-d H:i:s');
            }
        }
    }
    
    /**
     * Get all registrations for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowRegistrations($showId) {
        try {
            // First try with registration_number column
            $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.license_plate, v.owner_id,
                            u.name as owner_name, u.email, u.phone, sc.name as category_name,
                            s.name as show_name, s.start_date, s.end_date, s.location as show_location
                            FROM registrations r 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            JOIN shows s ON r.show_id = s.id
                            WHERE r.show_id = :show_id 
                            ORDER BY r.registration_number');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->resultSet();
            
            // Ensure all required fields are present
            if (is_array($result)) {
                foreach ($result as &$registration) {
                    $this->ensureRequiredFields($registration);
                }
            }
            
            return $result;
        } catch (Exception $e) {
            // If registration_number column doesn't exist, use id for ordering
            error_log('Error in getShowRegistrations: ' . $e->getMessage() . '. Trying alternative query.');
            
            try {
                $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.license_plate, v.owner_id,
                                u.name as owner_name, u.email, u.phone, sc.name as category_name,
                                s.name as show_name, s.start_date, s.end_date, s.location as show_location
                                FROM registrations r 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN users u ON v.owner_id = u.id 
                                JOIN show_categories sc ON r.category_id = sc.id 
                                JOIN shows s ON r.show_id = s.id
                                WHERE r.show_id = :show_id 
                                ORDER BY r.id');
                $this->db->bind(':show_id', $showId);
                
                $result = $this->db->resultSet();
                
                // Ensure all required fields are present
                if (is_array($result)) {
                    foreach ($result as &$registration) {
                        $this->ensureRequiredFields($registration);
                    }
                }
                
                return $result;
            } catch (Exception $e2) {
                error_log('Error in getShowRegistrations fallback: ' . $e2->getMessage());
                return [];
            }
        }
    }
    
    /**
     * Get registrations for a user
     * 
     * @param int $userId User ID
     * @return array
     */
    public function getUserRegistrations($userId) {
        try {
            // Try to select with checked_in column
            $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                            s.location as show_location, r.checked_in,
                            v.make, v.model, v.year, sc.name as category_name 
                            FROM registrations r 
                            JOIN shows s ON r.show_id = s.id 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            WHERE v.owner_id = :user_id 
                            ORDER BY s.start_date DESC');
            $this->db->bind(':user_id', $userId);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            try {
                // If there's an error (likely because checked_in doesn't exist), try without it
                $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                                s.location as show_location, 0 as checked_in,
                                v.make, v.model, v.year, sc.name as category_name 
                                FROM registrations r 
                                JOIN shows s ON r.show_id = s.id 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN show_categories sc ON r.category_id = sc.id 
                                WHERE v.owner_id = :user_id 
                                ORDER BY s.start_date DESC');
                $this->db->bind(':user_id', $userId);
                
                $result = $this->db->resultSet();
                return is_array($result) ? $result : [];
            } catch (Exception $e) {
                // Log error if needed
                error_log('Error in getUserRegistrations: ' . $e->getMessage());
                return [];
            }
        }
    }
    
    /**
     * Public method to get category prefix for display number
     * 
     * @param int $categoryId Category ID
     * @return string Category prefix (e.g., "CL" for Classics)
     */
    public function getCategoryPrefixPublic($categoryId) {
        return $this->getCategoryPrefix($categoryId);
    }
    
    /**
     * Get category prefix for display number
     * 
     * @param int $categoryId Category ID
     * @return string Category prefix (e.g., "CL" for Classics)
     */
    private function getCategoryPrefix($categoryId) {
        try {
            // Get category name
            $this->db->query('SELECT name FROM show_categories WHERE id = :id');
            $this->db->bind(':id', $categoryId);
            $category = $this->db->single();
            
            if (!$category) {
                return 'X-'; // Default prefix if category not found
            }
            
            // Clean the category name - remove parentheses and special characters
            $cleanName = preg_replace('/\([^)]*\)/', '', $category->name); // Remove content in parentheses
            $cleanName = trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $cleanName)); // Remove special characters
            
            // Get words from the cleaned name
            $words = explode(' ', $cleanName);
            $words = array_filter($words); // Remove empty elements
            $prefix = '';
            
            // Handle different category naming patterns
            if (count($words) == 0) {
                // If no valid words after cleaning, use first two letters of original name
                $prefix = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $category->name), 0, 2));
            }
            else if (count($words) == 1) {
                // For single words, use first two letters
                $prefix = strtoupper(substr($words[array_key_first($words)], 0, 2));
            }
            else if (count($words) == 2) {
                // For two words, use first letter of each
                $prefix = strtoupper(
                    substr($words[array_key_first($words)], 0, 1) . 
                    substr($words[array_key_first($words) + 1], 0, 1)
                );
            }
            else {
                // For multiple words, use a more distinctive approach
                // If first word is short (like "The", "A", etc.), use first letter of first word and first letter of second word
                if (strlen($words[array_key_first($words)]) <= 3) {
                    $prefix = strtoupper(
                        substr($words[array_key_first($words)], 0, 1) . 
                        substr($words[array_key_first($words) + 1], 0, 1)
                    );
                } 
                // Otherwise use first letter of first word and first letter of last significant word
                else {
                    $prefix = strtoupper(
                        substr($words[array_key_first($words)], 0, 1) . 
                        substr($words[array_key_last($words)], 0, 1)
                    );
                }
            }
            
            // Special case handling for common categories
            $lowerName = strtolower($category->name);
            if (strpos($lowerName, 'classic') !== false) {
                $prefix = 'CL';
            } else if (strpos($lowerName, 'muscle') !== false) {
                $prefix = 'MU';
            } else if (strpos($lowerName, 'truck') !== false || strpos($lowerName, 'suv') !== false) {
                $prefix = 'TR';
            } else if (strpos($lowerName, 'motorcycle') !== false || strpos($lowerName, 'bike') !== false) {
                $prefix = 'MC';
            } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'asian') !== false) {
                $prefix = 'AI';
            } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'european') !== false) {
                $prefix = 'EI';
            } else if (strpos($lowerName, 'modern') !== false) {
                $prefix = 'MO';
            } else if (strpos($lowerName, 'custom') !== false || strpos($lowerName, 'modified') !== false) {
                $prefix = 'CM';
            } else if (strpos($lowerName, 'contemporary') !== false) {
                $prefix = 'CT';
            } else if (strpos($lowerName, 'antique') !== false) {
                $prefix = 'AN';
            } else if (strpos($lowerName, 'vintage') !== false) {
                $prefix = 'VN';
            } else if (strpos($lowerName, 'sport') !== false) {
                $prefix = 'SP';
            } else if (strpos($lowerName, 'luxury') !== false) {
                $prefix = 'LX';
            } else if (strpos($lowerName, 'electric') !== false || strpos($lowerName, 'ev') !== false) {
                $prefix = 'EV';
            } else if (strpos($lowerName, 'hybrid') !== false) {
                $prefix = 'HY';
            } else if (strpos($lowerName, 'convertible') !== false) {
                $prefix = 'CV';
            } else if (strpos($lowerName, 'coupe') !== false) {
                $prefix = 'CP';
            } else if (strpos($lowerName, 'sedan') !== false) {
                $prefix = 'SD';
            } else if (strpos($lowerName, 'race') !== false || strpos($lowerName, 'racing') !== false) {
                $prefix = 'RC';
            } else if (strpos($lowerName, 'off') !== false && strpos($lowerName, 'road') !== false) {
                $prefix = 'OR';
            }
            
            // For custom categories, ensure we have a meaningful 2-letter prefix
            if (strlen($prefix) != 2) {
                // If we have a single letter, add the next most significant letter
                if (strlen($prefix) == 1) {
                    // Try to find a second significant letter
                    if (count($words) > 1) {
                        // Use the first letter of the second word
                        $prefix .= strtoupper(substr($words[array_key_first($words) + 1], 0, 1));
                    } else if (strlen($words[array_key_first($words)]) > 1) {
                        // Use the second letter of the first word
                        $prefix .= strtoupper(substr($words[array_key_first($words)], 1, 1));
                    } else {
                        // Fallback: add X
                        $prefix .= 'X';
                    }
                } else if (strlen($prefix) > 2) {
                    // If we have more than 2 letters, truncate to 2
                    $prefix = substr($prefix, 0, 2);
                } else {
                    // If we somehow have no prefix, use the first two letters of the category name
                    // or the first letter + X if the name is only one character
                    $categoryName = preg_replace('/[^a-zA-Z0-9]/', '', $category->name);
                    if (strlen($categoryName) > 1) {
                        $prefix = strtoupper(substr($categoryName, 0, 2));
                    } else if (strlen($categoryName) == 1) {
                        $prefix = strtoupper($categoryName) . 'X';
                    } else {
                        $prefix = 'XX'; // Last resort fallback
                    }
                }
            }
            
            return $prefix . '-';
        } catch (Exception $e) {
            error_log('Error getting category prefix: ' . $e->getMessage());
            return 'R-'; // Default prefix if error occurs
        }
    }
    
    /**
     * Get registration by ID
     * 
     * @param int $id Registration ID
     * @return object|bool Registration object or false if not found
     */
    public function getRegistrationById($id) {
        try {
            // Check which columns exist in the users table
            $this->db->query("DESCRIBE users");
            $userColumns = $this->db->resultSet();
            
            // Create a map of column names for easy lookup
            $columnMap = [];
            foreach ($userColumns as $column) {
                $columnMap[$column->Field] = true;
            }
            
            // Build the query based on available columns
            $selectFields = 'r.*, v.make, v.model, v.year, v.color, v.license_plate, v.owner_id, u.name as owner_name';
            
            // Add optional fields if they exist
            if (isset($columnMap['email'])) {
                $selectFields .= ', u.email as owner_email';
            }
            
            if (isset($columnMap['phone'])) {
                $selectFields .= ', u.phone as owner_phone';
            }
            
            if (isset($columnMap['username'])) {
                $selectFields .= ', u.username';
            }
            
            $selectFields .= ', sc.name as category_name, s.name as show_name';
            
            $query = "SELECT $selectFields 
                      FROM registrations r 
                      JOIN vehicles v ON r.vehicle_id = v.id 
                      JOIN users u ON v.owner_id = u.id 
                      JOIN show_categories sc ON r.category_id = sc.id 
                      JOIN shows s ON r.show_id = s.id 
                      WHERE r.id = :id";
            
            $this->db->query($query);
            $this->db->bind(':id', $id);
            
            $registration = $this->db->single();
            
            // Set default values for potentially missing fields
            if ($registration) {
                if (!isset($registration->registration_fee)) {
                    $registration->registration_fee = 0;
                }
                if (!isset($registration->registration_date)) {
                    $registration->registration_date = gmdate('Y-m-d H:i:s');
                }
                if (!isset($registration->owner_email)) {
                    $registration->owner_email = '';
                }
                if (!isset($registration->owner_phone)) {
                    $registration->owner_phone = '';
                }
                if (!isset($registration->username)) {
                    $registration->username = 'User #' . $registration->owner_id;
                }
            }
            
            return $registration;
        } catch (Exception $e) {
            // Log the error
            error_log('Error in getRegistrationById: ' . $e->getMessage());
            
            // Fallback to a minimal query with only essential fields
            try {
                $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.owner_id, 
                                  u.name as owner_name, sc.name as category_name, s.name as show_name 
                                  FROM registrations r 
                                  JOIN vehicles v ON r.vehicle_id = v.id 
                                  JOIN users u ON v.owner_id = u.id 
                                  JOIN show_categories sc ON r.category_id = sc.id 
                                  JOIN shows s ON r.show_id = s.id 
                                  WHERE r.id = :id');
                $this->db->bind(':id', $id);
                
                $registration = $this->db->single();
                
                // Set default values for potentially missing fields
                if ($registration) {
                    if (!isset($registration->registration_fee)) {
                        $registration->registration_fee = 0;
                    }
                    if (!isset($registration->registration_date)) {
                        $registration->registration_date = gmdate('Y-m-d H:i:s');
                    }
                    if (!isset($registration->license_plate)) {
                        $registration->license_plate = '';
                    }
                    // Set default values for user-related fields
                    $registration->owner_email = '';
                    $registration->owner_phone = '';
                    $registration->username = 'User #' . $registration->owner_id;
                }
                
                return $registration;
            } catch (Exception $e2) {
                // Log the second error
                error_log('Error in getRegistrationById fallback: ' . $e2->getMessage());
                
                // Last resort fallback - create a minimal registration object
                try {
                    $this->db->query('SELECT r.id, r.show_id, r.vehicle_id, r.category_id, r.status 
                                      FROM registrations r 
                                      WHERE r.id = :id');
                    $this->db->bind(':id', $id);
                    
                    $registration = $this->db->single();
                    
                    if ($registration) {
                        // Add minimal required properties
                        $registration->registration_fee = 0;
                        $registration->registration_date = gmdate('Y-m-d H:i:s');
                        $registration->owner_name = 'Unknown';
                        $registration->owner_email = '';
                        $registration->owner_phone = '';
                        $registration->username = '';
                        $registration->make = 'Unknown';
                        $registration->model = 'Unknown';
                        $registration->year = '';
                        $registration->color = '';
                        $registration->license_plate = '';
                        $registration->category_name = 'Unknown';
                        $registration->show_name = 'Unknown';
                    }
                    
                    return $registration;
                } catch (Exception $e3) {
                    error_log('Error in getRegistrationById last resort: ' . $e3->getMessage());
                    return false;
                }
            }
        }
    }
    
    /**
     * Create a new registration
     * 
     * @param array $data Registration data
     * @return bool|int False on failure, registration ID on success
     */
    public function createRegistration($data) {
        // Generate registration number
        $registrationNumber = $this->generateRegistrationNumber($data['show_id']);
        
        $this->db->query('INSERT INTO registrations (show_id, category_id, vehicle_id, user_id, registration_number, 
                          status, fee, payment_method_id, payment_status, payment_reference, created_at) 
                          VALUES (:show_id, :category_id, :vehicle_id, :user_id, :registration_number, 
                          :status, :fee, :payment_method_id, :payment_status, :payment_reference, NOW())');
        
        $this->db->bind(':show_id', $data['show_id']);
        $this->db->bind(':category_id', $data['category_id']);
        $this->db->bind(':vehicle_id', $data['vehicle_id']);
        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':registration_number', $registrationNumber);
        $this->db->bind(':status', $data['status']);
        $this->db->bind(':fee', $data['fee'] ?? 0.00);
        $this->db->bind(':payment_method_id', $data['payment_method_id'] ?? null);
        $this->db->bind(':payment_status', $data['payment_status'] ?? 'pending');
        $this->db->bind(':payment_reference', $data['payment_reference'] ?? null);
        
        try {
            if ($this->db->execute()) {
                $registrationId = $this->db->lastInsertId();
                
                // Get category prefix for display number
                $categoryPrefix = $this->getCategoryPrefix($data['category_id']);
                
                // Set display_number to category prefix + registration ID
                $displayNumber = $categoryPrefix . $registrationId;
                $this->db->query('UPDATE registrations SET display_number = :display_number WHERE id = :id');
                $this->db->bind(':display_number', $displayNumber);
                $this->db->bind(':id', $registrationId);
                $this->db->execute();
                
                return $registrationId;
            } else {
                error_log("RegistrationModel::createRegistration - Failed to execute query");
                return false;
            }
        } catch (Exception $e) {
            error_log("RegistrationModel::createRegistration - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a registration
     * 
     * @param int|array $id Registration ID or data array with 'id' key
     * @param array|null $data Registration data (optional if first param is array)
     * @return bool
     */
    public function updateRegistration($id, $data = null) {
        try {
            // Handle both function signatures:
            // 1. updateRegistration(array $data) - where $data includes 'id'
            // 2. updateRegistration($id, array $data) - where $id is the registration ID
            
            if (is_array($id) && $data === null) {
                // First format: updateRegistration(array $data)
                $data = $id;
                if (!isset($data['id'])) {
                    throw new Exception("Registration ID is required for update");
                }
                $registrationId = $data['id'];
            } else {
                // Second format: updateRegistration($id, array $data)
                if (!is_numeric($id)) {
                    throw new Exception("Invalid registration ID for update");
                }
                $registrationId = $id;
                if (!is_array($data)) {
                    throw new Exception("Update data must be an array");
                }
            }
            
            // Build the query dynamically based on provided data
            $updateFields = [];
            $queryParams = [':id' => $registrationId];
            
            // Add status if provided
            if (isset($data['status'])) {
                // Log the status value for debugging
                error_log('DEBUG: Setting status to: "' . $data['status'] . '" for registration ID: ' . $registrationId);
                
                // Check if the status value is valid for the enum
                $validStatuses = ['pending', 'approved', 'rejected', 'cancelled'];
                if (in_array($data['status'], $validStatuses)) {
                    $updateFields[] = "status = :status";
                    $queryParams[':status'] = $data['status'];
                } else {
                    error_log('ERROR: Invalid status value: "' . $data['status'] . '". Must be one of: ' . implode(', ', $validStatuses));
                }
            }
            
            // Add category_id if provided
            if (isset($data['category_id'])) {
                $updateFields[] = "category_id = :category_id";
                $queryParams[':category_id'] = $data['category_id'];
            }
            
            // Handle display_number field
            $displayNumberHandled = false;
            
            // Add display_number if provided
            if (isset($data['display_number'])) {
                $updateFields[] = "display_number = :display_number";
                $queryParams[':display_number'] = $data['display_number'];
                $displayNumberHandled = true;
            } 
            // If category_id is changed but display_number is not provided, update display_number with new prefix
            else if (isset($data['category_id']) && !$displayNumberHandled) {
                // Get the registration to check if we need to update display_number
                $this->db->query('SELECT display_number FROM registrations WHERE id = :id');
                $this->db->bind(':id', $registrationId);
                $registration = $this->db->single();
                
                if ($registration && (empty($registration->display_number) || strpos($registration->display_number, '-') !== false)) {
                    // Generate new display number with category prefix
                    $displayNumber = $this->getCategoryPrefix($data['category_id']) . $registrationId;
                    $updateFields[] = "display_number = :display_number";
                    $queryParams[':display_number'] = $displayNumber;
                    $displayNumberHandled = true;
                }
            }
            
            // Add check-in status if provided
            if (isset($data['checked_in'])) {
                $updateFields[] = "checked_in = :checked_in";
                $queryParams[':checked_in'] = $data['checked_in'] ? 1 : 0;
            }
            
            // Add check-in time if provided
            if (isset($data['check_in_time'])) {
                if ($data['check_in_time'] === null) {
                    $updateFields[] = "check_in_time = NULL";
                } else {
                    $updateFields[] = "check_in_time = :check_in_time";
                    $queryParams[':check_in_time'] = $data['check_in_time'];
                }
            }
            
            // Add other fields as needed
            if (isset($data['payment_status'])) {
                // Check the current payment_status in the database and get column information
                try {
                    // Get column information to check exact column name and type
                    $this->db->query("SHOW COLUMNS FROM registrations");
                    $columns = $this->db->resultSet();
                    $paymentStatusColumn = null;
                    
                    foreach ($columns as $column) {
                        if (strtolower($column->Field) == 'payment_status') {
                            $paymentStatusColumn = $column;
                        }
                    }
                    
                    // Check current value (only for debugging critical issues)
                    $this->db->query("SELECT payment_status FROM registrations WHERE id = :id");
                    $this->db->bind(':id', $registrationId);
                    $currentStatus = $this->db->single();
                } catch (Exception $e) {
                    error_log('Error checking current payment_status: ' . $e->getMessage());
                }
                
                // Add payment_status to update fields - use exact column name if found
                if (isset($paymentStatusColumn)) {
                    $updateFields[] = "`" . $paymentStatusColumn->Field . "` = :payment_status";
                } else {
                    $updateFields[] = "payment_status = :payment_status";
                }
                $queryParams[':payment_status'] = $data['payment_status'];
                
                // Check if payment_date column exists before trying to update it
                try {
                    $this->db->query("SHOW COLUMNS FROM registrations LIKE 'payment_date'");
                    $columnExists = $this->db->single();
                    
                    // If payment status is completed and payment_date column exists, update it
                    if ($columnExists && $data['payment_status'] == 'completed') {
                        $updateFields[] = "payment_date = CASE WHEN payment_date IS NULL THEN NOW() ELSE payment_date END";
                    }
                } catch (Exception $e) {
                    // Column doesn't exist, continue with other updates
                    // Only log critical errors
                }
            }
            
            if (isset($data['payment_date'])) {
                $updateFields[] = "payment_date = :payment_date";
                $queryParams[':payment_date'] = $data['payment_date'];
            }
            
            if (isset($data['payment_method'])) {
                $updateFields[] = "payment_method = :payment_method";
                $queryParams[':payment_method'] = $data['payment_method'];
            }
            
            if (isset($data['payment_reference'])) {
                $updateFields[] = "payment_reference = :payment_reference";
                $queryParams[':payment_reference'] = $data['payment_reference'];
            }
            
            if (isset($data['transaction_id'])) {
                $updateFields[] = "transaction_id = :transaction_id";
                $queryParams[':transaction_id'] = $data['transaction_id'];
            }
            
            // display_number is already handled above, so we skip it here
            
            if (isset($data['qr_code'])) {
                $updateFields[] = "qr_code = :qr_code";
                $queryParams[':qr_code'] = $data['qr_code'];
            }
            
            // Always add updated_at timestamp
            $updateFields[] = "updated_at = NOW()";
            
            // If no fields to update, return true (no changes needed)
            if (empty($updateFields)) {
                return true;
            }
            
            // Build and execute the query
            $query = 'UPDATE registrations SET ' . implode(', ', $updateFields) . ' WHERE id = :id';
            
            // Log the query and parameters for debugging
            error_log('DEBUG: Registration update query: ' . $query);
            error_log('DEBUG: Registration update params: ' . json_encode($queryParams));
            
            $this->db->query($query);
            
            // Bind all parameters
            foreach ($queryParams as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            // Execute and get result
            $result = $this->db->execute();
            
            // Log the result
            if (!$result) {
                error_log('ERROR: Failed to update registration ID ' . $registrationId);
            }
            
            return $result;
        } catch (Exception $e) {
            // Only log actual errors, not success messages
            error_log('ERROR in updateRegistration: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update payment information
     * 
     * @param array $data Payment data
     * @return bool
     */
    public function updatePayment($data) {
        $this->db->query('UPDATE registrations SET payment_status = :payment_status, 
                          fee = :fee, payment_method_id = :payment_method_id, 
                          payment_date = CASE WHEN :payment_status_case = "completed" AND payment_date IS NULL THEN NOW() ELSE payment_date END, 
                          payment_reference = :payment_reference, 
                          updated_at = NOW() 
                          WHERE id = :id');
        
        $this->db->bind(':payment_status', $data['payment_status']);
        $this->db->bind(':payment_status_case', $data['payment_status']); // Bind the same value with a different parameter name
        $this->db->bind(':fee', $data['fee'] ?? 0.00);
        $this->db->bind(':payment_method_id', $data['payment_method_id']);
        $this->db->bind(':payment_reference', $data['payment_reference'] ?? null);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Get check-in statistics for a show
     * 
     * @param int $showId Show ID
     * @return object Statistics object with total, checked_in, and percentage
     */
    public function getCheckInStats($showId) {
        try {
            $this->db->query('SELECT 
                            COUNT(*) as total,
                            SUM(CASE WHEN checked_in = 1 THEN 1 ELSE 0 END) as checked_in
                            FROM registrations 
                            WHERE show_id = :show_id 
                            AND (payment_status = "paid" OR payment_status = "free" OR payment_status = "completed")');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->single();
            
            if ($result) {
                $result->percentage = $result->total > 0 ? round(($result->checked_in / $result->total) * 100) : 0;
            } else {
                $result = (object)[
                    'total' => 0,
                    'checked_in' => 0,
                    'percentage' => 0
                ];
            }
            
            return $result;
        } catch (Exception $e) {
            error_log('Error in getCheckInStats: ' . $e->getMessage());
            return (object)[
                'total' => 0,
                'checked_in' => 0,
                'percentage' => 0
            ];
        }
    }
    
    /**
     * Get checked-in vehicles for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getCheckedInVehicles($showId) {
        try {
            $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.owner_id, 
                            u.name as owner_name, sc.name as category_name, 
                            r.check_in_time
                            FROM registrations r 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            WHERE r.show_id = :show_id AND r.checked_in = 1
                            ORDER BY r.check_in_time DESC');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log('Error in getCheckedInVehicles: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get registrations with pending payments
     * 
     * @param string $paymentStatus Payment status to filter by
     * @return array
     */
    public function getRegistrationsByPaymentStatus($paymentStatus) {
        $this->db->query('SELECT r.*, s.name as show_name, s.start_date, 
                          v.make, v.model, v.year, v.owner_id, 
                          u.name as owner_name, u.email as owner_email,
                          sc.name as category_name, pm.name as payment_method_name
                          FROM registrations r 
                          JOIN shows s ON r.show_id = s.id 
                          JOIN vehicles v ON r.vehicle_id = v.id 
                          JOIN users u ON v.owner_id = u.id 
                          JOIN show_categories sc ON r.category_id = sc.id 
                          LEFT JOIN payment_methods pm ON r.payment_method_id = pm.id
                          WHERE r.payment_status = :payment_status
                          ORDER BY r.created_at DESC');
        $this->db->bind(':payment_status', $paymentStatus);
        
        return $this->db->resultSet();
    }
    
    /**
     * Delete a registration
     * 
     * @param int $id Registration ID
     * @return bool
     */
    public function deleteRegistration($id) {
        $this->db->query('DELETE FROM registrations WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Check if a vehicle is already registered for a show
     * 
     * @param int $showId Show ID
     * @param int $vehicleId Vehicle ID
     * @return bool
     */
    public function isVehicleRegistered($showId, $vehicleId) {
        $this->db->query('SELECT id FROM registrations WHERE show_id = :show_id AND vehicle_id = :vehicle_id');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':vehicle_id', $vehicleId);
        $this->db->single();
        
        return $this->db->rowCount() > 0;
    }
    
    /**
     * Generate a unique registration number for a show
     * 
     * @param int $showId Show ID
     * @return string
     */
    private function generateRegistrationNumber($showId) {
        // Get the current highest registration number for this show
        try {
            // First try to find registrations with the new RER- prefix
            $this->db->query('SELECT registration_number FROM registrations 
                              WHERE show_id = :show_id AND registration_number LIKE "RER-%" 
                              ORDER BY CAST(SUBSTRING(registration_number, 5) AS DECIMAL(20,0)) DESC LIMIT 1');
            $this->db->bind(':show_id', $showId);
            $result = $this->db->single();
            
            $nextNum = 1;
            if ($result && isset($result->registration_number)) {
                // Extract the numeric part after "RER-"
                $numericPart = substr($result->registration_number, 4);
                // Convert to integer and increment
                $nextNum = intval($numericPart) + 1;
            } else {
                // If no registrations with RER- prefix exist, start from 1
                $nextNum = 1;
            }
            
            // Format: RER-0000000001 (10 digits with leading zeros)
            return 'RER-' . str_pad($nextNum, 10, '0', STR_PAD_LEFT);
        } catch (Exception $e) {
            // Log error and use a fallback method
            error_log("Error in generateRegistrationNumber: " . $e->getMessage());
            
            // Fallback: Use timestamp + random number to ensure uniqueness
            $timestamp = time();
            $random = mt_rand(1000, 9999);
            $nextNum = $timestamp . $random;
            
            // Ensure it's exactly 10 digits by truncating or padding
            $nextNum = substr(str_pad($nextNum, 10, '0', STR_PAD_LEFT), -10);
            
            return 'RER-' . $nextNum;
        }
    }
    
    /**
     * Get registrations to be judged by a specific judge
     * 
     * @param int $showId Show ID
     * @param int $judgeId Judge ID
     * @return array
     */
    public function getJudgingAssignments($showId, $judgeId) {
        try {
            // First, check if there are any approved registrations for this show
            $this->db->query('SELECT COUNT(*) as count FROM registrations WHERE show_id = :show_id AND status = :status');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':status', 'approved');
            $result = $this->db->single();
            
            if (!$result || $result->count == 0) {
                error_log("No approved registrations found for show ID: $showId");
                return [];
            }
            
            // Check if the user is an admin (admins can judge all vehicles)
            $this->db->query('SELECT role FROM users WHERE id = :user_id');
            $this->db->bind(':user_id', $judgeId);
            $user = $this->db->single();
            $isAdmin = $user && strpos($user->role, 'admin') !== false;
            
            // For admins, get all vehicles regardless of category assignment
            if ($isAdmin) {
                $this->db->query('SELECT r.id as registration_id, r.registration_number, r.display_number, 
                                r.vehicle_id, v.make, v.model, v.year, v.color, 
                                sc.id as category_id, sc.name as category_name, 
                                u.name as owner_name, 
                                (SELECT file_path FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image,
                                (SELECT COUNT(*) FROM scores WHERE registration_id = r.id AND judge_id = :judge_id1 AND is_draft = 0) > 0 as judging_completed,
                                (SELECT COUNT(*) FROM scores WHERE registration_id = r.id AND judge_id = :judge_id2) > 0 as judging_started
                                FROM registrations r 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN users u ON v.owner_id = u.id 
                                LEFT JOIN show_categories sc ON r.category_id = sc.id 
                                WHERE r.show_id = :show_id AND r.status = :status
                                ORDER BY sc.name, r.registration_number');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':judge_id1', $judgeId);
                $this->db->bind(':judge_id2', $judgeId);
                $this->db->bind(':status', 'approved');
            } else {
                // For regular judges, check their category assignments
                // First, check if they have any category assignments
                $this->db->query('SELECT COUNT(*) as count FROM judge_assignments 
                                WHERE show_id = :show_id AND judge_id = :judge_id AND category_id IS NOT NULL');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':judge_id', $judgeId);
                $result = $this->db->single();
                
                if ($result && $result->count > 0) {
                    // Judge has specific category assignments
                    $this->db->query('SELECT r.id as registration_id, r.registration_number, r.display_number, 
                                    r.vehicle_id, v.make, v.model, v.year, v.color, 
                                    sc.id as category_id, sc.name as category_name, 
                                    u.name as owner_name, 
                                    (SELECT file_path FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image,
                                    (SELECT COUNT(*) FROM scores WHERE registration_id = r.id AND judge_id = :judge_id1 AND is_draft = 0) > 0 as judging_completed,
                                    (SELECT COUNT(*) FROM scores WHERE registration_id = r.id AND judge_id = :judge_id2) > 0 as judging_started
                                    FROM registrations r 
                                    JOIN vehicles v ON r.vehicle_id = v.id 
                                    JOIN users u ON v.owner_id = u.id 
                                    JOIN show_categories sc ON r.category_id = sc.id 
                                    WHERE r.show_id = :show_id AND r.status = :status
                                    AND r.category_id IN (
                                        SELECT category_id FROM judge_assignments 
                                        WHERE show_id = :show_id2 AND judge_id = :judge_id3 AND category_id IS NOT NULL
                                    )
                                    ORDER BY sc.name, r.registration_number');
                } else {
                    // Judge has no specific category assignments, check if they have a general assignment
                    $this->db->query('SELECT COUNT(*) as count FROM judge_assignments 
                                    WHERE show_id = :show_id AND judge_id = :judge_id AND category_id IS NULL');
                    $this->db->bind(':show_id', $showId);
                    $this->db->bind(':judge_id', $judgeId);
                    $result = $this->db->single();
                    
                    if ($result && $result->count > 0) {
                        // Judge has a general assignment (can judge all categories)
                        $this->db->query('SELECT r.id as registration_id, r.registration_number, r.display_number, 
                                        r.vehicle_id, v.make, v.model, v.year, v.color, 
                                        sc.id as category_id, sc.name as category_name, 
                                        u.name as owner_name, 
                                        (SELECT file_path FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image,
                                        (SELECT COUNT(*) FROM scores WHERE registration_id = r.id AND judge_id = :judge_id1 AND is_draft = 0) > 0 as judging_completed,
                                        (SELECT COUNT(*) FROM scores WHERE registration_id = r.id AND judge_id = :judge_id2) > 0 as judging_started
                                        FROM registrations r 
                                        JOIN vehicles v ON r.vehicle_id = v.id 
                                        JOIN users u ON v.owner_id = u.id 
                                        LEFT JOIN show_categories sc ON r.category_id = sc.id 
                                        WHERE r.show_id = :show_id AND r.status = :status
                                        ORDER BY sc.name, r.registration_number');
                    } else {
                        // Judge has no assignments at all
                        error_log("Judge ID: $judgeId has no category assignments for show ID: $showId");
                        return [];
                    }
                }
                
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':judge_id1', $judgeId);
                $this->db->bind(':judge_id2', $judgeId);
                $this->db->bind(':judge_id3', $judgeId);
                $this->db->bind(':show_id2', $showId);
                $this->db->bind(':status', 'approved');
            }
            
            $assignments = $this->db->resultSet();
            
            // Log the number of assignments found
            error_log("Found " . count($assignments) . " judging assignments for judge ID: $judgeId in show ID: $showId");
            
            return $assignments;
        } catch (Exception $e) {
            error_log("Error in getJudgingAssignments: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count registrations for a show
     * 
     * @param int $showId Show ID
     * @param string $status Optional status filter
     * @return int
     */
    public function countShowRegistrations($showId, $status = null) {
        $sql = 'SELECT COUNT(*) as count FROM registrations WHERE show_id = :show_id';
        
        if ($status) {
            $sql .= ' AND status = :status';
        }
        
        $this->db->query($sql);
        $this->db->bind(':show_id', $showId);
        
        if ($status) {
            $this->db->bind(':status', $status);
        }
        
        $result = $this->db->single();
        
        return $result ? $result->count : 0;
    }
    
    /**
     * Count registrations by category
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function countRegistrationsByCategory($showId) {
        try {
            // Try with max_entries column
            $this->db->query('SELECT sc.id, sc.id as category_id, sc.name as category_name, COUNT(r.id) as count, sc.max_entries 
                            FROM show_categories sc 
                            LEFT JOIN registrations r ON sc.id = r.category_id AND r.status = :status 
                            WHERE sc.show_id = :show_id 
                            GROUP BY sc.id, sc.name, sc.max_entries 
                            ORDER BY sc.name');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':status', 'approved');
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            // If max_entries column doesn't exist, use a default value of 0
            error_log('Error in countRegistrationsByCategory: ' . $e->getMessage() . '. Using fallback query.');
            
            $this->db->query('SELECT sc.id, sc.id as category_id, sc.name as category_name, COUNT(r.id) as count, 0 as max_entries 
                            FROM show_categories sc 
                            LEFT JOIN registrations r ON sc.id = r.category_id AND r.status = :status 
                            WHERE sc.show_id = :show_id 
                            GROUP BY sc.id, sc.name 
                            ORDER BY sc.name');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':status', 'approved');
            
            return $this->db->resultSet();
        }
    }
    
    /**
     * Get registrations by vehicle IDs
     * 
     * @param array $vehicleIds Array of vehicle IDs
     * @return array Registrations
     */
    public function getRegistrationsByVehicleIds($vehicleIds) {
        if (empty($vehicleIds)) {
            return [];
        }
        
        try {
            // Create a comma-separated list of vehicle IDs
            $vehicleIdList = implode(',', array_map('intval', $vehicleIds));
            
            // Use the list directly in the query (safe since we've sanitized with intval)
            $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                            s.location as show_location, 
                            v.make, v.model, v.year, v.color, v.owner_id,
                            u.name as owner_name,
                            sc.name as category_name 
                            FROM registrations r 
                            JOIN shows s ON r.show_id = s.id 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id
                            JOIN show_categories sc ON r.category_id = sc.id 
                            WHERE r.vehicle_id IN (' . $vehicleIdList . ') 
                            ORDER BY s.start_date DESC');
            
            $result = $this->db->resultSet();
            
            // Ensure all required fields are present
            if (is_array($result)) {
                foreach ($result as &$registration) {
                    $this->ensureRequiredFields($registration);
                }
            }
            
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in getRegistrationsByVehicleIds: ' . $e->getMessage());
            
            try {
                // Try a simpler query as fallback
                $vehicleIdList = implode(',', array_map('intval', $vehicleIds));
                
                $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                                v.make, v.model, v.year, v.color, v.owner_id
                                FROM registrations r 
                                JOIN shows s ON r.show_id = s.id 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                WHERE r.vehicle_id IN (' . $vehicleIdList . ') 
                                ORDER BY r.id DESC');
                
                $result = $this->db->resultSet();
                
                // Ensure all required fields are present
                if (is_array($result)) {
                    foreach ($result as &$registration) {
                        $this->ensureRequiredFields($registration);
                    }
                }
                
                return is_array($result) ? $result : [];
            } catch (Exception $e2) {
                error_log('Error in getRegistrationsByVehicleIds fallback: ' . $e2->getMessage());
                return [];
            }
        }
    }
    
    /**
     * Get registrations by show IDs
     * 
     * @param array $showIds Array of show IDs
     * @return array Array of registration objects
     */
    public function getRegistrationsByShowIds($showIds) {
        if (empty($showIds)) {
            return [];
        }
        
        try {
            // Create a comma-separated list of show IDs
            $showIdList = implode(',', array_map('intval', $showIds));
            
            // Use the list directly in the query (safe since we've sanitized with intval)
            $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                            s.location as show_location, 
                            v.make, v.model, v.year, v.color,
                            u.name as owner_name, v.owner_id,
                            sc.name as category_name 
                            FROM registrations r 
                            JOIN shows s ON r.show_id = s.id 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id
                            JOIN show_categories sc ON r.category_id = sc.id 
                            WHERE r.show_id IN (' . $showIdList . ') 
                            ORDER BY s.start_date DESC, r.id DESC');
            
            $result = $this->db->resultSet();
            
            // Ensure all required fields are present
            if (is_array($result)) {
                foreach ($result as &$registration) {
                    $this->ensureRequiredFields($registration);
                }
            }
            
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in getRegistrationsByShowIds: ' . $e->getMessage());
            
            // Try a more basic query as fallback
            try {
                // Create a comma-separated list of show IDs
                $showIdList = implode(',', array_map('intval', $showIds));
                
                $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                                v.make, v.model, v.year, v.color,
                                u.name as owner_name, v.owner_id,
                                sc.name as category_name 
                                FROM registrations r 
                                JOIN shows s ON r.show_id = s.id 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN users u ON v.owner_id = u.id
                                JOIN show_categories sc ON r.category_id = sc.id 
                                WHERE r.show_id IN (' . $showIdList . ') 
                                ORDER BY r.id DESC');
                
                $result = $this->db->resultSet();
                
                // Ensure all required fields are present
                if (is_array($result)) {
                    foreach ($result as &$registration) {
                        $this->ensureRequiredFields($registration);
                    }
                }
                
                return is_array($result) ? $result : [];
            } catch (Exception $e2) {
                error_log('Error in getRegistrationsByShowIds fallback: ' . $e2->getMessage());
                
                // Last resort fallback - try with minimal joins
                try {
                    $showIdList = implode(',', array_map('intval', $showIds));
                    
                    $this->db->query('SELECT r.* FROM registrations r 
                                    WHERE r.show_id IN (' . $showIdList . ') 
                                    ORDER BY r.id DESC');
                    
                    $result = $this->db->resultSet();
                    
                    // Ensure all required fields are present
                    if (is_array($result)) {
                        foreach ($result as &$registration) {
                            $this->ensureRequiredFields($registration);
                        }
                    }
                    
                    return is_array($result) ? $result : [];
                } catch (Exception $e3) {
                    error_log('Error in getRegistrationsByShowIds last resort: ' . $e3->getMessage());
                    return [];
                }
            }
        }
    }
    
    /**
     * Get all registrations with limit
     * 
     * @param int $limit Maximum number of registrations to return
     * @return array Array of registration objects
     */
    public function getAllRegistrations($limit = 1000) {
        try {
            $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                            s.location as show_location, 
                            v.make, v.model, v.year, v.color,
                            u.name as owner_name, v.owner_id,
                            sc.name as category_name 
                            FROM registrations r 
                            JOIN shows s ON r.show_id = s.id 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id
                            JOIN show_categories sc ON r.category_id = sc.id 
                            ORDER BY r.id DESC
                            LIMIT :limit');
            
            $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            
            $result = $this->db->resultSet();
            
            // Ensure all required fields are present
            if (is_array($result)) {
                foreach ($result as &$registration) {
                    $this->ensureRequiredFields($registration);
                }
            }
            
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in getAllRegistrations: ' . $e->getMessage());
            
            // Try a more basic query as fallback
            try {
                $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                                v.make, v.model, v.year, v.color,
                                u.name as owner_name, v.owner_id,
                                sc.name as category_name 
                                FROM registrations r 
                                JOIN shows s ON r.show_id = s.id 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN users u ON v.owner_id = u.id
                                JOIN show_categories sc ON r.category_id = sc.id 
                                ORDER BY r.id DESC
                                LIMIT :limit');
                
                $this->db->bind(':limit', $limit, PDO::PARAM_INT);
                
                $result = $this->db->resultSet();
                
                // Ensure all required fields are present
                if (is_array($result)) {
                    foreach ($result as &$registration) {
                        $this->ensureRequiredFields($registration);
                    }
                }
                
                return is_array($result) ? $result : [];
            } catch (Exception $e2) {
                error_log('Error in getAllRegistrations fallback: ' . $e2->getMessage());
                
                // Last resort fallback - try with minimal joins
                try {
                    $this->db->query('SELECT r.* FROM registrations r 
                                    ORDER BY r.id DESC
                                    LIMIT :limit');
                    
                    $this->db->bind(':limit', $limit, PDO::PARAM_INT);
                    
                    $result = $this->db->resultSet();
                    
                    // Ensure all required fields are present
                    if (is_array($result)) {
                        foreach ($result as &$registration) {
                            $this->ensureRequiredFields($registration);
                        }
                    }
                    
                    return is_array($result) ? $result : [];
                } catch (Exception $e3) {
                    error_log('Error in getAllRegistrations last resort: ' . $e3->getMessage());
                    return [];
                }
            }
        }
    }
    
    /**
     * Get registration count by show
     * 
     * @param int $showId Show ID
     * @return int Count of registrations
     */
    public function getRegistrationCountByShow($showId) {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM registrations WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->single();
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log('Error getting registration count by show: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get registration count by category
     * 
     * @param int $categoryId Category ID
     * @return int Count of registrations
     */
    public function getRegistrationCountByCategory($categoryId) {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM registrations WHERE category_id = :category_id');
            $this->db->bind(':category_id', $categoryId);
            
            $result = $this->db->single();
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log('Error getting registration count by category: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get registrations by category
     * 
     * @param int $categoryId Category ID
     * @param string $status Optional status filter (default: 'approved')
     * @return array Array of registrations
     */
    public function getRegistrationsByCategory($categoryId, $status = 'approved') {
        try {
            $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.owner_id, 
                            u.name as owner_name, u.email as owner_email,
                            sc.name as category_name,
                            (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.id AND is_primary = 1 LIMIT 1) as primary_image
                            FROM registrations r 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            WHERE r.category_id = :category_id AND r.status = :status
                            ORDER BY r.registration_number');
            $this->db->bind(':category_id', $categoryId);
            $this->db->bind(':status', $status);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log('Error getting registrations by category: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get user's registered vehicles for a specific show
     * 
     * @param int $showId Show ID
     * @param int $userId User ID
     * @param string $status Optional status filter (default: 'approved')
     * @return array Array of registrations with vehicle details
     */
    public function getUserRegistrationsForShow($showId, $userId, $status = 'approved') {
        try {
            $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.owner_id, 
                            u.name as owner_name, u.email as owner_email,
                            sc.name as category_name, sc.id as category_id,
                            (SELECT file_path FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image,
                            (SELECT file_name FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image_name,
                            r.created_at as registration_date
                            FROM registrations r 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            WHERE r.show_id = :show_id AND v.owner_id = :user_id AND r.status = :status
                            ORDER BY r.created_at DESC');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':status', $status);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log('Error getting user registrations for show: ' . $e->getMessage());
            
            // Try alternative query if the first one fails (might be due to missing columns)
            try {
                $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.owner_id, 
                                u.name as owner_name,
                                sc.name as category_name, sc.id as category_id,
                                r.created_at as registration_date
                                FROM registrations r 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN users u ON v.owner_id = u.id 
                                JOIN show_categories sc ON r.category_id = sc.id 
                                WHERE r.show_id = :show_id AND v.owner_id = :user_id AND r.status = :status
                                ORDER BY r.created_at DESC');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':status', $status);
                
                $result = $this->db->resultSet();
                return is_array($result) ? $result : [];
            } catch (Exception $e2) {
                error_log('Error in alternative query for getUserRegistrationsForShow: ' . $e2->getMessage());
                return [];
            }
        }
    }
    
    /**
     * Get registration by vehicle ID and show ID
     * 
     * @param int $vehicleId Vehicle ID
     * @param int $showId Show ID
     * @return object|bool Registration object or false if not found
     */
    public function getRegistrationByVehicleAndShow($vehicleId, $showId) {
        try {
            // Check which columns exist in the users table
            $this->db->query("DESCRIBE users");
            $userColumns = $this->db->resultSet();
            
            // Create a map of column names for easy lookup
            $columnMap = [];
            foreach ($userColumns as $column) {
                $columnMap[$column->Field] = true;
            }
            
            // Build the query based on available columns
            $selectFields = 'r.*, v.make, v.model, v.year, v.color, v.license_plate, v.owner_id, u.name as owner_name';
            
            // Add optional fields if they exist
            if (isset($columnMap['email'])) {
                $selectFields .= ', u.email as owner_email';
            }
            
            if (isset($columnMap['phone'])) {
                $selectFields .= ', u.phone as owner_phone';
            }
            
            // Add show and category information
            $selectFields .= ', s.name as show_name, s.start_date, s.end_date, s.location as show_location';
            $selectFields .= ', sc.name as category_name';
            
            // Build the query
            $query = "SELECT $selectFields
                     FROM registrations r
                     JOIN vehicles v ON r.vehicle_id = v.id
                     JOIN users u ON v.owner_id = u.id
                     JOIN shows s ON r.show_id = s.id
                     JOIN show_categories sc ON r.category_id = sc.id
                     WHERE r.vehicle_id = :vehicle_id AND r.show_id = :show_id";
            
            $this->db->query($query);
            $this->db->bind(':vehicle_id', $vehicleId);
            $this->db->bind(':show_id', $showId);
            
            $registration = $this->db->single();
            
            if ($registration) {
                // Ensure all required fields are present
                $this->ensureRequiredFields($registration);
                return $registration;
            }
            
            return false;
        } catch (Exception $e) {
            error_log('Error in getRegistrationByVehicleAndShow: ' . $e->getMessage());
            
            // Try a simpler query as fallback
            try {
                $this->db->query('SELECT * FROM registrations 
                                 WHERE vehicle_id = :vehicle_id AND show_id = :show_id');
                $this->db->bind(':vehicle_id', $vehicleId);
                $this->db->bind(':show_id', $showId);
                
                $registration = $this->db->single();
                
                if ($registration) {
                    // Get additional information separately
                    $this->db->query('SELECT make, model, year, color, license_plate, owner_id FROM vehicles WHERE id = :id');
                    $this->db->bind(':id', $vehicleId);
                    $vehicle = $this->db->single();
                    
                    if ($vehicle) {
                        foreach ((array)$vehicle as $key => $value) {
                            $registration->$key = $value;
                        }
                        
                        // Get owner name
                        $this->db->query('SELECT name FROM users WHERE id = :id');
                        $this->db->bind(':id', $vehicle->owner_id);
                        $user = $this->db->single();
                        
                        if ($user) {
                            $registration->owner_name = $user->name;
                        }
                        
                        // Get show information
                        $this->db->query('SELECT name, start_date, end_date, location FROM shows WHERE id = :id');
                        $this->db->bind(':id', $showId);
                        $show = $this->db->single();
                        
                        if ($show) {
                            $registration->show_name = $show->name;
                            $registration->start_date = $show->start_date;
                            $registration->end_date = $show->end_date;
                            $registration->show_location = $show->location;
                        }
                        
                        // Get category information
                        $this->db->query('SELECT name FROM show_categories WHERE id = :id');
                        $this->db->bind(':id', $registration->category_id);
                        $category = $this->db->single();
                        
                        if ($category) {
                            $registration->category_name = $category->name;
                        }
                    }
                    
                    // Ensure all required fields are present
                    $this->ensureRequiredFields($registration);
                    return $registration;
                }
                
                return false;
            } catch (Exception $e2) {
                error_log('Error in getRegistrationByVehicleAndShow fallback: ' . $e2->getMessage());
                return false;
            }
        }
    }
    
    /**
     * Update notes for a registration
     * 
     * @param array $data Registration data with 'id' and 'notes' keys
     * @return bool
     */
    public function updateNotes($data) {
        try {
            // Check if notes column exists
            $this->db->query("SHOW COLUMNS FROM registrations LIKE 'notes'");
            $columnExists = $this->db->single();
            
            // If notes column doesn't exist, create it
            if (!$columnExists) {
                $this->db->query("ALTER TABLE registrations ADD COLUMN notes TEXT");
                $this->db->execute();
            }
            
            // Update notes
            $this->db->query("UPDATE registrations SET notes = :notes WHERE id = :id");
            $this->db->bind(':notes', $data['notes']);
            $this->db->bind(':id', $data['id']);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("RegistrationModel::updateNotes - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check in a vehicle for a show
     * 
     * @param int $id Registration ID
     * @return bool True if successful, false otherwise
     */
    public function checkInVehicle($id) {
        try {
            // Get registration to verify it exists
            $registration = $this->getRegistrationById($id);
            
            if (!$registration) {
                error_log("RegistrationModel::checkInVehicle - Registration not found: " . $id);
                return false;
            }
            
            // Check if already checked in
            if (isset($registration->checked_in) && $registration->checked_in) {
                // Already checked in, consider this a success
                return true;
            }
            
            // Update check-in status
            $data = [
                'id' => $id,
                'checked_in' => 1,
                'check_in_time' => gmdate('Y-m-d H:i:s')
            ];
            
            // Update registration
            $result = $this->updateRegistration($id, $data);
            
            if ($result) {
                error_log("Vehicle checked in: Registration #" . $id);
            } else {
                error_log("Failed to check in vehicle: Registration #" . $id);
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("RegistrationModel::checkInVehicle - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Undo check-in for a vehicle
     * 
     * @param int $id Registration ID
     * @return bool True if successful, false otherwise
     */
    public function undoCheckInVehicle($id) {
        try {
            // Get registration to verify it exists
            $registration = $this->getRegistrationById($id);
            
            if (!$registration) {
                error_log("RegistrationModel::undoCheckInVehicle - Registration not found: " . $id);
                return false;
            }
            
            // Check if not checked in
            if (isset($registration->checked_in) && !$registration->checked_in) {
                // Not checked in, consider this a success
                return true;
            }
            
            // Update check-in status
            $data = [
                'id' => $id,
                'checked_in' => 0,
                'check_in_time' => null
            ];
            
            // Update registration
            $result = $this->updateRegistration($id, $data);
            
            if ($result) {
                error_log("Vehicle check-in undone: Registration #" . $id);
            } else {
                error_log("Failed to undo vehicle check-in: Registration #" . $id);
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("RegistrationModel::undoCheckInVehicle - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get total registration count
     * 
     * @return int Total number of registrations
     */
    public function getRegistrationCount() {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM registrations');
            $result = $this->db->single();
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            error_log("Error in RegistrationModel::getRegistrationCount: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get recent registrations
     *
     * @param int $limit Number of registrations to return
     * @return array Array of recent registrations
     */
    public function getRecentRegistrations($limit = 5) {
        try {
            $this->db->query('SELECT r.*, s.name as show_name, u.name as user_name, v.make, v.model, v.year
                             FROM registrations r
                             LEFT JOIN shows s ON r.show_id = s.id
                             LEFT JOIN vehicles v ON r.vehicle_id = v.id
                             LEFT JOIN users u ON v.owner_id = u.id
                             ORDER BY r.created_at DESC
                             LIMIT :limit');
            $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in RegistrationModel::getRecentRegistrations: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get paginated registrations for a specific user
     *
     * @param int $userId User ID
     * @param int $page Page number (1-based)
     * @param int $perPage Records per page
     * @param string $search Search term
     * @param string $showFilter Show ID filter
     * @param string $statusFilter Status filter (upcoming, past, pending_payment)
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC/DESC)
     * @return array Paginated results with metadata
     */
    public function getPaginatedUserRegistrations($userId, $page = 1, $perPage = 20, $search = '', $showFilter = '', $statusFilter = 'all', $orderBy = 'start_date', $orderDir = 'DESC') {
        $startTime = microtime(true);

        try {
            // Validate parameters
            $page = max(1, (int)$page);
            $perPage = min(100, max(1, (int)$perPage));
            $offset = ($page - 1) * $perPage;

            // Validate order by field
            $allowedOrderFields = ['id', 'show_name', 'start_date', 'created_at', 'make', 'model', 'year'];
            if (!in_array($orderBy, $allowedOrderFields)) {
                $orderBy = 'start_date';
            }

            $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

            // Build WHERE conditions
            $whereConditions = ['v.owner_id = :user_id'];
            $bindParams = ['user_id' => $userId];

            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(s.name LIKE :search_show OR v.make LIKE :search_make OR v.model LIKE :search_model OR sc.name LIKE :search_category)';
                $bindParams['search_show'] = '%' . $search . '%';
                $bindParams['search_make'] = '%' . $search . '%';
                $bindParams['search_model'] = '%' . $search . '%';
                $bindParams['search_category'] = '%' . $search . '%';
            }

            // Show filter
            if (!empty($showFilter)) {
                $whereConditions[] = 'r.show_id = :show_filter';
                $bindParams['show_filter'] = $showFilter;
            }

            // Status filter
            if ($statusFilter !== 'all') {
                switch ($statusFilter) {
                    case 'upcoming':
                        $whereConditions[] = 's.start_date > NOW()';
                        break;
                    case 'past':
                        $whereConditions[] = 's.start_date <= NOW()';
                        break;
                    case 'pending_payment':
                        $whereConditions[] = 'r.payment_status = "pending"';
                        break;
                }
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            // Count total records
            $countQuery = "SELECT COUNT(*) as total
                          FROM registrations r
                          JOIN shows s ON r.show_id = s.id
                          JOIN vehicles v ON r.vehicle_id = v.id
                          JOIN show_categories sc ON r.category_id = sc.id
                          $whereClause";

            $this->db->query($countQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }

            $totalResult = $this->db->single();
            $totalRecords = $totalResult->total ?? 0;

            // Get paginated data
            $dataQuery = "SELECT r.*, s.name as show_name, s.start_date, s.end_date,
                         s.location as show_location,
                         v.make, v.model, v.year, v.color, v.owner_id,
                         u.name as owner_name, u.email as owner_email,
                         sc.name as category_name,
                         r.created_at as registration_date
                         FROM registrations r
                         JOIN shows s ON r.show_id = s.id
                         JOIN vehicles v ON r.vehicle_id = v.id
                         JOIN users u ON v.owner_id = u.id
                         JOIN show_categories sc ON r.category_id = sc.id
                         $whereClause
                         ORDER BY $orderBy $orderDir
                         LIMIT :limit OFFSET :offset";

            $this->db->query($dataQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
            $this->db->bind(':offset', $offset, PDO::PARAM_INT);

            $registrations = $this->db->resultSet();

            // Ensure all required fields are present
            if (is_array($registrations)) {
                foreach ($registrations as &$registration) {
                    $this->ensureRequiredFields($registration);
                }
            }

            // Calculate pagination metadata
            $totalPages = ceil($totalRecords / $perPage);
            $startRecord = $totalRecords > 0 ? $offset + 1 : 0;
            $endRecord = min($offset + $perPage, $totalRecords);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 1);

            error_log("RegistrationModel::getPaginatedUserRegistrations - Page: $page, Registrations: " . count($registrations) . ", Time: {$executionTime}ms");

            return [
                'registrations' => $registrations,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_registrations' => $totalRecords,
                    'start_record' => $startRecord,
                    'end_record' => $endRecord,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ]
            ];

        } catch (Exception $e) {
            error_log('Error in RegistrationModel::getPaginatedUserRegistrations: ' . $e->getMessage());
            return [
                'registrations' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $perPage,
                    'total_pages' => 0,
                    'total_registrations' => 0,
                    'start_record' => 0,
                    'end_record' => 0,
                    'has_prev' => false,
                    'has_next' => false
                ]
            ];
        }
    }

    /**
     * Get registration counts for a specific user
     *
     * @param int $userId User ID
     * @return array Registration counts by category
     */
    public function getUserRegistrationCounts($userId) {
        try {
            $counts = [
                'total' => 0,
                'upcoming' => 0,
                'past' => 0,
                'pending_payment' => 0
            ];

            // Get total count
            $this->db->query('SELECT COUNT(*) as total
                             FROM registrations r
                             JOIN vehicles v ON r.vehicle_id = v.id
                             WHERE v.owner_id = :user_id');
            $this->db->bind(':user_id', $userId);
            $totalResult = $this->db->single();
            $counts['total'] = $totalResult->total ?? 0;

            // Get upcoming count
            $this->db->query('SELECT COUNT(*) as upcoming
                             FROM registrations r
                             JOIN vehicles v ON r.vehicle_id = v.id
                             JOIN shows s ON r.show_id = s.id
                             WHERE v.owner_id = :user_id AND s.start_date > NOW()');
            $this->db->bind(':user_id', $userId);
            $upcomingResult = $this->db->single();
            $counts['upcoming'] = $upcomingResult->upcoming ?? 0;

            // Get past count
            $this->db->query('SELECT COUNT(*) as past
                             FROM registrations r
                             JOIN vehicles v ON r.vehicle_id = v.id
                             JOIN shows s ON r.show_id = s.id
                             WHERE v.owner_id = :user_id AND s.start_date <= NOW()');
            $this->db->bind(':user_id', $userId);
            $pastResult = $this->db->single();
            $counts['past'] = $pastResult->past ?? 0;

            // Get pending payment count
            $this->db->query('SELECT COUNT(*) as pending_payment
                             FROM registrations r
                             JOIN vehicles v ON r.vehicle_id = v.id
                             WHERE v.owner_id = :user_id AND r.payment_status = "pending"');
            $this->db->bind(':user_id', $userId);
            $pendingResult = $this->db->single();
            $counts['pending_payment'] = $pendingResult->pending_payment ?? 0;

            return $counts;

        } catch (Exception $e) {
            error_log('Error in RegistrationModel::getUserRegistrationCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'upcoming' => 0,
                'past' => 0,
                'pending_payment' => 0
            ];
        }
    }

    /**
     * Get admin registration counts for dashboard
     *
     * @return array Registration counts
     */
    public function getAdminRegistrationCounts() {
        try {
            $counts = [
                'total_registrations' => 0,
                'active_registrations' => 0,
                'pending_payment' => 0,
                'today_registrations' => 0
            ];

            // Get total registrations count
            $this->db->query('SELECT COUNT(*) as total FROM registrations');
            $totalResult = $this->db->single();
            $counts['total_registrations'] = $totalResult->total ?? 0;
            error_log('RegistrationModel::getAdminRegistrationCounts - Total registrations: ' . $counts['total_registrations']);

            // Get active registrations (upcoming shows)
            $this->db->query('SELECT COUNT(*) as active FROM registrations r
                             JOIN shows s ON r.show_id = s.id
                             WHERE s.start_date > NOW()');
            $activeResult = $this->db->single();
            $counts['active_registrations'] = $activeResult->active ?? 0;

            // Get pending payment registrations
            $this->db->query('SELECT COUNT(*) as pending FROM registrations
                             WHERE payment_status = "pending"');
            $pendingResult = $this->db->single();
            $counts['pending_payment'] = $pendingResult->pending ?? 0;

            // Get today's registrations
            $this->db->query('SELECT COUNT(*) as today FROM registrations
                             WHERE DATE(created_at) = CURDATE()');
            $todayResult = $this->db->single();
            $counts['today_registrations'] = $todayResult->today ?? 0;

            error_log('RegistrationModel::getAdminRegistrationCounts - Final counts: ' . json_encode($counts));
            return $counts;

        } catch (Exception $e) {
            error_log('Error in RegistrationModel::getAdminRegistrationCounts: ' . $e->getMessage());
            return [
                'total_registrations' => 0,
                'active_registrations' => 0,
                'pending_payment' => 0,
                'today_registrations' => 0
            ];
        }
    }

    /**
     * Get admin registration statistics for dashboard
     *
     * @return array Registration statistics
     */
    public function getAdminRegistrationStats() {
        try {
            $stats = [
                'total_revenue' => 0,
                'monthly_revenue' => 0,
                'avg_per_show' => 0,
                'this_week' => 0
            ];

            // Get total revenue from completed payments
            $this->db->query('SELECT SUM(p.amount) as total_revenue
                             FROM payments p
                             WHERE p.payment_type = "registration" AND p.payment_status = "completed"');
            $revenueResult = $this->db->single();
            $stats['total_revenue'] = $revenueResult->total_revenue ?? 0;

            // Get monthly revenue
            $this->db->query('SELECT SUM(p.amount) as monthly_revenue
                             FROM payments p
                             WHERE p.payment_type = "registration" AND p.payment_status = "completed"
                             AND MONTH(p.created_at) = MONTH(NOW())
                             AND YEAR(p.created_at) = YEAR(NOW())');
            $monthlyResult = $this->db->single();
            $stats['monthly_revenue'] = $monthlyResult->monthly_revenue ?? 0;

            // Get average registrations per show
            $this->db->query('SELECT AVG(reg_count) as avg_per_show
                             FROM (SELECT COUNT(*) as reg_count FROM registrations GROUP BY show_id) as show_counts');
            $avgResult = $this->db->single();
            $stats['avg_per_show'] = $avgResult->avg_per_show ?? 0;

            // Get this week's registrations
            $this->db->query('SELECT COUNT(*) as this_week FROM registrations
                             WHERE WEEK(created_at) = WEEK(NOW())
                             AND YEAR(created_at) = YEAR(NOW())');
            $weekResult = $this->db->single();
            $stats['this_week'] = $weekResult->this_week ?? 0;

            return $stats;

        } catch (Exception $e) {
            error_log('Error in RegistrationModel::getAdminRegistrationStats: ' . $e->getMessage());
            return [
                'total_revenue' => 0,
                'monthly_revenue' => 0,
                'avg_per_show' => 0,
                'this_week' => 0
            ];
        }
    }

    /**
     * Get paginated admin registrations
     *
     * @param int $page Page number (1-based)
     * @param int $perPage Records per page
     * @param string $search Search term
     * @param string $showFilter Show filter
     * @param string $statusFilter Status filter
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC/DESC)
     * @return array Paginated results with metadata
     */
    public function getPaginatedAdminRegistrations($page = 1, $perPage = 20, $search = '', $showFilter = '', $statusFilter = 'all', $orderBy = 'created_at', $orderDir = 'DESC') {
        $startTime = microtime(true);

        try {
            // Validate parameters
            $page = max(1, (int)$page);
            $perPage = min(100, max(1, (int)$perPage));
            $offset = ($page - 1) * $perPage;

            // Validate order by field
            $allowedOrderFields = ['id', 'created_at', 'user_name', 'show_name', 'payment_status'];
            if (!in_array($orderBy, $allowedOrderFields)) {
                $orderBy = 'created_at';
            }

            $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

            // Build WHERE conditions
            $whereConditions = ['1=1'];
            $bindParams = [];

            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(u.name LIKE :search_user OR s.name LIKE :search_show OR v.make LIKE :search_make OR v.model LIKE :search_model)';
                $bindParams['search_user'] = '%' . $search . '%';
                $bindParams['search_show'] = '%' . $search . '%';
                $bindParams['search_make'] = '%' . $search . '%';
                $bindParams['search_model'] = '%' . $search . '%';
            }

            // Show filter
            if (!empty($showFilter)) {
                $whereConditions[] = 'r.show_id = :show_filter';
                $bindParams['show_filter'] = $showFilter;
            }

            // Status filter
            if ($statusFilter !== 'all') {
                switch ($statusFilter) {
                    case 'active':
                        $whereConditions[] = 's.start_date > NOW()';
                        break;
                    case 'pending_payment':
                        $whereConditions[] = 'r.payment_status = "pending"';
                        break;
                    case 'completed':
                        $whereConditions[] = 'r.payment_status = "completed"';
                        break;
                    case 'today':
                        $whereConditions[] = 'DATE(r.created_at) = CURDATE()';
                        break;
                }
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            // Count total records
            $countQuery = "SELECT COUNT(*) as total
                          FROM registrations r
                          JOIN users u ON r.user_id = u.id
                          JOIN shows s ON r.show_id = s.id
                          JOIN vehicles v ON r.vehicle_id = v.id
                          $whereClause";

            $this->db->query($countQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }

            $totalResult = $this->db->single();
            $totalRecords = $totalResult->total ?? 0;

            // Get paginated data
            $dataQuery = "SELECT r.*,
                         u.name as user_name,
                         u.email as user_email,
                         s.name as show_name,
                         s.start_date as show_start_date,
                         v.year, v.make, v.model, v.color
                         FROM registrations r
                         JOIN users u ON r.user_id = u.id
                         JOIN shows s ON r.show_id = s.id
                         JOIN vehicles v ON r.vehicle_id = v.id
                         $whereClause
                         ORDER BY r.$orderBy $orderDir
                         LIMIT :limit OFFSET :offset";

            $this->db->query($dataQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
            $this->db->bind(':offset', $offset, PDO::PARAM_INT);

            $registrations = $this->db->resultSet();

            // Calculate pagination metadata
            $totalPages = ceil($totalRecords / $perPage);
            $startRecord = $totalRecords > 0 ? $offset + 1 : 0;
            $endRecord = min($offset + $perPage, $totalRecords);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 1);

            error_log("RegistrationModel::getPaginatedAdminRegistrations - Page: $page, Registrations: " . count($registrations) . ", Time: {$executionTime}ms");

            return [
                'registrations' => $registrations,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_registrations' => $totalRecords,
                    'start_record' => $startRecord,
                    'end_record' => $endRecord,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ]
            ];

        } catch (Exception $e) {
            error_log('Error in RegistrationModel::getPaginatedAdminRegistrations: ' . $e->getMessage());
            return [
                'registrations' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $perPage,
                    'total_pages' => 0,
                    'total_registrations' => 0,
                    'start_record' => 0,
                    'end_record' => 0,
                    'has_prev' => false,
                    'has_next' => false
                ]
            ];
        }
    }
}