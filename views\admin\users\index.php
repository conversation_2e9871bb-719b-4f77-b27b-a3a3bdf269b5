<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>User Management</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/roles" class="btn btn-info me-2">
                <i class="fas fa-user-tag me-2"></i> Manage Roles
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/addUser" class="btn btn-primary me-2">
                <i class="fas fa-user-plus me-2"></i> Add User
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <?php if (hasFlashMessage('user_success')) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_success')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (hasFlashMessage('user_error')) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_error')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Search and Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-search me-2"></i>Search & Filter Users
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo BASE_URL; ?>/admin/users" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Name/Email</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Enter name or email...">
                </div>
                <div class="col-md-2">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">All Roles</option>
                        <?php foreach ($available_roles as $roleValue => $roleLabel): ?>
                            <option value="<?php echo $roleValue; ?>"
                                    <?php echo $role_filter === $roleValue ? 'selected' : ''; ?>>
                                <?php echo $roleLabel; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <?php foreach ($available_statuses as $statusValue => $statusLabel): ?>
                            <option value="<?php echo $statusValue; ?>"
                                    <?php echo $status_filter === $statusValue ? 'selected' : ''; ?>>
                                <?php echo $statusLabel; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="per_page" class="form-label">Per Page</label>
                    <select class="form-select" id="per_page" name="per_page">
                        <option value="20" <?php echo $per_page == 20 ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo $per_page == 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $per_page == 100 ? 'selected' : ''; ?>>100</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                    </div>
                </div>
            </form>

            <?php if (!empty($search) || !empty($role_filter) || !empty($status_filter)): ?>
                <div class="mt-3">
                    <a href="<?php echo BASE_URL; ?>/admin/users" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="card-title mb-0">
                        Users
                        <span class="badge bg-primary ms-2">
                            <?php echo number_format($pagination['total_users']); ?> total
                        </span>
                    </h5>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        Showing <?php echo number_format($pagination['start_record']); ?>-<?php echo number_format($pagination['end_record']); ?>
                        of <?php echo number_format($pagination['total_users']); ?> users
                    </small>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="usersTable">
                    <thead>
                        <tr>
                            <th>
                                <a href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['order_by' => 'name', 'order_dir' => ($order_by === 'name' && $order_dir === 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                   class="text-decoration-none text-dark">
                                    Name
                                    <?php if ($order_by === 'name'): ?>
                                        <i class="fas fa-sort-<?php echo $order_dir === 'ASC' ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['order_by' => 'email', 'order_dir' => ($order_by === 'email' && $order_dir === 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                   class="text-decoration-none text-dark">
                                    Email
                                    <?php if ($order_by === 'email'): ?>
                                        <i class="fas fa-sort-<?php echo $order_dir === 'ASC' ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['order_by' => 'role', 'order_dir' => ($order_by === 'role' && $order_dir === 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                   class="text-decoration-none text-dark">
                                    Role
                                    <?php if ($order_by === 'role'): ?>
                                        <i class="fas fa-sort-<?php echo $order_dir === 'ASC' ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['order_by' => 'status', 'order_dir' => ($order_by === 'status' && $order_dir === 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                   class="text-decoration-none text-dark">
                                    Status
                                    <?php if ($order_by === 'status'): ?>
                                        <i class="fas fa-sort-<?php echo $order_dir === 'ASC' ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['order_by' => 'created_at', 'order_dir' => ($order_by === 'created_at' && $order_dir === 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                   class="text-decoration-none text-dark">
                                    Created
                                    <?php if ($order_by === 'created_at'): ?>
                                        <i class="fas fa-sort-<?php echo $order_dir === 'ASC' ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['order_by' => 'last_login', 'order_dir' => ($order_by === 'last_login' && $order_dir === 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                   class="text-decoration-none text-dark">
                                    Last Login
                                    <?php if ($order_by === 'last_login'): ?>
                                        <i class="fas fa-sort-<?php echo $order_dir === 'ASC' ? 'up' : 'down'; ?> ms-1"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user) : ?>
                            <tr>
                                <td>
                                    <?php 
                                    // Use our new helper function to display the profile image
                                    $imageUrl = getUserProfileImageUrl($user->id);
                                    if ($imageUrl) : 
                                    ?>
                                        <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="Profile" class="rounded-circle me-2" width="30" height="30">
                                    <?php else : ?>
                                        <i class="fas fa-user-circle me-2 text-secondary" style="font-size: 1.5rem;"></i>
                                    <?php endif; ?>
                                    <?php echo $user->name; ?>
                                </td>
                                <td><?php echo $user->email; ?></td>
                                <td>
                                    <?php if ($user->role == 'admin') : ?>
                                        <span class="badge bg-danger">Administrator</span>
                                    <?php elseif ($user->role == 'coordinator') : ?>
                                        <span class="badge bg-primary">Coordinator</span>
                                    <?php elseif ($user->role == 'judge') : ?>
                                        <span class="badge bg-success">Judge</span>
                                    <?php else : ?>
                                        <span class="badge bg-info">User</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user->status == 'active') : ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php elseif ($user->status == 'inactive') : ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php else : ?>
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo formatDateTimeForUser($user->created_at, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></td>
                                <td>
                                    <?php echo $user->last_login ? formatDateTimeForUser($user->last_login, $_SESSION['user_id'] ?? null, 'M j, Y') : 'Never'; ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo BASE_URL; ?>/admin/editUser/<?php echo $user->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin/assignRole/<?php echo $user->id; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-user-tag"></i> Role
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin/userRegistrations/<?php echo $user->id; ?>" class="btn btn-sm btn-success">
                                            <i class="fas fa-clipboard-list"></i> Registrations
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="modal" data-bs-target="#deleteModal" data-id="<?php echo $user->id; ?>" data-name="<?php echo $user->name; ?>">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($pagination['total_pages'] > 1): ?>
                <div class="card-footer">
                    <nav aria-label="User pagination">
                        <ul class="pagination justify-content-center mb-0">
                            <!-- Previous page link -->
                            <li class="page-item <?php echo !$pagination['has_prev'] ? 'disabled' : ''; ?>">
                                <a class="page-link" href="<?php echo $pagination['has_prev'] ? BASE_URL . '/admin/users?' . http_build_query(array_merge($_GET, ['page' => $current_page - 1])) : '#'; ?>">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>

                            <?php
                            // Calculate page range to show
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);

                            // Show first page if not in range
                            if ($start_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                                </li>
                                <?php if ($start_page > 2): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Page numbers -->
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <!-- Show last page if not in range -->
                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/admin/users?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>"><?php echo $total_pages; ?></a>
                                </li>
                            <?php endif; ?>

                            <!-- Next page link -->
                            <li class="page-item <?php echo !$pagination['has_next'] ? 'disabled' : ''; ?>">
                                <a class="page-link" href="<?php echo $pagination['has_next'] ? BASE_URL . '/admin/users?' . http_build_query(array_merge($_GET, ['page' => $current_page + 1])) : '#'; ?>">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the user <strong id="userName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" action="" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete modal
        const deleteModal = document.getElementById('deleteModal');
        if (deleteModal) {
            deleteModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-id');
                const userName = button.getAttribute('data-name');
                
                document.getElementById('userName').textContent = userName;
                document.getElementById('deleteForm').action = '<?php echo BASE_URL; ?>/admin/deleteUser/' + userId;
            });
        }

        // Auto-submit search form on per_page change
        document.getElementById('per_page').addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>