<?php
/**
 * User Controller
 * 
 * This controller handles all user-related functionality.
 */
class UserController extends Controller {
    private $userModel;
    private $vehicleModel;
    private $registrationModel;
    private $showModel;
    private $judgingModel;
    private $vehicleScoringModel;
    private $auth;
    private $db;
    
    /**
     * Generate CSRF token
     * 
     * @return string CSRF token
     */
    private function generateCsrfToken() {
        // Use the global function from csrf_helper.php
        return generateCsrfToken();
    }
    
    /**
     * Verify CSRF token
     * 
     * @param string $token Optional token to verify
     * @param string $source Source of the token (post, get, header)
     * @return bool
     */
    protected function verifyCsrfToken($token = null, $source = 'post') {
        // Use the global function from csrf_helper.php
        return verifyCsrfToken($token, $source);
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        $this->userModel = $this->model('UserModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->showModel = $this->model('ShowModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->vehicleScoringModel = $this->model('VehicleScoringModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('user/dashboard');
    }
    
    /**
     * User dashboard
     */
    public function dashboard() {
        // Get user's vehicles
        $userId = $this->auth->getCurrentUserId();
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Get user's registrations
        $registrations = $this->registrationModel->getUserRegistrations($userId);
        
        // Get upcoming shows
        $upcomingShows = $this->showModel->getUpcomingShows(5);
        
        // Ensure all variables are arrays
        if (!is_array($vehicles)) {
            $vehicles = [];
        }
        
        if (!is_array($registrations)) {
            $registrations = [];
        }
        
        if (!is_array($upcomingShows)) {
            $upcomingShows = [];
        }
        
        // Get user data
        $user = $this->userModel->getUserById($userId);
        
        $data = [
            'title' => 'Dashboard',
            'vehicles' => $vehicles,
            'registrations' => $registrations,
            'upcoming_shows' => $upcomingShows,
            'user' => $user
        ];
        
        $this->view('user/dashboard', $data);
    }
    
    /**
     * Profile page
     */
    public function profile() {
        // Get user
        $userId = $this->auth->getCurrentUserId();
        $user = $this->userModel->getUserById($userId);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $userId,
                'name' => trim($_POST['name']),
                'phone' => isset($_POST['phone']) ? trim($_POST['phone']) : null,
                'address' => isset($_POST['address']) ? trim($_POST['address']) : null,
                'city' => isset($_POST['city']) ? trim($_POST['city']) : null,
                'state' => isset($_POST['state']) ? trim($_POST['state']) : null,
                'zip' => isset($_POST['zip']) ? trim($_POST['zip']) : null,
                'timezone' => isset($_POST['timezone']) ? trim($_POST['timezone']) : 'America/New_York',
                'password' => trim($_POST['password']),
                'confirm_password' => trim($_POST['confirm_password']),
                'name_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'timezone_err' => '',
                'title' => 'Profile',
                'user' => $user
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter your name';
            }
            
            // Validate timezone using helper function
            if (!empty($data['timezone']) && !isValidUSATimezone($data['timezone'])) {
                $data['timezone_err'] = 'Please select a valid timezone';
            }
            
            // Validate password (only if provided)
            if (!empty($data['password'])) {
                if (strlen($data['password']) < 6) {
                    $data['password_err'] = 'Password must be at least 6 characters';
                }
                
                if (empty($data['confirm_password'])) {
                    $data['confirm_password_err'] = 'Please confirm password';
                } elseif ($data['password'] != $data['confirm_password']) {
                    $data['confirm_password_err'] = 'Passwords do not match';
                }
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['password_err']) && 
                empty($data['confirm_password_err']) && empty($data['timezone_err'])) {
                
                // Update profile
                if ($this->userModel->updateProfile($data)) {
                    // Redirect to dashboard
                    $this->redirect('user/dashboard');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('user/profile', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $userId,
                'name' => $user->name,
                'password' => '',
                'confirm_password' => '',
                'name_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'timezone_err' => '',
                'title' => 'Profile',
                'user' => $user,
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            // Load view
            $this->view('user/profile', $data);
        }
    }
    
    /**
     * Update profile image
     */
    public function updateProfileImage() {
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Check if file was uploaded
            if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                // Load image editor model
                $imageEditorModel = $this->model('ImageEditorModel');
                
                // Process image upload
                $uploadDir = 'uploads/users/';
                
                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                // First, delete any existing profile images for this user
                $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
                $this->db->bind(':entity_type', 'user');
                $this->db->bind(':entity_id', $userId);
                $existingImages = $this->db->resultSet();
                
                // Delete each existing image
                foreach ($existingImages as $image) {
                    $imageEditorModel->deleteImage($image->id);
                }
                
                // Process the image upload
                $imageData = $imageEditorModel->processImageUpload(
                    $_FILES['profile_image'], 
                    'user', 
                    $userId, 
                    $uploadDir,
                    $userId,
                    true // Set as primary image
                );
                
                if ($imageData) {
                    // Set success message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('success', 'Profile image updated successfully.');
                    }
                    
                    // Redirect back to profile page
                    $this->redirect('user/profile');
                } else {
                    // Set error message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('error', 'Failed to upload profile image. Please try again.');
                    }
                    
                    // Redirect back to profile page
                    $this->redirect('user/profile');
                }
            } else {
                // Set error message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('error', 'No file uploaded or file upload error occurred.');
                }
                
                // Redirect back to profile page
                $this->redirect('user/profile');
            }
        } else {
            // Redirect to profile page if not a POST request
            $this->redirect('user/profile');
        }
    }
    

    
    /**
     * Delete profile image
     */
    public function deleteProfileImage() {
        // Check if DEBUG_MODE is enabled
        $debugMode = defined('DEBUG_MODE') && DEBUG_MODE === true;
        
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        if ($debugMode) {
            error_log("Deleting profile image for user ID: $userId");
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Load image editor model
            $imageEditorModel = $this->model('ImageEditorModel');
            
            // Get primary image for this user
            $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
            $this->db->bind(':entity_type', 'user');
            $this->db->bind(':entity_id', $userId);
            $image = $this->db->single();
            
            $imageDeleted = false;
            
            if ($image) {
                // Delete the image using the ImageEditorModel
                if ($imageEditorModel->deleteImage($image->id)) {
                    $imageDeleted = true;
                    
                    if ($debugMode) {
                        error_log("Successfully deleted image ID: {$image->id}");
                    }
                    
                    // Set success message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('success', 'Profile image deleted successfully.');
                    }
                } else {
                    // Set error message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('error', 'Failed to delete profile image.');
                    }
                    
                    if ($debugMode) {
                        error_log("Failed to delete image ID: {$image->id}");
                    }
                }
            } else {
                // No image found in the images table, but we'll still clear Facebook cache
                $imageDeleted = true;
                
                if ($debugMode) {
                    error_log("No image found in images table, proceeding with Facebook cache cleanup");
                }
            }
            
            if ($imageDeleted) {
                // Get user's Facebook ID
                $this->db->query('SELECT facebook_id FROM users WHERE id = :id');
                $this->db->bind(':id', $userId);
                $user = $this->db->single();
                
                if ($user && !empty($user->facebook_id)) {
                    // Clear the Facebook download cache for this user
                    $cacheFile = APPROOT . '/uploads/temp/fb_download_' . $userId . '.txt';
                    if (file_exists($cacheFile)) {
                        if (unlink($cacheFile)) {
                            if ($debugMode) {
                                error_log("Deleted Facebook download cache file: $cacheFile");
                            }
                        } else {
                            if ($debugMode) {
                                error_log("Failed to delete Facebook download cache file: $cacheFile");
                            }
                        }
                    }
                    
                    if ($debugMode) {
                        error_log("User has Facebook ID: {$user->facebook_id}, cache cleared");
                    }
                }
                
                // Update user's profile_image to use the default image
                $this->db->query('UPDATE users SET profile_image = :profile_image WHERE id = :id');
                $this->db->bind(':profile_image', 'public/images/profile.png');
                $this->db->bind(':id', $userId);
                
                if ($this->db->execute()) {
                    if ($debugMode) {
                        error_log("Updated user's profile_image to default image");
                    }
                } else {
                    if ($debugMode) {
                        error_log("Failed to update user's profile_image");
                    }
                }
                
                // Clear the profile image cache in the helper
                global $profileImageCache;
                if (isset($profileImageCache[$userId])) {
                    unset($profileImageCache[$userId]);
                    
                    if ($debugMode) {
                        error_log("Cleared profile image cache for user ID: $userId");
                    }
                }
            } else if ($debugMode) {
                error_log("No image was deleted, skipping Facebook cache cleanup");
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
        } else {
            // Redirect to profile page if not a POST request
            $this->redirect('user/profile');
        }
    }
    
    /**
     * Synchronize profile image with Facebook
     */
    public function syncFacebookImage() {
        // Always enable debugging for this method
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
        
        // Force error logging to file
        error_log('======= FACEBOOK SYNC STARTED ======= ' . gmdate('Y-m-d H:i:s'));
        
        error_log('UserController::syncFacebookImage - Method called');
        
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            error_log('UserController::syncFacebookImage - User not logged in');
            $this->redirect('auth/login');
            return;
        }
        
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        error_log('UserController::syncFacebookImage - User ID: ' . $userId);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            error_log('UserController::syncFacebookImage - POST request received');
            
            // Debug CSRF token
            if (isset($_POST[CSRF_TOKEN_NAME])) {
                error_log('UserController::syncFacebookImage - CSRF token in request: ' . $_POST[CSRF_TOKEN_NAME]);
            } else {
                error_log('UserController::syncFacebookImage - No CSRF token in request');
            }
            
            if (isset($_SESSION[CSRF_TOKEN_NAME])) {
                error_log('UserController::syncFacebookImage - CSRF token in session: ' . $_SESSION[CSRF_TOKEN_NAME]);
            } else {
                error_log('UserController::syncFacebookImage - No CSRF token in session');
            }
            
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                error_log('UserController::syncFacebookImage - Invalid CSRF token');
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            error_log('UserController::syncFacebookImage - CSRF token verified');
            
            // Set a flag in the session to indicate we're in a sync operation
            $_SESSION['facebook_sync_in_progress'] = $userId;
            error_log('UserController::syncFacebookImage - Set facebook_sync_in_progress flag for user ' . $userId);
            
            // Immediately clear the profile_image field in the users table
            // This is critical to ensure we're not using the default image flag
            $this->db->query('UPDATE users SET profile_image = NULL WHERE id = :id');
            $this->db->bind(':id', $userId);
            $this->db->execute();
            error_log('UserController::syncFacebookImage - Immediately cleared profile_image field in users table for user ID: ' . $userId);
            
            // Get user data
            $user = $this->userModel->getUserById($userId);
            
            if (!$user) {
                error_log('UserController::syncFacebookImage - User not found: ' . $userId);
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('error', 'User not found.');
                }
                $this->redirect('user/profile');
                return;
            }
            
            error_log('UserController::syncFacebookImage - User found: ' . $user->name . ' (ID: ' . $userId . ')');
            
            // Check if user has a Facebook ID
            if (empty($user->facebook_id)) {
                error_log('UserController::syncFacebookImage - User does not have a Facebook ID: ' . $userId);
                // Set error message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('error', 'Your account is not linked with Facebook.');
                }
                
                // Redirect back to profile page
                $this->redirect('user/profile');
                return;
            }
            
            error_log('UserController::syncFacebookImage - User has Facebook ID: ' . $user->facebook_id);
            
            // Load Facebook service
            require_once APPROOT . '/libraries/facebook/FacebookService.php';
            $fbService = new FacebookService();
            
            // Check if Facebook SDK is available
            if (!$fbService->isSdkAvailable()) {
                error_log('UserController::syncFacebookImage - Facebook SDK not available');
                // Set error message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('error', 'Facebook integration is not properly configured.');
                }
                
                // Redirect back to profile page
                $this->redirect('user/profile');
                return;
            }
            
            error_log('UserController::syncFacebookImage - Facebook SDK is available');
            
            // Get user's Facebook access token
            $this->db->query('SELECT facebook_token FROM users WHERE id = :id');
            $this->db->bind(':id', $userId);
            $tokenResult = $this->db->single();
            
            $accessToken = $tokenResult && isset($tokenResult->facebook_token) ? $tokenResult->facebook_token : null;
            
            error_log('UserController::syncFacebookImage - Facebook token for user ' . $userId . ': ' . ($accessToken ? 'Found' : 'Not found'));
            
            // If no token is stored, we need to use the Facebook API to get a new one
            if (!$accessToken) {
                // Set error message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('warning', 'Please log in with Facebook again to update your profile image.');
                }
                
                // Redirect back to profile page
                $this->redirect('user/profile');
                return;
            }
            
            try {
                // Use the Facebook ID directly to get the profile image
                $fbId = $user->facebook_id;
                error_log('UserController::syncFacebookImage - Getting profile picture for Facebook ID: ' . $fbId);
                
                // Construct the Facebook Graph API URL for the profile picture
                $imageUrl = "https://graph.facebook.com/{$fbId}/picture?type=large&width=500&height=500";
                error_log('UserController::syncFacebookImage - Image URL: ' . $imageUrl);
                
                // Load image editor model for storing the image
                require_once APPROOT . '/models/ImageEditorModel.php';
                $imageEditorModel = new ImageEditorModel();
                
                // First, clear the profile_image field in the users table
                $this->db->query('UPDATE users SET profile_image = NULL WHERE id = :id');
                $this->db->bind(':id', $userId);
                $this->db->execute();
                error_log('UserController::syncFacebookImage - Cleared profile_image field in users table for user ID: ' . $userId);
                
                // Second, clear the profile image cache in the helper if it exists
                if (isset($GLOBALS['profileImageCache']) && isset($GLOBALS['profileImageCache'][$userId])) {
                    unset($GLOBALS['profileImageCache'][$userId]);
                    error_log('UserController::syncFacebookImage - Cleared profile image cache for user ID: ' . $userId);
                }
                
                // Third, delete any existing profile images from the images table
                $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
                $this->db->bind(':entity_type', 'user');
                $this->db->bind(':entity_id', $userId);
                $existingImages = $this->db->resultSet();
                
                // Delete each existing image
                foreach ($existingImages as $image) {
                    $imageEditorModel->deleteImage($image->id);
                    error_log('UserController::syncFacebookImage - Deleted existing image: ' . $image->id);
                }
                
                // Fourth, clear the Facebook download cache for this user to force a new download
                $cacheFile = APPROOT . '/uploads/temp/fb_download_' . $userId . '.txt';
                if (file_exists($cacheFile)) {
                    if (unlink($cacheFile)) {
                        error_log('UserController::syncFacebookImage - Deleted Facebook download cache file: ' . $cacheFile);
                    } else {
                        error_log('UserController::syncFacebookImage - Failed to delete Facebook download cache file: ' . $cacheFile);
                    }
                }
                
                // Clear the profile image cache in the helper if it exists
                if (isset($GLOBALS['profileImageCache']) && isset($GLOBALS['profileImageCache'][$userId])) {
                    unset($GLOBALS['profileImageCache'][$userId]);
                    error_log('UserController::syncFacebookImage - Cleared profile image cache for user ID: ' . $userId);
                }
                
                // Download the image
                error_log('UserController::syncFacebookImage - Downloading image from: ' . $imageUrl);
                $imageContent = $this->downloadImage($imageUrl);
                
                if (!$imageContent) {
                    error_log('UserController::syncFacebookImage - Failed to download image');
                    throw new Exception('Failed to download profile image from Facebook');
                }
                
                // Check if the image content is valid
                error_log('UserController::syncFacebookImage - Image content length: ' . strlen($imageContent) . ' bytes');
                
                // Save the image content to a temporary file for debugging
                $tempDebugFile = APPROOT . '/temp_facebook_image_' . time() . '.jpg';
                file_put_contents($tempDebugFile, $imageContent);
                error_log('UserController::syncFacebookImage - Saved debug image to: ' . $tempDebugFile);
                
                // Verify the image content is valid
                $tempImageCheck = @imagecreatefromstring($imageContent);
                if ($tempImageCheck === false) {
                    error_log('UserController::syncFacebookImage - Downloaded content is not a valid image');
                    throw new Exception('Downloaded content is not a valid image');
                }
                imagedestroy($tempImageCheck);
                
                // Process the downloaded image directly
                // Use absolute path for upload directory
                $uploadDir = APPROOT . '/uploads/users/';
                error_log('UserController::syncFacebookImage - Upload directory: ' . $uploadDir);
                error_log('UserController::syncFacebookImage - APPROOT: ' . APPROOT);
                
                // Make sure the upload directory exists
                if (!file_exists($uploadDir)) {
                    error_log('UserController::syncFacebookImage - Creating upload directory: ' . $uploadDir);
                    if (!mkdir($uploadDir, 0755, true)) {
                        error_log('UserController::syncFacebookImage - Failed to create upload directory: ' . $uploadDir);
                        throw new Exception('Failed to create upload directory: ' . $uploadDir);
                    }
                }
                
                // Check if the directory is writable
                if (!is_writable($uploadDir)) {
                    error_log('UserController::syncFacebookImage - Upload directory is not writable: ' . $uploadDir);
                    throw new Exception('Upload directory is not writable: ' . $uploadDir);
                }
                
                // Process the image using our new method for downloaded images
                $result = $imageEditorModel->processDownloadedImage(
                    $imageContent,
                    'facebook_profile.jpg',
                    'user', 
                    $userId, 
                    $uploadDir,
                    $userId,
                    true // Set as primary image
                );
                
                error_log('UserController::syncFacebookImage - processDownloadedImage result: ' . ($result ? json_encode($result) : 'false'));
                
                if ($result) {
                    error_log('UserController::syncFacebookImage - Successfully synchronized profile image with Facebook');
                    // Set success message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('success', 'Profile image synchronized with Facebook successfully.');
                    }
                } else {
                    error_log('UserController::syncFacebookImage - Failed to synchronize profile image with Facebook');
                    throw new Exception('Failed to synchronize profile image with Facebook');
                }
                
            } catch (Exception $e) {
                // Log the error
                error_log('Error synchronizing Facebook profile image: ' . $e->getMessage());
                error_log('Error trace: ' . $e->getTraceAsString());
                
                // Clear the sync flag
                if (isset($_SESSION['facebook_sync_in_progress'])) {
                    unset($_SESSION['facebook_sync_in_progress']);
                    error_log('UserController::syncFacebookImage - Cleared facebook_sync_in_progress flag due to error');
                }
                
                // Set error message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('error', 'Failed to synchronize profile image with Facebook: ' . $e->getMessage());
                }
            }
            
            // Clear the sync flag if it's still set
            if (isset($_SESSION['facebook_sync_in_progress'])) {
                unset($_SESSION['facebook_sync_in_progress']);
                error_log('UserController::syncFacebookImage - Cleared facebook_sync_in_progress flag at end of method');
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
        } else {
            // Redirect to profile page if not a POST request
            error_log('UserController::syncFacebookImage - Not a POST request');
            
            // Clear the sync flag if it's somehow set
            if (isset($_SESSION['facebook_sync_in_progress'])) {
                unset($_SESSION['facebook_sync_in_progress']);
                error_log('UserController::syncFacebookImage - Cleared facebook_sync_in_progress flag (not a POST request)');
            }
            
            $this->redirect('user/profile');
        }
    }
    
    /**
     * Download an image from a URL
     * 
     * @param string $url Image URL
     * @return string|false Image content or false on failure
     */
    private function downloadImage($url) {
        error_log('UserController::downloadImage - Starting download from URL: ' . $url);
        
        // Set a user agent to avoid potential blocking
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept: image/jpeg,image/png,image/*,*/*'
                ]
            ]
        ]);
        
        // Try to download with file_get_contents
        error_log('UserController::downloadImage - Attempting download with file_get_contents');
        $imageContent = @file_get_contents($url, false, $context);
        
        // Log any PHP errors
        $error = error_get_last();
        if ($error) {
            error_log('UserController::downloadImage - PHP Error: ' . json_encode($error));
        }
        
        // If file_get_contents fails, try with cURL
        if (!$imageContent) {
            error_log('UserController::downloadImage - Failed to download image with file_get_contents, trying cURL');
            
            if (!function_exists('curl_init')) {
                error_log('UserController::downloadImage - cURL is not available on this server');
                
                // Try one more time with file_get_contents but with different options
                $opts = [
                    'http' => [
                        'method' => 'GET',
                        'header' => [
                            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept: image/jpeg,image/png,image/*,*/*'
                        ],
                        'ignore_errors' => true,
                        'timeout' => 30
                    ],
                    'ssl' => [
                        'verify_peer' => false,
                        'verify_peer_name' => false
                    ]
                ];
                
                $context = stream_context_create($opts);
                $imageContent = @file_get_contents($url, false, $context);
                
                if (!$imageContent) {
                    error_log('UserController::downloadImage - Failed to download image with file_get_contents (second attempt)');
                    return false;
                }
                
                error_log('UserController::downloadImage - Successfully downloaded image with file_get_contents (second attempt)');
            } else {
                // Use cURL
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Disable SSL verification for compatibility
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // Disable SSL host verification for compatibility
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: image/jpeg,image/png,image/*,*/*']);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects
                
                error_log('UserController::downloadImage - Executing cURL request to: ' . $url);
                $imageContent = curl_exec($ch);
                
                if (curl_errno($ch)) {
                    error_log('UserController::downloadImage - cURL error: ' . curl_error($ch));
                    curl_close($ch);
                    return false;
                }
                
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode != 200 || empty($imageContent)) {
                    error_log('UserController::downloadImage - HTTP error: ' . $httpCode);
                    return false;
                }
                
                error_log('UserController::downloadImage - Successfully downloaded image with cURL');
            }
        }
        
        // Verify the image content is valid
        if (strlen($imageContent) < 100) {
            error_log('UserController::downloadImage - Downloaded image is too small: ' . strlen($imageContent) . ' bytes');
            return false;
        }
        
        // Check if the content is a valid image
        $tempImageCheck = @imagecreatefromstring($imageContent);
        if ($tempImageCheck === false) {
            error_log('UserController::downloadImage - Downloaded content is not a valid image');
            return false;
        }
        imagedestroy($tempImageCheck);
        
        return $imageContent;
    }
    
    /**
     * Convert an image to a data URI
     * 
     * @param string $imageContent The image content
     * @param string $contentType The content type (default: image/jpeg)
     * @return string The data URI
     */
    private function imageToDataURI($imageContent, $contentType = 'image/jpeg') {
        return 'data:' . $contentType . ';base64,' . base64_encode($imageContent);
    }
    
    /**
     * Get Facebook profile image as data URI
     * 
     * This method downloads a Facebook profile image and returns it as a data URI
     * that can be directly embedded in HTML.
     * 
     * @param string $facebookId The Facebook ID
     * @return string|null The data URI or null on failure
     */
    public function getFacebookProfileImageAsDataURI($facebookId) {
        if (empty($facebookId)) {
            return null;
        }
        
        // Construct the Facebook Graph API URL for the profile picture
        $imageUrl = "https://graph.facebook.com/{$facebookId}/picture?type=large&width=500&height=500";
        
        // Download the image
        $imageContent = $this->downloadImage($imageUrl);
        
        if (!$imageContent) {
            return null;
        }
        
        // Convert to data URI
        return $this->imageToDataURI($imageContent);
    }
    
    /**
     * Get user's profile image
     * 
     * @param int $userId User ID
     * @return object|null Image object or null if not found
     */
    private function getUserProfileImage($userId) {
        // Query the images table for the user's primary image
        $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
        $this->db->bind(':entity_type', 'user');
        $this->db->bind(':entity_id', $userId);
        return $this->db->single();
    }
    
    /**
     * Manage vehicles
     * 
     * @param string $action Optional action to perform
     */
    public function vehicles($action = null) {
        // Handle the deleteImage action
        if ($action === 'deleteImage') {
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['image_id'])) {
                $imageId = $_POST['image_id'];
                $vehicleId = $_POST['vehicle_id'] ?? null;
                
                error_log('Attempting to delete image ID: ' . $imageId . ' from the images table');
                
                // Validate CSRF token
                if (!$this->verifyCsrfToken()) {
                    error_log('CSRF token verification failed');
                    $this->redirect('home/error/Invalid%20request');
                    return;
                }
                
                // Get the image from the images table
                $imageEditorModel = $this->model('ImageEditorModel');
                $image = $imageEditorModel->getImageById($imageId);
                
                if (!$image) {
                    error_log('Image not found in images table with ID: ' . $imageId);
                    if ($vehicleId) {
                        $this->redirect('user/vehicleImages/' . $vehicleId);
                    } else {
                        $this->redirect('user/vehicles');
                    }
                    return;
                }
                
                // Check if user owns this image
                $userId = $this->auth->getCurrentUserId();
                if ($image->user_id != $userId && !$this->auth->hasRole('admin')) {
                    error_log('User does not own this image');
                    $this->redirect('home/access_denied');
                    return;
                }
                
                // Delete the image
                if ($imageEditorModel->deleteImage($imageId)) {
                    error_log('Successfully deleted image ID: ' . $imageId . ' from images table');
                    
                    // Redirect to the vehicle images page
                    if ($vehicleId) {
                        $this->redirect('user/vehicleImages/' . $vehicleId);
                    } else {
                        $this->redirect('user/vehicles');
                    }
                } else {
                    error_log('Failed to delete image ID: ' . $imageId . ' from images table');
                    $this->redirect('home/error/Failed%20to%20delete%20image');
                }
            } else {
                $this->redirect('user/vehicles');
            }
            return;
        }
        
        // Default behavior - show vehicles list
        $userId = $this->auth->getCurrentUserId();
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Get images for each vehicle from the unified image system
        $imageEditorModel = $this->model('ImageEditorModel');
        
        foreach ($vehicles as &$vehicle) {
            // Get images from the unified image system
            $images = $imageEditorModel->getImagesByEntity('vehicle', $vehicle->id);
            $vehicle->images = $images;
            $vehicle->new_images = $images; // For backward compatibility
        }
        
        $data = [
            'title' => 'My Vehicles',
            'vehicles' => $vehicles,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('user/vehicles/index', $data);
    }
    
    /**
     * Add vehicle
     */
    public function addVehicle() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'owner_id' => $this->auth->getCurrentUserId(),
                'make' => trim($_POST['make']),
                'model' => trim($_POST['model']),
                'year' => intval($_POST['year']),
                'color' => trim($_POST['color']),
                'license_plate' => trim($_POST['license_plate']),
                'vin' => trim($_POST['vin']),
                'description' => trim($_POST['description']),
                'make_err' => '',
                'model_err' => '',
                'year_err' => '',
                'title' => 'Add Vehicle'
            ];
            
            // Validate make
            if (empty($data['make'])) {
                $data['make_err'] = 'Please enter the make';
            }
            
            // Validate model
            if (empty($data['model'])) {
                $data['model_err'] = 'Please enter the model';
            }
            
            // Validate year
            if (empty($data['year'])) {
                $data['year_err'] = 'Please enter the year';
            } elseif ($data['year'] < 1900 || $data['year'] > gmdate('Y') + 1) {
                $data['year_err'] = 'Year must be between 1900 and ' . (gmdate('Y') + 1);
            }
            
            // Check for errors
            if (empty($data['make_err']) && empty($data['model_err']) && 
                empty($data['year_err'])) {
                
                // Create vehicle
                $vehicleId = $this->vehicleModel->createVehicle($data);
                
                if ($vehicleId) {
                    // Redirect to vehicles page
                    $this->redirect('user/vehicles');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('user/vehicles/add', $data);
            }
        } else {
            // Init data
            $data = [
                'make' => '',
                'model' => '',
                'year' => gmdate('Y'),
                'color' => '',
                'license_plate' => '',
                'vin' => '',
                'description' => '',
                'make_err' => '',
                'model_err' => '',
                'year_err' => '',
                'title' => 'Add Vehicle'
            ];
            
            // Load view
            $this->view('user/vehicles/add', $data);
        }
    }
    
    /**
     * Edit vehicle
     * 
     * @param int $id Vehicle ID
     */
    public function editVehicle($id) {
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($id);
        
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this vehicle
        $userId = $this->auth->getCurrentUserId();
        if ($vehicle->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'make' => trim($_POST['make']),
                'model' => trim($_POST['model']),
                'year' => intval($_POST['year']),
                'color' => trim($_POST['color']),
                'license_plate' => trim($_POST['license_plate']),
                'vin' => trim($_POST['vin']),
                'description' => trim($_POST['description']),
                'make_err' => '',
                'model_err' => '',
                'year_err' => '',
                'title' => 'Edit Vehicle',
                'vehicle' => $vehicle
            ];
            
            // Validate make
            if (empty($data['make'])) {
                $data['make_err'] = 'Please enter the make';
            }
            
            // Validate model
            if (empty($data['model'])) {
                $data['model_err'] = 'Please enter the model';
            }
            
            // Validate year
            if (empty($data['year'])) {
                $data['year_err'] = 'Please enter the year';
            } elseif ($data['year'] < 1900 || $data['year'] > gmdate('Y') + 1) {
                $data['year_err'] = 'Year must be between 1900 and ' . (gmdate('Y') + 1);
            }
            
            // Check for errors
            if (empty($data['make_err']) && empty($data['model_err']) && 
                empty($data['year_err'])) {
                
                // Update vehicle
                if ($this->vehicleModel->updateVehicle($data)) {
                    // Redirect to vehicles page
                    $this->redirect('user/vehicles');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('user/vehicles/edit', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $vehicle->id,
                'make' => $vehicle->make,
                'model' => $vehicle->model,
                'year' => $vehicle->year,
                'color' => $vehicle->color,
                'license_plate' => $vehicle->license_plate,
                'vin' => $vehicle->vin,
                'description' => $vehicle->description,
                'make_err' => '',
                'model_err' => '',
                'year_err' => '',
                'title' => 'Edit Vehicle',
                'vehicle' => $vehicle
            ];
            
            // Load view
            $this->view('user/vehicles/edit', $data);
        }
    }
    
    /**
     * Delete vehicle
     * 
     * @param int $id Vehicle ID
     */
    public function deleteVehicle($id) {
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($id);
        
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this vehicle
        $userId = $this->auth->getCurrentUserId();
        if ($vehicle->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete vehicle
            if ($this->vehicleModel->deleteVehicle($id)) {
                // Redirect to vehicles page
                $this->redirect('user/vehicles');
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('user/vehicles');
        }
    }
    
    /**
     * Manage vehicle images
     * 
     * @param int $id Vehicle ID
     */
    public function vehicleImages($id) {
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($id);
        
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this vehicle
        $userId = $this->auth->getCurrentUserId();
        if ($vehicle->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get all vehicle images from the unified images table
        // Note: We no longer need to combine images from multiple tables as all images are now in the images table
        $imageEditorModel = $this->model('ImageEditorModel');
        $allImages = $imageEditorModel->getImagesByEntity('vehicle', $id);
        
        // Add source identifier for compatibility with existing code
        foreach ($allImages as $image) {
            $image->source = 'new';
        }
        
        // Find primary image
        $primaryImage = null;
        foreach ($allImages as $image) {
            if (isset($image->is_primary) && $image->is_primary) {
                $primaryImage = $image;
                break;
            }
        }
        
        // If no primary image is set, use the first image
        if (!$primaryImage && count($allImages) > 0) {
            $primaryImage = $allImages[0];
            $primaryImage->is_primary = 1;
        }
        
        $data = [
            'title' => 'Vehicle Images',
            'vehicle' => $vehicle,
            'images' => $allImages,
            'primary_image' => $primaryImage,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('user/vehicles/images', $data);
    }
    
    /**
     * Redirect to image editor for vehicle
     * 
     * @param int $id Vehicle ID
     */
    public function editVehicleImages($id) {
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($id);
        
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this vehicle
        $userId = $this->auth->getCurrentUserId();
        if ($vehicle->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Redirect to image editor
        $this->redirect('image_editor/vehicle/' . $id);
    }
    
    /**
     * Upload vehicle image
     * 
     * @param int $id Vehicle ID
     */
    public function uploadImage($id) {
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($id);
        
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this vehicle
        $userId = $this->auth->getCurrentUserId();
        if ($vehicle->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Check if file was uploaded
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                // Get file info
                $fileName = $_FILES['image']['name'];
                $fileTmpName = $_FILES['image']['tmp_name'];
                $fileSize = $_FILES['image']['size'];
                $fileError = $_FILES['image']['error'];
                
                // Get file extension
                $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                
                // Check if file is an image
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
                
                if (in_array($fileExt, $allowedExtensions)) {
                    // Check file size
                    if ($fileSize <= MAX_FILE_SIZE) {
                        // Generate unique file name
                        $newFileName = uniqid('vehicle_' . $id . '_') . '.' . $fileExt;
                        $uploadDir = 'uploads/vehicles/';
                        
                        // Create upload directory if it doesn't exist
                        if (!file_exists($uploadDir)) {
                            mkdir($uploadDir, 0777, true);
                        }
                        
                        $uploadPath = $uploadDir . $newFileName;
                        
                        // Upload file
                        if (move_uploaded_file($fileTmpName, $uploadPath)) {
                            // Check if this is the first image
                            $isPrimary = count($this->vehicleModel->getVehicleImages($id)) == 0;
                            
                            // Add image to database
                            $imageData = [
                                'vehicle_id' => $id,
                                'image_path' => $uploadPath,
                                'is_primary' => $isPrimary
                            ];
                            
                            if ($this->vehicleModel->addVehicleImage($imageData)) {
                                // Redirect to images page
                                $this->redirect('user/vehicle_images/' . $id);
                            } else {
                                // Delete uploaded file
                                unlink($uploadPath);
                                $this->redirect('home/error/Failed%20to%20save%20image');
                            }
                        } else {
                            $this->redirect('home/error/Failed%20to%20upload%20image');
                        }
                    } else {
                        $this->redirect('home/error/File%20size%20too%20large');
                    }
                } else {
                    $this->redirect('home/error/Invalid%20file%20type');
                }
            } else {
                $this->redirect('home/error/No%20file%20uploaded');
            }
        } else {
            $this->redirect('user/vehicle_images/' . $id);
        }
    }
    
    /**
     * Set primary image
     * 
     * @param int $id Image ID
     */
    public function setPrimaryImage($id) {
        // Get image
        $this->db->query('SELECT * FROM vehicle_images WHERE id = :id');
        $this->db->bind(':id', $id);
        $image = $this->db->single();
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($image->vehicle_id);
        
        // Check if user owns this vehicle
        $userId = $this->auth->getCurrentUserId();
        if ($vehicle->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Set as primary
            if ($this->vehicleModel->setPrimaryImage($id)) {
                // Redirect to images page
                $this->redirect('user/vehicle_images/' . $image->vehicle_id);
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('user/vehicle_images/' . $image->vehicle_id);
        }
    }
    
    /**
     * Delete image
     * 
     * @param int $id Image ID
     */
    public function deleteImage($id = null) {
        error_log('deleteImage method called with ID: ' . ($id ? $id : 'null'));
        
        // Check if form was submitted with image_id
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['image_id'])) {
            // Use the image_id from POST data
            $id = $_POST['image_id'];
            error_log('Using image_id from POST: ' . $id);
            
            // Debug CSRF token
            if (isset($_POST[CSRF_TOKEN_NAME])) {
                error_log('CSRF token found in POST data: ' . substr($_POST[CSRF_TOKEN_NAME], 0, 10) . '...');
            } else {
                error_log('CSRF token NOT found in POST data');
            }
            
            if (isset($_SESSION[CSRF_TOKEN_NAME])) {
                error_log('CSRF token found in SESSION: ' . substr($_SESSION[CSRF_TOKEN_NAME], 0, 10) . '...');
            } else {
                error_log('CSRF token NOT found in SESSION');
            }
        }
        
        // Ensure we have an ID
        if (!$id) {
            error_log('No image ID provided');
            $this->redirect('home/error/Missing%20image%20ID');
            return;
        }
        
        // Get image
        $this->db->query('SELECT * FROM vehicle_images WHERE id = :id');
        $this->db->bind(':id', $id);
        $image = $this->db->single();
        
        if (!$image) {
            error_log('Image not found with ID: ' . $id);
            
            // Try to get the vehicle ID from the POST data
            if (isset($_POST['vehicle_id'])) {
                $vehicleId = $_POST['vehicle_id'];
                error_log('Redirecting to vehicle images using POST vehicle_id: ' . $vehicleId);
                $this->redirect('user/vehicleImages/' . $vehicleId);
            } else {
                $this->redirect('home/not_found');
            }
            return;
        }
        
        error_log('Found image with ID ' . $id . ': ' . print_r($image, true));
        
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($image->vehicle_id);
        
        if (!$vehicle) {
            error_log('Vehicle not found for image ID: ' . $id);
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this vehicle
        $userId = $this->auth->getCurrentUserId();
        if ($vehicle->owner_id != $userId && !$this->auth->hasRole('admin')) {
            error_log('User ' . $userId . ' does not own vehicle ' . $vehicle->id);
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            error_log('Verifying CSRF token in deleteImage method...');
            if (!$this->verifyCsrfToken()) {
                error_log('CSRF token verification failed in deleteImage method');
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            error_log('CSRF token verification passed in deleteImage method');
            
            // Delete image
            if ($this->vehicleModel->deleteVehicleImage($id)) {
                error_log('Successfully deleted image from database: ' . $id);
                
                // Delete original file
                $imagePath = $image->image_path;
                // Add leading slash if not present
                if (substr($imagePath, 0, 1) !== '/' && substr($imagePath, 1, 1) !== ':') {
                    $imagePath = '/' . $imagePath;
                }
                
                error_log('Attempting to delete file: ' . $imagePath);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                    error_log('Deleted file: ' . $imagePath);
                } else {
                    error_log('File not found: ' . $imagePath);
                }
                
                // Delete thumbnail if it exists
                // Determine thumbnail path based on original image path
                $pathInfo = pathinfo($imagePath);
                $thumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['basename'];
                
                error_log('Checking for thumbnail: ' . $thumbnailPath);
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                    error_log('Deleted thumbnail: ' . $thumbnailPath);
                } else {
                    error_log('Thumbnail not found: ' . $thumbnailPath);
                }
                
                // Redirect to images page
                error_log('Redirecting to vehicle images page for vehicle: ' . $image->vehicle_id);
                $this->redirect('user/vehicleImages/' . $image->vehicle_id);
            } else {
                error_log('Failed to delete image from database: ' . $id);
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            error_log('Not a POST request, redirecting to vehicle images page');
            $this->redirect('user/vehicleImages/' . $image->vehicle_id);
        }
    }
    
    // The registrations method has been moved to the registrations($action, $id) method below
    
    /**
     * Register for a show
     * 
     * @param int $showId Show ID
     */
    public function register($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if registration is open
        if (!$this->showModel->isRegistrationOpen($showId)) {
            $this->redirect('home/error/Registration%20is%20not%20open%20for%20this%20show');
            return;
        }
        
        // Get user's vehicles
        $userId = $this->auth->getCurrentUserId();
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'vehicle_id' => intval($_POST['vehicle_id']),
                'category_id' => intval($_POST['category_id']),
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_amount' => 0,
                'vehicle_id_err' => '',
                'category_id_err' => '',
                'title' => 'Register for Show',
                'show' => $show,
                'vehicles' => $vehicles,
                'categories' => $categories
            ];
            
            // Validate vehicle
            if (empty($data['vehicle_id'])) {
                $data['vehicle_id_err'] = 'Please select a vehicle';
            } else {
                // Check if vehicle belongs to user
                if (!$this->vehicleModel->isVehicleOwner($data['vehicle_id'], $userId)) {
                    $data['vehicle_id_err'] = 'Invalid vehicle';
                }
                
                // Check if vehicle is already registered for this show
                if ($this->registrationModel->isVehicleRegistered($showId, $data['vehicle_id'])) {
                    $data['vehicle_id_err'] = 'This vehicle is already registered for this show';
                }
            }
            
            // Validate category
            if (empty($data['category_id'])) {
                $data['category_id_err'] = 'Please select a category';
            } else {
                // Check if category is full
                if ($this->showModel->isCategoryFull($data['category_id'])) {
                    $data['category_id_err'] = 'This category is full';
                }
            }
            
            // Get registration fee
            if (empty($data['category_id_err'])) {
                $category = $this->showModel->getCategoryById($data['category_id']);
                $data['payment_amount'] = $category->registration_fee;
            }
            
            // Check for errors
            if (empty($data['vehicle_id_err']) && empty($data['category_id_err'])) {
                // Create registration
                $registrationId = $this->registrationModel->createRegistration($data);
                
                if ($registrationId) {
                    // Redirect to payment page if fee > 0, otherwise to registrations page
                    if ($data['payment_amount'] > 0) {
                        $this->redirect('user/payment/' . $registrationId);
                    } else {
                        $this->redirect('user/registrations');
                    }
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('user/registrations/register', $data);
            }
        } else {
            // Init data
            $data = [
                'show_id' => $showId,
                'vehicle_id' => '',
                'category_id' => '',
                'vehicle_id_err' => '',
                'category_id_err' => '',
                'title' => 'Register for Show',
                'show' => $show,
                'vehicles' => $vehicles,
                'categories' => $categories
            ];
            
            // Load view
            $this->view('user/registrations/register', $data);
        }
    }
    
    /**
     * Payment page
     * 
     * @param int $registrationId Registration ID
     */
    /**
     * Display user payment history
     */
    public function payments($action = null, $id = null) {
        // Handle sub-routes
        if ($action) {
            switch ($action) {
                case 'details':
                case 'paymentDetails':
                    if ($id) {
                        $this->paymentDetails($id);
                        return;
                    }
                    break;
                case 'receipt':
                    if ($id) {
                        $this->receipt($id);
                        return;
                    }
                    break;
                default:
                    // If it's a numeric ID, treat it as payment details
                    if (is_numeric($action)) {
                        $this->paymentDetails($action);
                        return;
                    }
                    break;
            }
        }
        // Get user ID
        $userId = $this->auth->getCurrentUserId();

        // Create an instance of the PaymentModel
        $paymentModel = $this->model('PaymentModel');

        // Get payment counts and statistics only (for performance)
        $paymentCounts = $paymentModel->getUserPaymentCounts($userId);
        $paymentStats = $paymentModel->getUserPaymentStats($userId);

        $data = [
            'title' => 'My Payment History',
            'payment_counts' => $paymentCounts,
            'payment_stats' => $paymentStats
        ];

        $this->view('user/payments', $data);
    }

    /**
     * Display payment details for a specific payment
     *
     * @param int $paymentId Payment ID
     */
    public function paymentDetails($paymentId) {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();

        // Create an instance of the PaymentModel
        $paymentModel = $this->model('PaymentModel');

        // Get payment details
        $payment = $paymentModel->getPaymentById($paymentId);

        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }

        // Check if user owns this payment or is admin
        if ($payment->user_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }

        // Get related registration if exists
        $registration = null;
        if ($payment->registration_id) {
            $registration = $this->registrationModel->getRegistrationById($payment->registration_id);
        }

        // Get related show if registration exists
        $show = null;
        if ($registration) {
            $show = $this->showModel->getShowById($registration->show_id);
        }

        $data = [
            'title' => 'Payment Details',
            'payment' => $payment,
            'registration' => $registration,
            'show' => $show
        ];

        $this->view('user/payments/details', $data);
    }

    /**
     * AJAX endpoint for loading paginated user payments
     *
     * @return void
     */
    public function loadPayments() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $typeFilter = $_GET['type_filter'] ?? '';
        $orderBy = $_GET['order_by'] ?? 'created_at';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            // Create an instance of the PaymentModel
            $paymentModel = $this->model('PaymentModel');

            $result = $paymentModel->getPaginatedUserPayments(
                $userId, $page, $perPage, $search, $statusFilter, $typeFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'payments' => $result['payments'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in UserController::loadPayments: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load payments']);
        }
    }
    
    /**
     * Display payment receipt for a registration
     * 
     * @param int $registrationId Registration ID
     */
    public function receipt($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if payment is completed
        if ($registration->payment_status !== 'paid' && $registration->payment_status !== 'free') {
            $this->redirect('user/registrations/view/' . $registrationId . '#payment');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get vehicle details
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
        
        // Get user details
        $user = $this->userModel->getUserById($userId);
        
        // Get payment details
        $paymentModel = $this->model('PaymentModel');
        $payment = $paymentModel->getPaymentByRegistration($registrationId);
        
        $data = [
            'title' => 'Payment Receipt',
            'registration' => $registration,
            'show' => $show,
            'vehicle' => $vehicle,
            'user' => $user,
            'payment' => $payment
        ];
        
        $this->view('user/registrations/receipt', $data);
    }
    
    public function payment($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if payment is needed
        if ($registration->payment_status == 'paid') {
            $this->redirect('user/registrations');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $registrationId,
                'payment_status' => 'paid',
                'payment_amount' => $registration->payment_amount,
                'payment_method' => trim($_POST['payment_method']),
                'payment_reference' => uniqid('PAY-'),
                'payment_method_err' => '',
                'title' => 'Payment',
                'registration' => $registration,
                'show' => $show
            ];
            
            // Validate payment method
            if (empty($data['payment_method'])) {
                $data['payment_method_err'] = 'Please select a payment method';
            }
            
            // Check for errors
            if (empty($data['payment_method_err'])) {
                // Update payment
                if ($this->registrationModel->updatePayment($data)) {
                    // Redirect to registrations page
                    $this->redirect('user/registrations');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('user/registrations/payment', $data);
            }
        } else {
            // Init data
            $data = [
                'payment_method' => '',
                'payment_method_err' => '',
                'title' => 'Payment',
                'registration' => $registration,
                'show' => $show
            ];
            
            // Load view
            $this->view('user/registrations/payment', $data);
        }
    }
    
    /**
     * Cancel registration
     * 
     * @param int $id Registration ID
     */
    public function cancelRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Update registration status
            $data = [
                'id' => $id,
                'category_id' => $registration->category_id,
                'status' => 'cancelled'
            ];
            
            if ($this->registrationModel->updateRegistration($data)) {
                // Redirect to registrations page
                $this->redirect('user/registrations');
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('user/registrations');
        }
    }
    
    /**
     * View registration details
     * 
     * @param int $id Registration ID
     */
    public function viewRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get vehicle images from old system (vehicle_images table)
        $oldImages = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        
        // Get images from new system (images table)
        $imageEditorModel = $this->model('ImageEditorModel');
        $newImages = $imageEditorModel->getImagesByEntity('vehicle', $registration->vehicle_id);
        
        // Combine images from both systems
        $allImages = [];
        
        // Process old images
        foreach ($oldImages as $image) {
            // Add a source identifier
            $image->source = 'old';
            $allImages[] = $image;
        }
        
        // Process new images
        foreach ($newImages as $image) {
            // Add a source identifier
            $image->source = 'new';
            $allImages[] = $image;
        }
        
        // Find primary image
        $primaryImage = null;
        foreach ($allImages as $image) {
            if (isset($image->is_primary) && $image->is_primary) {
                $primaryImage = $image;
                break;
            }
        }
        
        // If no primary image is set, use the first image
        if (!$primaryImage && count($allImages) > 0) {
            $primaryImage = $allImages[0];
            $primaryImage->is_primary = 1;
        }
        
        // Get judging results if show is completed
        $judgingResults = [];
        $awards = [];
        
        if ($show && $show->status === 'completed') {
            $judgingModel = $this->model('JudgingModel');
            $judgingResults = $judgingModel->getJudgingResultsByRegistration($id);
            $awards = $judgingModel->getAwardsByRegistration($id);
        }
        
        $data = [
            'title' => 'Registration Details',
            'registration' => $registration,
            'show' => $show,
            'vehicle_images' => $allImages,
            'primary_image' => $primaryImage,
            'judging_results' => $judgingResults,
            'awards' => $awards,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('user/registrations/view', $data);
    }
    
    /**
     * Print QR Code for a registration
     * 
     * @param int $id Registration ID
     */
    public function printQrCode($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->setFlashMessage('error', 'Registration not found');
            $this->redirect('user/registrations');
            return;
        }
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if QR code exists
        if (empty($registration->qr_code)) {
            // Redirect to admin's generateQrCode method with return URL
            $returnUrl = BASE_URL . '/user/registrations/printQrCode/' . $id;
            $this->redirect('admin/generateQrCode/' . $id . '?return=' . urlencode($returnUrl));
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Prepare data for view
        $data = [
            'title' => 'Print QR Code',
            'registration' => $registration,
            'show' => $show
        ];
        
        // Load print view
        $this->view('user/registrations/print_qr_code', $data);
    }
    
    /**
     * Download QR Code for a registration
     * 
     * @param int $id Registration ID
     */
    public function downloadQrCode($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->setFlashMessage('error', 'Registration not found');
            $this->redirect('user/registrations');
            return;
        }
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if QR code exists
        if (empty($registration->qr_code)) {
            $this->setFlashMessage('error', 'QR code not found');
            $this->redirect('user/viewRegistration/' . $id);
            return;
        }
        
        // Set file path
        $filePath = APPROOT . '/../uploads/qrcodes/' . $registration->qr_code;
        
        // Check if file exists
        if (!file_exists($filePath)) {
            $this->setFlashMessage('error', 'QR code file not found');
            $this->redirect('user/viewRegistration/' . $id);
            return;
        }
        
        // Get file info
        $fileInfo = pathinfo($filePath);
        $fileName = 'registration_' . $id . '_qrcode.' . $fileInfo['extension'];
        
        // Set headers for download
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . filesize($filePath));
        
        // Output file
        readfile($filePath);
        exit;
    }
    
    /**
     * Show notification preferences page
     */
    public function event_subscriptions() {
        // Load notification model
        $notificationModel = $this->model('NotificationModel');
        
        // Get current user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get user's event subscriptions
        $subscriptions = $notificationModel->getUserSubscriptions($userId);
        
        $data = [
            'title' => 'Event Subscriptions',
            'subscriptions' => $subscriptions,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('user/event_subscriptions', $data);
    }

    public function notification_preferences() {
        // Redirect to new event subscriptions page
        $this->redirect('user/event_subscriptions');
    }


    
    /**
     * Handle the registrations routes
     * 
     * @param string $action The action to perform (edit, view, printQrCode, downloadQrCode)
     * @param int $id Registration ID
     */
    public function registrations($action = null, $id = null) {
        if ($action === 'edit' && $id) {
            // Call the editRegistration method
            $this->editRegistration($id);
        } else if ($action === 'view' && $id) {
            // Call the viewRegistration method
            $this->viewRegistration($id);
        } else if ($action === 'printQrCode' && $id) {
            // Call the printQrCode method
            $this->printQrCode($id);
        } else if ($action === 'downloadQrCode' && $id) {
            // Call the downloadQrCode method
            $this->downloadQrCode($id);
        } else if ($action === 'receipt' && $id) {
            // Call the receipt method
            $this->receipt($id);
        } else if ($action !== null) {
            // Invalid action
            $this->redirect('home/not_found');
        } else {
            // No action specified, show all registrations
            // Get user ID
            $userId = $this->auth->getCurrentUserId();

            // Get registration counts only (for performance)
            $registrationCounts = $this->registrationModel->getUserRegistrationCounts($userId);

            // Get available shows for filtering
            $availableShows = $this->showModel->getShows('published');

            $data = [
                'title' => 'My Registrations',
                'registration_counts' => $registrationCounts,
                'available_shows' => $availableShows
            ];

            $this->view('user/registrations', $data);
        }
    }

    /**
     * AJAX endpoint for loading paginated user registrations
     *
     * @return void
     */
    public function loadRegistrations() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $showFilter = $_GET['show_filter'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $orderBy = $_GET['order_by'] ?? 'start_date';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            $result = $this->registrationModel->getPaginatedUserRegistrations(
                $userId, $page, $perPage, $search, $showFilter, $statusFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'registrations' => $result['registrations'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in UserController::loadRegistrations: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load registrations']);
        }
    }

    /**
     * Edit registration
     *
     * @param int $id Registration ID
     */
    public function editRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->owner_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if show is upcoming and registration can be edited (convert to user timezone for comparison)
        $userStartDate = convertUTCToUserDateTime($registration->start_date, $_SESSION['user_id'], 'Y-m-d H:i:s');
        if (strtotime($userStartDate) <= time() || $registration->payment_status === 'cancelled') {
            $this->redirect('home/error/This%20registration%20cannot%20be%20edited');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get user vehicles
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($registration->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $vehicleId = isset($_POST['vehicle_id']) ? intval($_POST['vehicle_id']) : 0;
            $categoryId = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
            
            // Validate form data
            $errors = [];
            
            if (empty($vehicleId)) {
                $errors['vehicle_id'] = 'Please select a vehicle';
            } else {
                // Verify vehicle belongs to user
                $vehicle = $this->vehicleModel->getVehicleById($vehicleId);
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $errors['vehicle_id'] = 'Invalid vehicle selection';
                }
                
                // Check if vehicle is already registered for this show (except current registration)
                if ($vehicleId != $registration->vehicle_id && 
                    $this->registrationModel->isVehicleRegistered($registration->show_id, $vehicleId)) {
                    $errors['vehicle_id'] = 'This vehicle is already registered for this show';
                }
            }
            
            if (empty($categoryId)) {
                $errors['category_id'] = 'Please select a category';
            } else {
                // Verify category belongs to show
                $category = $this->showModel->getCategoryById($categoryId);
                if (!$category || $category->show_id != $registration->show_id) {
                    $errors['category_id'] = 'Invalid category selection';
                }
                
                // Check if category is full (only if changing categories)
                if ($categoryId != $registration->category_id && $category->max_entries > 0) {
                    $categoryCount = 0;
                    foreach ($this->registrationModel->countRegistrationsByCategory($registration->show_id) as $count) {
                        if ($count->id == $categoryId) {
                            $categoryCount = $count->count;
                            break;
                        }
                    }
                    
                    if ($categoryCount >= $category->max_entries) {
                        $errors['category_id'] = 'This category is full';
                    }
                }
            }
            
            // If no errors, update registration
            if (empty($errors)) {
                // Get category fee
                $fee = 0;
                foreach ($categories as $cat) {
                    if ($cat->id == $categoryId) {
                        $fee = $cat->registration_fee;
                        break;
                    }
                }
                
                // Create registration data
                $registrationData = [
                    'id' => $id,
                    'category_id' => $categoryId,
                    'vehicle_id' => $vehicleId
                ];
                
                // If fee changed, update payment status
                if ($fee != $registration->fee && $registration->payment_status === 'paid') {
                    $registrationData['fee'] = $fee;
                    $registrationData['payment_status'] = 'pending';
                }
                
                // Update registration
                if ($this->registrationModel->updateRegistration($registrationData)) {
                    // Redirect to registrations page
                    setFlashMessage('success', 'Registration updated successfully');
                    $this->redirect('user/registrations');
                } else {
                    $this->redirect('home/error/Failed%20to%20update%20registration');
                }
            } else {
                // If there are errors, show the form again with errors
                $data = [
                    'title' => 'Edit Registration',
                    'registration' => $registration,
                    'show' => $show,
                    'vehicles' => $vehicles,
                    'categories' => $categories,
                    'errors' => $errors,
                    'vehicle_id' => $vehicleId,
                    'category_id' => $categoryId
                ];
                
                $this->view('user/registrations/edit', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Edit Registration',
                'registration' => $registration,
                'show' => $show,
                'vehicles' => $vehicles,
                'categories' => $categories,
                'errors' => [],
                'vehicle_id' => $registration->vehicle_id,
                'category_id' => $registration->category_id
            ];
            
            // Load view
            $this->view('user/registrations/edit', $data);
        }
    }
    
    /**
     * View vehicle scores for the owner
     * 
     * @param int $registrationId Registration ID
     */
    public function viewScores($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user owns this registration
        $userId = $this->auth->getCurrentUserId();
        if ($registration->user_id != $userId && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get vehicle images
        $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        
        // Try to get primary image if no images were found
        if (empty($images)) {
            // First try using the getPrimaryImage method
            $primaryImage = $this->vehicleModel->getPrimaryImage($registration->vehicle_id);
            if ($primaryImage) {
                // If we found a primary image, add it to the images array
                $images = [$primaryImage];
            } else {
                // Try to get the primary image directly from the vehicles table
                $this->db->query('SELECT primary_image FROM vehicles WHERE id = :vehicle_id AND primary_image IS NOT NULL AND primary_image != ""');
                $this->db->bind(':vehicle_id', $registration->vehicle_id);
                $vehicle = $this->db->single();
                
                if ($vehicle && isset($vehicle->primary_image) && !empty($vehicle->primary_image)) {
                    // Create a simple object to mimic an image record
                    $imageObj = new stdClass();
                    $imageObj->filename = $vehicle->primary_image;
                    $imageObj->is_primary = 1;
                    $images = [$imageObj];
                }
            }
        }
        
        // Get scores - only completed scores (is_draft = 0)
        $this->db->query("SELECT s.*, m.name as metric_name, m.max_score, m.weight, j.name as judge_name 
                         FROM scores s 
                         JOIN judging_metrics m ON s.metric_id = m.id 
                         JOIN users j ON s.judge_id = j.id 
                         WHERE s.registration_id = :registration_id 
                         AND s.is_draft = 0");
        $this->db->bind(':registration_id', $registrationId);
        $scores = $this->db->resultSet();
        
        // Get judging metrics for this show
        $this->db->query("SELECT id, name, max_score, weight FROM judging_metrics 
                         WHERE show_id = :show_id ORDER BY display_order");
        $this->db->bind(':show_id', $registration->show_id);
        $metrics = $this->db->resultSet();
        
        // Get age weight for this vehicle
        $this->db->query("SELECT multiplier FROM age_weights 
                         WHERE show_id = :show_id 
                         AND :vehicle_year BETWEEN min_age AND max_age");
        $this->db->bind(':show_id', $registration->show_id);
        $this->db->bind(':vehicle_year', $registration->year);
        $ageWeightResult = $this->db->single();
        
        $ageWeight = $ageWeightResult ? (float)$ageWeightResult->multiplier : 1.0;
        
        // Get scoring settings
        $this->db->query("SELECT * FROM show_scoring_settings WHERE show_id = :show_id");
        $this->db->bind(':show_id', $registration->show_id);
        $scoringSettings = $this->db->single();
        
        if (!$scoringSettings) {
            // Create default settings object
            $scoringSettings = (object)[
                'normalize_scores' => true,
                'use_age_weight' => true,
                'weight_multiplier' => 1,
                'age_weight_multiplier' => 1,
                'formula' => 'rawScore * metricWeight * ageWeight'
            ];
        }
        
        // Get the active formula
        $activeFormula = isset($scoringSettings->formula) ? $scoringSettings->formula : 'rawScore * metricWeight * ageWeight';
        
        // Get formula name
        $formulaName = 'Standard';
        if (isset($scoringSettings->formula_id)) {
            $this->db->query("SELECT name FROM scoring_formulas WHERE id = :id");
            $this->db->bind(':id', $scoringSettings->formula_id);
            $formula = $this->db->single();
            if ($formula) {
                $formulaName = $formula->name;
            }
        }
        
        // Calculate total score
        $weightedTotalScore = $this->vehicleScoringModel->getVehicleTotalScore($registration->show_id, $registration->vehicle_id);
        
        $data = [
            'title' => 'Vehicle Scores',
            'registration' => $registration,
            'show' => $show,
            'images' => $images,
            'scores' => $scores,
            'metrics' => $metrics,
            'age_weight' => $ageWeight,
            'scoring_settings' => $scoringSettings,
            'active_formula' => $activeFormula,
            'formula_name' => $formulaName,
            'weighted_total_score' => $weightedTotalScore
        ];
        
        $this->view('user/view_scores', $data);
    }
    
    /**
     * User notification preferences
     */
    public function createShow() {
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        
        // Load required models
        $formDesignerModel = $this->model('FormDesignerModel');
        $entityTemplateManager = $this->model('EntityTemplateManager');
        $settingsModel = $this->model('SettingsModel');
        $emailService = $this->model('EmailService');
        
        // Get listing fee from settings (currently free)
        $listingFee = $settingsModel->getSetting('default_listing_fee', 0);
        $listingFeeType = $settingsModel->getSetting('listing_fee_type', 'per_show');
        
        // Get the same template system used by admin
        $templateId = $entityTemplateManager->getBestTemplateId('show', 0);
        
        // If a template was found, use it
        if ($templateId) {
            $template = $formDesignerModel->getFormTemplateById($templateId);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("UserController::createShow - Using template ID {$templateId} from DefaultTemplateManager");
            }
        } else {
            // Fall back to the default admin form template
            $template = $formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("UserController::createShow - Using default admin_show template");
            }
        }
        
        // If no template exists, create a basic one
        if (!$template) {
            // Log the issue
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Default show form template not found. Creating a basic template.');
            }
            
            // Create a basic template with essential fields
            $basicFields = [
                (object)[
                    'id' => 'name',
                    'type' => 'text',
                    'label' => 'Show Name',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'location',
                    'type' => 'text',
                    'label' => 'Location',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'description',
                    'type' => 'textarea',
                    'label' => 'Description',
                    'required' => false,
                    'row' => 1,
                    'width' => 'col-md-12'
                ],
                (object)[
                    'id' => 'start_date',
                    'type' => 'date',
                    'label' => 'Start Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'end_date',
                    'type' => 'date',
                    'label' => 'End Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_start',
                    'type' => 'date',
                    'label' => 'Registration Start',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_end',
                    'type' => 'date',
                    'label' => 'Registration End',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'status',
                    'type' => 'select',
                    'label' => 'Status',
                    'required' => true,
                    'row' => 4,
                    'width' => 'col-md-6',
                    'options' => [
                        (object)['value' => 'draft', 'label' => 'Draft'],
                        (object)['value' => 'published', 'label' => 'Published']
                    ]
                ],
                (object)[
                    'id' => 'featured_image',
                    'type' => 'image',
                    'label' => 'Featured Image',
                    'required' => false,
                    'row' => 5,
                    'width' => 'col-md-6',
                    'placeholder' => 'Select a featured image for this show'
                ]
            ];
            
            // Create a temporary template object
            $template = (object)[
                'id' => 0,
                'name' => 'Default Show Form (Temporary)',
                'type' => 'admin_show',
                'entity_id' => 0,
                'fields' => json_encode($basicFields)
            ];
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Determine initial status based on listing fee
            $initialStatus = ($listingFee > 0) ? 'payment_pending' : 'draft';
            
            // Get form data - start with standard fields
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => convertUserDateTimeToUTC(trim($_POST['start_date']), $userId),
                'end_date' => convertUserDateTimeToUTC(trim($_POST['end_date']), $userId),
                'registration_start' => convertUserDateTimeToUTC(trim($_POST['registration_start']), $userId),
                'registration_end' => convertUserDateTimeToUTC(trim($_POST['registration_end']), $userId),
                'status' => $initialStatus, // Set status automatically based on payment requirements
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? (bool)$_POST['fan_voting_enabled'] : true,
                'is_free' => isset($_POST['is_free']) ? (bool)$_POST['is_free'] : false,
                'registration_fee' => (isset($_POST['is_free']) && $_POST['is_free']) ? 0.00 : (isset($_POST['registration_fee']) ? $_POST['registration_fee'] : 0.00),
                'listing_fee' => $listingFee,
                'coordinator_id' => $userId, // User becomes coordinator of their show
                'listing_paid' => ($listingFee == 0), // If no listing fee, mark as paid
                'featured_image_id' => isset($_POST['featured_image_id']) ? $_POST['featured_image_id'] : null,
                'featured_image' => isset($_POST['featured_image_text']) ? $_POST['featured_image_text'] : '',
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'title' => 'Create Show',
                'template' => $template,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType
            ];
            
            // Get custom fields from the template
            $templateFields = json_decode($template->fields, true);
            if (is_array($templateFields)) {
                foreach ($templateFields as $field) {
                    // Skip if field doesn't have an ID or is a system field
                    if (!isset($field['id']) || in_array($field['id'], [
                        'id', 'created_at', 'updated_at', 'name', 'description', 'location', 
                        'start_date', 'end_date', 'registration_start', 'registration_end', 
                        'coordinator_id', 'status', 'fan_voting_enabled', 'registration_fee', 
                        'is_free', 'listing_fee', 'listing_paid', 'featured_image_id'
                    ])) {
                        continue;
                    }
                    
                    // Skip section dividers and HTML fields
                    if (isset($field['type']) && in_array($field['type'], ['section', 'html'])) {
                        continue;
                    }
                    
                    // Add the field to the data array
                    $fieldId = $field['id'];
                    if (isset($_POST[$fieldId])) {
                        // Handle different field types
                        switch ($field['type']) {
                            case 'checkbox':
                                $data[$fieldId] = isset($_POST[$fieldId]) ? true : false;
                                break;
                            case 'number':
                                $data[$fieldId] = is_numeric($_POST[$fieldId]) ? $_POST[$fieldId] : 0;
                                break;
                            default:
                                $data[$fieldId] = trim($_POST[$fieldId]);
                        }
                    } else {
                        // Set default value if field is not in POST data
                        $data[$fieldId] = isset($field['default']) ? $field['default'] : null;
                    }
                }
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates (now in UTC format)
            if (empty(trim($_POST['start_date']))) {
                $data['start_date_err'] = 'Please enter a start date';
            } elseif (empty($data['start_date'])) {
                $data['start_date_err'] = 'Invalid start date format';
            }
            
            if (empty(trim($_POST['end_date']))) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif (empty($data['end_date'])) {
                $data['end_date_err'] = 'Invalid end date format';
            } elseif (!empty($data['start_date']) && !empty($data['end_date'])) {
                try {
                    $startDateTime = new DateTime($data['start_date']);
                    $endDateTime = new DateTime($data['end_date']);
                    
                    if ($endDateTime <= $startDateTime) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['end_date'] <= $data['start_date']) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("UserController::createShow - DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            if (empty(trim($_POST['registration_start']))) {
                $data['registration_start_err'] = 'Please enter a registration start date';
            } elseif (empty($data['registration_start'])) {
                $data['registration_start_err'] = 'Invalid registration start date format';
            }
            
            if (empty(trim($_POST['registration_end']))) {
                $data['registration_end_err'] = 'Please enter a registration end date';
            } elseif (empty($data['registration_end'])) {
                $data['registration_end_err'] = 'Invalid registration end date format';
            } elseif (!empty($data['registration_start']) && !empty($data['registration_end'])) {
                try {
                    $regStartDateTime = new DateTime($data['registration_start']);
                    $regEndDateTime = new DateTime($data['registration_end']);
                    
                    if ($regEndDateTime <= $regStartDateTime) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    } elseif (!empty($data['start_date'])) {
                        $showStartDateTime = new DateTime($data['start_date']);
                        if ($regEndDateTime > $showStartDateTime) {
                            $data['registration_end_err'] = 'Registration must end before the show starts';
                        }
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['registration_end'] <= $data['registration_start']) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    } elseif (!empty($data['start_date']) && $data['registration_end'] > $data['start_date']) {
                        $data['registration_end_err'] = 'Registration must end before the show starts';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("UserController::createShow - Registration DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            // Status is set automatically, no validation needed
            
            // Check for errors
            $hasErrors = !empty($data['name_err']) || !empty($data['location_err']) || 
                        !empty($data['start_date_err']) || !empty($data['end_date_err']) || 
                        !empty($data['registration_start_err']) || !empty($data['registration_end_err']);
            
            if (!$hasErrors) {
                // Create show
                $showId = $this->showModel->createShow($data);
                
                if ($showId) {
                    // Update user role to coordinator if they're just a regular user
                    $currentUser = $this->userModel->getUserById($userId);
                    if ($currentUser && $currentUser->role === 'user') {
                        $this->userModel->updateUserRole($userId, 'coordinator');
                        // Update session role
                        $_SESSION['user_role'] = 'coordinator';
                    }
                    
                    // Get user info for email notification
                    $user = $this->userModel->getUserById($userId);
                    $show = $this->showModel->getShowById($showId);
                    
                    // Send email notification to admins
                    $emailService->sendNewShowNotification($show, $user);
                    
                    // Set success message
                    $this->setFlashMessage('success', 'Show created successfully! You are now the coordinator for this show.', 'success');
                    
                    // If there's a listing fee, redirect to payment
                    if ($listingFee > 0) {
                        $this->redirect('payment/showListing/' . $showId);
                    } else {
                        // If no listing fee, redirect to show page
                        $this->redirect('show/view/' . $showId);
                    }
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('user/create_show', $data);
            }
        } else {
            // Determine initial status for display purposes
            $initialStatus = ($listingFee > 0) ? 'payment_pending' : 'draft';
            
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'location' => '',
                'start_date' => '',
                'end_date' => '',
                'registration_start' => '',
                'registration_end' => '',
                'status' => $initialStatus, // Set automatically based on payment requirements
                'fan_voting_enabled' => true,
                'registration_fee' => 0.00,
                'is_free' => false,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType,
                'title' => 'Create Show',
                'template' => $template,
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('user/create_show', $data);
        }
    }

    /**
     * User notification settings page
     */
    public function notifications() {
        // Get user
        $userId = $this->auth->getCurrentUserId();
        $user = $this->userModel->getUserById($userId);
        
        // Load notification model
        $notificationModel = $this->model("NotificationModel");
        
        // Get global notification settings
        try {
            $notificationModel->initializeDefaultSettings();
            $globalSettings = [
                "email_enabled" => $notificationModel->getNotificationSettings("email_enabled") ?? true,
                "sms_enabled" => $notificationModel->getNotificationSettings("sms_enabled") ?? true,
                "push_enabled" => $notificationModel->getNotificationSettings("push_enabled") ?? true,
                "toast_enabled" => $notificationModel->getNotificationSettings("toast_enabled") ?? true
            ];
        } catch (Exception $e) {
            $globalSettings = [
                "email_enabled" => true,
                "sms_enabled" => true,
                "push_enabled" => true,
                "toast_enabled" => true
            ];
        }
        
        // Handle form submission
        if ($_SERVER["REQUEST_METHOD"] == "POST") {
            if (!$this->verifyCsrfToken()) {
                $this->redirect("home/error/Invalid%20request");
                return;
            }
            
            $_POST = $this->sanitizeInput($_POST);
            
            $preferences = [
                "email_notifications" => (isset($_POST["email_notifications"]) && $globalSettings["email_enabled"]) ? 1 : 0,
                "sms_notifications" => (isset($_POST["sms_notifications"]) && $globalSettings["sms_enabled"]) ? 1 : 0,
                "push_notifications" => (isset($_POST["push_notifications"]) && $globalSettings["push_enabled"]) ? 1 : 0,
                "toast_notifications" => (isset($_POST["toast_notifications"]) && $globalSettings["toast_enabled"]) ? 1 : 0,
                "event_reminders" => isset($_POST["event_reminders"]) ? 1 : 0,
                "registration_updates" => isset($_POST["registration_updates"]) ? 1 : 0,
                "judging_updates" => isset($_POST["judging_updates"]) ? 1 : 0,
                "award_notifications" => isset($_POST["award_notifications"]) ? 1 : 0,
                "system_announcements" => isset($_POST["system_announcements"]) ? 1 : 0,
                "reminder_times" => $_POST["reminder_times"] ?? "[1440, 60]"
            ];
            
            if (!empty($_POST["phone"])) {
                $this->userModel->updateUserPhone($userId, $_POST["phone"]);
            }
            
            if ($notificationModel->updateUserPreferences($userId, $preferences)) {
                $this->setFlashMessage("success", "Notification preferences updated successfully");
            } else {
                $this->setFlashMessage("error", "Failed to update notification preferences");
            }
            
            $this->redirect("user/notifications");
            return;
        }
        
        // Get user preferences
        $preferences = $notificationModel->getUserPreferences($userId);
        $settings = $notificationModel->getSettings();
        
        $data = [
            "title" => "Notification Settings",
            "preferences" => $preferences,
            "settings" => $settings,
            "global_settings" => $globalSettings,
            "user_id" => $userId,
            "user" => $user,
            "csrf_token" => $this->generateCsrfToken()
        ];
        
        $this->view("user/notifications", $data);
    }


}