<?php
/**
 * Admin Controller
 * 
 * This controller handles all admin-related functionality.
 */
class AdminController extends Controller {
    private $userModel;
    private $showModel;
    private $settingsModel;
    private $printableTemplateModel;
    private $judgingModel;
    private $systemFieldManager;
    private $registrationModel;
    private $vehicleModel;
    private $categoryModel;
    private $defaultCategoryModel;
    private $defaultMetricModel;
    private $defaultAgeWeightModel;
    private $awardModel;
    private $calendarModel;
    private $paymentModel;
    private $auth;
    private $db;
    

    
    /**
     * Process textarea fields
     * 
     * This function ensures that textarea fields are properly included in the form data
     * even if they are empty or not submitted.
     * 
     * @param array $data Form data
     * @param string $fieldId Field ID
     * @return array Updated form data
     */
    private function processTextareaField($data, $fieldId) {
        // Check if the field was included in the form (using our hidden marker field)
        if (isset($_POST['_has_' . $fieldId])) {
            // If the field exists in POST data, use it
            if (isset($_POST[$fieldId])) {
                $data[$fieldId] = $_POST[$fieldId];
                error_log("AdminController: Added textarea field {$fieldId} from POST data with length: " . strlen($_POST[$fieldId]));
            } 
            // Otherwise, set it to an empty string to ensure it's included in the update
            else {
                $data[$fieldId] = '';
                error_log("AdminController: Added empty textarea field {$fieldId}");
            }
        }
        
        return $data;
    }
    
    /**
     * Process checkbox fields
     * 
     * This function ensures that checkbox fields are properly converted to integers (0 or 1)
     * for database storage, especially for tinyint(1) fields.
     * 
     * @param array $data Form data
     * @param string $fieldId Field ID
     * @return array Updated form data
     */
    private function processCheckboxField($data, $fieldId) {
        // Check if this is a checkbox group
        if (isset($_POST['_is_checkbox_group_' . $fieldId])) {
            // This is a checkbox group, process it differently
            return $this->processCheckboxGroupField($data, $fieldId);
        }
        
        // Regular single checkbox processing
        // Convert boolean values to integers (0 or 1) for database storage
        if (isset($data[$fieldId])) {
            // If the field exists in the data array, convert it to an integer
            $data[$fieldId] = $data[$fieldId] ? 1 : 0;
            error_log("AdminController: Converted checkbox field {$fieldId} to integer: {$data[$fieldId]}");
        } else if (isset($_POST['_has_' . $fieldId])) {
            // If the field was included in the form but not checked, set it to 0
            $data[$fieldId] = 0;
            error_log("AdminController: Added unchecked checkbox field {$fieldId} with value: 0");
        }
        
        return $data;
    }
    
    /**
     * Process checkbox group fields
     * 
     * @param array $data Form data
     * @param string $fieldId Field ID
     * @return array Updated form data
     */
    private function processCheckboxGroupField($data, $fieldId) {
        error_log("AdminController: Processing checkbox group field: {$fieldId}");
        
        // Get the original value for reference
        $originalValue = isset($_POST[$fieldId . '_original']) ? $_POST[$fieldId . '_original'] : '';
        error_log("AdminController: Original value: {$originalValue}");
        
        // Collect all selected values from individual checkboxes
        $selectedValues = [];
        
        // Look for all POST fields that start with the field ID followed by an underscore
        foreach ($_POST as $key => $value) {
            // Check if this is a checkbox for this group
            if (strpos($key, $fieldId . '_') === 0 && $key !== $fieldId . '_original') {
                // Extract the option value from the field name
                $optionValue = substr($key, strlen($fieldId) + 1);
                
                // If the checkbox is checked, add its value to the selected values
                if ($value) {
                    $selectedValues[] = $optionValue;
                }
            }
        }
        
        error_log("AdminController: Selected values: " . json_encode($selectedValues));
        
        // Store the selected values as a JSON string
        $data[$fieldId] = json_encode($selectedValues);
        error_log("AdminController: Stored checkbox group values for {$fieldId}: {$data[$fieldId]}");
        
        return $data;
    }
    
    /**
     * Generate CSRF token
     * 
     * @return string CSRF token
     */
    private function generateCsrfToken() {
        // Use the global function from csrf_helper.php
        return generateCsrfToken();
    }
    
    /**
     * Sanitize input data
     * 
     * @param array $data Data to sanitize
     * @return array Sanitized data
     */
    protected function sanitizeInput($data) {
        $result = [];
        
        // Try to load field types from the database
        $fieldTypes = $this->getFieldTypes();
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Check if we know the field type
                $fieldType = isset($fieldTypes[$key]) ? $fieldTypes[$key] : $this->guessFieldType($key);
                
                // Apply appropriate sanitization based on field type
                if ($fieldType === 'textarea') {
                    // For textareas, we'll strip tags but allow line breaks and basic formatting
                    $result[$key] = strip_tags($value, '<p><br><strong><em><ul><ol><li>');
                } else {
                    // For regular fields, use htmlspecialchars
                    $result[$key] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                }
            } elseif (is_array($value)) {
                $result[$key] = $this->sanitizeInput($value);
            } else {
                $result[$key] = $value;
            }
        }
        return $result;
    }
    
    /**
     * Get field types from the database
     * 
     * @return array Field types indexed by field ID
     */
    private function getFieldTypes() {
        $fieldTypes = [];
        
        try {
            $db = new Database();
            
            // Check if field_mappings table exists
            $db->query("SHOW TABLES LIKE 'field_mappings'");
            $db->execute();
            
            if ($db->rowCount() > 0) {
                // Load field types from the database
                $db->query("SELECT form_field_id, field_type FROM field_mappings WHERE field_type IS NOT NULL");
                $db->execute();
                $mappings = $db->resultSet();
                
                foreach ($mappings as $mapping) {
                    $fieldTypes[$mapping->form_field_id] = $mapping->field_type;
                }
            }
        } catch (Exception $e) {
            error_log("AdminController: Error loading field types: " . $e->getMessage());
        }
        
        return $fieldTypes;
    }
    
    /**
     * Guess the field type based on the field ID
     * 
     * @param string $fieldId Field ID
     * @return string Field type
     */
    private function guessFieldType($fieldId) {
        // Known field types
        $knownFields = [
            'description' => 'textarea',
            'name' => 'text',
            'location' => 'text',
            'start_date' => 'date',
            'end_date' => 'date',
            'registration_start' => 'date',
            'registration_end' => 'date',
            'coordinator_id' => 'select',
            'status' => 'select',
            'fan_voting_enabled' => 'checkbox',
            'registration_fee' => 'number',
            'is_free' => 'checkbox',
            'listing_fee' => 'number'
        ];
        
        // Check if this is a known field
        if (isset($knownFields[$fieldId])) {
            return $knownFields[$fieldId];
        }
        
        // Try to guess based on field ID prefix
        if (strpos($fieldId, 'textarea_') === 0) {
            return 'textarea';
        } elseif (strpos($fieldId, 'checkbox_') === 0) {
            return 'checkbox';
        } elseif (strpos($fieldId, 'select_') === 0) {
            return 'select';
        } elseif (strpos($fieldId, 'date_') === 0) {
            return 'date';
        } elseif (strpos($fieldId, 'number_') === 0) {
            return 'number';
        }
        
        // Default to text
        return 'text';
    }
    
    /**
     * Scheduled Tasks page
     * 
     * @return void
     */
    public function scheduled_tasks() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        $this->view('admin/scheduled_tasks');
    }
    
    /**
     * Get task details for AJAX request
     * 
     * @param int $id Task ID
     * @return void
     */
    public function getTask($id) {
        try {
            // Check if user is logged in and is an admin
            if (!isLoggedIn() || !isAdmin()) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Unauthorized'
                ]);
                exit;
            }
            
            // Always create a new database connection for this request
            $db = new Database();
            
            // Log the request for debugging
            error_log("AdminController::getTask - Fetching task ID: " . $id);
            
            // Get task details
            $db->query("SELECT * FROM scheduled_tasks WHERE id = :id");
            $db->bind(':id', $id);
            $task = $db->single();
            
            if (!$task) {
                error_log("AdminController::getTask - Task not found with ID: " . $id);
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Task not found'
                ]);
                exit;
            }
            
            // Return task details
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'task' => $task
            ]);
            exit;
        } catch (Exception $e) {
            error_log("AdminController::getTask - Error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is an admin or is in an impersonation session
        $this->auth = new Auth();
        
        // Initialize database connection
        $this->db = new Database();
        
        // Get the requested method
        $requestedMethod = isset($_GET['url']) ? explode('/', trim($_GET['url'], '/')) : [];
        
        // Special case for endImpersonation method - allow access if impersonating
        $isEndingImpersonation = (count($requestedMethod) >= 2 && 
                                 $requestedMethod[0] === 'admin' && 
                                 $requestedMethod[1] === 'endImpersonation');
        
        // Special case for addShow method - allow access for coordinators
        $isAddingShow = (count($requestedMethod) >= 2 && 
                        $requestedMethod[0] === 'admin' && 
                        $requestedMethod[1] === 'addShow');
        
        // Allow access if:
        // 1. User is admin, OR
        // 2. User is ending impersonation while impersonating, OR
        // 3. User is a coordinator accessing the addShow method
        if (!$this->auth->isLoggedIn() || 
            (!$this->auth->hasRole('admin') && 
             !(isset($_SESSION['admin_impersonating']) && $_SESSION['admin_impersonating'] && $isEndingImpersonation) &&
             !($this->auth->hasRole('coordinator') && $isAddingShow))) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->userModel = $this->model('UserModel');
        $this->showModel = $this->model('ShowModel');
        $this->settingsModel = $this->model('SettingsModel');
        $this->printableTemplateModel = $this->model('PrintableTemplateModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->categoryModel = $this->model('ShowCategoryModel');
        $this->paymentModel = $this->model('PaymentModel');
        
        // Initialize DefaultCategoryModel, DefaultMetricModel, and DefaultAgeWeightModel
        $this->defaultCategoryModel = $this->model('DefaultCategoryModel');
        $this->defaultMetricModel = $this->model('DefaultMetricModel');
        $this->defaultAgeWeightModel = $this->model('DefaultAgeWeightModel');
        
        // Initialize AwardModel
        $this->awardModel = $this->model('AwardModel');
        
        // Initialize CalendarModel
        $this->calendarModel = $this->model('CalendarModel');
        
        // Create tables if they don't exist
        $this->defaultCategoryModel->createTableIfNotExists();
        $this->defaultMetricModel->createTableIfNotExists();
        $this->defaultAgeWeightModel->createTableIfNotExists();
        
        // Initialize SystemFieldManager if available
        if (file_exists(APPROOT . '/models/SystemFieldManager.php')) {
            require_once APPROOT . '/models/SystemFieldManager.php';
            $this->systemFieldManager = new SystemFieldManager();
        }
        
        // Load form field manager if available
        if (file_exists(APPROOT . '/models/FormFieldManager.php')) {
            require_once APPROOT . '/models/FormFieldManager.php';
        }
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('admin/dashboard');
    }
    
    /**
     * User impersonation page
     * 
     * This method allows administrators to select a user to impersonate.
     */
    public function impersonateUser() {
        // Check if user is logged in and is an admin
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get all users except the current admin
        $users = $this->userModel->getUsersExcept($_SESSION['user_id']);
        
        // Group users by role for easier selection
        $usersByRole = [];
        foreach ($users as $user) {
            if (!isset($usersByRole[$user->role])) {
                $usersByRole[$user->role] = [];
            }
            $usersByRole[$user->role][] = $user;
        }
        
        $data = [
            'title' => 'Impersonate User',
            'users' => $users,
            'usersByRole' => $usersByRole,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/impersonate_user', $data);
    }
    
    /**
     * Start impersonation
     * 
     * This method starts the impersonation process.
     * 
     * @param int $userId ID of the user to impersonate
     */
    public function startImpersonation($userId = null) {
        // Check if user is logged in and is an admin
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if already impersonating someone
        if (isset($_SESSION['admin_impersonating']) && $_SESSION['admin_impersonating']) {
            // End the current impersonation first
            $this->endImpersonation();
        }
        
        // Validate user ID
        if (!$userId) {
            $this->setFlashMessage('error', 'Invalid user ID.');
            $this->redirect('admin/impersonateUser');
            return;
        }
        
        // Get the user to impersonate
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            $this->setFlashMessage('error', 'User not found.');
            $this->redirect('admin/impersonateUser');
            return;
        }
        
        // Store the admin's session data
        $adminId = $_SESSION['user_id'];
        $sessionId = session_id();
        
        // Create a database record of this impersonation
        try {
            $db = new Database();
            
            // Check if admin_impersonation table exists
            $db->query("SHOW TABLES LIKE 'admin_impersonation'");
            $db->execute();
            
            if ($db->rowCount() === 0) {
                // Table doesn't exist, create it
                $db->query("CREATE TABLE IF NOT EXISTS `admin_impersonation` (
                    `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
                    `admin_id` int UNSIGNED NOT NULL,
                    `impersonated_user_id` int UNSIGNED NOT NULL,
                    `session_id` varchar(255) NOT NULL,
                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `admin_id` (`admin_id`),
                    KEY `impersonated_user_id` (`impersonated_user_id`),
                    KEY `session_id` (`session_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
                $db->execute();
                
                // Note: Foreign key constraints have been removed to avoid compatibility issues
                // The application will maintain referential integrity through its code
            }
            
            // Insert the impersonation record
            $db->query('INSERT INTO admin_impersonation (admin_id, impersonated_user_id, session_id) 
                        VALUES (:admin_id, :impersonated_user_id, :session_id)');
            $db->bind(':admin_id', $adminId);
            $db->bind(':impersonated_user_id', $userId);
            $db->bind(':session_id', $sessionId);
            $db->execute();
        } catch (Exception $e) {
            // Log the error but continue - this is not critical
            error_log('Error creating impersonation record: ' . $e->getMessage());
        }
        
        // Store admin info in session for later restoration
        $_SESSION['admin_impersonating'] = true;
        $_SESSION['admin_id'] = $adminId;
        $_SESSION['admin_session_id'] = $sessionId;
        
        // Change session to impersonated user
        $_SESSION['user_id'] = $user->id;
        $_SESSION['user_role'] = $user->role;
        $_SESSION['user_name'] = $user->name;
        $_SESSION['user_email'] = $user->email;
        
        // Debug logging if enabled
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Admin impersonation started: Admin ID {$adminId} impersonating User ID {$user->id} ({$user->name})");
        }
        
        // Redirect to appropriate dashboard based on role
        switch ($user->role) {
            case 'judge':
                $this->redirect('judge/dashboard');
                break;
            case 'coordinator':
                $this->redirect('coordinator/dashboard');
                break;
            default:
                $this->redirect('user/dashboard');
                break;
        }
    }
    
    /**
     * End impersonation
     * 
     * This method ends the impersonation and restores the admin session.
     */
    public function endImpersonation() {
        // Check if we're in an impersonation session
        if (!isset($_SESSION['admin_impersonating']) || !$_SESSION['admin_impersonating']) {
            $this->redirect('home/dashboard');
            return;
        }
        
        // Get the admin ID
        $adminId = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : null;
        
        if (!$adminId) {
            // Something went wrong, clear session and redirect to login
            session_destroy();
            $this->redirect('auth/login');
            return;
        }
        
        // Get the admin user
        $admin = $this->userModel->getUserById($adminId);
        
        if (!$admin) {
            // Something went wrong, clear session and redirect to login
            session_destroy();
            $this->redirect('auth/login');
            return;
        }
        
        // Remove the impersonation record from the database
        try {
            $db = new Database();
            
            // Check if admin_impersonation table exists
            $db->query("SHOW TABLES LIKE 'admin_impersonation'");
            $db->execute();
            
            if ($db->rowCount() > 0) {
                $db->query('DELETE FROM admin_impersonation 
                            WHERE admin_id = :admin_id AND session_id = :session_id');
                $db->bind(':admin_id', $adminId);
                $db->bind(':session_id', $_SESSION['admin_session_id']);
                $db->execute();
            }
        } catch (Exception $e) {
            // Log the error but continue - this is not critical
            error_log('Error removing impersonation record: ' . $e->getMessage());
        }
        
        // Store the current session data that we want to keep
        $sessionData = [];
        if (isset($_SESSION['csrf_token'])) {
            $sessionData['csrf_token'] = $_SESSION['csrf_token'];
        }
        
        // Clear the session
        $_SESSION = [];
        
        // Restore any session data we want to keep
        foreach ($sessionData as $key => $value) {
            $_SESSION[$key] = $value;
        }
        
        // Restore admin session
        $_SESSION['user_id'] = $adminId;
        $_SESSION['user_role'] = 'admin';
        $_SESSION['user_name'] = $admin->name;
        $_SESSION['user_email'] = $admin->email;
        
        // Set success message
        $this->setFlashMessage('success', 'You have returned to your admin account.');
        
        // Redirect to admin dashboard
        $this->redirect('admin/dashboard');
    }
    
    /**
     * Image Manager
     */
    public function imageManager() {
        // Get current user ID and role
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Only admins can see all images, other users only see their own
        $filterUserId = ($userRole === 'admin') ? null : $userId;
        
        // Get recent images
        $imageEditorModel = $this->model('ImageEditorModel');
        $recent_images = $imageEditorModel->getRecentImages(8, $filterUserId);
        
        // Get image settings
        $settingsModel = $this->model('SettingsModel');
        $imageSettings = [
            'image_quality' => $settingsModel->getSetting('image_image_quality', 80),
            'thumbnail_size' => $settingsModel->getSetting('image_thumbnail_size', 200),
            'max_upload_size' => $settingsModel->getSetting('image_max_upload_size', 5),
            'allowed_extensions' => $settingsModel->getSetting('image_allowed_extensions', 'jpg,jpeg,png,gif'),
            'optimize_images' => $settingsModel->getSetting('image_optimize_images', '0') === '1'
        ];
        
        $data = [
            'title' => 'Image Manager',
            'recent_images' => $recent_images,
            'csrf_token' => $this->generateCsrfToken(),
            'user_role' => $userRole,
            'settings' => $imageSettings
        ];
        
        $this->view('admin/imageManager', $data);
    }
    
    /**
     * Image Library
     */
    public function imageLibrary() {
        // Redirect to the image editor browse page
        $this->redirect('image_editor/browse');
    }
    
    /**
     * Manage default templates
     * 
     * This method allows administrators to set default templates for different entity types.
     */
    public function defaultTemplates() {
        // Debug information
        error_log("AdminController::defaultTemplates - Method called");
        
        // Check if user is logged in and is an admin
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        try {
            // Load the DefaultTemplateManager
            require_once APPROOT . '/models/DefaultTemplateManager.php';
            $defaultTemplateManager = new DefaultTemplateManager();
            
            // Load the FormDesignerModel to get templates
            $formDesignerModel = $this->model('FormDesignerModel');
            
            // If form was submitted
            if ($_SERVER['REQUEST_METHOD'] == 'POST') {
                // Validate CSRF token
                if (!$this->verifyCsrfToken()) {
                    $this->redirect('home/error/Invalid%20request');
                    return;
                }
                
                // Get form data
                $entityType = isset($_POST['entity_type']) ? trim($_POST['entity_type']) : '';
                $templateId = isset($_POST['template_id']) ? intval($_POST['template_id']) : 0;
                $action = isset($_POST['action']) ? trim($_POST['action']) : '';
                
                // Validate data
                if (empty($entityType)) {
                    $this->setFlashMessage('error', 'Entity type is required.');
                    $this->redirect('admin/defaultTemplates');
                    return;
                }
                
                // Process the action
                if ($action === 'set' && $templateId > 0) {
                    // Set the default template
                    if ($defaultTemplateManager->setDefaultTemplate($entityType, $templateId, $_SESSION['user_id'])) {
                        $this->setFlashMessage('success', 'Default template for ' . ucfirst($entityType) . ' has been set successfully.');
                    } else {
                        $this->setFlashMessage('error', 'Failed to set default template.');
                    }
                } elseif ($action === 'remove') {
                    // Remove the default template
                    if ($defaultTemplateManager->removeDefaultTemplate($entityType)) {
                        $this->setFlashMessage('success', 'Default template for ' . ucfirst($entityType) . ' has been removed.');
                    } else {
                        $this->setFlashMessage('error', 'Failed to remove default template.');
                    }
                }
                
                // Redirect back
                $this->redirect('admin/defaultTemplates');
            } else {
                // Get all templates
                $allTemplates = $formDesignerModel->getFormTemplates();
                
                // Group templates by type
                $templatesByType = [];
                foreach ($allTemplates as $template) {
                    $type = $template->type ?? 'unknown';
                    if (!isset($templatesByType[$type])) {
                        $templatesByType[$type] = [];
                    }
                    $templatesByType[$type][] = $template;
                }
                
                // Get current default templates - force a fresh query
                $defaultTemplateManager = new DefaultTemplateManager(); // Create a fresh instance
                $defaultTemplates = $defaultTemplateManager->getAllDefaultTemplates();
                
                // Debug log to check what's being returned
                error_log("AdminController: Retrieved " . count($defaultTemplates) . " default templates");
                
                // Convert to associative array for easier lookup
                $defaultTemplatesByType = [];
                foreach ($defaultTemplates as $template) {
                    error_log("AdminController: Default template for {$template->entity_type}: {$template->template_name}");
                    $defaultTemplatesByType[$template->entity_type] = $template;
                }
                
                // Additional debug to check the final array
                foreach ($defaultTemplatesByType as $type => $template) {
                    error_log("AdminController: Final array - Default for {$type}: {$template->template_name}");
                }
                
                // Define entity types and their corresponding template types
                $entityTypes = [
                    'show' => 'event',
                    'vehicle' => 'vehicle',
                    'registration' => 'show'
                ];
                
                $data = [
                    'title' => 'Default Templates',
                    'allTemplates' => $allTemplates,
                    'templatesByType' => $templatesByType,
                    'defaultTemplates' => $defaultTemplatesByType,
                    'entityTypes' => $entityTypes,
                    'csrf_token' => $this->generateCsrfToken()
                ];
                
                $this->view('admin/defaultTemplates', $data);
            }
        } catch (Exception $e) {
            // Log the error
            error_log("Error in defaultTemplates: " . $e->getMessage());
            
            // Show error page
            $this->setFlashMessage('error', 'An error occurred while loading default templates: ' . $e->getMessage());
            $this->redirect('admin/dashboard');
            return;
        }
    }
    
    /**
     * Image Settings
     */
    public function imageSettings() {
        $settingsModel = $this->model('SettingsModel');
        
        // If form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get settings from form
            $settings = [
                'image_quality' => isset($_POST['image_quality']) ? intval($_POST['image_quality']) : 80,
                'thumbnail_size' => isset($_POST['thumbnail_size']) ? intval($_POST['thumbnail_size']) : 200,
                'max_upload_size' => isset($_POST['max_upload_size']) ? intval($_POST['max_upload_size']) : 5,
                'allowed_extensions' => isset($_POST['allowed_extensions']) ? trim($_POST['allowed_extensions']) : 'jpg,jpeg,png,gif',
                'optimize_images' => isset($_POST['optimize_images']) ? '1' : '0',
                'max_width' => isset($_POST['max_width']) ? intval($_POST['max_width']) : 1920,
                'max_height' => isset($_POST['max_height']) ? intval($_POST['max_height']) : 1080,
                'resize_large_images' => isset($_POST['resize_large_images']) ? '1' : '0',
                'watermark_enabled' => isset($_POST['watermark_enabled']) ? '1' : '0',
                'watermark_text' => isset($_POST['watermark_text']) ? trim($_POST['watermark_text']) : '',
                'watermark_position' => isset($_POST['watermark_position']) ? trim($_POST['watermark_position']) : 'bottom-right',
                'watermark_opacity' => isset($_POST['watermark_opacity']) ? intval($_POST['watermark_opacity']) : 50
            ];
            
            // Save settings
            foreach ($settings as $key => $value) {
                $settingsModel->setSetting('image_' . $key, $value);
            }
            
            // Set flash message
            $this->setFlashMessage('success', 'Image settings updated successfully.');
            
            // Redirect back
            $this->redirect('admin/imageSettings');
        } else {
            // Get current settings
            $settings = [
                'image_quality' => $settingsModel->getSetting('image_image_quality', 80),
                'thumbnail_size' => $settingsModel->getSetting('image_thumbnail_size', 200),
                'max_upload_size' => $settingsModel->getSetting('image_max_upload_size', 5),
                'allowed_extensions' => $settingsModel->getSetting('image_allowed_extensions', 'jpg,jpeg,png,gif'),
                'optimize_images' => $settingsModel->getSetting('image_optimize_images', '0'),
                'max_width' => $settingsModel->getSetting('image_max_width', 1920),
                'max_height' => $settingsModel->getSetting('image_max_height', 1080),
                'resize_large_images' => $settingsModel->getSetting('image_resize_large_images', '0'),
                'watermark_enabled' => $settingsModel->getSetting('image_watermark_enabled', '0'),
                'watermark_text' => $settingsModel->getSetting('image_watermark_text', ''),
                'watermark_position' => $settingsModel->getSetting('image_watermark_position', 'bottom-right'),
                'watermark_opacity' => $settingsModel->getSetting('image_watermark_opacity', 50)
            ];
            
            $data = [
                'title' => 'Image Settings',
                'settings' => $settings,
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/imageSettings', $data);
        }
    }
    
    /**
     * Redirect to image editor for show
     * 
     * @param int $id Show ID
     */
    public function editShowImages($id) {
        // Check if user has admin or coordinator role
        if ($_SESSION['user_role'] != 'admin' && $_SESSION['user_role'] != 'coordinator') {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        // Check if show exists
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Redirect to image editor
        $this->redirect('image_editor/show/' . $id);
    }
    
    /**
     * Redirect to form designer for show
     * 
     * @param int $id Show ID
     */
    public function editShowForm($id) {
        // Check if user has admin or coordinator role
        if ($_SESSION['user_role'] != 'admin' && $_SESSION['user_role'] != 'coordinator') {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        // Check if show exists
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Redirect to form designer
        $this->redirect('form_designer/edit/show/' . $id);
    }
    
    /**
     * Design admin show form
     */
    public function designAdminShowForm() {
        // Check if user has admin role
        if ($_SESSION['user_role'] != 'admin') {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Redirect to form designer for admin show form
        $this->redirect('form_designer/designAdminShowForm/0');
    }
    
    /**
     * Add category
     * 
     * @param int $showId Show ID
     */
    public function addCategory($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'registration_fee' => floatval($_POST['registration_fee']),
                'max_entries' => intval($_POST['max_entries']),
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Add Category',
                'show' => $show
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate registration fee
            if ($data['registration_fee'] < 0) {
                $data['registration_fee_err'] = 'Registration fee cannot be negative';
            }
            
            // Validate max entries
            if ($data['max_entries'] < 0) {
                $data['max_entries_err'] = 'Max entries cannot be negative';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['registration_fee_err']) && 
                empty($data['max_entries_err'])) {
                
                // Create category
                $categoryId = $this->showModel->createCategory($data);
                
                if ($categoryId) {
                    // Set success message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Category has been added successfully.'
                    ];
                    
                    // Check if there's a referrer in the form data
                    if (isset($_POST['referrer']) && !empty($_POST['referrer'])) {
                        $referrer = $_POST['referrer'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from form
                            header('Location: ' . $referrer);
                            exit;
                        }
                    } 
                    // Check if there's a referrer in the HTTP headers
                    else if (isset($_SERVER['HTTP_REFERER']) && !empty($_SERVER['HTTP_REFERER'])) {
                        $referrer = $_SERVER['HTTP_REFERER'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from headers
                            header('Location: ' . $referrer);
                            exit;
                        }
                    }
                    
                    // Fallback to edit show page if no valid referrer
                    $this->redirect('admin/editShow/' . $showId);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/add_category', $data);
            }
        } else {
            // Init data
            $data = [
                'show_id' => $showId,
                'name' => '',
                'description' => '',
                'registration_fee' => 0,
                'max_entries' => 0,
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Add Category',
                'show' => $show
            ];
            
            // Load view
            $this->view('admin/add_category', $data);
        }
    }
    
    /**
     * Add metric - redirects to the appropriate method
     * 
     * @param int $showId Show ID
     */
    public function addMetric($showId) {
        $this->redirect('admin/addJudgingMetric/' . $showId);
    }
    
    /**
     * Admin dashboard
     */
    public function dashboard() {
        // Get counts for dashboard
        $userCount = count($this->userModel->getUsers());
        $showCount = count($this->showModel->getShows());
        $upcomingShows = $this->showModel->getUpcomingShows(5);
        
        $data = [
            'title' => 'Admin Dashboard',
            'user_count' => $userCount,
            'show_count' => $showCount,
            'upcoming_shows' => $upcomingShows
        ];
        
        $this->view('admin/dashboard', $data);
    }
    
    /**
     * List all coordinators
     */
    public function coordinators() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get all coordinators
        $coordinators = $this->userModel->getUsersByRole('coordinator');
        
        // Get show counts for each coordinator
        foreach ($coordinators as $coordinator) {
            $coordinator->show_count = $this->showModel->getCoordinatorShowCount($coordinator->id);
        }
        
        $data = [
            'title' => 'Manage Coordinators',
            'coordinators' => $coordinators
        ];
        
        $this->view('admin/coordinators', $data);
    }
    
    /**
     * Display scoring settings page
     */
    public function scoringSettings() {
        // Get all shows
        $this->db->query("SELECT id, name, status, start_date FROM shows ORDER BY start_date DESC");
        $shows = $this->db->resultSet();
        
        // Get all scoring formulas
        $this->db->query("SELECT * FROM scoring_formulas ORDER BY is_default DESC, name ASC");
        $formulas = $this->db->resultSet();
        
        $data = [
            'title' => 'Scoring Settings',
            'shows' => $shows,
            'formulas' => $formulas
        ];
        
        // Check if there's a message to display
        if (isset($_SESSION['message'])) {
            $data['message'] = $_SESSION['message'];
            $data['message_type'] = $_SESSION['message_type'] ?? 'info';
            unset($_SESSION['message']);
            unset($_SESSION['message_type']);
        }
        
        $this->view('admin/scoring_settings', $data);
    }
    
    /**
     * Update scoring settings for a show
     */
    public function updateScoringSettings() {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('admin/scoringSettings');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        $showId = (int)$_POST['show_id'];
        $formulaId = (int)$_POST['formula_id'];
        $weightMultiplier = (float)$_POST['weight_multiplier'];
        $normalizeScores = isset($_POST['normalize_scores']) ? 1 : 0;
        
        try {
            // Check if settings already exist for this show
            $this->db->query("SELECT id FROM show_scoring_settings WHERE show_id = :show_id");
            $this->db->bind(':show_id', $showId);
            $existingSettings = $this->db->single();
            
            if ($existingSettings) {
                // Update existing settings
                $this->db->query("UPDATE show_scoring_settings SET 
                    formula_id = :formula_id,
                    weight_multiplier = :weight_multiplier,
                    normalize_scores = :normalize_scores,
                    updated_at = NOW()
                    WHERE show_id = :show_id");
            } else {
                // Insert new settings
                $this->db->query("INSERT INTO show_scoring_settings 
                    (show_id, formula_id, weight_multiplier, normalize_scores, created_at, updated_at)
                    VALUES (:show_id, :formula_id, :weight_multiplier, :normalize_scores, NOW(), NOW())");
            }
            
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':formula_id', $formulaId);
            $this->db->bind(':weight_multiplier', $weightMultiplier);
            $this->db->bind(':normalize_scores', $normalizeScores);
            
            if ($this->db->execute()) {
                $_SESSION['message'] = 'Scoring settings updated successfully';
                $_SESSION['message_type'] = 'success';
            } else {
                $_SESSION['message'] = 'Error updating scoring settings';
                $_SESSION['message_type'] = 'danger';
            }
        } catch (Exception $e) {
            $_SESSION['message'] = 'Error: ' . $e->getMessage();
            $_SESSION['message_type'] = 'danger';
        }
        
        $this->redirect('admin/scoringSettings');
    }
    
    /**
     * Run scoring calculation
     */
    public function runScoringCalculation() {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('admin/scoringSettings');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        $showId = (int)$_POST['show_id'];
        $calculationType = $_POST['calculation_type'];
        
        $scriptPath = APPROOT . '/scripts/';
        $outputMessages = [];
        $returnCode = 0;
        
        try {
            // Check if PHP executable is available
            $phpPath = "php";
            $testCommand = "$phpPath -v";
            $testOutput = [];
            $testReturnCode = 0;
            exec($testCommand, $testOutput, $testReturnCode);
            
            if ($testReturnCode !== 0) {
                throw new Exception("PHP executable not found or not working. Return code: $testReturnCode");
            }
            
            // Log the PHP version for debugging
            error_log("PHP version check: " . implode("\n", $testOutput));
            
            // Check if script directory exists
            if (!is_dir($scriptPath)) {
                throw new Exception("Script directory not found: $scriptPath");
            }
            
            switch ($calculationType) {
                case 'judge_scores':
                    $scriptFile = $scriptPath . "generate_judge_scores.php";
                    if (!file_exists($scriptFile)) {
                        throw new Exception("Script file not found: $scriptFile");
                    }
                    
                    // Set up the command with proper escaping
                    $command = escapeshellcmd($phpPath) . " " . escapeshellarg($scriptFile) . " " . escapeshellarg("--show_id=$showId");
                    error_log("Executing command: $command");
                    
                    // Execute the command and capture both stdout and stderr
                    exec($command . " 2>&1", $outputMessages, $returnCode);
                    
                    // Log the full output for debugging
                    error_log("Command output: " . implode("\n", $outputMessages));
                    
                    $scriptName = "Judge scores";
                    break;
                    
                case 'vehicle_scores':
                    $scriptFile = $scriptPath . "generate_vehicle_scores.php";
                    if (!file_exists($scriptFile)) {
                        throw new Exception("Script file not found: $scriptFile");
                    }
                    
                    // Set up the command with proper escaping
                    $command = escapeshellcmd($phpPath) . " " . escapeshellarg($scriptFile) . " " . escapeshellarg("--show_id=$showId");
                    error_log("Executing command: $command");
                    
                    // Execute the command and capture both stdout and stderr
                    exec($command . " 2>&1", $outputMessages, $returnCode);
                    
                    // Log the full output for debugging
                    error_log("Command output: " . implode("\n", $outputMessages));
                    
                    $scriptName = "Vehicle scores";
                    break;
                    
                case 'category_winners':
                    $scriptFile = $scriptPath . "calculate_show_winners.php";
                    if (!file_exists($scriptFile)) {
                        throw new Exception("Script file not found: $scriptFile");
                    }
                    
                    // Set up the command with proper escaping
                    $command = escapeshellcmd($phpPath) . " " . escapeshellarg($scriptFile) . " " . escapeshellarg("--show_id=$showId");
                    error_log("Executing command: $command");
                    
                    // Execute the command and capture both stdout and stderr
                    exec($command . " 2>&1", $outputMessages, $returnCode);
                    
                    // Log the full output for debugging
                    error_log("Command output: " . implode("\n", $outputMessages));
                    
                    $scriptName = "Category winners";
                    break;
                    
                case 'all':
                    $scriptFile = $scriptPath . "run_all_scoring.php";
                    if (!file_exists($scriptFile)) {
                        throw new Exception("Script file not found: $scriptFile");
                    }
                    
                    // Set up the command with proper escaping
                    $command = escapeshellcmd($phpPath) . " " . escapeshellarg($scriptFile) . " " . escapeshellarg("--show_id=$showId");
                    error_log("Executing command: $command");
                    
                    // Execute the command and capture both stdout and stderr
                    exec($command . " 2>&1", $outputMessages, $returnCode);
                    
                    // Log the full output for debugging
                    error_log("Command output: " . implode("\n", $outputMessages));
                    
                    $scriptName = "All scoring calculations";
                    break;
                    
                case 'clear_judge_scores':
                    $this->db->query("DELETE FROM judge_total_scores WHERE show_id = :show_id");
                    $this->db->bind(':show_id', $showId);
                    $this->db->execute();
                    
                    $this->db->query("DELETE FROM judge_metric_scores WHERE show_id = :show_id");
                    $this->db->bind(':show_id', $showId);
                    $this->db->execute();
                    
                    $scriptName = "Clear judge scores";
                    $returnCode = 0;
                    $outputMessages[] = "Judge scores cleared successfully for show ID: " . $showId;
                    break;
                    
                case 'clear_vehicle_scores':
                    $this->db->query("DELETE FROM vehicle_total_scores WHERE show_id = :show_id");
                    $this->db->bind(':show_id', $showId);
                    $this->db->execute();
                    
                    $this->db->query("DELETE FROM vehicle_metric_scores WHERE show_id = :show_id");
                    $this->db->bind(':show_id', $showId);
                    $this->db->execute();
                    
                    $scriptName = "Clear vehicle scores";
                    $returnCode = 0;
                    $outputMessages[] = "Vehicle scores cleared successfully for show ID: " . $showId;
                    break;
                    
                case 'clear_category_winners':
                    $this->db->query("DELETE FROM category_winners WHERE show_id = :show_id");
                    $this->db->bind(':show_id', $showId);
                    $this->db->execute();
                    
                    $scriptName = "Clear category winners";
                    $returnCode = 0;
                    $outputMessages[] = "Category winners cleared successfully for show ID: " . $showId;
                    break;
                    
                default:
                    $_SESSION['message'] = "Invalid calculation type selected";
                    $_SESSION['message_type'] = 'danger';
                    $this->redirect('admin/scoringSettings#calculations');
                    return;
            }
            
            // Log the output for debugging
            error_log("Script output: " . implode("\n", $outputMessages));
            error_log("Return code: $returnCode");
            
            if ($returnCode === 0) {
                $_SESSION['message'] = "$scriptName calculation completed successfully for show ID: $showId";
                $_SESSION['message_type'] = 'success';
            } else {
                // Get more detailed error information
                $errorDetails = !empty($outputMessages) ? implode("\n", $outputMessages) : "No output captured";
                $_SESSION['message'] = "$scriptName calculation failed with error code: $returnCode";
                $_SESSION['message_type'] = 'danger';
                error_log("Script execution failed with error code $returnCode. Details: $errorDetails");
            }
            
            $_SESSION['calculation_output'] = implode("\n", $outputMessages);
            
        } catch (Exception $e) {
            error_log("Exception in runScoringCalculation: " . $e->getMessage());
            $_SESSION['message'] = "Error: " . $e->getMessage();
            $_SESSION['message_type'] = 'danger';
            $_SESSION['calculation_output'] = $e->getMessage() . "\n" . $e->getTraceAsString();
        }
        
        $this->redirect('admin/scoringSettings#calculations');
    }
    
    /**
     * Manage scores page
     */
    public function manageScores() {
        // Get all shows
        $this->db->query("SELECT id, name, status, start_date FROM shows ORDER BY start_date DESC");
        $shows = $this->db->resultSet();
        
        $data = [
            'title' => 'Manage Scores',
            'shows' => $shows
        ];
        
        // Check if there's a message to display
        if (isset($_SESSION['message'])) {
            $data['message'] = $_SESSION['message'];
            $data['message_type'] = $_SESSION['message_type'] ?? 'info';
            unset($_SESSION['message']);
            unset($_SESSION['message_type']);
        }
        
        // Check if a show is selected
        if (isset($_GET['show_id']) && !empty($_GET['show_id'])) {
            $showId = (int)$_GET['show_id'];
            
            // Get show details
            $this->db->query("SELECT * FROM shows WHERE id = :id");
            $this->db->bind(':id', $showId);
            $selectedShow = $this->db->single();
            
            if ($selectedShow) {
                $data['selected_show'] = $selectedShow;
                
                // Get scoring settings for this show
                $this->db->query("SELECT ss.*, sf.name as formula_name 
                                 FROM show_scoring_settings ss
                                 LEFT JOIN scoring_formulas sf ON ss.formula_id = sf.id
                                 WHERE ss.show_id = :show_id");
                $this->db->bind(':show_id', $showId);
                $data['scoring_settings'] = $this->db->single();
                
                // Get vehicle scores with error handling
                try {
                    // First, check the structure of vehicle_total_scores table
                    $this->db->query("DESCRIBE vehicle_total_scores");
                    $columns = $this->db->resultSet();
                    $columnNames = [];
                    foreach ($columns as $column) {
                        $columnNames[] = $column->Field;
                    }
                    error_log("Vehicle total scores columns: " . implode(", ", $columnNames));
                    
                    // Now get the vehicle scores
                    $this->db->query("SELECT vts.*, v.year, v.make, v.model, r.display_number
                                     FROM vehicle_total_scores vts
                                     JOIN vehicles v ON vts.vehicle_id = v.id
                                     JOIN registrations r ON vts.registration_id = r.id
                                     WHERE vts.show_id = :show_id
                                     ORDER BY vts.total_score DESC");
                    $this->db->bind(':show_id', $showId);
                    $data['vehicle_scores'] = $this->db->resultSet();
                } catch (Exception $e) {
                    error_log("Error getting vehicle scores: " . $e->getMessage());
                    $data['vehicle_scores'] = [];
                }
                
                // Get judge scores with error handling
                try {
                    // First, check the structure of judge_total_scores table
                    $this->db->query("DESCRIBE judge_total_scores");
                    $columns = $this->db->resultSet();
                    $columnNames = [];
                    foreach ($columns as $column) {
                        $columnNames[] = $column->Field;
                    }
                    error_log("Judge total scores columns: " . implode(", ", $columnNames));
                    
                    // Now get the judge scores
                    $this->db->query("SELECT jts.*, v.year, v.make, v.model, r.display_number, 
                                     u.name as judge_name
                                     FROM judge_total_scores jts
                                     JOIN vehicles v ON jts.vehicle_id = v.id
                                     JOIN registrations r ON jts.registration_id = r.id
                                     JOIN users u ON jts.judge_id = u.id
                                     WHERE jts.show_id = :show_id
                                     ORDER BY jts.judge_id, jts.final_score DESC");
                    $this->db->bind(':show_id', $showId);
                    $data['judge_scores'] = $this->db->resultSet();
                } catch (Exception $e) {
                    error_log("Error getting judge scores: " . $e->getMessage());
                    $data['judge_scores'] = [];
                }
                
                // Get category winners with error handling
                try {
                    // First, check the structure of category_winners table
                    $this->db->query("DESCRIBE category_winners");
                    $columns = $this->db->resultSet();
                    $columnNames = [];
                    foreach ($columns as $column) {
                        $columnNames[] = $column->Field;
                    }
                    error_log("Category winners columns: " . implode(", ", $columnNames));
                    
                    // Now get the category winners
                    $this->db->query("SELECT cw.*, c.name as category_name, v.year, v.make, v.model, 
                                     r.display_number, u.name as owner_name
                                     FROM category_winners cw
                                     JOIN show_categories c ON cw.category_id = c.id
                                     JOIN vehicles v ON cw.vehicle_id = v.id
                                     JOIN registrations r ON cw.registration_id = r.id
                                     JOIN users u ON cw.user_id = u.id
                                     WHERE cw.show_id = :show_id
                                     ORDER BY c.id, cw.place");
                    $this->db->bind(':show_id', $showId);
                    $data['category_winners'] = $this->db->resultSet();
                } catch (Exception $e) {
                    error_log("Error getting category winners: " . $e->getMessage());
                    $data['category_winners'] = [];
                }
                
                // Get statistics
                $stats = new stdClass();
                
                // Total vehicles
                $this->db->query("SELECT COUNT(*) as count FROM registrations WHERE show_id = :show_id AND status = 'confirmed'");
                $this->db->bind(':show_id', $showId);
                $stats->total_vehicles = $this->db->single()->count;
                
                // Check scores table structure
                try {
                    $this->db->query("DESCRIBE scores");
                    $columns = $this->db->resultSet();
                    $columnNames = [];
                    foreach ($columns as $column) {
                        $columnNames[] = $column->Field;
                    }
                    error_log("Scores table columns: " . implode(", ", $columnNames));
                    
                    // Judged vehicles - use registration_id instead of vehicle_id if needed
                    if (in_array('vehicle_id', $columnNames)) {
                        $this->db->query("SELECT COUNT(DISTINCT vehicle_id) as count FROM scores 
                                         WHERE show_id = :show_id AND is_draft = 0");
                    } else {
                        $this->db->query("SELECT COUNT(DISTINCT registration_id) as count FROM scores 
                                         WHERE show_id = :show_id AND is_draft = 0");
                    }
                    $this->db->bind(':show_id', $showId);
                    $stats->judged_vehicles = $this->db->single()->count;
                } catch (Exception $e) {
                    error_log("Error checking scores table: " . $e->getMessage());
                    $stats->judged_vehicles = 0;
                }
                
                // Pending vehicles
                $stats->pending_vehicles = $stats->total_vehicles - $stats->judged_vehicles;
                
                // Score statistics
                if (isset($data['vehicle_scores']) && !empty($data['vehicle_scores'])) {
                    $scores = array_map(function($score) {
                        return $score->total_score;
                    }, $data['vehicle_scores']);
                    
                    $stats->highest_score = max($scores);
                    $stats->lowest_score = min($scores);
                    $stats->average_score = array_sum($scores) / count($scores);
                    $stats->total_scores = count($scores);
                } else {
                    $stats->highest_score = 0;
                    $stats->lowest_score = 0;
                    $stats->average_score = 0;
                    $stats->total_scores = 0;
                }
                
                $data['stats'] = $stats;
            }
        }
        
        $this->view('admin/manage_scores', $data);
    }
    
    /**
     * User management with pagination and search
     */
    public function users() {
        // Get filter parameters from GET request
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $role = isset($_GET['role']) ? trim($_GET['role']) : '';
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        $orderBy = isset($_GET['order_by']) ? trim($_GET['order_by']) : 'created_at';
        $orderDir = isset($_GET['order_dir']) ? trim($_GET['order_dir']) : 'DESC';

        // Get pagination parameters
        $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
        // Validate per_page to only allow 20, 50, or 100
        if (!in_array($perPage, [20, 50, 100])) {
            $perPage = 20; // Default to 20 if invalid
        }

        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        if ($page < 1) $page = 1;

        // Get paginated users with search and filtering
        $result = $this->userModel->getPaginatedUsers(
            $page,
            $perPage,
            $search,
            $role,
            $status,
            $orderBy,
            $orderDir
        );

        // Get available roles for filter dropdown
        $availableRoles = [
            'admin' => 'Administrator',
            'coordinator' => 'Show Coordinator',
            'judge' => 'Judge',
            'staff' => 'Show Staff',
            'user' => 'Registered User'
        ];

        // Get available statuses for filter dropdown
        $availableStatuses = [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'pending' => 'Pending'
        ];

        $data = [
            'title' => 'User Management',
            'users' => $result['users'],
            'pagination' => $result['pagination'],
            'filters' => $result['filters'],
            'available_roles' => $availableRoles,
            'available_statuses' => $availableStatuses,
            'current_page' => $page,
            'per_page' => $perPage,
            'total_pages' => $result['pagination']['total_pages'],
            'search' => $search,
            'role_filter' => $role,
            'status_filter' => $status,
            'order_by' => $orderBy,
            'order_dir' => $orderDir
        ];

        $this->view('admin/users/index', $data);
    }
    
    /**
     * Role management with lazy loading
     */
    public function roles() {
        // Get all available roles
        $roles = [
            'admin' => 'Administrator',
            'coordinator' => 'Show Coordinator',
            'judge' => 'Judge',
            'staff' => 'Show Staff',
            'user' => 'Registered User'
        ];

        // Get role counts only (fast query)
        $roleCounts = $this->userModel->getRoleCounts();

        // Don't load users initially - they'll be loaded via AJAX when needed
        $data = [
            'title' => 'Role Management',
            'roles' => $roles,
            'roleCounts' => $roleCounts
        ];

        $this->view('admin/users/roles', $data);
    }

    /**
     * AJAX endpoint for loading users by role with pagination
     */
    public function loadRoleUsers() {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        // Get parameters
        $role = isset($_GET['role']) ? trim($_GET['role']) : '';
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        $orderBy = isset($_GET['order_by']) ? trim($_GET['order_by']) : 'name';
        $orderDir = isset($_GET['order_dir']) ? trim($_GET['order_dir']) : 'ASC';

        // Validate role
        $validRoles = ['admin', 'coordinator', 'judge', 'staff', 'user'];
        if (!in_array($role, $validRoles)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid role']);
            return;
        }

        // Validate per_page
        if (!in_array($perPage, [10, 20, 50])) {
            $perPage = 20;
        }

        try {
            // Get paginated users for the role
            $result = $this->userModel->getPaginatedUsersByRole(
                $role,
                $page,
                $perPage,
                $search,
                $status,
                $orderBy,
                $orderDir
            );

            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'users' => $result['users'],
                'pagination' => $result['pagination'],
                'filters' => $result['filters'],
                'performance' => $result['performance']
            ]);

        } catch (Exception $e) {
            error_log('Error in loadRoleUsers: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load users']);
        }
    }

    /**
     * Bulk role management operations
     */
    public function bulkRoleAction() {
        // Check if this is a POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON input']);
            return;
        }

        $action = $input['action'] ?? '';
        $userIds = $input['user_ids'] ?? [];
        $newRole = $input['new_role'] ?? '';
        $newStatus = $input['new_status'] ?? '';

        // Validate inputs
        if (empty($userIds) || !is_array($userIds)) {
            http_response_code(400);
            echo json_encode(['error' => 'No users selected']);
            return;
        }

        $validRoles = ['admin', 'coordinator', 'judge', 'staff', 'user'];
        $validStatuses = ['active', 'inactive', 'pending'];

        try {
            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($userIds as $userId) {
                $userId = (int)$userId;
                if ($userId <= 0) continue;

                $success = false;

                switch ($action) {
                    case 'change_role':
                        if (in_array($newRole, $validRoles)) {
                            $success = $this->userModel->updateUserRole($userId, $newRole);
                        }
                        break;

                    case 'change_status':
                        if (in_array($newStatus, $validStatuses)) {
                            $success = $this->userModel->updateUserStatus($userId, $newStatus);
                        }
                        break;

                    case 'delete_users':
                        $success = $this->userModel->softDeleteUser($userId);
                        break;

                    default:
                        $errors[] = "Unknown action: $action";
                        continue 2;
                }

                if ($success) {
                    $successCount++;
                } else {
                    $errorCount++;
                    $errors[] = "Failed to update user ID: $userId";
                }
            }

            // Return results
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => "Bulk operation completed: $successCount successful, $errorCount failed",
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ]);

        } catch (Exception $e) {
            error_log('Error in bulkRoleAction: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Bulk operation failed']);
        }
    }
    
    /**
     * Assign role to user
     * 
     * @param int $userId User ID
     */
    public function assignRole($userId) {
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            setFlashMessage('user_error', 'User not found', 'danger');
            $this->redirect('admin/roles');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                setFlashMessage('user_error', 'Invalid request', 'danger');
                $this->redirect('admin/roles');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $role = trim($_POST['role']);
            
            // Validate role
            $validRoles = ['admin', 'coordinator', 'judge', 'user'];
            if (!in_array($role, $validRoles)) {
                setFlashMessage('user_error', 'Invalid role', 'danger');
                $this->redirect('admin/roles');
                return;
            }
            
            // Update user role
            if ($this->userModel->updateUserRole($userId, $role)) {
                setFlashMessage('user_success', 'User role updated successfully', 'success');
                $this->redirect('admin/roles');
            } else {
                setFlashMessage('user_error', 'Failed to update user role', 'danger');
                $this->redirect('admin/roles');
            }
        } else {
            // Get all available roles
            $roles = [
                'admin' => 'Administrator',
                'coordinator' => 'Show Coordinator',
                'judge' => 'Judge',
                'staff' => 'Show Staff',
                'user' => 'Registered User'
            ];
            
            $data = [
                'title' => 'Assign Role',
                'user' => $user,
                'roles' => $roles,
                'current_role' => $user->role
            ];
            
            $this->view('admin/users/assign_role', $data);
        }
    }
    
    /**
     * Add user
     */
    public function addUser() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'name' => trim($_POST['name']),
                'email' => trim($_POST['email']),
                'password' => trim($_POST['password']),
                'role' => trim($_POST['role']),
                'phone' => isset($_POST['phone']) ? trim($_POST['phone']) : null,
                'address' => isset($_POST['address']) ? trim($_POST['address']) : null,
                'city' => isset($_POST['city']) ? trim($_POST['city']) : null,
                'state' => isset($_POST['state']) ? trim($_POST['state']) : null,
                'zip' => isset($_POST['zip']) ? trim($_POST['zip']) : null,
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'role_err' => '',
                'title' => 'Add User'
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter an email';
            } elseif ($this->userModel->emailExists($data['email'])) {
                $data['email_err'] = 'Email is already registered';
            }
            
            // Validate password
            if (empty($data['password'])) {
                $data['password_err'] = 'Please enter a password';
            } elseif (strlen($data['password']) < 6) {
                $data['password_err'] = 'Password must be at least 6 characters';
            }
            
            // Validate role
            if (empty($data['role'])) {
                $data['role_err'] = 'Please select a role';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['email_err']) && 
                empty($data['password_err']) && empty($data['role_err'])) {
                
                // Create user
                $userId = $this->auth->register(
                    $data['name'],
                    $data['email'],
                    $data['password'],
                    $data['role'],
                    $data['phone'],
                    $data['address'],
                    $data['city'],
                    $data['state'],
                    $data['zip']
                );
                
                if ($userId) {
                    // Redirect to users page
                    $this->redirect('admin/users');
                } else {
                    // Log the error for debugging
                    error_log('Failed to create user in AdminController::addUser. Name: ' . $data['name'] . ', Email: ' . $data['email'] . ', Role: ' . $data['role']);
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/users/add', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'email' => '',
                'password' => '',
                'role' => '',
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'role_err' => '',
                'title' => 'Add User'
            ];
            
            // Load view
            $this->view('admin/users/add', $data);
        }
    }
    
    /**
     * Edit user
     * 
     * @param int $id User ID
     */
    public function editUser($id) {
        // Get user
        $user = $this->userModel->getUserById($id);
        
        if (!$user) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'email' => trim($_POST['email']),
                'password' => trim($_POST['password']),
                'role' => trim($_POST['role']),
                'status' => trim($_POST['status']),
                'phone' => isset($_POST['phone']) ? trim($_POST['phone']) : null,
                'address' => isset($_POST['address']) ? trim($_POST['address']) : null,
                'city' => isset($_POST['city']) ? trim($_POST['city']) : null,
                'state' => isset($_POST['state']) ? trim($_POST['state']) : null,
                'zip' => isset($_POST['zip']) ? trim($_POST['zip']) : null,
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'role_err' => '',
                'status_err' => '',
                'title' => 'Edit User'
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter an email';
            } elseif ($this->userModel->emailExists($data['email'], $id)) {
                $data['email_err'] = 'Email is already registered';
            }
            
            // Validate password (only if provided)
            if (!empty($data['password']) && strlen($data['password']) < 6) {
                $data['password_err'] = 'Password must be at least 6 characters';
            }
            
            // Validate role
            if (empty($data['role'])) {
                $data['role_err'] = 'Please select a role';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['email_err']) && 
                empty($data['password_err']) && empty($data['role_err']) && 
                empty($data['status_err'])) {
                
                // Update user
                if ($this->userModel->updateUser($data)) {
                    // Redirect to users page
                    $this->redirect('admin/users');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/users/edit', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'password' => '',
                'role' => $user->role,
                'status' => $user->status,
                'phone' => $user->phone ?? '',
                'address' => $user->address ?? '',
                'city' => $user->city ?? '',
                'state' => $user->state ?? '',
                'zip' => $user->zip ?? '',
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'role_err' => '',
                'status_err' => '',
                'title' => 'Edit User'
            ];
            
            // Load view
            $this->view('admin/users/edit', $data);
        }
    }
    
    /**
     * Delete user
     * 
     * @param int $id User ID
     */
    public function deleteUser($id) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete user
            if ($this->userModel->deleteUser($id)) {
                // Redirect to users page
                $this->redirect('admin/users');
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/users');
        }
    }
    
    /**
     * Comprehensive Show Management Dashboard with Pagination
     * 
     * @param string $status Filter by show status (optional)
     * @param string $sort Sort field (optional)
     * @param string $order Sort order (optional)
     */
    public function shows($status = null, $sort = null, $order = 'asc') {
        // Get filter parameters from GET request if not provided as parameters
        $status = $status ?? ($_GET['status'] ?? 'all');
        $sort = $sort ?? ($_GET['sort'] ?? 'start_date');
        $order = $order ?? ($_GET['order'] ?? 'desc');
        $search = $_GET['search'] ?? '';
        
        // Pagination parameters
        $page = max(1, intval($_GET['page'] ?? 1));
        $perPage = intval($_GET['per_page'] ?? ($_SESSION['shows_per_page'] ?? 25));
        
        // Validate and store per_page preference
        $validPerPageOptions = [10, 25, 50, 100];
        if (!in_array($perPage, $validPerPageOptions)) {
            $perPage = 25;
        }
        $_SESSION['shows_per_page'] = $perPage;
        
        // Validate status filter
        $validStatuses = ['all', 'upcoming', 'active', 'completed', 'cancelled', 'draft', 'payment_pending'];
        if (!in_array($status, $validStatuses)) {
            $status = 'all';
        }
        
        // Validate sort field
        $validSortFields = ['name', 'start_date', 'end_date', 'location', 'status', 'registration_count', 'created_at'];
        if (!in_array($sort, $validSortFields)) {
            $sort = 'start_date';
        }
        
        // Validate sort order
        if (!in_array($order, ['asc', 'desc'])) {
            $order = 'desc';
        }
        
        // Check user role for access control
        $isAdmin = $this->auth->hasRole('admin');
        $currentUserId = $this->auth->getCurrentUserId();
        
        // Build base query with JOINs for better performance
        $baseSql = "FROM shows s 
                    LEFT JOIN users u ON s.coordinator_id = u.id 
                    LEFT JOIN registrations r ON s.id = r.show_id";
        
        $whereConditions = [];
        $params = [];
        
        // Role-based access control
        if (!$isAdmin) {
            // Coordinators can only see their own shows
            $whereConditions[] = "s.coordinator_id = :current_user_id";
            $params[':current_user_id'] = $currentUserId;
        }
        
        // Status filter
        if ($status !== 'all') {
            switch ($status) {
                case 'upcoming':
                    $whereConditions[] = "s.start_date > NOW() AND s.status NOT IN ('cancelled', 'completed')";
                    break;
                case 'active':
                    $whereConditions[] = "s.start_date <= NOW() AND s.end_date >= NOW() AND s.status NOT IN ('cancelled', 'completed')";
                    break;
                case 'payment_pending':
                    $whereConditions[] = "s.listing_paid = 0";
                    break;
                default:
                    $whereConditions[] = "s.status = :status";
                    $params[':status'] = $status;
                    break;
            }
        }
        
        // Search filter
        if (!empty($search)) {
            $whereConditions[] = "(s.name LIKE :search OR s.location LIKE :search OR s.description LIKE :search OR u.name LIKE :search)";
            $params[':search'] = '%' . $search . '%';
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = " WHERE " . implode(' AND ', $whereConditions);
        }
        
        // Get total count for pagination
        $countSql = "SELECT COUNT(DISTINCT s.id) as total " . $baseSql . $whereClause;
        $this->db->query($countSql);
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        $totalRecords = $this->db->single()->total;
        $totalPages = ceil($totalRecords / $perPage);
        
        // Build main query with pagination
        $sql = "SELECT s.*, 
                       u.name as coordinator_name,
                       u.email as coordinator_email,
                       COUNT(DISTINCT r.id) as registration_count,
                       COUNT(DISTINCT CASE WHEN r.payment_status = 'completed' THEN r.id END) as paid_registrations,
                       COUNT(DISTINCT CASE WHEN r.payment_status = 'pending' THEN r.id END) as pending_registrations,
                       SUM(CASE WHEN r.payment_status = 'completed' THEN COALESCE(r.fee, 0) ELSE 0 END) as total_revenue,
                       COUNT(DISTINCT sc.id) as category_count,
                       COUNT(DISTINCT jm.id) as metrics_count
                " . $baseSql . "
                LEFT JOIN show_categories sc ON s.id = sc.show_id
                LEFT JOIN judging_metrics jm ON s.id = jm.show_id
                " . $whereClause . "
                GROUP BY s.id";
        
        // Add ORDER BY
        $orderField = $sort;
        if ($sort === 'registration_count') {
            $orderField = 'COUNT(DISTINCT r.id)';
        } elseif ($sort === 'coordinator_name') {
            $orderField = 'u.name';
        } else {
            $orderField = 's.' . $sort;
        }
        
        $sql .= " ORDER BY {$orderField} {$order}";
        
        // Add LIMIT for pagination
        $offset = ($page - 1) * $perPage;
        $sql .= " LIMIT {$offset}, {$perPage}";
        
        // Execute query
        $this->db->query($sql);
        
        // Bind parameters
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        $shows = $this->db->resultSet();
        
        // Get dashboard statistics
        $statistics = $this->getShowStatistics($isAdmin, $currentUserId);
        
        // Get recent activities
        $recentActivities = $this->getRecentShowActivities($isAdmin, $currentUserId);
        
        // Get upcoming deadlines
        $upcomingDeadlines = $this->getUpcomingShowDeadlines($isAdmin, $currentUserId);
        
        // Build pagination info
        $pagination = [
            'current_page' => $page,
            'per_page' => $perPage,
            'total_records' => $totalRecords,
            'total_pages' => $totalPages,
            'has_prev' => $page > 1,
            'has_next' => $page < $totalPages,
            'prev_page' => max(1, $page - 1),
            'next_page' => min($totalPages, $page + 1),
            'start_record' => $totalRecords > 0 ? $offset + 1 : 0,
            'end_record' => min($offset + $perPage, $totalRecords)
        ];
        
        $data = [
            'title' => 'Show Management Dashboard',
            'shows' => $shows,
            'stats' => $statistics,
            'recent_activities' => $recentActivities,
            'upcoming_deadlines' => $upcomingDeadlines,
            'current_status' => $status,
            'current_sort' => $sort,
            'current_order' => $order,
            'current_search' => $search,
            'pagination' => $pagination,
            'is_admin' => $isAdmin,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/shows', $data);
    }

    /**
     * Add show - shared between admin and coordinator roles
     */
    public function addShow() {
        // Determine if user is admin or coordinator
        $isAdmin = $this->auth->hasRole('admin');
        $userId = $this->auth->getCurrentUserId();
        
        // Get coordinators (for admin only)
        $coordinators = $isAdmin ? $this->userModel->getUsers('coordinator') : [];
        
        // Get listing fee from settings
        $listingFee = $this->settingsModel->getSetting('default_listing_fee', 0);
        $listingFeeType = $this->settingsModel->getSetting('listing_fee_type', 'per_show');
        
        // Load the form designer model and entity template manager
        $formDesignerModel = $this->model('FormDesignerModel');
        $entityTemplateManager = $this->model('EntityTemplateManager');
        
        // First check if there's a default template set in DefaultTemplateManager
        $templateId = $entityTemplateManager->getBestTemplateId('show', 0);
        
        // If a template was found, use it
        if ($templateId) {
            $template = $formDesignerModel->getFormTemplateById($templateId);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("AdminController::addShow - Using template ID {$templateId} from DefaultTemplateManager");
            }
        } else {
            // Fall back to the default admin form template
            $template = $formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("AdminController::addShow - Using default admin_show template");
            }
        }
        
        // If no template exists, create a basic one
        if (!$template) {
            // Log the issue
            error_log('Default show admin form template not found. Creating a basic template.');
            
            // Create a basic template with essential fields including featured image
            $basicFields = [
                (object)[
                    'id' => 'name',
                    'type' => 'text',
                    'label' => 'Show Name',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'location',
                    'type' => 'text',
                    'label' => 'Location',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'description',
                    'type' => 'textarea',
                    'label' => 'Description',
                    'required' => false,
                    'row' => 1,
                    'width' => 'col-md-12'
                ],
                (object)[
                    'id' => 'start_date',
                    'type' => 'date',
                    'label' => 'Start Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'end_date',
                    'type' => 'date',
                    'label' => 'End Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_start',
                    'type' => 'date',
                    'label' => 'Registration Start',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_end',
                    'type' => 'date',
                    'label' => 'Registration End',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'coordinator_id',
                    'type' => 'select',
                    'label' => 'Coordinator',
                    'required' => true,
                    'row' => 4,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'status',
                    'type' => 'select',
                    'label' => 'Status',
                    'required' => true,
                    'row' => 4,
                    'width' => 'col-md-6',
                    'options' => [
                        (object)['value' => 'draft', 'label' => 'Draft'],
                        (object)['value' => 'published', 'label' => 'Published'],
                        (object)['value' => 'cancelled', 'label' => 'Cancelled'],
                        (object)['value' => 'completed', 'label' => 'Completed']
                    ]
                ],
                (object)[
                    'id' => 'listing_paid',
                    'type' => 'checkbox',
                    'label' => 'Listing Fee Paid',
                    'required' => false,
                    'row' => 5,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'featured_image',
                    'type' => 'image',
                    'label' => 'Featured Image',
                    'required' => false,
                    'row' => 6,
                    'width' => 'col-md-6',
                    'placeholder' => 'Select a featured image for this show'
                ]
            ];
            
            // Create a temporary template object
            $template = (object)[
                'id' => 0,
                'name' => 'Default Show Admin Form (Temporary)',
                'type' => 'admin_show',
                'entity_id' => 0,
                'fields' => json_encode($basicFields)
            ];
            
            // Add a flash message to inform the admin
            if (method_exists($this, 'setFlash')) {
                $this->setFlash('warning', 'Using a basic show form template. Please customize it by clicking "Edit Form Template".');
            } elseif (function_exists('setFlashMessage')) {
                setFlashMessage('admin_warning', 'Using a basic show form template. Please customize it by clicking "Edit Form Template".', 'warning');
            } else {
                // If no flash message function exists, just log it
                error_log('Using a basic show form template. Please customize it by clicking "Edit Form Template".');
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data - start with standard fields
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => convertUserDateTimeToUTC(trim($_POST['start_date']), $this->auth->getCurrentUserId()),
                'end_date' => convertUserDateTimeToUTC(trim($_POST['end_date']), $this->auth->getCurrentUserId()),
                'registration_start' => convertUserDateTimeToUTC(trim($_POST['registration_start']), $this->auth->getCurrentUserId()),
                'registration_end' => convertUserDateTimeToUTC(trim($_POST['registration_end']), $this->auth->getCurrentUserId()),
                'status' => trim($_POST['status']),
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? (bool)$_POST['fan_voting_enabled'] : true,
                'is_free' => isset($_POST['is_free']) ? (bool)$_POST['is_free'] : false,
                // If is_free is checked, always set registration_fee to 0
                'registration_fee' => (isset($_POST['is_free']) && $_POST['is_free']) ? 0.00 : (isset($_POST['registration_fee']) ? $_POST['registration_fee'] : 0.00),
                'listing_fee' => $listingFee,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'status_err' => '',
                'title' => $isAdmin ? 'Add Show' : 'Create Show',
                'coordinators' => $coordinators,
                'template' => $template,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType,
                'is_admin' => $isAdmin
            ];
            
            // Set coordinator_id based on role
            if ($isAdmin) {
                $data['coordinator_id'] = trim($_POST['coordinator_id']);
                $data['coordinator_id_err'] = '';
                $data['listing_paid'] = isset($_POST['listing_paid']) ? (bool)$_POST['listing_paid'] : false;
                $data['featured_image_id'] = isset($_POST['featured_image_id']) ? $_POST['featured_image_id'] : null;
                $data['featured_image'] = isset($_POST['featured_image_text']) ? $_POST['featured_image_text'] : '';
            } else {
                $data['coordinator_id'] = $userId; // Always assign to current coordinator
                $data['listing_paid'] = false; // Not paid yet for coordinators
                
                // Add these fields for coordinators too
                $data['featured_image_id'] = isset($_POST['featured_image_id']) ? $_POST['featured_image_id'] : null;
                $data['featured_image'] = isset($_POST['featured_image_text']) ? $_POST['featured_image_text'] : '';
            }
            
            // Get custom fields from the template
            $templateFields = json_decode($template->fields, true);
            if (is_array($templateFields)) {
                foreach ($templateFields as $field) {
                    // Skip if field doesn't have an ID or is a system field
                    if (!isset($field['id']) || in_array($field['id'], [
                        'id', 'created_at', 'updated_at', 'name', 'description', 'location', 
                        'start_date', 'end_date', 'registration_start', 'registration_end', 
                        'coordinator_id', 'status', 'fan_voting_enabled', 'registration_fee', 
                        'is_free', 'listing_fee', 'listing_paid', 'featured_image_id'
                    ])) {
                        continue;
                    }
                    
                    // Skip section dividers and HTML fields (they don't need database columns)
                    if (isset($field['type']) && in_array($field['type'], ['section', 'html'])) {
                        continue;
                    }
                    
                    // Add the field to the data array
                    $fieldId = $field['id'];
                    if (isset($_POST[$fieldId])) {
                        // Handle different field types
                        switch ($field['type']) {
                            case 'checkbox':
                                $data[$fieldId] = isset($_POST[$fieldId]) ? true : false;
                                break;
                            case 'number':
                                $data[$fieldId] = is_numeric($_POST[$fieldId]) ? $_POST[$fieldId] : 0;
                                break;
                            default:
                                $data[$fieldId] = trim($_POST[$fieldId]);
                        }
                    } else {
                        // Set default value if field is not in POST data
                        $data[$fieldId] = isset($field['default']) ? $field['default'] : null;
                    }
                }
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif (!empty($data['start_date']) && !empty($data['end_date'])) {
                try {
                    $startDateTime = new DateTime($data['start_date']);
                    $endDateTime = new DateTime($data['end_date']);
                    
                    if ($endDateTime <= $startDateTime) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['end_date'] <= $data['start_date']) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("AdminController::addShow - DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            if (empty($data['registration_start'])) {
                $data['registration_start_err'] = 'Please enter a registration start date';
            }
            
            if (empty($data['registration_end'])) {
                $data['registration_end_err'] = 'Please enter a registration end date';
            } elseif (!empty($data['registration_start']) && !empty($data['registration_end'])) {
                try {
                    $regStartDateTime = new DateTime($data['registration_start']);
                    $regEndDateTime = new DateTime($data['registration_end']);
                    
                    if ($regEndDateTime <= $regStartDateTime) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    } elseif (!empty($data['start_date'])) {
                        $showStartDateTime = new DateTime($data['start_date']);
                        if ($regEndDateTime > $showStartDateTime) {
                            $data['registration_end_err'] = 'Registration must end before the show starts';
                        }
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['registration_end'] <= $data['registration_start']) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    } elseif (!empty($data['start_date']) && $data['registration_end'] > $data['start_date']) {
                        $data['registration_end_err'] = 'Registration must end before the show starts';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("AdminController::addShow - Registration DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            // Validate coordinator for admin only
            if ($isAdmin && empty($data['coordinator_id'])) {
                $data['coordinator_id_err'] = 'Please select a coordinator';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            $hasErrors = !empty($data['name_err']) || !empty($data['location_err']) || 
                        !empty($data['start_date_err']) || !empty($data['end_date_err']) || 
                        !empty($data['registration_start_err']) || !empty($data['registration_end_err']) || 
                        !empty($data['status_err']);
                        
            // Add coordinator_id_err check only for admin
            if ($isAdmin) {
                $hasErrors = $hasErrors || !empty($data['coordinator_id_err']);
            }
            
            if (!$hasErrors) {
                // Debug logging for form submission
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("AdminController: Creating show with data keys: " . implode(', ', array_keys($data)));
                }
                
                // For coordinators, check if they need to pay listing fee
                if (!$isAdmin) {
                    // Check if coordinator is exempt from listing fees
                    $this->db->query('SELECT exempt_from_listing_fees FROM users WHERE id = :id');
                    $this->db->bind(':id', $userId);
                    $user = $this->db->single();
                    $isExempt = $user && $user->exempt_from_listing_fees;
                    
                    // Check if coordinator is pre-approved
                    $this->db->query('SELECT * FROM pre_approved_cords 
                                     WHERE coordinator_id = :coordinator_id 
                                     AND begin_date <= CURDATE() 
                                     AND end_date >= CURDATE()');
                    $this->db->bind(':coordinator_id', $userId);
                    $preApproved = $this->db->single();
                    $isPreApproved = !empty($preApproved);
                    
                    // If coordinator is exempt or pre-approved, mark listing as paid
                    if ($isExempt || $isPreApproved) {
                        $data['listing_paid'] = true;
                    } else {
                        // For coordinators who need to pay, set status to payment_pending
                        // unless admin has explicitly set a different status
                        $data['status'] = 'payment_pending';
                    }
                }
                
                // Create show
                $showId = $this->showModel->createShow($data);
                
                if ($showId) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("AdminController: Successfully created show with ID: {$showId}");
                    }
                    
                    // If admin created the show
                    if ($isAdmin) {
                        $this->setFlashMessage('admin_message', 'Show created successfully', 'success');
                        $this->redirect('admin/shows');
                    } 
                    // If coordinator created the show
                    else {
                        // Get coordinator info for email notification
                        $coordinator = $this->userModel->getUserById($userId);
                        $show = $this->showModel->getShowById($showId);
                        
                        // Send email notification to admins
                        $emailService = $this->model('EmailService');
                        $emailService->sendNewShowNotification($show, $coordinator);
                        
                        // Set success message
                        $this->setFlashMessage('coordinator_message', 'Show created successfully', 'success');
                        
                        // If exempt or pre-approved, redirect to show page
                        if ($isExempt || $isPreApproved) {
                            if ($isExempt) {
                                $this->setFlashMessage('coordinator_message', 'Show created successfully. Listing fee waived based on your account privileges.', 'success');
                            } else {
                                $this->setFlashMessage('coordinator_message', 'Show created successfully. Listing fee waived based on your pre-approved status.', 'success');
                            }
                            $this->redirect('coordinator/show/' . $showId);
                        } 
                        // Otherwise, redirect to payment page
                        else if ($listingFee > 0) {
                            $this->redirect('payment/showListing/' . $showId);
                        } else {
                            // If no listing fee, redirect to show page
                            $this->redirect('coordinator/show/' . $showId);
                        }
                    }
                } else {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("AdminController: Failed to create show");
                    }
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/shows/add_with_template', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'location' => '',
                'start_date' => '',
                'end_date' => '',
                'registration_start' => '',
                'registration_end' => '',
                'status' => 'draft',
                'fan_voting_enabled' => true,
                'registration_fee' => 0.00,
                'is_free' => false,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType,
                'listing_paid' => false,
                'featured_image_id' => '',
                'featured_image' => '',
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'status_err' => '',
                'title' => $isAdmin ? 'Add Show' : 'Create Show',
                'coordinators' => $coordinators,
                'template' => $template,
                'is_admin' => $isAdmin,
                'data' => [] // This will hold all field values for the template
            ];
            
            // Set coordinator-specific fields
            if ($isAdmin) {
                $data['coordinator_id'] = '';
                $data['coordinator_id_err'] = '';
            } else {
                $data['coordinator_id'] = $userId; // Always assign to current coordinator
                // No need to set error field for coordinators as they won't see this field
            }
            
            // Extract template fields to pre-populate with default values
            if ($template && isset($template->fields)) {
                $fields = json_decode($template->fields, true);
                if ($fields) {
                    foreach ($fields as $field) {
                        if (isset($field['id']) && isset($field['default'])) {
                            $data['data'][$field['id']] = $field['default'];
                        } else if (isset($field['id'])) {
                            $data['data'][$field['id']] = '';
                        }
                    }
                }
            }
            
            // Load view with template
            $this->view('admin/shows/add_with_template', $data);
        }
    }
    
    /**
     * Edit show
     * 
     * @param int $id Show ID
     */
    public function editShow($id) {
        // Store the referring page URL in session for later redirect
        // Only store it on GET requests (when loading the form)
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            if (isset($_SERVER['HTTP_REFERER'])) {
                $_SESSION['previous_page'] = $_SERVER['HTTP_REFERER'];
                error_log("AdminController::editShow - Stored previous page: " . $_SESSION['previous_page']);
            }
        }
        
        // Debug function for POST data
        $debugPostData = function() {
            error_log("AdminController::editShow - POST data:");
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'field_') === 0) {
                    $valuePreview = is_string($value) ? substr($value, 0, 100) . (strlen($value) > 100 ? '...' : '') : gettype($value);
                    error_log("  {$key}: {$valuePreview}");
                }
            }
        };
        
        // If this is a POST request, debug the data
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $debugPostData();
            
            // Specifically check for the 'test' field
            if (isset($_POST['test'])) {
                error_log("AdminController::editShow - TEST FIELD FOUND IN POST DATA: " . $_POST['test']);
            } else {
                error_log("AdminController::editShow - TEST FIELD NOT FOUND IN POST DATA");
            }
        }
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get coordinators
        $coordinators = $this->userModel->getUsers('coordinator');
        
        // Get categories and judging metrics for this show
        $categories = $this->showModel->getShowCategories($id);
        $metrics = $this->showModel->getJudgingMetrics($id);
        
        // Get age weights for this show
        $ageWeights = $this->judgingModel->getAgeWeightsByShowId($id);
        
        // Load the FormDesignerModel and EntityTemplateManager
        $formDesignerModel = $this->model('FormDesignerModel');
        $entityTemplateManager = $this->model('EntityTemplateManager');
        
        // First check if there's an entity-specific or default template
        $templateId = $entityTemplateManager->getBestTemplateId('show', $id);
        
        // If a template was found, use it
        if ($templateId) {
            $formTemplate = $formDesignerModel->getFormTemplateById($templateId);
            error_log("AdminController::editShow - Using template ID {$templateId} for show {$id}");
        } else {
            // Fall back to the default admin form template
            $formTemplate = $formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
            error_log("AdminController::editShow - Using default admin_show template for show {$id}");
        }
        
        // Synchronize form fields with database columns
        require_once APPROOT . '/models/FormFieldSynchronizer.php';
        $formFieldSynchronizer = new FormFieldSynchronizer();
        $formFieldSynchronizer->synchronizeTemplate($formTemplate);
        
        // If no default template exists, create one with default fields
        if (!$formTemplate) {
            // Get default fields and convert 'id' to 'name' for compatibility
            $defaultFields = $formDesignerModel->getDefaultFields('admin_show');
            foreach ($defaultFields as &$field) {
                if (isset($field['id']) && !isset($field['name'])) {
                    $field['name'] = $field['id'];
                }
            }
            
            $templateData = [
                'name' => 'Default Show Admin Form',
                'type' => 'admin_show',
                'entity_id' => 0,
                'fields' => json_encode($defaultFields)
            ];
            
            $templateId = $formDesignerModel->createFormTemplate($templateData);
            $formTemplate = $formDesignerModel->getFormTemplateById($templateId);
        }
        
        // Decode the form fields and ensure they have 'name' attribute
        $formFields = json_decode($formTemplate->fields, true);
        
        // Check if form fields are valid
        if (!is_array($formFields) || empty($formFields)) {
            // Log the issue
            error_log('Invalid or empty form fields in template ID: ' . $formTemplate->id);
            
            // If form fields are not valid, create default fields
            $formFields = $formDesignerModel->getDefaultFields('admin_show');
            
            // Update the template with valid fields
            $updateData = [
                'id' => $formTemplate->id,
                'fields' => json_encode($formFields)
            ];
            $formDesignerModel->updateFormTemplate($formTemplate->id, $updateData);
        }
        
        // Ensure all fields have a name attribute
        foreach ($formFields as &$field) {
            if (isset($field['id']) && !isset($field['name'])) {
                $field['name'] = $field['id'];
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data - start with standard fields
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => convertUserDateTimeToUTC(trim($_POST['start_date']), $this->auth->getCurrentUserId()),
                'end_date' => convertUserDateTimeToUTC(trim($_POST['end_date']), $this->auth->getCurrentUserId()),
                'registration_start' => convertUserDateTimeToUTC(trim($_POST['registration_start']), $this->auth->getCurrentUserId()),
                'registration_end' => convertUserDateTimeToUTC(trim($_POST['registration_end']), $this->auth->getCurrentUserId()),
                'coordinator_id' => trim($_POST['coordinator_id']),
                'status' => trim($_POST['status']),
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? 1 : 0, // Store as integer (0 or 1)
                'is_free' => isset($_POST['is_free']) ? 1 : 0, // Store as integer (0 or 1)
                // If is_free is checked, always set registration_fee to 0
                'registration_fee' => (isset($_POST['is_free']) && $_POST['is_free']) ? 0.00 : (isset($_POST['registration_fee']) ? $_POST['registration_fee'] : 0.00),
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'coordinator_id_err' => '',
                'status_err' => '',
                'title' => 'Edit Show',
                'coordinators' => $coordinators
            ];
            
            // Get custom fields from the form fields
            if (is_array($formFields)) {
                foreach ($formFields as $field) {
                    // Skip if field doesn't have an ID or is a system field
                    if (!isset($field['id']) || in_array($field['id'], [
                        'id', 'created_at', 'updated_at', 'name', 'description', 'location', 
                        'start_date', 'end_date', 'registration_start', 'registration_end', 
                        'coordinator_id', 'status', 'fan_voting_enabled', 'registration_fee', 
                        'is_free', 'featured_image_id'
                    ])) {
                        continue;
                    }
                    
                    // Skip section dividers and HTML fields (they don't need database columns)
                    if (isset($field['type']) && in_array($field['type'], ['section', 'html'])) {
                        continue;
                    }
                    
                    // Add the field to the data array
                    $fieldId = $field['id'];
                    if (isset($_POST[$fieldId])) {
                        // Handle different field types
                        switch ($field['type']) {
                            case 'checkbox':
                                $data[$fieldId] = isset($_POST[$fieldId]) ? true : false;
                                break;
                            case 'number':
                                $data[$fieldId] = is_numeric($_POST[$fieldId]) ? $_POST[$fieldId] : 0;
                                break;
                            default:
                                $data[$fieldId] = trim($_POST[$fieldId]);
                        }
                    } else {
                        // Set default value if field is not in POST data
                        $data[$fieldId] = isset($field['default']) ? $field['default'] : null;
                    }
                }
            }
            
            // Process any additional fields from the form template
            foreach ($formFields as $field) {
                // Use id if available, otherwise use name
                $fieldName = isset($field['id']) ? $field['id'] : (isset($field['name']) ? $field['name'] : null);
                
                if (!$fieldName) {
                    continue; // Skip fields without a name or id
                }
                
                // Special handling for complex field types
                if (isset($field['type'])) {
                    // Fields that need special handling with hidden marker fields
                    if (in_array($field['type'], ['textarea', 'richtext', 'file'])) {
                        // Check if the field was included in the form (using our hidden marker field)
                        if (isset($_POST['_has_' . $fieldName])) {
                            // If the field exists in POST data, use it
                            if (isset($_POST[$fieldName])) {
                                $data[$fieldName] = $_POST[$fieldName];
                                error_log("AdminController: Added {$field['type']} field {$fieldName} from POST data with length: " . strlen($_POST[$fieldName]));
                            } 
                            // Otherwise, set it to an empty string to ensure it's included in the update
                            else {
                                $data[$fieldName] = '';
                                error_log("AdminController: Added empty {$field['type']} field {$fieldName}");
                            }
                        }
                    }
                    // Special handling for checkbox fields (they're not submitted when unchecked)
                    else if ($field['type'] === 'checkbox') {
                        $data = $this->processCheckboxField($data, $fieldName);
                        error_log("AdminController: Processed checkbox field {$fieldName} with value: " . $data[$fieldName]);
                    }
                    // Special handling for number fields
                    else if ($field['type'] === 'number') {
                        $data[$fieldName] = isset($_POST[$fieldName]) && is_numeric($_POST[$fieldName]) ? $_POST[$fieldName] : 0;
                        error_log("AdminController: Added number field {$fieldName} with value: {$data[$fieldName]}");
                    }
                }
                // For other fields, just add them if they exist in POST data
                else if (isset($_POST[$fieldName]) && !isset($data[$fieldName])) {
                    $data[$fieldName] = trim($_POST[$fieldName]);
                }
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif (!empty($data['start_date']) && !empty($data['end_date'])) {
                try {
                    $startDateTime = new DateTime($data['start_date']);
                    $endDateTime = new DateTime($data['end_date']);
                    
                    if ($endDateTime <= $startDateTime) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['end_date'] <= $data['start_date']) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("AdminController::editShow - DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            if (empty($data['registration_start'])) {
                $data['registration_start_err'] = 'Please enter a registration start date';
            }
            
            if (empty($data['registration_end'])) {
                $data['registration_end_err'] = 'Please enter a registration end date';
            } elseif (!empty($data['registration_start']) && !empty($data['registration_end'])) {
                try {
                    $regStartDateTime = new DateTime($data['registration_start']);
                    $regEndDateTime = new DateTime($data['registration_end']);
                    
                    if ($regEndDateTime <= $regStartDateTime) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    } elseif (!empty($data['start_date'])) {
                        $showStartDateTime = new DateTime($data['start_date']);
                        if ($regEndDateTime > $showStartDateTime) {
                            $data['registration_end_err'] = 'Registration must end before the show starts';
                        }
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['registration_end'] <= $data['registration_start']) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    } elseif (!empty($data['start_date']) && $data['registration_end'] > $data['start_date']) {
                        $data['registration_end_err'] = 'Registration must end before the show starts';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("AdminController::editShow - Registration DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            // Validate coordinator
            if (empty($data['coordinator_id'])) {
                $data['coordinator_id_err'] = 'Please select a coordinator';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['location_err']) && 
                empty($data['start_date_err']) && empty($data['end_date_err']) && 
                empty($data['registration_start_err']) && empty($data['registration_end_err']) && 
                empty($data['coordinator_id_err']) && empty($data['status_err'])) {
                
                // Debug logging for form submission
                error_log("AdminController: Updating show with data keys: " . implode(', ', array_keys($data)));
                
                // Debug logging for form submission
                error_log("AdminController: Processing form data with " . count($data) . " fields");
                
                // Process any fields that might be in POST but not in the form data
                foreach ($_POST as $key => $value) {
                    // Skip special fields and fields already in data
                    if (strpos($key, '_') === 0 || isset($data[$key])) {
                        continue;
                    }
                    
                    // Process all custom fields - not just those starting with 'field_'
                    // Skip standard fields which are already handled
                    if (!in_array($key, [
                        'id', 'name', 'description', 'location', 'start_date', 'end_date',
                        'registration_start', 'registration_end', 'coordinator_id', 'status',
                        'fan_voting_enabled', 'registration_fee', 'is_free', 'listing_fee',
                        'csrf_token', 'submit'
                    ])) {
                        $data[$key] = $value;
                        error_log("AdminController: Added custom field {$key} from POST data with length: " . (is_string($value) ? strlen($value) : gettype($value)));
                    }
                }
                
                // Check for hidden marker fields
                foreach ($_POST as $key => $value) {
                    if (strpos($key, '_has_field_') === 0) {
                        $fieldName = str_replace('_has_', '', $key);
                        if (!isset($data[$fieldName])) {
                            $data[$fieldName] = '';
                            error_log("AdminController: Added empty value for {$fieldName} based on marker field");
                        }
                    }
                }
                
                // Update show
                if ($this->showModel->updateShow($data)) {
                    // Set success message
                    $this->setFlashMessage('success', 'Show updated successfully');
                    
                    // Set a cache buster in the session to ensure HTML content is refreshed
                    $_SESSION['cache_buster'] = time();
                    
                    // Check if we have a stored previous page URL in the session
                    if (isset($_SESSION['previous_page']) && !empty($_SESSION['previous_page'])) {
                        $previousPage = $_SESSION['previous_page'];
                        // Clear the stored URL
                        unset($_SESSION['previous_page']);
                        
                        // Add cache buster to URL to ensure changes are visible
                        $urlCacheBuster = (strpos($previousPage, '?') !== false) ? '&' : '?';
                        $urlCacheBuster .= 'updated=' . $_SESSION['cache_buster'];
                        
                        // Redirect to the previous page
                        header('Location: ' . $previousPage . $urlCacheBuster);
                        exit;
                    } else {
                        // Fallback to shows page if no previous page is stored
                        $this->redirect('admin/shows');
                    }
                } else {
                    error_log("AdminController: Failed to update show");
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Add form fields and template to data
                $data['formFields'] = $formFields;
                $data['formTemplate'] = $formTemplate;
                $data['categories'] = $categories;
                $data['metrics'] = $metrics;
                $data['ageWeights'] = $ageWeights;
                $data['show'] = $show;
                
                // Load view with errors
                $this->view('admin/shows/edit_with_template', $data);
            }
        } else {
            // Load the CustomFieldRetriever to map database columns to form fields
            require_once APPROOT . '/models/CustomFieldRetriever.php';
            $customFieldRetriever = new CustomFieldRetriever();
            
            // Map database columns to form fields
            $mappedData = $customFieldRetriever->mapDatabaseToForm($show);
            
            // Debug output
            error_log("AdminController::editShow - Show data before mapping:");
            foreach ($show as $key => $value) {
                if (strpos($key, 'custom_field_') === 0) {
                    error_log("  {$key}: " . (is_string($value) ? $value : gettype($value)));
                }
            }
            
            error_log("AdminController::editShow - Mapped data:");
            foreach ($mappedData as $key => $value) {
                if (!property_exists($show, $key)) {
                    error_log("  {$key}: " . (is_string($value) ? $value : gettype($value)));
                }
            }
            
            // Extract all properties from the show object
            $showData = [];
            foreach ($show as $key => $value) {
                // Skip date fields that need timezone conversion - they're handled separately
                if (!in_array($key, ['start_date', 'end_date', 'registration_start', 'registration_end'])) {
                    $showData[$key] = $value;
                }
                // Log all show data for debugging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("Show data: {$key} = " . (is_string($value) ? $value : var_export($value, true)));
                }
                
                // Special debugging for status field
                if ($key === 'status' && defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("STATUS FIELD IN CONTROLLER:");
                    error_log("  Raw value: " . var_export($value, true));
                    error_log("  Type: " . gettype($value));
                    error_log("  Length: " . strlen($value));
                    error_log("  Hex representation: " . bin2hex($value));
                    error_log("  After trim: '" . trim($value) . "'");
                    error_log("  Lowercase: '" . strtolower(trim($value)) . "'");
                }
            }
            
            // Convert UTC dates to user timezone for display in datetime-local format
            $userStartDate = convertUTCToUserDateTime($show->start_date, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');
            $userEndDate = convertUTCToUserDateTime($show->end_date, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');
            $userRegStart = convertUTCToUserDateTime($show->registration_start, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');
            $userRegEnd = convertUTCToUserDateTime($show->registration_end, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');
            
            // Debug timezone conversion
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("AdminController::editShow - Timezone conversion for show {$show->id}:");
                error_log("  UTC start_date: {$show->start_date} -> User: {$userStartDate}");
                error_log("  UTC end_date: {$show->end_date} -> User: {$userEndDate}");
                error_log("  UTC reg_start: {$show->registration_start} -> User: {$userRegStart}");
                error_log("  UTC reg_end: {$show->registration_end} -> User: {$userRegEnd}");
            }
            
            // Merge with basic data, but ensure timezone-converted dates are preserved
            $baseData = [
                'id' => $show->id,
                'name' => $show->name,
                'description' => $show->description,
                'location' => $show->location,
                'start_date' => $userStartDate,
                'end_date' => $userEndDate,
                'registration_start' => $userRegStart,
                'registration_end' => $userRegEnd,
                'coordinator_id' => $show->coordinator_id,
                'status' => $show->status,
                'fan_voting_enabled' => isset($show->fan_voting_enabled) ? $show->fan_voting_enabled : true,
                'registration_fee' => isset($show->registration_fee) ? $show->registration_fee : 0,
                'is_free' => isset($show->is_free) ? $show->is_free : 0,
                'listing_fee' => isset($show->listing_fee) ? $show->listing_fee : 0,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'coordinator_id_err' => '',
                'status_err' => '',
                'title' => 'Edit Show',
                'coordinators' => $coordinators,
                'categories' => $categories,
                'metrics' => $metrics,
                'ageWeights' => $ageWeights,
                'show' => $show,
                'formFields' => $formFields,
                'formTemplate' => $formTemplate
            ];
            
            // First merge the base data with mapped data
            $data = array_merge($baseData, $mappedData);
            
            // Then merge with show data, but preserve the timezone-converted date fields
            $data = array_merge($showData, $data);
            
            // Ensure timezone-converted dates are preserved by explicitly setting them again
            $data['start_date'] = $userStartDate;
            $data['end_date'] = $userEndDate;
            $data['registration_start'] = $userRegStart;
            $data['registration_end'] = $userRegEnd;
            
            // Debug the final data array
            error_log("Final data array keys: " . implode(', ', array_keys($data)));
            
            // Debug final timezone values being sent to view
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("AdminController::editShow - Final data being sent to view:");
                error_log("  start_date: " . ($data['start_date'] ?? 'NOT SET'));
                error_log("  end_date: " . ($data['end_date'] ?? 'NOT SET'));
                error_log("  registration_start: " . ($data['registration_start'] ?? 'NOT SET'));
                error_log("  registration_end: " . ($data['registration_end'] ?? 'NOT SET'));
            }
            
            // Load view with template
            $this->view('admin/shows/edit_with_template', $data);
        }
    }
    
    /**
     * Add default categories to a show
     * 
     * @param int $id Show ID
     */
    public function addDefaultCategoriesToShow($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Add default categories to show
        if ($this->defaultCategoryModel->addDefaultCategoriesToShow($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'Default categories have been added to the show.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to add default categories to the show.'
            ];
        }
        
        // Redirect back to edit show page
        $this->redirect('admin/editShow/' . $id);
    }
    
    /**
     * Add default metrics to a show
     * 
     * @param int $id Show ID
     */
    public function addDefaultMetricsToShow($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Add default metrics to show
        if ($this->defaultMetricModel->addDefaultMetricsToShow($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'Default metrics have been added to the show.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to add default metrics to the show.'
            ];
        }
        
        // Redirect back to edit show page
        $this->redirect('admin/editShow/' . $id);
    }
    
    /**
     * Default Categories
     */
    public function defaultCategories() {
        // Get all default categories
        $defaultCategories = $this->defaultCategoryModel->getDefaultCategories();
        
        $data = [
            'title' => 'Default Categories',
            'defaultCategories' => $defaultCategories,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/default_categories', $data);
    }
    
    /**
     * Add Default Category
     */
    public function addDefaultCategory() {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'registration_fee' => isset($_POST['registration_fee']) ? floatval($_POST['registration_fee']) : 0,
                'max_entries' => isset($_POST['max_entries']) ? intval($_POST['max_entries']) : 0,
                'name_err' => '',
                'description_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['description_err']) && 
                empty($data['registration_fee_err']) && empty($data['max_entries_err'])) {
                
                // Add default category
                if ($this->defaultCategoryModel->addDefaultCategory($data)) {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Default category added successfully.'
                    ];
                    
                    // Redirect to default categories page
                    $this->redirect('admin/defaultCategories');
                } else {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'danger',
                        'message' => 'Failed to add default category.'
                    ];
                    
                    // Load view with errors
                    $this->view('admin/add_default_category', $data);
                }
            } else {
                // Load view with errors
                $this->view('admin/add_default_category', $data);
            }
        } else {
            $data = [
                'title' => 'Add Default Category',
                'name' => '',
                'description' => '',
                'registration_fee' => 0,
                'max_entries' => 0,
                'name_err' => '',
                'description_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/add_default_category', $data);
        }
    }
    
    /**
     * Edit Default Category
     * 
     * @param int $id Default category ID
     */
    public function editDefaultCategory($id) {
        // Get default category
        $defaultCategory = $this->defaultCategoryModel->getDefaultCategoryById($id);
        
        if (!$defaultCategory) {
            $this->redirect('home/not_found');
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'registration_fee' => isset($_POST['registration_fee']) ? floatval($_POST['registration_fee']) : 0,
                'max_entries' => isset($_POST['max_entries']) ? intval($_POST['max_entries']) : 0,
                'name_err' => '',
                'description_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['description_err']) && 
                empty($data['registration_fee_err']) && empty($data['max_entries_err'])) {
                
                // Update default category
                if ($this->defaultCategoryModel->updateDefaultCategory($data)) {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Default category updated successfully.'
                    ];
                    
                    // Redirect to default categories page
                    $this->redirect('admin/defaultCategories');
                } else {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'danger',
                        'message' => 'Failed to update default category.'
                    ];
                    
                    // Load view with errors
                    $this->view('admin/edit_default_category', $data);
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_default_category', $data);
            }
        } else {
            $data = [
                'title' => 'Edit Default Category',
                'id' => $defaultCategory->id,
                'name' => $defaultCategory->name,
                'description' => $defaultCategory->description,
                'registration_fee' => $defaultCategory->registration_fee,
                'max_entries' => $defaultCategory->max_entries,
                'name_err' => '',
                'description_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/edit_default_category', $data);
        }
    }
    
    /**
     * Delete Default Category
     * 
     * @param int $id Default category ID
     */
    public function deleteDefaultCategory($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get default category
        $defaultCategory = $this->defaultCategoryModel->getDefaultCategoryById($id);
        
        if (!$defaultCategory) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Delete default category
        if ($this->defaultCategoryModel->deleteDefaultCategory($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'Default category deleted successfully.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to delete default category.'
            ];
        }
        
        // Redirect to default categories page
        $this->redirect('admin/defaultCategories');
    }
    
    /**
     * Default Metrics
     */
    public function defaultMetrics() {
        // Get all default metrics
        $defaultMetrics = $this->defaultMetricModel->getDefaultMetrics();
        
        $data = [
            'title' => 'Default Metrics',
            'defaultMetrics' => $defaultMetrics,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/default_metrics', $data);
    }
    
    /**
     * Add Default Metric
     */
    public function addDefaultMetric() {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'weight' => isset($_POST['weight']) ? intval($_POST['weight']) : 1,
                'max_score' => isset($_POST['max_score']) ? intval($_POST['max_score']) : 10,
                'name_err' => '',
                'description_err' => '',
                'weight_err' => '',
                'max_score_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['description_err']) && 
                empty($data['weight_err']) && empty($data['max_score_err'])) {
                
                // Add default metric
                if ($this->defaultMetricModel->addDefaultMetric($data)) {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Default metric added successfully.'
                    ];
                    
                    // Redirect to default metrics page
                    $this->redirect('admin/defaultMetrics');
                } else {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'danger',
                        'message' => 'Failed to add default metric.'
                    ];
                    
                    // Load view with errors
                    $this->view('admin/add_default_metric', $data);
                }
            } else {
                // Load view with errors
                $this->view('admin/add_default_metric', $data);
            }
        } else {
            $data = [
                'title' => 'Add Default Metric',
                'name' => '',
                'description' => '',
                'weight' => 1,
                'max_score' => 10,
                'name_err' => '',
                'description_err' => '',
                'weight_err' => '',
                'max_score_err' => '',
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/add_default_metric', $data);
        }
    }
    
    /**
     * Edit Default Metric
     * 
     * @param int $id Default metric ID
     */
    public function editDefaultMetric($id) {
        // Get default metric
        $defaultMetric = $this->defaultMetricModel->getDefaultMetricById($id);
        
        if (!$defaultMetric) {
            $this->redirect('home/not_found');
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'weight' => isset($_POST['weight']) ? intval($_POST['weight']) : 1,
                'max_score' => isset($_POST['max_score']) ? intval($_POST['max_score']) : 10,
                'name_err' => '',
                'description_err' => '',
                'weight_err' => '',
                'max_score_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['description_err']) && 
                empty($data['weight_err']) && empty($data['max_score_err'])) {
                
                // Update default metric
                if ($this->defaultMetricModel->updateDefaultMetric($data)) {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Default metric updated successfully.'
                    ];
                    
                    // Redirect to default metrics page
                    $this->redirect('admin/defaultMetrics');
                } else {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'danger',
                        'message' => 'Failed to update default metric.'
                    ];
                    
                    // Load view with errors
                    $this->view('admin/edit_default_metric', $data);
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_default_metric', $data);
            }
        } else {
            $data = [
                'title' => 'Edit Default Metric',
                'id' => $defaultMetric->id,
                'name' => $defaultMetric->name,
                'description' => $defaultMetric->description,
                'weight' => $defaultMetric->weight,
                'max_score' => $defaultMetric->max_score,
                'name_err' => '',
                'description_err' => '',
                'weight_err' => '',
                'max_score_err' => '',
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/edit_default_metric', $data);
        }
    }
    
    /**
     * Delete Default Metric
     * 
     * @param int $id Default metric ID
     */
    public function deleteDefaultMetric($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get default metric
        $defaultMetric = $this->defaultMetricModel->getDefaultMetricById($id);
        
        if (!$defaultMetric) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Delete default metric
        if ($this->defaultMetricModel->deleteDefaultMetric($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'Default metric deleted successfully.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to delete default metric.'
            ];
        }
        
        // Redirect to default metrics page
        $this->redirect('admin/defaultMetrics');
    }
    
    /**
     * Add default age weights to a show
     * 
     * @param int $id Show ID
     */
    public function addDefaultAgeWeightsToShow($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if show already has age weights
        $existingAgeWeights = $this->judgingModel->getAgeWeightsByShowId($id);
        
        // Debug log
        error_log('Existing age weights for show ' . $id . ': ' . count($existingAgeWeights));
        
        if (!empty($existingAgeWeights)) {
            // Set flash message with more detailed instructions
            $_SESSION['flash_message'] = [
                'type' => 'warning',
                'message' => '<strong>This show already has age weights.</strong> Please use the "Delete All Age Weights" button first, then try adding default age weights again.'
            ];
            
            // Debug log
            error_log('Show already has ' . count($existingAgeWeights) . ' age weights. Redirecting to edit page with warning.');
            
            $this->redirect('admin/editShow/' . $id . '#age-weights');
            return;
        }
        
        // Add default age weights to show
        if ($this->defaultAgeWeightModel->addDefaultAgeWeightsToShow($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'Default age weights have been added to the show.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to add default age weights to the show.'
            ];
        }
        
        // Redirect back to edit show page
        $this->redirect('admin/editShow/' . $id);
    }
    
    /**
     * Delete all age weights for a show
     * 
     * @param int $id Show ID
     */
    public function deleteAllAgeWeights($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Delete all age weights for the show
        if ($this->judgingModel->deleteAllAgeWeightsByShowId($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'All age weights have been deleted from the show.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to delete age weights from the show.'
            ];
        }
        
        // Redirect back to edit show page
        $this->redirect('admin/editShow/' . $id);
    }
    
    /**
     * Delete all categories for a show
     * 
     * @param int $id Show ID
     */
    public function deleteAllCategories($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Delete all categories for the show
        if ($this->showModel->deleteAllCategoriesByShowId($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'All categories have been deleted from the show.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to delete categories from the show.'
            ];
        }
        
        // Redirect back to edit show page
        $this->redirect('admin/editShow/' . $id . '#categories');
    }
    
    /**
     * Delete all metrics for a show
     * 
     * @param int $id Show ID
     */
    public function deleteAllMetrics($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Delete all metrics for the show
        if ($this->showModel->deleteAllMetricsByShowId($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'All judging metrics have been deleted from the show.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to delete judging metrics from the show.'
            ];
        }
        
        // Redirect back to edit show page
        $this->redirect('admin/editShow/' . $id . '#metrics');
    }
    
    /**
     * Default Age Weights
     */
    public function defaultAgeWeights() {
        // Get all default age weights
        $defaultAgeWeights = $this->defaultAgeWeightModel->getDefaultAgeWeights();
        
        $data = [
            'title' => 'Default Age Weights',
            'defaultAgeWeights' => $defaultAgeWeights,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/default_age_weights', $data);
    }
    
    /**
     * Add Default Age Weight
     */
    public function addDefaultAgeWeight() {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'min_year' => isset($_POST['min_year']) ? intval($_POST['min_year']) : 0,
                'max_year' => isset($_POST['max_year']) ? intval($_POST['max_year']) : 0,
                'weight' => isset($_POST['weight']) ? floatval($_POST['weight']) : 1.0,
                'description' => trim($_POST['description']),
                'min_year_err' => '',
                'max_year_err' => '',
                'weight_err' => '',
                'description_err' => ''
            ];
            
            // Validate min_year
            if (empty($data['min_year'])) {
                $data['min_year_err'] = 'Please enter a minimum year';
            }
            
            // Validate max_year
            if (empty($data['max_year'])) {
                $data['max_year_err'] = 'Please enter a maximum year';
            } elseif ($data['max_year'] < $data['min_year']) {
                $data['max_year_err'] = 'Maximum year must be greater than or equal to minimum year';
            }
            
            // Validate weight
            if (empty($data['weight'])) {
                $data['weight_err'] = 'Please enter a weight';
            } elseif ($data['weight'] <= 0) {
                $data['weight_err'] = 'Weight must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['min_year_err']) && empty($data['max_year_err']) && 
                empty($data['weight_err']) && empty($data['description_err'])) {
                
                // Add default age weight
                if ($this->defaultAgeWeightModel->addDefaultAgeWeight($data)) {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Default age weight added successfully.'
                    ];
                    
                    // Redirect to default age weights page
                    $this->redirect('admin/defaultAgeWeights');
                } else {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'danger',
                        'message' => 'Failed to add default age weight.'
                    ];
                    
                    // Load view with errors
                    $this->view('admin/add_default_age_weight', $data);
                }
            } else {
                // Load view with errors
                $this->view('admin/add_default_age_weight', $data);
            }
        } else {
            $data = [
                'title' => 'Add Default Age Weight',
                'min_year' => gmdate('Y') - 100, // Default to 100 years ago
                'max_year' => gmdate('Y') - 90,  // Default to 90 years ago
                'weight' => 1.0,
                'description' => '',
                'min_year_err' => '',
                'max_year_err' => '',
                'weight_err' => '',
                'description_err' => '',
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/add_default_age_weight', $data);
        }
    }
    
    /**
     * Edit Default Age Weight
     * 
     * @param int $id Default age weight ID
     */
    public function editDefaultAgeWeight($id) {
        // Get default age weight
        $defaultAgeWeight = $this->defaultAgeWeightModel->getDefaultAgeWeightById($id);
        
        if (!$defaultAgeWeight) {
            $this->redirect('home/not_found');
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'min_year' => isset($_POST['min_year']) ? intval($_POST['min_year']) : 0,
                'max_year' => isset($_POST['max_year']) ? intval($_POST['max_year']) : 0,
                'weight' => isset($_POST['weight']) ? floatval($_POST['weight']) : 1.0,
                'description' => trim($_POST['description']),
                'min_year_err' => '',
                'max_year_err' => '',
                'weight_err' => '',
                'description_err' => ''
            ];
            
            // Validate min_year
            if (empty($data['min_year'])) {
                $data['min_year_err'] = 'Please enter a minimum year';
            }
            
            // Validate max_year
            if (empty($data['max_year'])) {
                $data['max_year_err'] = 'Please enter a maximum year';
            } elseif ($data['max_year'] < $data['min_year']) {
                $data['max_year_err'] = 'Maximum year must be greater than or equal to minimum year';
            }
            
            // Validate weight
            if (empty($data['weight'])) {
                $data['weight_err'] = 'Please enter a weight';
            } elseif ($data['weight'] <= 0) {
                $data['weight_err'] = 'Weight must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['min_year_err']) && empty($data['max_year_err']) && 
                empty($data['weight_err']) && empty($data['description_err'])) {
                
                // Update default age weight
                if ($this->defaultAgeWeightModel->updateDefaultAgeWeight($data)) {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Default age weight updated successfully.'
                    ];
                    
                    // Redirect to default age weights page
                    $this->redirect('admin/defaultAgeWeights');
                } else {
                    // Set flash message
                    $_SESSION['flash_message'] = [
                        'type' => 'danger',
                        'message' => 'Failed to update default age weight.'
                    ];
                    
                    // Load view with errors
                    $this->view('admin/edit_default_age_weight', $data);
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_default_age_weight', $data);
            }
        } else {
            $data = [
                'title' => 'Edit Default Age Weight',
                'id' => $defaultAgeWeight->id,
                'min_year' => $defaultAgeWeight->min_year,
                'max_year' => $defaultAgeWeight->max_year,
                'weight' => $defaultAgeWeight->weight,
                'description' => $defaultAgeWeight->description,
                'min_year_err' => '',
                'max_year_err' => '',
                'weight_err' => '',
                'description_err' => '',
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/edit_default_age_weight', $data);
        }
    }
    
    /**
     * Delete Default Age Weight
     * 
     * @param int $id Default age weight ID
     */
    public function deleteDefaultAgeWeight($id) {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get default age weight
        $defaultAgeWeight = $this->defaultAgeWeightModel->getDefaultAgeWeightById($id);
        
        if (!$defaultAgeWeight) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Delete default age weight
        if ($this->defaultAgeWeightModel->deleteDefaultAgeWeight($id)) {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'Default age weight deleted successfully.'
            ];
        } else {
            // Set flash message
            $_SESSION['flash_message'] = [
                'type' => 'danger',
                'message' => 'Failed to delete default age weight.'
            ];
        }
        
        // Redirect to default age weights page
        $this->redirect('admin/defaultAgeWeights');
    }
    
    /**
     * Debug form fields
     * 
     * @param int $id Show ID
     */
    public function debugFormFields($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get coordinators
        $coordinators = $this->userModel->getUsers('coordinator');
        
        // Get categories and judging metrics for this show
        $categories = $this->showModel->getShowCategories($id);
        $metrics = $this->showModel->getJudgingMetrics($id);
        
        // Load the FormDesignerModel to get the default admin form template
        $formDesignerModel = $this->model('FormDesignerModel');
        $formTemplate = $formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
        
        // If no default template exists, create one with default fields
        if (!$formTemplate) {
            // Get default fields and convert 'id' to 'name' for compatibility
            $defaultFields = $formDesignerModel->getDefaultFields('admin_show');
            foreach ($defaultFields as &$field) {
                if (isset($field['id']) && !isset($field['name'])) {
                    $field['name'] = $field['id'];
                }
            }
            
            $templateData = [
                'name' => 'Default Show Admin Form',
                'type' => 'admin_show',
                'entity_id' => 0,
                'fields' => json_encode($defaultFields)
            ];
            
            $templateId = $formDesignerModel->createFormTemplate($templateData);
            $formTemplate = $formDesignerModel->getFormTemplateById($templateId);
        }
        
        // Decode the form fields and ensure they have 'name' attribute
        $formFields = json_decode($formTemplate->fields, true);
        
        // Check if form fields are valid
        if (!is_array($formFields) || empty($formFields)) {
            // Log the issue
            error_log('Invalid or empty form fields in template ID: ' . $formTemplate->id);
            
            // If form fields are not valid, create default fields
            $formFields = $formDesignerModel->getDefaultFields('admin_show');
            
            // Update the template with valid fields
            $updateData = [
                'id' => $formTemplate->id,
                'fields' => json_encode($formFields)
            ];
            $formDesignerModel->updateFormTemplate($formTemplate->id, $updateData);
        }
        
        // Ensure all fields have a name attribute
        foreach ($formFields as &$field) {
            if (isset($field['id']) && !isset($field['name'])) {
                $field['name'] = $field['id'];
            }
        }
        
        // Load the CustomFieldRetriever to map database columns to form fields
        require_once APPROOT . '/models/CustomFieldRetriever.php';
        $customFieldRetriever = new CustomFieldRetriever();
        
        // Map database columns to form fields
        $mappedData = $customFieldRetriever->mapDatabaseToForm($show);
        
        // Debug output
        error_log("AdminController::debugFormFields - Show data before mapping:");
        foreach ($show as $key => $value) {
            if (strpos($key, 'custom_field_') === 0) {
                error_log("  {$key}: " . (is_string($value) ? $value : gettype($value)));
            }
        }
        
        error_log("AdminController::debugFormFields - Mapped data:");
        foreach ($mappedData as $key => $value) {
            if (!property_exists($show, $key)) {
                error_log("  {$key}: " . (is_string($value) ? $value : gettype($value)));
            }
        }
        
        // Merge with basic data
        $data = array_merge([
            'id' => $show->id,
            'name' => $show->name,
            'description' => $show->description,
            'location' => $show->location,
            'start_date' => convertUTCToUserDateTime($show->start_date, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i'),
            'end_date' => convertUTCToUserDateTime($show->end_date, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i'),
            'registration_start' => convertUTCToUserDateTime($show->registration_start, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i'),
            'registration_end' => convertUTCToUserDateTime($show->registration_end, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i'),
            'coordinator_id' => $show->coordinator_id,
            'status' => $show->status,
            'fan_voting_enabled' => isset($show->fan_voting_enabled) ? $show->fan_voting_enabled : true,
            'registration_fee' => isset($show->registration_fee) ? $show->registration_fee : 0,
            'is_free' => isset($show->is_free) ? $show->is_free : 0,
            'listing_fee' => isset($show->listing_fee) ? $show->listing_fee : 0,
            'title' => 'Debug Form Fields',
            'coordinators' => $coordinators,
            'categories' => $categories,
            'metrics' => $metrics,
            'show' => $show,
            'formFields' => $formFields,
            'formTemplate' => $formTemplate
        ], $mappedData);
        
        // Load debug view
        $this->view('admin/debug/form_fields', $data);
    }
    
    /**
     * Simple debug for form fields
     * 
     * @param int $id Show ID
     */
    public function simpleDebug($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            echo "Show not found";
            return;
        }
        
        // Load the FormDesignerModel to get the form template
        $formDesignerModel = $this->model('FormDesignerModel');
        $formTemplate = $formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
        
        // Decode the form fields
        $formFields = $formTemplate ? json_decode($formTemplate->fields, true) : [];
        
        // Load the CustomFieldRetriever
        require_once APPROOT . '/models/CustomFieldRetriever.php';
        $customFieldRetriever = new CustomFieldRetriever();
        
        // Map database columns to form fields
        $mappedData = $customFieldRetriever->mapDatabaseToForm($show);
        
        // Debug output
        error_log("AdminController::simpleDebug - Show data:");
        foreach ($show as $key => $value) {
            if (strpos($key, 'custom_field_') === 0) {
                error_log("  {$key}: " . (is_string($value) ? $value : gettype($value)));
            }
        }
        
        // Prepare data for the view
        $data = [
            'show' => $show,
            'formFields' => $formFields,
            'formTemplate' => $formTemplate,
            'mappedData' => $mappedData,
            'urlroot' => URLROOT
        ];
        
        // Load simple debug view
        $this->view('admin/debug/simple_debug', $data);
    }
    
    /**
     * Delete show
     * 
     * @param int $id Show ID
     */
    public function deleteShow($id) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete show
            if ($this->showModel->deleteShow($id)) {
                // Redirect to shows page
                $this->redirect('admin/shows');
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/shows');
        }
    }
    
    /**
     * Edit category
     * 
     * @param int $id Category ID
     */
    public function editCategory($id) {
        // Get category
        $category = $this->showModel->getCategoryById($id);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($category->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'registration_fee' => floatval($_POST['registration_fee']),
                'max_entries' => intval($_POST['max_entries']),
                'display_order' => isset($_POST['display_order']) ? intval($_POST['display_order']) : 0,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'requires_judging' => isset($_POST['requires_judging']) ? 1 : 0,
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Edit Category',
                'show' => $show,
                'category' => $category
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate registration fee
            if ($data['registration_fee'] < 0) {
                $data['registration_fee_err'] = 'Registration fee cannot be negative';
            }
            
            // Validate max entries
            if ($data['max_entries'] < 0) {
                $data['max_entries_err'] = 'Max entries cannot be negative';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['registration_fee_err']) && 
                empty($data['max_entries_err'])) {
                
                // Update category
                if ($this->showModel->updateCategory($data)) {
                    // Redirect to edit show page
                    $this->redirect('admin/editShow/' . $category->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_category', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $category->id,
                'name' => $category->name,
                'description' => $category->description,
                'registration_fee' => $category->registration_fee,
                'max_entries' => $category->max_entries,
                'display_order' => isset($category->display_order) ? $category->display_order : 0,
                'is_active' => isset($category->is_active) ? $category->is_active : 1,
                'requires_judging' => isset($category->requires_judging) ? $category->requires_judging : 1,
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Edit Category',
                'show' => $show,
                'category' => $category
            ];
            
            // Set default values for missing properties
            if (!isset($category->is_active)) {
                $category->is_active = 1; // Default to active
            }
            if (!isset($category->requires_judging)) {
                $category->requires_judging = 1; // Default to requiring judging
            }
            if (!isset($category->display_order)) {
                $category->display_order = 0; // Default display order
            }
            
            // Load view
            $this->view('admin/edit_category', $data);
        }
    }
    
    /**
     * Delete category
     * 
     * @param int $id Category ID
     */
    public function deleteCategory($id) {
        // Get category
        $category = $this->showModel->getCategoryById($id);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($category->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete category
            if ($this->showModel->deleteCategory($id)) {
                // Redirect to edit show page
                $this->redirect('admin/editShow/' . $category->show_id);
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/editShow/' . $category->show_id);
        }
    }
    
    /**
     * Edit judging metric
     * 
     * @param int $id Metric ID
     */
    public function editMetric($id) {
        // Get metric
        $metric = $this->showModel->getMetricById($id);
        
        if (!$metric) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($metric->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'max_score' => intval($_POST['max_score']),
                'weight' => floatval($_POST['weight']),
                'display_order' => intval($_POST['display_order']),
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'display_order_err' => '',
                'title' => 'Edit Judging Metric',
                'show' => $show,
                'metric' => $metric
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate max score
            if ($data['max_score'] <= 0) {
                $data['max_score_err'] = 'Max score must be greater than 0';
            }
            
            // Validate weight
            if ($data['weight'] <= 0) {
                $data['weight_err'] = 'Weight must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['max_score_err']) && 
                empty($data['weight_err']) && empty($data['display_order_err'])) {
                
                // Update metric
                if ($this->showModel->updateMetric($data)) {
                    // Redirect to edit show page
                    $this->redirect('admin/editShow/' . $metric->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_judging_metric', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $metric->id,
                'name' => $metric->name,
                'description' => $metric->description,
                'max_score' => $metric->max_score,
                'weight' => $metric->weight,
                'display_order' => $metric->display_order,
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'display_order_err' => '',
                'title' => 'Edit Judging Metric',
                'show' => $show,
                'metric' => $metric
            ];
            
            // Load view
            $this->view('admin/edit_judging_metric', $data);
        }
    }
    
    /**
     * Delete judging metric
     * 
     * @param int $id Metric ID
     */
    public function deleteMetric($id) {
        // Get metric
        $metric = $this->showModel->getMetricById($id);
        
        if (!$metric) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($metric->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete metric
            if ($this->showModel->deleteMetric($id)) {
                // Redirect to edit show page
                $this->redirect('admin/editShow/' . $metric->show_id);
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/editShow/' . $metric->show_id);
        }
    }
    
    /**
     * System settings
     */
    public function settings() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'app_name' => trim($_POST['app_name']),
                'fb_app_id' => trim($_POST['fb_app_id']),
                'fb_app_secret' => trim($_POST['fb_app_secret']),
                'app_name_err' => '',
                'title' => 'System Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css'
            ];
            
            // Validate app name
            if (empty($data['app_name'])) {
                $data['app_name_err'] = 'Please enter an application name';
            }
            
            // Check for errors
            if (empty($data['app_name_err'])) {
                // Update settings
                $settings = [
                    'app_name' => $data['app_name'],
                    'fb_app_id' => $data['fb_app_id'],
                    'fb_app_secret' => $data['fb_app_secret']
                ];
                
                $this->settingsModel->saveSettings($settings, 'general');
                
                // Show success message
                $data['success'] = true;
                
                // Reload settings
                $allSettings = $this->settingsModel->getAllSettings();
                $data = array_merge($data, $allSettings);
                
                $this->view('admin/settings', $data);
            } else {
                // Load view with errors
                $this->view('admin/settings', $data);
            }
        } else {
            // Init data
            $data = [
                'app_name' => APP_NAME,
                'fb_app_id' => defined('FB_APP_ID') ? FB_APP_ID : '',
                'fb_app_secret' => defined('FB_APP_SECRET') ? FB_APP_SECRET : '',
                'app_name_err' => '',
                'title' => 'System Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css'
            ];
            
            // Get all settings
            $allSettings = $this->settingsModel->getAllSettings();
            $data = array_merge($data, $allSettings);
            
            // Load view
            $this->view('admin/settings', $data);
        }
    }
    
    /**
     * QR Code Settings Category Page
     */
    public function settings_qr_codes() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'QR Code Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_qr_codes', $data);
    }
    
    /**
     * Judging & Metrics Category Page
     */
    public function settings_judging() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Judging & Metrics Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_judging', $data);
    }
    
    /**
     * Scoring Routes
     * 
     * These methods handle redirects to the AdminScoringController
     */
    public function scoring($action = 'formulas', $id = null) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Load the AdminScoringController
        require_once APPROOT . '/controllers/AdminScoringController.php';
        $scoringController = new AdminScoringController();
        
        // Call the appropriate method based on the action
        switch ($action) {
            case 'formulas':
                $scoringController->formulas();
                break;
            case 'addFormula':
                $scoringController->addFormula();
                break;
            case 'updateFormula':
                $scoringController->updateFormula();
                break;
            case 'deleteFormula':
                if ($id === null) {
                    $this->redirect('admin/scoring/formulas');
                    return;
                }
                $scoringController->deleteFormula($id);
                break;
            case 'setDefault':
                if ($id === null) {
                    $this->redirect('admin/scoring/formulas');
                    return;
                }
                $scoringController->setDefaultFormula($id);
                break;
            case 'show':
                if ($id === null) {
                    $this->redirect('admin/shows');
                    return;
                }
                $scoringController->showSettings($id);
                break;
            case 'updateShowSettings':
                if ($id === null) {
                    $this->redirect('admin/shows');
                    return;
                }
                $scoringController->updateShowSettings($id);
                break;
            default:
                $scoringController->formulas();
                break;
        }
    }
    
    /**
     * Forms & Fields Category Page
     */
    public function settings_forms() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Forms & Fields Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_forms', $data);
    }
    
    /**
     * Media & Appearance Category Page
     */
    public function settings_media() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Media & Appearance Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_media', $data);
    }
    
    /**
     * Camera Banner Management Page
     */
    public function camera_banners() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Camera Banner Management',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/camera_banners', $data);
    }
    
    /**
     * Payment & Finance Category Page
     */
    public function settings_payment() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Payment & Finance Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_payment', $data);
    }
    
    /**
     * Developer Tools Category Page
     */
    public function settings_developer() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get development settings
        $settings = $this->settingsModel->getSettingsByGroup('development');
        
        $data = [
            'title' => 'Developer Tools',
            'custom_css' => BASE_URL . '/css/admin-settings.css',
            'settings' => $settings
        ];
        
        $this->view('admin/settings_developer', $data);
    }
    
    /**
     * Email Settings Page
     */
    /**
     * Calendar and Map Settings
     * 
     * @return void
     */
    public function settings_calendar() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Load calendar model
        $calendarModel = $this->model('CalendarModel');
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get map settings
            $mapSettings = [
                'provider' => trim($_POST['map_provider']),
                'api_key' => trim($_POST['map_api_key']),
                'server_api_key' => isset($_POST['map_server_api_key']) ? trim($_POST['map_server_api_key']) : '',
                'default_lat' => trim($_POST['map_default_lat']),
                'default_lng' => trim($_POST['map_default_lng']),
                'default_zoom' => trim($_POST['map_default_zoom']),
                'filter_radius' => trim($_POST['map_filter_radius']),
                'tile_url' => trim($_POST['map_tile_url']),
                'attribution' => trim($_POST['map_attribution'])
            ];
            
            // Get calendar settings
            $calendarSettings = [
                'default_view' => trim($_POST['calendar_default_view']),
                'start_day' => trim($_POST['calendar_start_day']),
                'time_format' => trim($_POST['calendar_time_format']),
                'date_format' => trim($_POST['calendar_date_format']),
                'events_per_page' => trim($_POST['calendar_events_per_page']),
                // Event Chart Settings
                'event_show_weekends' => isset($_POST['event_show_weekends']) ? 1 : 0,
                'event_enable_drag_drop' => isset($_POST['event_enable_drag_drop']) ? 1 : 0,
                'event_show_today_line' => isset($_POST['event_show_today_line']) ? 1 : 0,
                'event_show_event_hover' => isset($_POST['event_show_event_hover']) ? 1 : 0,
                'event_mobile_breakpoint' => trim($_POST['event_mobile_breakpoint'] ?? '992'),
                // Event Image Settings
                'event_max_images' => isset($_POST['event_max_images']) ? intval($_POST['event_max_images']) : 2,
                'event_max_image_size' => isset($_POST['event_max_image_size']) ? floatval($_POST['event_max_image_size']) : 2,
                'event_allowed_image_types' => isset($_POST['event_allowed_image_types']) ? implode(',', $_POST['event_allowed_image_types']) : 'image/jpeg,image/jpg,image/png,image/gif',
                'event_enable_wysiwyg' => isset($_POST['event_enable_wysiwyg']) ? 1 : 0,
                'event_social_sharing_images' => isset($_POST['event_social_sharing_images']) ? 1 : 0
            ];
            
            // Validate Google API keys if Google is selected
            $validationError = '';
            if ($mapSettings['provider'] === 'google') {
                if (empty($mapSettings['api_key'])) {
                    $validationError = 'Client-side API key is required when Google Maps is selected.';
                } elseif (empty($mapSettings['server_api_key'])) {
                    $validationError = 'Server-side API key is required when Google Maps is selected for Places and Geocoding APIs.';
                }
            }
            
            if (!empty($validationError)) {
                // Prepare data for view with error
                $data = [
                    'title' => 'Calendar & Map Settings',
                    'custom_css' => BASE_URL . '/css/admin-settings.css',
                    'mapSettings' => $mapSettings,
                    'calendarSettings' => $calendarSettings,
                    'error' => $validationError
                ];
                
                $this->view('admin/settings_calendar', $data);
                return;
            }
            
            // Update settings
            $mapUpdated = $calendarModel->updateMapProviderSettings($mapSettings);
            $calendarUpdated = $calendarModel->updateCalendarSettings($calendarSettings);
            
            // Update individual event image settings
            $eventImageUpdated = true;
            foreach ($calendarSettings as $key => $value) {
                if (strpos($key, 'event_') === 0) {
                    $settingUpdated = $calendarModel->updateSetting($key, $value);
                    if (!$settingUpdated) {
                        $eventImageUpdated = false;
                    }
                }
            }
            
            // Prepare data for view
            $data = [
                'title' => 'Calendar & Map Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'mapSettings' => $mapSettings,
                'calendarSettings' => $calendarSettings,
                'success' => $mapUpdated && $calendarUpdated && $eventImageUpdated,
                'error' => !($mapUpdated && $calendarUpdated && $eventImageUpdated) ? 'Failed to update some settings' : ''
            ];
            
            $this->view('admin/settings_calendar', $data);
        } else {
            // Get current settings
            $mapSettings = $calendarModel->getMapProviderSettings();
            $calendarSettings = $calendarModel->getCalendarDisplaySettings();
            
            // Prepare data for view
            $data = [
                'title' => 'Calendar & Map Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'mapSettings' => $mapSettings,
                'calendarSettings' => $calendarSettings
            ];
            
            $this->view('admin/settings_calendar', $data);
        }
    }
    
    public function settings_email() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'email_smtp_host' => trim($_POST['email_smtp_host']),
                'email_smtp_port' => trim($_POST['email_smtp_port']),
                'email_smtp_username' => trim($_POST['email_smtp_username']),
                'email_smtp_password' => trim($_POST['email_smtp_password']),
                'email_smtp_encryption' => trim($_POST['email_smtp_encryption']),
                'email_from_address' => trim($_POST['email_from_address']),
                'email_from_name' => trim($_POST['email_from_name']),
                'email_notification_new_show' => isset($_POST['email_notification_new_show']) ? '1' : '0',
                'title' => 'Email Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css'
            ];
            
            // Validate email from address
            if (!empty($data['email_from_address']) && !filter_var($data['email_from_address'], FILTER_VALIDATE_EMAIL)) {
                $data['email_from_address_err'] = 'Please enter a valid email address';
            }
            
            // Check for errors
            if (empty($data['email_from_address_err'])) {
                // Update settings
                $settings = [
                    'email_smtp_host' => $data['email_smtp_host'],
                    'email_smtp_port' => $data['email_smtp_port'],
                    'email_smtp_username' => $data['email_smtp_username'],
                    'email_smtp_encryption' => $data['email_smtp_encryption'],
                    'email_from_address' => $data['email_from_address'],
                    'email_from_name' => $data['email_from_name'],
                    'email_notification_new_show' => $data['email_notification_new_show']
                ];
                
                // Only update password if it's not empty
                if (!empty($data['email_smtp_password'])) {
                    $settings['email_smtp_password'] = $data['email_smtp_password'];
                }
                
                // Save settings
                foreach ($settings as $key => $value) {
                    $this->settingsModel->setSetting($key, $value);
                }
                
                // Show success message
                $data['success'] = true;
                
                // Load view
                $this->view('admin/settings_email', $data);
            } else {
                // Load view with errors
                $this->view('admin/settings_email', $data);
            }
        } else {
            // Init data
            $data = [
                'email_smtp_host' => $this->settingsModel->getSetting('email_smtp_host', ''),
                'email_smtp_port' => $this->settingsModel->getSetting('email_smtp_port', '587'),
                'email_smtp_username' => $this->settingsModel->getSetting('email_smtp_username', ''),
                'email_smtp_password' => '', // Don't display the password
                'email_smtp_encryption' => $this->settingsModel->getSetting('email_smtp_encryption', 'tls'),
                'email_from_address' => $this->settingsModel->getSetting('email_from_address', ''),
                'email_from_name' => $this->settingsModel->getSetting('email_from_name', 'Events and Shows'),
                'email_notification_new_show' => $this->settingsModel->getSetting('email_notification_new_show', '1'),
                'title' => 'Email Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css'
            ];
            
            // Load view
            $this->view('admin/settings_email', $data);
        }
    }
    
    public function settings_notifications() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Prepare data for view
        $data = [
            'title' => 'Notification Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_notifications', $data);
    }
    
    public function settings_club_ownership() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Prepare data for view
        $data = [
            'title' => 'Club Ownership Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_club_ownership', $data);
    }
    
    public function settings_calendar_display() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Load calendar model
        $calendarModel = $this->model('CalendarModel');
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get calendar display settings
            $calendarSettings = [
                'default_view' => trim($_POST['calendar_default_view']),
                'start_day' => trim($_POST['calendar_start_day']),
                'time_format' => trim($_POST['calendar_time_format']),
                'date_format' => trim($_POST['calendar_date_format']),
                'events_per_page' => trim($_POST['calendar_events_per_page'])
            ];
            
            // Update settings
            $calendarUpdated = $calendarModel->updateCalendarSettings($calendarSettings);
            
            // Prepare data for view
            $data = [
                'title' => 'Calendar Display Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'calendarSettings' => $calendarSettings,
                'success' => $calendarUpdated,
                'error' => !$calendarUpdated ? 'Failed to update calendar display settings' : ''
            ];
            
            $this->view('admin/settings_calendar_display', $data);
        } else {
            // Get current settings
            $calendarSettings = $calendarModel->getCalendarDisplaySettings();
            
            // Prepare data for view
            $data = [
                'title' => 'Calendar Display Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'calendarSettings' => $calendarSettings
            ];
            
            $this->view('admin/settings_calendar_display', $data);
        }
    }
    
    public function settings_event_chart() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Load calendar model
        $calendarModel = $this->model('CalendarModel');
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get event chart settings
            $calendarSettings = [
                'event_show_weekends' => isset($_POST['event_show_weekends']) ? 1 : 0,
                'event_enable_drag_drop' => isset($_POST['event_enable_drag_drop']) ? 1 : 0,
                'event_show_today_line' => isset($_POST['event_show_today_line']) ? 1 : 0,
                'event_show_event_hover' => isset($_POST['event_show_event_hover']) ? 1 : 0,
                'event_mobile_breakpoint' => trim($_POST['event_mobile_breakpoint'] ?? '992')
            ];
            
            // Update individual event chart settings
            $eventChartUpdated = true;
            foreach ($calendarSettings as $key => $value) {
                $settingUpdated = $calendarModel->updateSetting($key, $value);
                if (!$settingUpdated) {
                    $eventChartUpdated = false;
                }
            }
            
            // Prepare data for view
            $data = [
                'title' => 'Event Chart Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'calendarSettings' => $calendarSettings,
                'success' => $eventChartUpdated,
                'error' => !$eventChartUpdated ? 'Failed to update some event chart settings' : ''
            ];
            
            $this->view('admin/settings_event_chart', $data);
        } else {
            // Get current settings
            $calendarSettings = $calendarModel->getCalendarDisplaySettings();
            
            // Prepare data for view
            $data = [
                'title' => 'Event Chart Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'calendarSettings' => $calendarSettings
            ];
            
            $this->view('admin/settings_event_chart', $data);
        }
    }
    
    public function settings_map() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Load calendar model
        $calendarModel = $this->model('CalendarModel');
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get map settings
            $mapSettings = [
                'provider' => trim($_POST['map_provider']),
                'api_key' => trim($_POST['map_api_key']),
                'server_api_key' => isset($_POST['map_server_api_key']) ? trim($_POST['map_server_api_key']) : '',
                'default_lat' => trim($_POST['map_default_lat']),
                'default_lng' => trim($_POST['map_default_lng']),
                'default_zoom' => trim($_POST['map_default_zoom']),
                'filter_radius' => trim($_POST['map_filter_radius']),
                'tile_url' => trim($_POST['map_tile_url']),
                'attribution' => trim($_POST['map_attribution'])
            ];
            
            // Validate Google API keys if Google is selected
            $validationError = '';
            if ($mapSettings['provider'] === 'google') {
                if (empty($mapSettings['api_key'])) {
                    $validationError = 'Client-side API key is required when Google Maps is selected.';
                } elseif (empty($mapSettings['server_api_key'])) {
                    $validationError = 'Server-side API key is required when Google Maps is selected for Places and Geocoding APIs.';
                }
            }
            
            if (!empty($validationError)) {
                // Prepare data for view with error
                $data = [
                    'title' => 'Map Settings',
                    'custom_css' => BASE_URL . '/css/admin-settings.css',
                    'mapSettings' => $mapSettings,
                    'error' => $validationError
                ];
                
                $this->view('admin/settings_map', $data);
                return;
            }
            
            // Update settings
            $mapUpdated = $calendarModel->updateMapProviderSettings($mapSettings);
            
            // Prepare data for view
            $data = [
                'title' => 'Map Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'mapSettings' => $mapSettings,
                'success' => $mapUpdated,
                'error' => !$mapUpdated ? 'Failed to update map settings' : ''
            ];
            
            $this->view('admin/settings_map', $data);
        } else {
            // Get current settings
            $mapSettings = $calendarModel->getMapProviderSettings();
            
            // Prepare data for view
            $data = [
                'title' => 'Map Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'mapSettings' => $mapSettings
            ];
            
            $this->view('admin/settings_map', $data);
        }
    }
    
    public function settings_map_tools() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Prepare data for view
        $data = [
            'title' => 'Map Tools',
            'custom_css' => BASE_URL . '/css/admin-settings.css'
        ];
        
        $this->view('admin/settings_map_tools', $data);
    }
    
    public function settings_event_images() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Load calendar model
        $calendarModel = $this->model('CalendarModel');
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get event image settings
            $calendarSettings = [
                'event_max_images' => isset($_POST['event_max_images']) ? intval($_POST['event_max_images']) : 2,
                'event_max_image_size' => isset($_POST['event_max_image_size']) ? floatval($_POST['event_max_image_size']) : 2,
                'event_allowed_image_types' => isset($_POST['event_allowed_image_types']) ? implode(',', $_POST['event_allowed_image_types']) : 'image/jpeg,image/jpg,image/png,image/gif',
                'event_enable_wysiwyg' => isset($_POST['event_enable_wysiwyg']) ? 1 : 0,
                'event_social_sharing_images' => isset($_POST['event_social_sharing_images']) ? 1 : 0
            ];
            
            // Update individual event image settings
            $eventImageUpdated = true;
            foreach ($calendarSettings as $key => $value) {
                $settingUpdated = $calendarModel->updateSetting($key, $value);
                if (!$settingUpdated) {
                    $eventImageUpdated = false;
                }
            }
            
            // Prepare data for view
            $data = [
                'title' => 'Event Image Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'calendarSettings' => $calendarSettings,
                'success' => $eventImageUpdated,
                'error' => !$eventImageUpdated ? 'Failed to update some event image settings' : ''
            ];
            
            $this->view('admin/settings_event_images', $data);
        } else {
            // Get current settings
            $calendarSettings = $calendarModel->getCalendarDisplaySettings();
            
            // Prepare data for view
            $data = [
                'title' => 'Event Image Settings',
                'custom_css' => BASE_URL . '/css/admin-settings.css',
                'calendarSettings' => $calendarSettings
            ];
            
            $this->view('admin/settings_event_images', $data);
        }
    }
    
    /**
     * Test Email Functionality
     * 
     * This method sends a test email to verify email settings
     */
    public function testEmail() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Unauthorized'
            ]);
            exit;
        }
        
        // Check if this is an AJAX request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
            exit;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Invalid CSRF token'
            ]);
            exit;
        }
        
        // Get the test email address
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Please provide a valid email address'
            ]);
            exit;
        }
        
        // Create email service instance
        $emailService = $this->model('EmailService');
        
        // Check if email is configured
        if (!$emailService->isConfigured()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Email is not configured. Please configure SMTP settings first.'
            ]);
            exit;
        }
        
        // Prepare test email
        $subject = 'Test Email from Events and Shows';
        $body = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #2c3e50; margin-bottom: 20px; }
                .info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>Test Email</h1>
                <div class='info'>
                    <p>This is a test email from your Events and Shows application.</p>
                    <p>If you're receiving this email, your email configuration is working correctly!</p>
                </div>
                <p>Server time (UTC): " . gmdate('Y-m-d H:i:s') . "</p>
                <p>This email was sent to: " . htmlspecialchars($email) . "</p>
            </div>
        </body>
        </html>
        ";
        
        // Send the test email
        $result = $emailService->send($email, $subject, $body);
        
        // Return the result
        header('Content-Type: application/json');
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Test email sent successfully!'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to send test email. Please check your email configuration and server logs.'
            ]);
        }
        exit;
    }
    
    /**
     * Save development settings
     */
    public function saveDevSettings() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Prepare settings data
            $settings = [
                'dev_admin_bypass' => isset($_POST['dev_admin_bypass']) ? '1' : '0',
                'session_lifetime' => isset($_POST['session_lifetime']) ? max(300, min(2592000, intval($_POST['session_lifetime']))) : 86400,
                'facebook_session_lifetime' => isset($_POST['facebook_session_lifetime']) ? max(300, min(2592000, intval($_POST['facebook_session_lifetime']))) : 86400,
                'remember_me_lifetime' => isset($_POST['remember_me_lifetime']) ? max(3600, min(31536000, intval($_POST['remember_me_lifetime']))) : 2592000
            ];
            
            // Save settings
            if ($this->settingsModel->saveSettings($settings, 'development')) {
                // Set flash message
                setFlashMessage('success', 'Development settings updated successfully');
            } else {
                // Set flash message
                setFlashMessage('error', 'Failed to update development settings');
            }
            
            // Redirect back to developer settings
            $this->redirect('admin/settings_developer');
        } else {
            // Redirect to developer settings
            $this->redirect('admin/settings_developer');
        }
    }
    
    /**
     * System Reports
     * 
     * Display various system reports and analytics
     */
    public function reports() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        try {
            // Get basic statistics
            $userCount = $this->userModel->getUserCount();
            $showCount = $this->showModel->getShowCount();
            $registrationCount = $this->registrationModel->getRegistrationCount();
            $paymentCount = $this->paymentModel->getPaymentCount();
            
            // Get recent activity
            $recentShows = $this->showModel->getRecentShows(5);
            $recentRegistrations = $this->registrationModel->getRecentRegistrations(5);
            $recentPayments = $this->paymentModel->getRecentPayments(5);
            
            // Get monthly statistics for the last 12 months
            $monthlyStats = $this->getMonthlyStatistics();
            
            // Get show status breakdown
            $showStatusStats = $this->showModel->getShowStatusStatistics();
            
            // Get payment status breakdown
            $paymentStatusStats = $this->paymentModel->getPaymentStatusStatistics();
            
            $data = [
                'title' => 'System Reports',
                'userCount' => $userCount,
                'showCount' => $showCount,
                'registrationCount' => $registrationCount,
                'paymentCount' => $paymentCount,
                'recentShows' => $recentShows,
                'recentRegistrations' => $recentRegistrations,
                'recentPayments' => $recentPayments,
                'monthlyStats' => $monthlyStats,
                'showStatusStats' => $showStatusStats,
                'paymentStatusStats' => $paymentStatusStats,
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/reports', $data);
            
        } catch (Exception $e) {
            error_log('AdminController::reports - Error: ' . $e->getMessage());
            
            // Fallback data in case of errors
            $data = [
                'title' => 'System Reports',
                'error' => 'Unable to load some report data. Please check system logs.',
                'userCount' => 0,
                'showCount' => 0,
                'registrationCount' => 0,
                'paymentCount' => 0,
                'recentShows' => [],
                'recentRegistrations' => [],
                'recentPayments' => [],
                'monthlyStats' => [],
                'showStatusStats' => [],
                'paymentStatusStats' => [],
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('admin/reports', $data);
        }
    }
    
    /**
     * Get monthly statistics for the last 12 months
     * 
     * @return array Monthly statistics
     */
    private function getMonthlyStatistics() {
        try {
            $db = new Database();
            $monthlyStats = [];
            
            // Get the last 12 months
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-{$i} months"));
                $monthName = date('M Y', strtotime("-{$i} months"));
                
                // Get shows created this month
                $db->query("SELECT COUNT(*) as count FROM shows WHERE DATE_FORMAT(created_at, '%Y-%m') = :month");
                $db->bind(':month', $month);
                $showCount = $db->single()->count ?? 0;
                
                // Get registrations created this month
                $db->query("SELECT COUNT(*) as count FROM registrations WHERE DATE_FORMAT(created_at, '%Y-%m') = :month");
                $db->bind(':month', $month);
                $registrationCount = $db->single()->count ?? 0;
                
                // Get payments created this month
                $db->query("SELECT COUNT(*) as count FROM payments WHERE DATE_FORMAT(created_at, '%Y-%m') = :month");
                $db->bind(':month', $month);
                $paymentCount = $db->single()->count ?? 0;
                
                $monthlyStats[] = [
                    'month' => $monthName,
                    'shows' => $showCount,
                    'registrations' => $registrationCount,
                    'payments' => $paymentCount
                ];
            }
            
            return $monthlyStats;
            
        } catch (Exception $e) {
            error_log('AdminController::getMonthlyStatistics - Error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Export report data
     * 
     * Handle export requests from the reports page
     */
    public function exportReport() {
        // Check if user is logged in and is an admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            $exportType = $_POST['export_type'] ?? '';
            
            try {
                switch ($exportType) {
                    case 'users':
                        $this->exportUsers();
                        break;
                    case 'shows':
                        $this->exportShows();
                        break;
                    case 'registrations':
                        $this->exportAllRegistrations();
                        break;
                    case 'payments':
                        $this->exportPayments();
                        break;
                    default:
                        $this->redirect('admin/reports');
                        break;
                }
            } catch (Exception $e) {
                error_log('AdminController::exportReport - Error: ' . $e->getMessage());
                $this->redirect('admin/reports');
            }
        } else {
            $this->redirect('admin/reports');
        }
    }
    
    /**
     * Export users data to CSV
     */
    private function exportUsers() {
        $users = $this->userModel->getUsers();
        
        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, ['ID', 'Name', 'Email', 'Role', 'Phone', 'City', 'State', 'Created At', 'Last Login', 'Status']);
        
        // CSV data
        foreach ($users as $user) {
            fputcsv($output, [
                $user->id,
                $user->name,
                $user->email,
                $user->role,
                $user->phone ?? '',
                $user->city ?? '',
                $user->state ?? '',
                $user->created_at,
                $user->last_login ?? '',
                $user->status ?? 'active'
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export shows data to CSV
     */
    private function exportShows() {
        $shows = $this->showModel->getShows();
        
        $filename = 'shows_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, ['ID', 'Name', 'Description', 'Location', 'Start Date', 'End Date', 'Registration Start', 'Registration End', 'Coordinator', 'Status', 'Created At']);
        
        // CSV data
        foreach ($shows as $show) {
            fputcsv($output, [
                $show->id,
                $show->name,
                $show->description ?? '',
                $show->location,
                $show->start_date,
                $show->end_date,
                $show->registration_start ?? '',
                $show->registration_end ?? '',
                $show->coordinator_name ?? '',
                $show->status,
                $show->created_at ?? ''
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export all registrations data to CSV
     */
    private function exportAllRegistrations() {
        $registrations = $this->registrationModel->getAllRegistrations(5000); // Limit to prevent memory issues
        
        $filename = 'registrations_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, ['ID', 'Registration Number', 'Show Name', 'Vehicle', 'Owner Name', 'Category', 'Status', 'Payment Status', 'Created At']);
        
        // CSV data
        foreach ($registrations as $registration) {
            $vehicle = ($registration->year ?? '') . ' ' . ($registration->make ?? '') . ' ' . ($registration->model ?? '');
            
            fputcsv($output, [
                $registration->id,
                $registration->registration_number ?? '',
                $registration->show_name ?? '',
                trim($vehicle),
                $registration->owner_name ?? '',
                $registration->category_name ?? '',
                $registration->status ?? '',
                $registration->payment_status ?? '',
                $registration->created_at ?? ''
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export payments data to CSV
     */
    private function exportPayments() {
        $payments = $this->paymentModel->getAllPaymentsWithUserInfo();
        
        $filename = 'payments_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, ['ID', 'User Name', 'User Email', 'Amount', 'Payment Method', 'Payment Type', 'Status', 'Reference', 'Description', 'Created At']);
        
        // CSV data
        foreach ($payments as $payment) {
            fputcsv($output, [
                $payment->id,
                $payment->user_name ?? '',
                $payment->user_email ?? '',
                $payment->amount,
                $payment->payment_method_name ?? '',
                $payment->payment_type ?? '',
                $payment->payment_status ?? '',
                $payment->payment_reference ?? '',
                $payment->description ?? '',
                $payment->created_at ?? ''
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * System Fields Management
     */
    public function systemFields() {
        // Check if SystemFieldManager is available
        if (!isset($this->systemFieldManager)) {
            $this->redirect('admin/settings');
            return;
        }
        
        // Load form templates
        $formTemplateModel = $this->model('FormTemplateModel');
        
        // Ensure the form_templates table exists
        $formTemplateModel->createTableIfNotExists();
        
        // Get all templates
        $templates = $formTemplateModel->getAllTemplates();
        
        // Group templates by type
        $templatesByType = [];
        foreach ($templates as $template) {
            $type = $template->type ?? 'unknown';
            if (!isset($templatesByType[$type])) {
                $templatesByType[$type] = [];
            }
            
            // Decode fields
            $template->fields = json_decode($template->fields, true);
            if (!is_array($template->fields)) {
                $template->fields = [];
            }
            
            $templatesByType[$type][] = $template;
        }
        
        // Get current system fields, required fields, and critical fields
        $systemFields = $this->systemFieldManager->getSystemFieldsArray();
        $requiredFields = $this->systemFieldManager->getRequiredSystemFields();
        $criticalFields = $this->systemFieldManager->getCriticalSystemFields();
        
        // Get entity types for each field
        $entityTypes = [];
        $allSystemFields = $this->systemFieldManager->getAllSystemFields();
        
        // Debug: Log all system fields
        error_log("AdminController::systemFields - All system fields: " . json_encode($allSystemFields));
        
        foreach ($allSystemFields as $field) {
            $entityTypes[$field->field_id] = $field->entity_type;
        }
        
        // Debug: Log entity types
        error_log("AdminController::systemFields - Entity types: " . json_encode($entityTypes));
        
        $data = [
            'title' => 'System Fields Management',
            'templates' => $templatesByType,
            'systemFields' => $systemFields,
            'requiredFields' => $requiredFields,
            'entityTypes' => $entityTypes,
            'criticalFields' => $criticalFields
        ];
        
        // Process form submissions
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Save system fields configuration
            if (isset($_POST['save_system_fields'])) {
                // Get submitted system fields and required fields
                $newSystemFields = isset($_POST['system_fields']) ? $_POST['system_fields'] : [];
                $newRequiredFields = isset($_POST['required_fields']) ? $_POST['required_fields'] : [];
                $newEntityTypes = isset($_POST['entity_types']) ? $_POST['entity_types'] : [];
                $allFields = isset($_POST['all_fields']) ? $_POST['all_fields'] : [];
                $allEntityTypes = isset($_POST['all_entity_types']) ? $_POST['all_entity_types'] : [];
                
                // Debug: Log all fields that were in the form
                error_log("AdminController::systemFields - All fields in form: " . json_encode($allFields));
                error_log("AdminController::systemFields - All entity types in form: " . json_encode($allEntityTypes));
                
                // Debug: Log the submitted data
                error_log("AdminController::systemFields - Submitted system fields: " . json_encode($newSystemFields));
                error_log("AdminController::systemFields - Submitted required fields: " . json_encode($newRequiredFields));
                error_log("AdminController::systemFields - Submitted entity types: " . json_encode($newEntityTypes));
                
                // Get current system fields to compare with new ones
                $currentSystemFields = $this->systemFieldManager->getSystemFieldsArray();
                error_log("AdminController::systemFields - Current system fields: " . json_encode($currentSystemFields));
                
                // Get critical fields that must be preserved
                $criticalFields = $this->systemFieldManager->getCriticalSystemFields();
                error_log("AdminController::systemFields - Critical fields: " . json_encode($criticalFields));
                
                // Make sure all critical fields are included in the system fields
                foreach ($criticalFields as $criticalField) {
                    if (!in_array($criticalField, $newSystemFields)) {
                        $newSystemFields[] = $criticalField;
                        error_log("AdminController::systemFields - Added critical field to system fields: " . $criticalField);
                    }
                }
                
                // Find fields that were unchecked (were in current but not in new)
                $uncheckedFields = array_diff($currentSystemFields, $newSystemFields);
                error_log("AdminController::systemFields - Unchecked fields: " . json_encode($uncheckedFields));
                
                // First, remove all non-critical system fields
                $db = new Database();
                $db->query("DELETE FROM system_fields WHERE is_critical = FALSE");
                $result = $db->execute();
                
                if (!$result) {
                    error_log("AdminController::systemFields - Failed to delete existing system fields");
                    $data['message'] = "Error: Failed to delete existing system fields. Check database connection.";
                    $data['message_type'] = 'danger';
                    return $this->view('admin/systemFields', $data);
                }
                
                error_log("AdminController::systemFields - Successfully deleted existing system fields");
                
                // Add new system fields
                $addedCount = 0;
                $errorCount = 0;
                
                // Log the fields that will be added
                error_log("AdminController::systemFields - Adding these fields as system fields: " . json_encode($newSystemFields));
                
                foreach ($newSystemFields as $fieldId) {
                    $isRequired = in_array($fieldId, $newRequiredFields);
                    
                    // Get entity type with a clear priority order
                    $entityType = $this->determineEntityType($fieldId, $allEntityTypes, $newEntityTypes, $templatesByType);
                    
                    // Log the entity type assignment for debugging
                    error_log("Setting entity_type for field {$fieldId} to {$entityType}");
                    
                    // Find display name from templates
                    $displayName = $fieldId;
                    foreach ($templatesByType as $type => $typeTemplates) {
                        foreach ($typeTemplates as $template) {
                            foreach ($template->fields as $field) {
                                if (isset($field['id']) && $field['id'] === $fieldId && isset($field['label'])) {
                                    $displayName = $field['label'];
                                    break 3;
                                }
                            }
                        }
                    }
                    
                    $fieldData = [
                        'field_id' => $fieldId,
                        'display_name' => $displayName,
                        'description' => '',
                        'entity_type' => $entityType,
                        'is_required' => $isRequired ? 1 : 0,
                        'can_edit_properties' => 0
                    ];
                    
                    if ($this->systemFieldManager->addSystemField($fieldData)) {
                        $addedCount++;
                    } else {
                        $errorCount++;
                    }
                }
                
                if ($errorCount === 0) {
                    $data['message'] = "System fields configuration saved successfully. {$addedCount} fields configured.";
                    $data['message_type'] = 'success';
                } else {
                    $data['message'] = "System fields configuration saved with {$errorCount} errors. Please check the logs.";
                    $data['message_type'] = 'warning';
                }
                
                // Refresh data
                $systemFields = $this->systemFieldManager->getSystemFieldsArray();
                $requiredFields = $this->systemFieldManager->getRequiredSystemFields();
                $allSystemFields = $this->systemFieldManager->getAllSystemFields();
                $entityTypes = [];
                foreach ($allSystemFields as $field) {
                    $entityTypes[$field->field_id] = $field->entity_type;
                }
                
                $data['systemFields'] = $systemFields;
                $data['requiredFields'] = $requiredFields;
                $data['entityTypes'] = $entityTypes;
            }
        }
        
        $this->view('admin/systemFields', $data);
    }
    
    /**
     * Helper method to determine entity type for a field
     * 
     * @param string $fieldId Field ID
     * @param array $allEntityTypes All entity types from form
     * @param array $newEntityTypes Entity types from form submission
     * @param array $templatesByType Templates organized by type
     * @return string Entity type
     */
    private function determineEntityType($fieldId, $allEntityTypes, $newEntityTypes, $templatesByType) {
        // Default entity type
        $entityType = 'event';
        
        // First check if we have an entity type from the all_entity_types array (highest priority)
        if (isset($allEntityTypes[$fieldId]) && !empty($allEntityTypes[$fieldId])) {
            $entityType = $allEntityTypes[$fieldId];
            error_log("Using entity type from all_entity_types for {$fieldId}: {$entityType}");
            return $entityType;
        }
        
        // Then check if we have an entity type from the form submission
        if (isset($newEntityTypes[$fieldId]) && !empty($newEntityTypes[$fieldId])) {
            $entityType = $newEntityTypes[$fieldId];
            error_log("Using entity type from form submission for {$fieldId}: {$entityType}");
            return $entityType;
        }
        
        // If not, try to determine from the default system fields
        $defaultSystemFields = $this->systemFieldManager->getDefaultSystemFields();
        if (isset($defaultSystemFields[$fieldId]['entity_type'])) {
            $entityType = $defaultSystemFields[$fieldId]['entity_type'];
            error_log("Using entity type from default system fields for {$fieldId}: {$entityType}");
            return $entityType;
        }
        
        // Try to determine from the template type
        foreach ($templatesByType as $type => $typeTemplates) {
            foreach ($typeTemplates as $template) {
                foreach ($template->fields as $field) {
                    if (isset($field['id']) && $field['id'] === $fieldId) {
                        if (strpos($type, 'vehicle') !== false) {
                            $entityType = 'vehicle';
                            error_log("Determined entity type from template type for {$fieldId}: {$entityType} (from {$type})");
                            return $entityType;
                        } elseif (strpos($type, 'registration') !== false) {
                            $entityType = 'registration';
                            error_log("Determined entity type from template type for {$fieldId}: {$entityType} (from {$type})");
                            return $entityType;
                        }
                    }
                }
            }
        }
        
        error_log("Using default entity type for {$fieldId}: {$entityType}");
        return $entityType;
    }

    /**
     * Branding settings
     */
    public function branding() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Process logo upload if provided
            $logoPath = $this->settingsModel->getSetting('site_logo', '');
            if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = 'uploads/branding/';
                
                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                    // Create an index.php file to prevent directory listing
                    if (!file_exists($uploadDir . 'index.php')) {
                        file_put_contents($uploadDir . 'index.php', '<?php // Silence is golden');
                    }
                }
                
                $fileName = 'logo_' . gmdate('U') . '_' . basename($_FILES['site_logo']['name']);
                $uploadFile = $uploadDir . $fileName;
                
                // Check if file is an image
                $check = getimagesize($_FILES['site_logo']['tmp_name']);
                if ($check !== false) {
                    // Upload file
                    if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $uploadFile)) {
                        $logoPath = '/uploads/branding/' . $fileName;
                    }
                }
            }
            
            // Process favicon upload if provided
            $faviconPath = $this->settingsModel->getSetting('site_favicon', '');
            if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = 'uploads/branding/';
                
                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                    // Create an index.php file to prevent directory listing
                    if (!file_exists($uploadDir . 'index.php')) {
                        file_put_contents($uploadDir . 'index.php', '<?php // Silence is golden');
                    }
                }
                
                $fileName = 'favicon_' . gmdate('U') . '_' . basename($_FILES['site_favicon']['name']);
                $uploadFile = $uploadDir . $fileName;
                
                // Check if file is an image
                $check = getimagesize($_FILES['site_favicon']['tmp_name']);
                if ($check !== false) {
                    // Upload file
                    if (move_uploaded_file($_FILES['site_favicon']['tmp_name'], $uploadFile)) {
                        $faviconPath = '/uploads/branding/' . $fileName;
                    }
                }
            }
            
            // Save settings
            $settings = [
                'site_logo' => $logoPath,
                'site_favicon' => $faviconPath,
                'primary_color' => trim($_POST['primary_color']),
                'secondary_color' => trim($_POST['secondary_color']),
                'accent_color' => trim($_POST['accent_color']),
                'background_color' => trim($_POST['background_color']),
                'text_color' => trim($_POST['text_color']),
                'enable_white_labeling' => isset($_POST['enable_white_labeling']) ? '1' : '0',
                'custom_css' => trim($_POST['custom_css']),
                'footer_text' => trim($_POST['footer_text'])
            ];
            
            $this->settingsModel->saveSettings($settings, 'branding');
            
            // Show success message
            $data = [
                'title' => 'Branding Settings',
                'success' => true
            ];
            
            // Get all settings
            $allSettings = $this->settingsModel->getAllSettings();
            $data = array_merge($data, $allSettings);
            
            $this->view('admin/branding', $data);
        } else {
            // Init data
            $data = [
                'title' => 'Branding Settings'
            ];
            
            // Get all settings
            $allSettings = $this->settingsModel->getAllSettings();
            $data = array_merge($data, $allSettings);
            
            // Load view
            $this->view('admin/branding', $data);
        }
    }

    /**
     * Racing Header Settings
     */
    public function headerSettings() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }

            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);

            // Save header settings
            if (isset($_POST['header_bg_image'])) {
                $this->settingsModel->setSetting('header_bg_image', trim($_POST['header_bg_image']));
            }
            if (isset($_POST['header_bg_size'])) {
                $this->settingsModel->setSetting('header_bg_size', trim($_POST['header_bg_size']));
            }
            if (isset($_POST['header_bg_position'])) {
                $this->settingsModel->setSetting('header_bg_position', trim($_POST['header_bg_position']));
            }
            if (isset($_POST['header_carbon_opacity'])) {
                // Validate carbon fiber opacity value (0-100)
                $carbonOpacity = intval($_POST['header_carbon_opacity']);
                if ($carbonOpacity < 0) $carbonOpacity = 0;
                if ($carbonOpacity > 100) $carbonOpacity = 100;
                $this->settingsModel->setSetting('header_carbon_opacity', $carbonOpacity);
            }
            if (isset($_POST['header_bg_brightness'])) {
                // Validate brightness value (0-150)
                $brightness = intval($_POST['header_bg_brightness']);
                if ($brightness < 0) $brightness = 0;
                if ($brightness > 150) $brightness = 150;
                $this->settingsModel->setSetting('header_bg_brightness', $brightness);
            }

            $this->setFlashMessage('success', 'Racing header settings updated successfully!');

            // Redirect back to prevent resubmission
            $this->redirect('admin/headerSettings');
            return;
        }

        // Get current settings
        $current_bg_image = $this->settingsModel->getSetting('header_bg_image', '');
        $header_bg_size = $this->settingsModel->getSetting('header_bg_size', 'cover');
        $header_bg_position = $this->settingsModel->getSetting('header_bg_position', 'center');
        $header_carbon_opacity = $this->settingsModel->getSetting('header_carbon_opacity', '60');
        $header_bg_brightness = $this->settingsModel->getSetting('header_bg_brightness', '40');

        $data = [
            'title' => 'Racing Header Settings',
            'current_bg_image' => $current_bg_image,
            'header_bg_size' => $header_bg_size,
            'header_bg_position' => $header_bg_position,
            'header_carbon_opacity' => $header_carbon_opacity,
            'header_bg_brightness' => $header_bg_brightness,
            'csrf_token' => $this->generateCsrfToken()
        ];

        $this->view('admin/header-settings', $data);
    }

    /**
     * Generate QR Code for a registration
     * 
     * @param int $registrationId Registration ID
     */
    public function generateQrCode($registrationId = null) {
        error_log("AdminController::generateQrCode - Method called with ID: " . $registrationId);
        
        // Check if registration ID is provided
        if (!$registrationId) {
            $this->setFlashMessage('error', 'Registration ID is required');
            $this->redirect('admin/registrations');
            return;
        }
        
        // Get registration details
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        if (!$registration) {
            $this->setFlashMessage('error', 'Registration not found');
            $this->redirect('admin/registrations');
            return;
        }
        
        // Check if QR code already exists for this registration
        if (!empty($registration->qr_code) && file_exists('uploads/qrcodes/' . $registration->qr_code)) {
            // QR code already exists, use the existing one
            $qrFilename = $registration->qr_code;
            $qrPath = 'uploads/qrcodes/' . $qrFilename;
            
            // Set success message
            $this->setFlashMessage('info', 'Using existing QR code');
            
            // Check if there's a return URL
            if (isset($_GET['return']) && !empty($_GET['return'])) {
                $returnUrl = $_GET['return'];
                // Validate the return URL to ensure it's within our site
                if (strpos($returnUrl, BASE_URL) === 0) {
                    // Redirect to the return URL
                    header('Location: ' . $returnUrl);
                    exit;
                }
            }
            
            // If no valid return URL, redirect back to registration details
            $this->redirect('admin/viewRegistration/' . $registrationId);
            return;
        }
        
        // Check if QR code directory exists, if not create it
        $uploadDir = 'uploads/qrcodes/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate a unique filename for the QR code
        $qrFilename = 'qr_' . $registrationId . '_' . time() . '.png';
        $qrPath = $uploadDir . $qrFilename;
        
        // Get QR code settings
        $qrEnabled = $this->settingsModel->getSetting('qr_code_enabled', '1');
        $qrColor = $this->settingsModel->getSetting('qr_code_color', '#000000');
        $qrBackground = $this->settingsModel->getSetting('qr_code_background', '#FFFFFF');
        $qrLogo = $this->settingsModel->getSetting('qr_code_logo', '');
        error_log("AdminController::generateQrCode - QR logo setting value: " . $qrLogo);
        
        // Convert hex colors to RGB
        $qrColorRGB = $this->hexToRgb($qrColor);
        $qrBackgroundRGB = $this->hexToRgb($qrBackground);
        
        // Create URL for QR code scanning with registration number as security token
        $baseUrl = rtrim(BASE_URL, '/');
        
        // Get registration number or generate a fallback if not available
        $registrationNumber = !empty($registration->registration_number) ? 
            $registration->registration_number : 
            'RER-' . str_pad($registrationId, 10, '0', STR_PAD_LEFT);
            
        // Create standardized URL for voting
        $qrScanUrl = URLROOT . '/show/vote/' . $registration->show_id . '/' . $registrationId;
        
        // Get or generate display number with category prefix
        $displayNumber = !empty($registration->display_number) ? 
            $registration->display_number : 
            $this->generateDisplayNumber($registration->id, $registration->category_id);
            
        // Create QR code data with URL
        $qrData = [
            'registration_id' => $registration->id,
            'registration_number' => $registrationNumber,
            'display_number' => $displayNumber,
            'show_id' => $registration->show_id,
            'vehicle' => $registration->year . ' ' . $registration->make . ' ' . $registration->model,
            'owner' => $registration->owner_name,
            'scan_url' => $qrScanUrl,
            'timestamp' => time()
        ];
        
        // Convert to JSON
        $qrJson = json_encode($qrData);
        
        // Get additional QR code settings
        $qrSize = (int)$this->settingsModel->getSetting('qrcode_size', '300');
        $qrMargin = (int)$this->settingsModel->getSetting('qrcode_margin', '10');
        $qrErrorCorrection = $this->settingsModel->getSetting('qr_code_error_correction', 'L');
        
        // Use Google Chart API to generate a QR code - reliable and widely compatible
        error_log("AdminController::generateQrCode - Using Google Chart API for QR code generation");
        
        // Create the Google Chart API URL with the appropriate parameters
        $googleChartUrl = 'https://chart.googleapis.com/chart?cht=qr&chs=' . $qrSize . 'x' . $qrSize . '&chl=' . urlencode($qrScanUrl);
        
        // Add error correction level
        if ($qrErrorCorrection === 'L') {
            $googleChartUrl .= '&chld=L|' . $qrMargin;
        } elseif ($qrErrorCorrection === 'M') {
            $googleChartUrl .= '&chld=M|' . $qrMargin;
        } elseif ($qrErrorCorrection === 'Q') {
            $googleChartUrl .= '&chld=Q|' . $qrMargin;
        } elseif ($qrErrorCorrection === 'H') {
            $googleChartUrl .= '&chld=H|' . $qrMargin;
        }
        
        // Download the QR code image
        $qrImage = @file_get_contents($googleChartUrl);
        
        if ($qrImage !== false) {
            // Save the QR code image
            file_put_contents($qrPath, $qrImage);
            error_log("AdminController::generateQrCode - Generated QR code using Google Chart API");
            
            // If we have a logo, add it to the QR code
            if (!empty($qrLogo)) {
                error_log("AdminController::generateQrCode - Logo path: " . $qrLogo);
                
                // Determine the full path to the logo file
                $logoFullPath = '';
                
                // Try different path combinations to find the logo
                $possiblePaths = [
                    $qrLogo,                         // As is
                    ltrim($qrLogo, '/'),             // Without leading slash
                    APPROOT . '/public' . $qrLogo,   // With APPROOT/public prefix
                    APPROOT . $qrLogo,               // With APPROOT prefix
                    $_SERVER['DOCUMENT_ROOT'] . $qrLogo // With document root prefix
                ];
                
                foreach ($possiblePaths as $path) {
                    error_log("AdminController::generateQrCode - Trying logo path: " . $path);
                    if (file_exists($path)) {
                        $logoFullPath = $path;
                        error_log("AdminController::generateQrCode - Found logo at: " . $logoFullPath);
                        break;
                    }
                }
                
                if (!empty($logoFullPath)) {
                    // Load the QR code image
                    $qrImage = imagecreatefrompng($qrPath);
                    
                    // Determine image type and load accordingly
                    $logoInfo = getimagesize($logoFullPath);
                    $logoType = $logoInfo[2];
                    
                    $logoImage = null;
                    if ($logoType == IMAGETYPE_JPEG) {
                        $logoImage = imagecreatefromjpeg($logoFullPath);
                    } elseif ($logoType == IMAGETYPE_PNG) {
                        $logoImage = imagecreatefrompng($logoFullPath);
                    } elseif ($logoType == IMAGETYPE_GIF) {
                        $logoImage = imagecreatefromgif($logoFullPath);
                    }
                    
                    if ($logoImage) {
                        // Get dimensions
                        $qrWidth = imagesx($qrImage);
                        $qrHeight = imagesy($qrImage);
                        $logoWidth = imagesx($logoImage);
                        $logoHeight = imagesy($logoImage);
                        
                        // Calculate logo size (max 20% of QR code)
                        $logoQrRatio = 0.2;
                        $logoNewWidth = $qrWidth * $logoQrRatio;
                        $logoNewHeight = $logoHeight * ($logoNewWidth / $logoWidth);
                        
                        // Calculate position (center)
                        $logoX = ($qrWidth - $logoNewWidth) / 2;
                        $logoY = ($qrHeight - $logoNewHeight) / 2;
                        
                        // Copy and resize logo on QR code
                        imagecopyresampled($qrImage, $logoImage, $logoX, $logoY, 0, 0, 
                                          $logoNewWidth, $logoNewHeight, $logoWidth, $logoHeight);
                        
                        // Save the final image
                        imagepng($qrImage, $qrPath);
                        
                        // Clean up
                        imagedestroy($qrImage);
                        imagedestroy($logoImage);
                        
                        error_log("AdminController::generateQrCode - Added logo to QR code");
                    } else {
                        error_log("AdminController::generateQrCode - Failed to create image from logo: " . $logoFullPath);
                    }
                } else {
                    error_log("AdminController::generateQrCode - Logo file not found at any of the attempted paths");
                }
            }
        } else {
            // If Google Chart API fails, try an alternative API
            $qrServerUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=' . $qrSize . 'x' . $qrSize . '&data=' . urlencode($qrScanUrl);
            
            // Add error correction level
            if ($qrErrorCorrection === 'L') {
                $qrServerUrl .= '&ecc=L';
            } elseif ($qrErrorCorrection === 'M') {
                $qrServerUrl .= '&ecc=M';
            } elseif ($qrErrorCorrection === 'Q') {
                $qrServerUrl .= '&ecc=Q';
            } elseif ($qrErrorCorrection === 'H') {
                $qrServerUrl .= '&ecc=H';
            }
            
            // Add margin
            $qrServerUrl .= '&margin=' . $qrMargin;
            
            // Download the QR code image
            $qrImage = @file_get_contents($qrServerUrl);
            
            if ($qrImage !== false) {
                // Save the QR code image
                file_put_contents($qrPath, $qrImage);
                error_log("AdminController::generateQrCode - Generated QR code using QRServer API");
                
                // If we have a logo, add it to the QR code
                if (!empty($qrLogo)) {
                    error_log("AdminController::generateQrCode - Logo path (fallback): " . $qrLogo);
                    
                    // Determine the full path to the logo file
                    $logoFullPath = '';
                    
                    // Try different path combinations to find the logo
                    $possiblePaths = [
                        $qrLogo,                         // As is
                        ltrim($qrLogo, '/'),             // Without leading slash
                        APPROOT . '/public' . $qrLogo,   // With APPROOT/public prefix
                        APPROOT . $qrLogo,               // With APPROOT prefix
                        $_SERVER['DOCUMENT_ROOT'] . $qrLogo // With document root prefix
                    ];
                    
                    foreach ($possiblePaths as $path) {
                        error_log("AdminController::generateQrCode - Trying logo path: " . $path);
                        if (file_exists($path)) {
                            $logoFullPath = $path;
                            error_log("AdminController::generateQrCode - Found logo at: " . $logoFullPath);
                            break;
                        }
                    }
                    
                    if (!empty($logoFullPath)) {
                        // Load the QR code image
                        $qrImage = imagecreatefrompng($qrPath);
                        
                        // Determine image type and load accordingly
                        $logoInfo = getimagesize($logoFullPath);
                        $logoType = $logoInfo[2];
                        
                        $logoImage = null;
                        if ($logoType == IMAGETYPE_JPEG) {
                            $logoImage = imagecreatefromjpeg($logoFullPath);
                        } elseif ($logoType == IMAGETYPE_PNG) {
                            $logoImage = imagecreatefrompng($logoFullPath);
                        } elseif ($logoType == IMAGETYPE_GIF) {
                            $logoImage = imagecreatefromgif($logoFullPath);
                        }
                        
                        if ($logoImage) {
                            // Get dimensions
                            $qrWidth = imagesx($qrImage);
                            $qrHeight = imagesy($qrImage);
                            $logoWidth = imagesx($logoImage);
                            $logoHeight = imagesy($logoImage);
                            
                            // Calculate logo size (max 20% of QR code)
                            $logoQrRatio = 0.2;
                            $logoNewWidth = $qrWidth * $logoQrRatio;
                            $logoNewHeight = $logoHeight * ($logoNewWidth / $logoWidth);
                            
                            // Calculate position (center)
                            $logoX = ($qrWidth - $logoNewWidth) / 2;
                            $logoY = ($qrHeight - $logoNewHeight) / 2;
                            
                            // Copy and resize logo on QR code
                            imagecopyresampled($qrImage, $logoImage, $logoX, $logoY, 0, 0, 
                                              $logoNewWidth, $logoNewHeight, $logoWidth, $logoHeight);
                            
                            // Save the final image
                            imagepng($qrImage, $qrPath);
                            
                            // Clean up
                            imagedestroy($qrImage);
                            imagedestroy($logoImage);
                            
                            error_log("AdminController::generateQrCode - Added logo to QR code (fallback)");
                        } else {
                            error_log("AdminController::generateQrCode - Failed to create image from logo: " . $logoFullPath);
                        }
                    } else {
                        error_log("AdminController::generateQrCode - Logo file not found at any of the attempted paths (fallback)");
                    }
                }
            } else {
                // If all APIs fail, create a simple error image
                $errorImage = imagecreatetruecolor(300, 300);
                $bgColor = imagecolorallocate($errorImage, 255, 255, 255);
                $textColor = imagecolorallocate($errorImage, 255, 0, 0);
                imagefill($errorImage, 0, 0, $bgColor);
                imagestring($errorImage, 5, 50, 140, "QR Code Generation Failed", $textColor);
                imagepng($errorImage, $qrPath);
                imagedestroy($errorImage);
                error_log("AdminController::generateQrCode - Failed to generate QR code using all methods");
            }
        }
        
        // Update registration with QR code filename
        // Make sure we have a valid registration ID
        if ($registrationId && is_numeric($registrationId)) {
            error_log("AdminController::generateQrCode - Updating registration $registrationId with QR code: $qrFilename");
            $result = $this->registrationModel->updateRegistration($registrationId, ['qr_code' => $qrFilename]);
            if (!$result) {
                error_log("AdminController::generateQrCode - Failed to update registration with QR code");
            }
        } else {
            error_log("AdminController::generateQrCode - Invalid registration ID for update: " . var_export($registrationId, true));
        }
        
        // Set success message
        $this->setFlashMessage('success', 'QR code generated successfully');
        
        // Check if there's a return URL
        if (isset($_GET['return']) && !empty($_GET['return'])) {
            $returnUrl = $_GET['return'];
            // Validate the return URL to ensure it's within our site
            if (strpos($returnUrl, BASE_URL) === 0) {
                // Redirect to the return URL
                header('Location: ' . $returnUrl);
                exit;
            }
        }
        
        // If no valid return URL, redirect back to registration details
        $this->redirect('admin/viewRegistration/' . $registrationId);
    }
    
    /**
     * Helper function to download a file from a URL
     * 
     * @param string $url URL to download
     * @param string $savePath Path to save the file
     * @return bool True if successful, false otherwise
     */
    private function downloadFile($url, $savePath) {
        $content = @file_get_contents($url);
        if ($content === false) {
            return false;
        }
        
        return file_put_contents($savePath, $content) !== false;
    }
    
    /**
     * Convert hex color to RGB array
     * 
     * @param string $hex Hex color code
     * @return array RGB color array
     */
    private function hexToRgb($hex) {
        $hex = ltrim($hex, '#');
        
        if (strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }
        
        return [$r, $g, $b];
    }
    
    /**
     * Generate display number with category prefix
     * 
     * @param int $registrationId Registration ID
     * @param int $categoryId Category ID
     * @return string Display number with category prefix
     */
    private function generateDisplayNumber($registrationId, $categoryId) {
        // Get category name
        $this->db->query('SELECT name FROM show_categories WHERE id = :id');
        $this->db->bind(':id', $categoryId);
        $category = $this->db->single();
        
        if (!$category) {
            return 'X-' . $registrationId; // Default prefix if category not found
        }
        
        // Clean the category name - remove parentheses and special characters
        $cleanName = preg_replace('/\([^)]*\)/', '', $category->name); // Remove content in parentheses
        $cleanName = trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $cleanName)); // Remove special characters
        
        // Get words from the cleaned name
        $words = explode(' ', $cleanName);
        $words = array_filter($words); // Remove empty elements
        $prefix = '';
        
        // Handle different category naming patterns
        if (count($words) == 0) {
            // If no valid words after cleaning, use first two letters of original name
            $prefix = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $category->name), 0, 2));
        }
        else if (count($words) == 1) {
            // For single words, use first two letters
            $prefix = strtoupper(substr($words[array_key_first($words)], 0, 2));
        }
        else if (count($words) == 2) {
            // For two words, use first letter of each
            $prefix = strtoupper(
                substr($words[array_key_first($words)], 0, 1) . 
                substr($words[array_key_first($words) + 1], 0, 1)
            );
        }
        else {
            // For multiple words, use a more distinctive approach
            // If first word is short (like "The", "A", etc.), use first letter of first word and first letter of second word
            if (strlen($words[array_key_first($words)]) <= 3) {
                $prefix = strtoupper(
                    substr($words[array_key_first($words)], 0, 1) . 
                    substr($words[array_key_first($words) + 1], 0, 1)
                );
            } 
            // Otherwise use first letter of first word and first letter of last significant word
            else {
                $prefix = strtoupper(
                    substr($words[array_key_first($words)], 0, 1) . 
                    substr($words[array_key_last($words)], 0, 1)
                );
            }
        }
        
        // Special case handling for common categories
        $lowerName = strtolower($category->name);
        if (strpos($lowerName, 'classic') !== false) {
            $prefix = 'CL';
        } else if (strpos($lowerName, 'muscle') !== false) {
            $prefix = 'MU';
        } else if (strpos($lowerName, 'truck') !== false || strpos($lowerName, 'suv') !== false) {
            $prefix = 'TR';
        } else if (strpos($lowerName, 'motorcycle') !== false || strpos($lowerName, 'bike') !== false) {
            $prefix = 'MC';
        } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'asian') !== false) {
            $prefix = 'AI';
        } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'european') !== false) {
            $prefix = 'EI';
        } else if (strpos($lowerName, 'modern') !== false) {
            $prefix = 'MO';
        } else if (strpos($lowerName, 'custom') !== false || strpos($lowerName, 'modified') !== false) {
            $prefix = 'CM';
        } else if (strpos($lowerName, 'contemporary') !== false) {
            $prefix = 'CT';
        } else if (strpos($lowerName, 'antique') !== false) {
            $prefix = 'AN';
        } else if (strpos($lowerName, 'vintage') !== false) {
            $prefix = 'VN';
        } else if (strpos($lowerName, 'sport') !== false) {
            $prefix = 'SP';
        } else if (strpos($lowerName, 'luxury') !== false) {
            $prefix = 'LX';
        } else if (strpos($lowerName, 'electric') !== false || strpos($lowerName, 'ev') !== false) {
            $prefix = 'EV';
        } else if (strpos($lowerName, 'hybrid') !== false) {
            $prefix = 'HY';
        } else if (strpos($lowerName, 'convertible') !== false) {
            $prefix = 'CV';
        } else if (strpos($lowerName, 'coupe') !== false) {
            $prefix = 'CP';
        } else if (strpos($lowerName, 'sedan') !== false) {
            $prefix = 'SD';
        } else if (strpos($lowerName, 'race') !== false || strpos($lowerName, 'racing') !== false) {
            $prefix = 'RC';
        } else if (strpos($lowerName, 'off') !== false && strpos($lowerName, 'road') !== false) {
            $prefix = 'OR';
        }
        
        // For custom categories, ensure we have a meaningful 2-letter prefix
        if (strlen($prefix) != 2) {
            // If we have a single letter, add the next most significant letter
            if (strlen($prefix) == 1) {
                // Try to find a second significant letter
                if (count($words) > 1) {
                    // Use the first letter of the second word
                    $prefix .= strtoupper(substr($words[array_key_first($words) + 1], 0, 1));
                } else if (strlen($words[array_key_first($words)]) > 1) {
                    // Use the second letter of the first word
                    $prefix .= strtoupper(substr($words[array_key_first($words)], 1, 1));
                } else {
                    // Fallback: add X
                    $prefix .= 'X';
                }
            } else if (strlen($prefix) > 2) {
                // If we have more than 2 letters, truncate to 2
                $prefix = substr($prefix, 0, 2);
            } else {
                // If we somehow have no prefix, use the first two letters of the category name
                // or the first letter + X if the name is only one character
                $categoryName = preg_replace('/[^a-zA-Z0-9]/', '', $category->name);
                if (strlen($categoryName) > 1) {
                    $prefix = strtoupper(substr($categoryName, 0, 2));
                } else if (strlen($categoryName) == 1) {
                    $prefix = strtoupper($categoryName) . 'X';
                } else {
                    $prefix = 'XX'; // Last resort fallback
                }
            }
        }
        
        return $prefix . '-' . $registrationId;
    }
    
    /**
     * QR Code settings
     */
    /**
     * QR Code Generator
     * 
     * Generate QR codes for various entities
     */
    public function qrGenerator() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Prepare data for view
        $data = [
            'title' => 'QR Code Generator'
        ];
        
        // Load view
        $this->view('admin/qr_generator', $data);
    }
    
    /**
     * Get entities for QR code generator
     * 
     * @param string $type Entity type
     * @return void
     */
    public function getEntities($type) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Access denied']);
            return;
        }
        
        // Get database connection
        try {
            $this->db = new Database();
            
            // Get entities based on type
            $entities = [];
            
            switch ($type) {
                case 'show':
                    // Check if shows table exists
                    $this->db->query("SELECT COUNT(*) as table_exists 
                                     FROM information_schema.tables 
                                     WHERE table_schema = DATABASE() 
                                     AND table_name = 'shows'");
                    $result = $this->db->single();
                    
                    if (!$result || $result->table_exists == 0) {
                        throw new Exception("Shows table does not exist");
                    }
                    
                    $this->db->query('SELECT id, name FROM shows ORDER BY name ASC');
                    $entities = $this->db->resultSet();
                    break;
                    
                case 'registration':
                    // Check if registrations table exists
                    $this->db->query("SELECT COUNT(*) as table_exists 
                                     FROM information_schema.tables 
                                     WHERE table_schema = DATABASE() 
                                     AND table_name = 'registrations'");
                    $result = $this->db->single();
                    
                    if (!$result || $result->table_exists == 0) {
                        throw new Exception("Registrations table does not exist");
                    }
                    
                    $this->db->query('SELECT r.id, CONCAT(IFNULL(r.registration_number, r.id), " - ", 
                                     IFNULL(v.year, ""), " ", IFNULL(v.make, ""), " ", IFNULL(v.model, "")) as name 
                                     FROM registrations r 
                                     LEFT JOIN vehicles v ON r.vehicle_id = v.id 
                                     ORDER BY r.id DESC');
                    $entities = $this->db->resultSet();
                    break;
                    
                case 'vehicle':
                    // Check if vehicles table exists
                    $this->db->query("SELECT COUNT(*) as table_exists 
                                     FROM information_schema.tables 
                                     WHERE table_schema = DATABASE() 
                                     AND table_name = 'vehicles'");
                    $result = $this->db->single();
                    
                    if (!$result || $result->table_exists == 0) {
                        throw new Exception("Vehicles table does not exist");
                    }
                    
                    $this->db->query('SELECT id, CONCAT(IFNULL(year, ""), " ", IFNULL(make, ""), " ", IFNULL(model, "")) as name 
                                     FROM vehicles ORDER BY id DESC');
                    $entities = $this->db->resultSet();
                    break;
                    
                case 'category':
                    // Check if show_categories table exists
                    $this->db->query("SELECT COUNT(*) as table_exists 
                                     FROM information_schema.tables 
                                     WHERE table_schema = DATABASE() 
                                     AND table_name = 'show_categories'");
                    $result = $this->db->single();
                    
                    if (!$result || $result->table_exists == 0) {
                        throw new Exception("Categories table does not exist");
                    }
                    
                    $this->db->query('SELECT sc.id, CONCAT(sc.name, " (", IFNULL(s.name, "Unknown Show"), ")") as name 
                                     FROM show_categories sc 
                                     LEFT JOIN shows s ON sc.show_id = s.id 
                                     ORDER BY sc.id DESC');
                    $entities = $this->db->resultSet();
                    break;
                    
                default:
                    $entities = [];
                    break;
            }
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode($entities);
            
        } catch (Exception $e) {
            // Return error response
            header('Content-Type: application/json');
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    /**
     * QR Code Analytics
     * 
     * Display analytics for QR code usage
     */
    public function qrAnalytics() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Database connection is already initialized in the constructor
        
        // Check if qr_scans table exists
        $this->db->query("SELECT COUNT(*) as table_exists 
                         FROM information_schema.tables 
                         WHERE table_schema = DATABASE() 
                         AND table_name = 'qr_scans'");
        $result = $this->db->single();
        $tableExists = ($result && $result->table_exists > 0);
        
        if (!$tableExists) {
            // Table doesn't exist, show empty data
            $data = [
                'title' => 'QR Code Analytics',
                'total_scans' => 0,
                'unique_users' => 0,
                'scans_by_role' => [],
                'scans_by_entity' => [],
                'recent_scans' => [],
                'table_exists' => false
            ];
            
            // Load view
            $this->view('admin/qr_analytics', $data);
            return;
        }
        
        // Get total scans
        $this->db->query('SELECT COUNT(*) as total FROM qr_scans');
        $result = $this->db->single();
        $totalScans = $result ? $result->total : 0;
        
        // Get unique users
        $this->db->query('SELECT COUNT(DISTINCT CONCAT(IFNULL(user_id, ""), IFNULL(ip_address, ""))) as total FROM qr_scans');
        $result = $this->db->single();
        $uniqueUsers = $result ? $result->total : 0;
        
        // Get scans by user role
        $this->db->query('SELECT user_role, COUNT(*) as count FROM qr_scans GROUP BY user_role ORDER BY count DESC');
        $scansByRole = $this->db->resultSet();
        
        // Get scans by entity type
        $this->db->query('SELECT entity_type, COUNT(*) as count FROM qr_scans GROUP BY entity_type ORDER BY count DESC');
        $scansByEntity = $this->db->resultSet();
        
        // Get recent scans
        $this->db->query('SELECT * FROM qr_scans ORDER BY created_at DESC LIMIT 50');
        $recentScans = $this->db->resultSet();
        
        // Prepare data for view
        $data = [
            'title' => 'QR Code Analytics',
            'total_scans' => $totalScans,
            'unique_users' => $uniqueUsers,
            'scans_by_role' => $scansByRole,
            'scans_by_entity' => $scansByEntity,
            'recent_scans' => $recentScans,
            'table_exists' => true
        ];
        
        // Load view
        $this->view('admin/qr_analytics', $data);
    }
    
    /**
     * QR Code Settings
     * 
     * Manage QR code settings
     */
    public function qrCodeSettings() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Process QR code logo upload or deletion
            $qrLogoPath = $this->settingsModel->getSetting('qr_code_logo', '');
            
            // Check if logo should be deleted
            if (isset($_POST['delete_qr_logo']) && $_POST['delete_qr_logo'] === '1') {
                // Delete the physical file if it exists
                if (!empty($qrLogoPath) && file_exists(ltrim($qrLogoPath, '/'))) {
                    @unlink(ltrim($qrLogoPath, '/'));
                }
                // Clear the logo path
                $qrLogoPath = '';
            } 
            // Process new logo upload if provided
            elseif (isset($_FILES['qr_code_logo']) && $_FILES['qr_code_logo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = 'uploads/branding/';
                
                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $fileName = 'qr_logo_' . time() . '_' . basename($_FILES['qr_code_logo']['name']);
                $uploadFile = $uploadDir . $fileName;
                
                // Check if file is an image
                $check = getimagesize($_FILES['qr_code_logo']['tmp_name']);
                if ($check !== false) {
                    // Delete old logo file if it exists
                    if (!empty($qrLogoPath) && file_exists(ltrim($qrLogoPath, '/'))) {
                        @unlink(ltrim($qrLogoPath, '/'));
                    }
                    
                    // Upload new file
                    if (move_uploaded_file($_FILES['qr_code_logo']['tmp_name'], $uploadFile)) {
                        $qrLogoPath = '/uploads/branding/' . $fileName;
                    }
                }
            }
            
            // Save settings
            $settings = [
                'qr_code_enabled' => isset($_POST['qr_code_enabled']) ? '1' : '0',
                'qr_code_logo' => $qrLogoPath,
                'qr_code_color' => trim($_POST['qr_code_color']),
                'qr_code_background' => trim($_POST['qr_code_background']),
                'qrcode_size' => intval($_POST['qrcode_size']) > 0 ? intval($_POST['qrcode_size']) : 300,
                'qrcode_margin' => intval($_POST['qrcode_margin']) >= 0 ? intval($_POST['qrcode_margin']) : 10,
                'qr_code_error_correction' => in_array($_POST['qr_code_error_correction'], ['L', 'M', 'Q', 'H']) ? $_POST['qr_code_error_correction'] : 'L'
            ];
            
            $this->settingsModel->saveSettings($settings, 'judging');
            
            // Show success message
            $data = [
                'title' => 'QR Code Settings',
                'success' => true
            ];
            
            // Get all settings
            $allSettings = $this->settingsModel->getAllSettings();
            $data = array_merge($data, $allSettings);
            
            $this->view('admin/qr_code_settings', $data);
        } else {
            // Init data
            $data = [
                'title' => 'QR Code Settings'
            ];
            
            // Get all settings
            $allSettings = $this->settingsModel->getAllSettings();
            $data = array_merge($data, $allSettings);
            
            // Load view
            $this->view('admin/qr_code_settings', $data);
        }
    }
    
    /**
     * Download QR Code
     * 
     * @param int $registrationId Registration ID
     */
    public function downloadQrCode($registrationId = null) {
        // Check if registration ID is provided
        if (!$registrationId) {
            $this->setFlashMessage('error', 'Registration ID is required');
            $this->redirect('admin/registrations');
            return;
        }
        
        // Get registration details
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        if (!$registration) {
            $this->setFlashMessage('error', 'Registration not found');
            $this->redirect('admin/registrations');
            return;
        }
        
        // Check if QR code exists
        if (empty($registration->qr_code)) {
            // Generate QR code if it doesn't exist
            $this->generateQrCode($registrationId);
            
            // Refresh registration data
            $registration = $this->registrationModel->getRegistrationById($registrationId);
            
            if (empty($registration->qr_code)) {
                $this->setFlashMessage('error', 'Failed to generate QR code');
                $this->redirect('admin/viewRegistration/' . $registrationId);
                return;
            }
        }
        
        // Set file path
        $filePath = 'public/uploads/qrcodes/' . $registration->qr_code;
        
        // Check if file exists
        if (!file_exists($filePath)) {
            $this->setFlashMessage('error', 'QR code file not found');
            $this->redirect('admin/viewRegistration/' . $registrationId);
            return;
        }
        
        // Set headers for download
        header('Content-Description: File Transfer');
        header('Content-Type: image/png');
        header('Content-Disposition: attachment; filename="' . $registration->qr_code . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filePath));
        
        // Clear output buffer
        ob_clean();
        flush();
        
        // Read file and output
        readfile($filePath);
        exit;
    }
    
    /**
     * Print QR Code
     * 
     * @param int $registrationId Registration ID
     */
    public function printQrCode($registrationId = null) {
        // Check if registration ID is provided
        if (!$registrationId) {
            $this->setFlashMessage('error', 'Registration ID is required');
            $this->redirect('admin/registrations');
            return;
        }
        
        // Get registration details
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        if (!$registration) {
            $this->setFlashMessage('error', 'Registration not found');
            $this->redirect('admin/registrations');
            return;
        }
        
        // Check if QR code exists
        if (empty($registration->qr_code)) {
            // Generate QR code if it doesn't exist
            $this->generateQrCode($registrationId);
            
            // Refresh registration data
            $registration = $this->registrationModel->getRegistrationById($registrationId);
            
            if (empty($registration->qr_code)) {
                $this->setFlashMessage('error', 'Failed to generate QR code');
                $this->redirect('admin/viewRegistration/' . $registrationId);
                return;
            }
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Prepare data for view
        $data = [
            'title' => 'Print QR Code',
            'registration' => $registration,
            'show' => $show
        ];
        
        // Load print view
        $this->view('admin/print_qr_code', $data);
    }
    
    /**
     * Printable templates
     */
    public function printableTemplates() {
        // Get all templates
        $templates = $this->printableTemplateModel->getTemplates();
        
        $data = [
            'title' => 'Printable Templates',
            'templates' => $templates
        ];
        
        $this->view('admin/printable_templates', $data);
    }
    
    /**
     * Add printable template
     */
    public function addPrintableTemplate() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $template_html = trim($_POST['template_html']);
            
            // Clean the template HTML from any jQuery UI artifacts
            $template_html = $this->cleanTemplateHTML($template_html);
            
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'template_html' => $template_html,
                'template_css' => trim($_POST['template_css']),
                'template_data' => isset($_POST['template_data']) ? trim($_POST['template_data']) : '{"width":800,"height":1100,"elements":[]}',
                'is_default' => isset($_POST['is_default']) ? true : false,
                'name_err' => '',
                'template_html_err' => '',
                'title' => 'Add Printable Template'
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a template name';
            }
            
            // Validate template HTML
            if (empty($data['template_html'])) {
                $data['template_html_err'] = 'Template HTML is required';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['template_html_err'])) {
                // Create template
                if ($this->printableTemplateModel->createTemplate($data)) {
                    $this->redirect('admin/printableTemplates');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/add_printable_template', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'template_html' => '<div class="template-container"></div>',
                'template_css' => '.template-container { position: relative; width: 800px; height: 1100px; background-color: white; margin: 0 auto; }',
                'template_data' => '{"width":800,"height":1100,"elements":[]}',
                'is_default' => false,
                'name_err' => '',
                'template_html_err' => '',
                'title' => 'Add Printable Template'
            ];
            
            // Load view
            $this->view('admin/add_printable_template', $data);
        }
    }
    
    /**
     * Edit printable template
     * 
     * @param int $id Template ID
     */
    public function editPrintableTemplate($id) {
        // Get template
        $template = $this->printableTemplateModel->getTemplateById($id);
        
        if (!$template) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $template_html = trim($_POST['template_html']);
            
            // Clean the template HTML from any jQuery UI artifacts
            $template_html = $this->cleanTemplateHTML($template_html);
            
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'template_html' => $template_html,
                'template_css' => trim($_POST['template_css']),
                'template_data' => isset($_POST['template_data']) ? trim($_POST['template_data']) : '{"width":800,"height":1100,"elements":[]}',
                'is_default' => isset($_POST['is_default']) ? true : false,
                'name_err' => '',
                'template_html_err' => '',
                'title' => 'Edit Printable Template'
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a template name';
            }
            
            // Validate template HTML
            if (empty($data['template_html'])) {
                $data['template_html_err'] = 'Template HTML is required';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['template_html_err'])) {
                // Update template
                if ($this->printableTemplateModel->updateTemplate($data)) {
                    $this->redirect('admin/printableTemplates');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_printable_template', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $template->id,
                'name' => $template->name,
                'description' => $template->description,
                'template_html' => $template->template_html ?? '<div class="template-container"></div>',
                'template_css' => $template->template_css ?? '.template-container { position: relative; width: 800px; height: 1100px; background-color: white; margin: 0 auto; }',
                'template_data' => $template->template_data ?? '{"width":800,"height":1100,"elements":[]}',
                'is_default' => $template->is_default,
                'name_err' => '',
                'template_html_err' => '',
                'title' => 'Edit Printable Template'
            ];
            
            // Load view
            $this->view('admin/edit_printable_template', $data);
        }
    }
    
    /**
     * Delete printable template
     * 
     * @param int $id Template ID
     */
    /**
     * Clean template HTML from jQuery UI artifacts
     * 
     * @param string $html Template HTML
     * @return string Cleaned HTML
     */
    private function cleanTemplateHTML($html) {
        // Check if the HTML is already entity-encoded
        if (strpos($html, '&lt;') !== false) {
            // Decode HTML entities first
            $html = html_entity_decode($html);
        }
        
        // Use DOMDocument to parse and clean the HTML
        $dom = new \DOMDocument();
        
        // Suppress warnings from malformed HTML
        libxml_use_internal_errors(true);
        
        // Load the HTML
        $dom->loadHTML('<div id="wrapper">' . $html . '</div>', LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        
        // Reset errors
        libxml_clear_errors();
        
        // Find all elements with class containing 'ui-resizable-handle'
        $xpath = new \DOMXPath($dom);
        $handles = $xpath->query("//*[contains(@class, 'ui-resizable-handle')]");
        
        // Remove all handle elements
        foreach ($handles as $handle) {
            $handle->parentNode->removeChild($handle);
        }
        
        // Clean up UI classes from all elements
        $elements = $xpath->query('//*[@class]');
        foreach ($elements as $element) {
            $classes = $element->getAttribute('class');
            $classes = preg_replace('/\bui-[^\s]+\s?/', '', $classes);
            $element->setAttribute('class', trim($classes));
        }
        
        // Get the cleaned HTML
        $wrapper = $dom->getElementById('wrapper');
        $cleanedHtml = '';
        
        if ($wrapper) {
            // Get all child nodes of the wrapper
            $children = $wrapper->childNodes;
            foreach ($children as $child) {
                $cleanedHtml .= $dom->saveHTML($child);
            }
        } else {
            // Fallback if wrapper not found
            $cleanedHtml = $html;
        }
        
        return $cleanedHtml;
    }
    
    public function deletePrintableTemplate($id) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete template
            if ($this->printableTemplateModel->deleteTemplate($id)) {
                $this->redirect('admin/printableTemplates');
            } else {
                $this->redirect('home/error/Cannot%20delete%20the%20only%20default%20template');
            }
        } else {
            $this->redirect('admin/printableTemplates');
        }
    }
    
    /**
     * Set printable template as default
     * 
     * @param int $id Template ID
     */
    public function setDefaultTemplate($id) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Set as default
            if ($this->printableTemplateModel->setAsDefault($id)) {
                $this->redirect('admin/printableTemplates');
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/printableTemplates');
        }
    }
    
    /**
     * Judging metrics
     * 
     * @param int $showId Show ID (optional)
     */
    public function judgingMetrics($showId = null) {
        // Get all shows if no show ID provided
        if (!$showId) {
            $shows = $this->showModel->getShows();
            
            $data = [
                'title' => 'Judging Metrics',
                'shows' => $shows
            ];
            
            $this->view('admin/judging_metrics_shows', $data);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get metrics for this show
        $metrics = $this->judgingModel->getMetricsByShowId($showId);
        
        $data = [
            'title' => 'Judging Metrics for ' . $show->name,
            'show' => $show,
            'metrics' => $metrics
        ];
        
        $this->view('admin/judging_metrics', $data);
    }
    
    /**
     * Add judging metric
     * 
     * @param int $showId Show ID
     */
    public function addJudgingMetric($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'max_score' => (int)trim($_POST['max_score']),
                'weight' => (float)trim($_POST['weight']),
                'display_order' => (int)trim($_POST['display_order']),
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'title' => 'Add Judging Metric',
                'show' => $show
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a metric name';
            }
            
            // Validate max score
            if ($data['max_score'] <= 0) {
                $data['max_score_err'] = 'Max score must be greater than 0';
            }
            
            // Validate weight
            if ($data['weight'] <= 0) {
                $data['weight_err'] = 'Weight must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['max_score_err']) && empty($data['weight_err'])) {
                // Add metric
                if ($this->judgingModel->addMetric($data)) {
                    // Set success message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Judging metric has been added successfully.'
                    ];
                    
                    // Check if there's a referrer in the form data
                    if (isset($_POST['referrer']) && !empty($_POST['referrer'])) {
                        $referrer = $_POST['referrer'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from form
                            header('Location: ' . $referrer);
                            exit;
                        }
                    } 
                    // Check if there's a referrer in the HTTP headers
                    else if (isset($_SERVER['HTTP_REFERER']) && !empty($_SERVER['HTTP_REFERER'])) {
                        $referrer = $_SERVER['HTTP_REFERER'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from headers
                            header('Location: ' . $referrer);
                            exit;
                        }
                    }
                    
                    // Fallback to judging metrics page if no valid referrer
                    $this->redirect('admin/judgingMetrics/' . $showId);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/add_judging_metric', $data);
            }
        } else {
            // Init data
            $data = [
                'show_id' => $showId,
                'name' => '',
                'description' => '',
                'max_score' => 10,
                'weight' => 1.0,
                'display_order' => 0,
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'title' => 'Add Judging Metric',
                'show' => $show
            ];
            
            // Load view
            $this->view('admin/add_judging_metric', $data);
        }
    }
    
    /**
     * Edit judging metric
     * 
     * @param int $id Metric ID
     */
    public function editJudgingMetric($id) {
        // Get metric
        $metric = $this->judgingModel->getMetricById($id);
        
        if (!$metric) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($metric->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'show_id' => $metric->show_id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'max_score' => (int)trim($_POST['max_score']),
                'weight' => (float)trim($_POST['weight']),
                'display_order' => (int)trim($_POST['display_order']),
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'title' => 'Edit Judging Metric',
                'show' => $show
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a metric name';
            }
            
            // Validate max score
            if ($data['max_score'] <= 0) {
                $data['max_score_err'] = 'Max score must be greater than 0';
            }
            
            // Validate weight
            if ($data['weight'] <= 0) {
                $data['weight_err'] = 'Weight must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['max_score_err']) && empty($data['weight_err'])) {
                // Update metric
                if ($this->judgingModel->updateMetric($data)) {
                    $this->redirect('admin/judgingMetrics/' . $metric->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_judging_metric', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $metric->id,
                'show_id' => $metric->show_id,
                'name' => $metric->name,
                'description' => $metric->description,
                'max_score' => $metric->max_score,
                'weight' => $metric->weight,
                'display_order' => $metric->display_order,
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'title' => 'Edit Judging Metric',
                'show' => $show
            ];
            
            // Load view
            $this->view('admin/edit_judging_metric', $data);
        }
    }
    
    /**
     * Delete judging metric
     * 
     * @param int $id Metric ID
     */
    public function deleteJudgingMetric($id) {
        // Get metric
        $metric = $this->judgingModel->getMetricById($id);
        
        if (!$metric) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete metric
            if ($this->judgingModel->deleteMetric($id)) {
                $this->redirect('admin/judgingMetrics/' . $metric->show_id);
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/judgingMetrics/' . $metric->show_id);
        }
    }
    
    /**
     * Judging
     * 
     * @param int $showId Show ID
     */
    public function judging($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // AwardModel is already initialized in the constructor
        
        // Get categories for this show
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get judges for this show
        $judges = $this->judgingModel->getJudgesByShowId($showId);
        
        // Get awards for this show
        $awards = $this->awardModel->getAwardsByShowId($showId);
        
        // Get judging statistics
        $total_vehicles = $this->registrationModel->getRegistrationCountByShow($showId);
        $judged_vehicles = $this->judgingModel->getJudgedVehicleCountByShow($showId);
        
        // Process categories to add judging progress
        foreach ($categories as &$category) {
            // Get vehicle count for this category
            $category->vehicle_count = $this->registrationModel->getRegistrationCountByCategory($category->id);
            
            // Get judged vehicle count for this category
            $category->judged_count = $this->judgingModel->getJudgedVehicleCountByCategory($category->id);
            
            // Get assigned judges for this category
            $category->assigned_judges = $this->judgingModel->getJudgesByCategory($category->id);
        }
        
        // Get top vehicles overall (placeholder for now)
        $top_vehicles = [];
        
        $data = [
            'title' => 'Judging for ' . $show->name,
            'show' => $show,
            'categories' => $categories,
            'judges' => $judges,
            'awards' => $awards,
            'total_vehicles' => $total_vehicles,
            'judged_vehicles' => $judged_vehicles,
            'top_vehicles' => $top_vehicles
        ];
        
        $this->view('admin/judging', $data);
    }
    
    /**
     * Assign Judges
     * 
     * @param int $showId Show ID
     */
    public function assignJudges($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get all users who can be judges (admins and coordinators)
        $potentialJudges = $this->userModel->getUsersByRoles(['admin', 'coordinator']);
        
        // Get current judges for this show
        $currentJudges = $this->judgingModel->getJudgesByShowId($showId);
        
        // Get categories for this show
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get assigned categories for each judge
        $judgeAssignedCategories = [];
        foreach ($currentJudges as $judge) {
            $assignedCategories = $this->judgingModel->getJudgeAssignedCategories($judge->id);
            $judgeAssignedCategories[$judge->user_id] = $assignedCategories;
        }
        
        // Process form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Get selected judges
            $selectedJudges = isset($_POST['judges']) ? $_POST['judges'] : [];
            
            // Get judge category assignments
            $judgeCategories = isset($_POST['judge_categories']) ? $_POST['judge_categories'] : [];
            
            // Update judges
            $success = $this->judgingModel->updateShowJudges($showId, $selectedJudges, $judgeCategories);
            
            if ($success) {
                setFlashMessage('judge_message', 'Judges assigned successfully', 'success');
                $this->redirect('admin/judging/' . $showId);
                return;
            } else {
                setFlashMessage('judge_message', 'Error assigning judges', 'danger');
            }
        }
        
        // Prepare data for the view
        $data = [
            'title' => 'Assign Judges for ' . $show->name,
            'show' => $show,
            'potential_judges' => $potentialJudges,
            'current_judges' => $currentJudges,
            'categories' => $categories,
            'judge_assigned_categories' => $judgeAssignedCategories
        ];
        
        $this->view('admin/assign_judges', $data);
    }
    
    /**
     * Category Judging
     * 
     * @param int $categoryId Category ID
     */
    public function categoryJudging($categoryId) {
        // Get category
        $category = $this->showModel->getCategoryById($categoryId);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($category->show_id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get registrations for this category
        $registrations = $this->registrationModel->getRegistrationsByCategory($categoryId);
        
        // Get judges assigned to this category
        $judges = $this->judgingModel->getJudgesByCategory($categoryId);
        
        // Get judging metrics for this show
        $metrics = $this->judgingModel->getMetricsByShowId($show->id);
        
        // Process form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Get scores
            $scores = isset($_POST['scores']) ? $_POST['scores'] : [];
            
            // Save scores
            $success = $this->judgingModel->saveScores($scores);
            
            if ($success) {
                flash('judging_message', 'Scores saved successfully');
                $this->redirect('admin/categoryJudging/' . $categoryId);
                return;
            } else {
                flash('judging_message', 'Error saving scores', 'alert alert-danger');
            }
        }
        
        // Add judging status to each registration
        foreach ($registrations as &$registration) {
            $registration->judging_status = [];
            
            // Check judging status for each judge
            foreach ($judges as $judge) {
                // Get scores for this registration and judge
                $judgeScores = $this->judgingModel->getScoresByRegistrationAndJudge($registration->id, $judge->id);
                
                // Debug log
                error_log("Registration {$registration->id}, Judge {$judge->id}: " . count($judgeScores) . " scores found");
                
                if (empty($judgeScores)) {
                    // No scores found
                    $registration->judging_status[$judge->id] = 'not_judged';
                } else {
                    // Check if all scores are final (not drafts)
                    $allFinal = true;
                    foreach ($judgeScores as $score) {
                        if ($score->is_draft) {
                            $allFinal = false;
                            break;
                        }
                    }
                    
                    // Check if all metrics have been scored
                    $allMetricsScored = count($judgeScores) >= count($metrics);
                    
                    if ($allMetricsScored && $allFinal) {
                        $registration->judging_status[$judge->id] = 'complete';
                    } else if ($allMetricsScored) {
                        $registration->judging_status[$judge->id] = 'draft';
                    } else {
                        $registration->judging_status[$judge->id] = 'partial';
                    }
                }
            }
        }
        
        // Prepare data for the view
        $data = [
            'title' => 'Judge ' . $category->name . ' for ' . $show->name,
            'show' => $show,
            'category' => $category,
            'registrations' => $registrations,
            'judges' => $judges,
            'metrics' => $metrics
        ];
        
        $this->view('admin/category_judging', $data);
    }
    
    /**
     * Judge a vehicle
     * 
     * @param int $registrationId Registration ID
     * @return void
     */
    public function judgeVehicle($registrationId = null) {
        // Check if user is logged in
        if (!isLoggedIn()) {
            $this->redirect('users/login');
            return;
        }
        
        // Check if user is an admin
        if (!isAdmin()) {
            $this->redirect('home/index');
            return;
        }
        
        // Check if registration ID is provided
        if (!$registrationId) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get category
        $category = $this->categoryModel->getCategoryById($registration->category_id);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get judges assigned to this category
        $judges = $this->judgingModel->getJudgesByCategory($registration->category_id);
        
        // Get judging metrics for this show
        $metrics = $this->judgingModel->getMetricsByShowId($show->id);
        
        // Process form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Get scores
            $scores = isset($_POST['scores']) ? $_POST['scores'] : [];
            $judgeId = isset($_POST['judge_id']) ? (int)$_POST['judge_id'] : (int)$_SESSION['user_id'];
            $isDraft = isset($_POST['is_draft']) ? (bool)$_POST['is_draft'] : false;
            
            // Debug log
            error_log("Processing score submission for registration {$registrationId}, judge {$judgeId}, isDraft: " . ($isDraft ? 'true' : 'false'));
            error_log("Received " . count($scores) . " scores to save");
            
            // Validate scores
            if (empty($scores)) {
                error_log("No scores submitted");
                setFlashMessage('score_message', 'No scores submitted', 'danger');
            } else {
                // Prepare scores for saving
                $scoresToSave = [];
                foreach ($scores as $metricId => $score) {
                    // Validate score
                    $metricId = (int)$metricId;
                    $score = (int)$score;
                    
                    // Debug log
                    error_log("Processing score for metric {$metricId}: {$score}");
                    
                    // Add to array for saving
                    $scoresToSave[] = [
                        'registration_id' => $registrationId,
                        'judge_id' => $judgeId,
                        'metric_id' => $metricId,
                        'score' => $score,
                        'comments' => '', // Default empty comment
                        'is_draft' => $isDraft ? 1 : 0
                    ];
                }
                
                // Save scores
                $success = $this->judgingModel->saveScores($scoresToSave);
                
                if ($success) {
                    error_log("Scores saved successfully");
                    setFlashMessage('score_message', 'Scores saved successfully', 'success');
                    
                    // Stay on the same page to show the success message
                    // and allow the user to continue judging
                    $this->redirect('admin/judgeVehicle/' . $registrationId);
                    return;
                } else {
                    error_log("Error saving scores");
                    setFlashMessage('score_message', 'Error saving scores. Please try again.', 'danger');
                }
            }
        }
        
        // Get existing scores for this registration
        $existingScores = [];
        foreach ($judges as $judge) {
            $existingScores[$judge->id] = $this->judgingModel->getScoresByRegistrationAndJudge($registrationId, $judge->id);
        }
        
        // Get vehicle images
        $vehicle_images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        
        $data = [
            'title' => 'Judge Vehicle',
            'registration' => $registration,
            'show' => $show,
            'category' => $category,
            'judges' => $judges,
            'metrics' => $metrics,
            'existingScores' => $existingScores,
            'vehicle_images' => $vehicle_images
        ];
        
        $this->view('admin/judge_vehicle', $data);
    }
    
    /**
     * View scores for a vehicle
     * 
     * @param int $registrationId Registration ID
     * @return void
     */
    public function viewScores($registrationId = null) {
        // Check if user is logged in
        if (!isLoggedIn()) {
            $this->redirect('users/login');
            return;
        }
        
        // Check if user is an admin
        if (!isAdmin()) {
            $this->redirect('home/index');
            return;
        }
        
        // Check if registration ID is provided
        if (!$registrationId) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get category
        $category = $this->categoryModel->getCategoryById($registration->category_id);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get judges assigned to this category
        $judges = $this->judgingModel->getJudgesByCategory($registration->category_id);
        
        // Get judging metrics for this show
        $metrics = $this->judgingModel->getMetricsByShowId($show->id);
        
        // Get scores for this registration (including drafts)
        $scores = $this->judgingModel->getScoresByRegistration($registrationId, true);
        
        // Debug log
        error_log("ViewScores: Found " . count($scores) . " scores for registration {$registrationId}");
        
        // Dump the raw scores for debugging
        error_log("Raw scores data: " . print_r($scores, true));
        
        // Organize scores by judge and metric
        $organizedScores = [];
        foreach ($scores as $score) {
            // Debug log for each score
            error_log("Processing score: judge_id={$score->judge_id}, metric_id={$score->metric_id}, score={$score->score}, is_draft=" . ($score->is_draft ? 'true' : 'false'));
            
            // Use the judge_id as the key for organizedScores
            $judgeKey = $score->judge_id;
            
            if (!isset($organizedScores[$judgeKey])) {
                $organizedScores[$judgeKey] = [
                    'judge_name' => $score->judge_name,
                    'scores' => [],
                    'draft_status' => []
                ];
            }
            
            $organizedScores[$judgeKey]['scores'][$score->metric_id] = $score->score;
            $organizedScores[$judgeKey]['draft_status'][$score->metric_id] = $score->is_draft;
        }
        
        // If we have scores but no organized scores, there might be a mismatch between judge IDs
        if (!empty($scores) && empty($organizedScores)) {
            error_log("WARNING: Scores found but not organized. Possible judge ID mismatch.");
            
            // Create a fallback organization by user ID
            foreach ($scores as $score) {
                $judgeKey = $score->judge_id;
                
                if (!isset($organizedScores[$judgeKey])) {
                    $organizedScores[$judgeKey] = [
                        'judge_name' => $score->judge_name,
                        'scores' => [],
                        'draft_status' => []
                    ];
                }
                
                $organizedScores[$judgeKey]['scores'][$score->metric_id] = $score->score;
                $organizedScores[$judgeKey]['draft_status'][$score->metric_id] = $score->is_draft;
            }
        }
        
        // Debug log for organized scores
        error_log("Organized scores structure: " . print_r($organizedScores, true));
        
        // Debug log for organized scores
        error_log("Organized scores for " . count($organizedScores) . " judges");
        
        // Calculate total scores
        $totalScores = [];
        $maxPossibleScore = 0;
        
        foreach ($judges as $judge) {
            $totalScores[$judge->id] = 0;
            
            foreach ($metrics as $metric) {
                // Add to max possible score only once per metric
                if ($judge === reset($judges)) {
                    $maxPossibleScore += $metric->max_score;
                }
                
                if (isset($organizedScores[$judge->id]['scores'][$metric->id])) {
                    // Only count non-draft scores in the total
                    if (!isset($organizedScores[$judge->id]['draft_status'][$metric->id]) || 
                        !$organizedScores[$judge->id]['draft_status'][$metric->id]) {
                        $totalScores[$judge->id] += $organizedScores[$judge->id]['scores'][$metric->id];
                    }
                }
            }
            
            // Debug log for total scores
            error_log("Judge {$judge->id} ({$judge->name}): Total score = " . ($totalScores[$judge->id] ?? 'not set'));
        }
        
        $data = [
            'title' => 'View Scores',
            'registration' => $registration,
            'show' => $show,
            'category' => $category,
            'judges' => $judges,
            'metrics' => $metrics,
            'scores' => $scores,  // Add raw scores for debugging
            'organizedScores' => $organizedScores,
            'totalScores' => $totalScores,
            'maxPossibleScore' => $maxPossibleScore
        ];
        
        $this->view('admin/view_scores', $data);
    }
    
    /**
     * Add Award
     * 
     * @param int $showId Show ID
     */
    public function addAward($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // AwardModel is already initialized in the constructor
        
        // Get categories for this show
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registrations for this show
        $registrations = $this->registrationModel->getShowRegistrations($showId);
        
        // Process form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Initialize data
            $data = [
                'show_id' => $showId,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'type' => $_POST['type'],
                'category_id' => null,
                'registration_id' => null,
                'name_err' => '',
                'type_err' => ''
            ];
            
            // Set category_id or registration_id based on type
            if ($data['type'] === 'category' && !empty($_POST['category_id'])) {
                $data['category_id'] = (int)$_POST['category_id'];
            } elseif ($data['type'] === 'special' && !empty($_POST['registration_id'])) {
                $data['registration_id'] = (int)$_POST['registration_id'];
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter an award name';
            }
            
            // Validate type
            if (!in_array($data['type'], ['category', 'special', 'overall'])) {
                $data['type_err'] = 'Please select a valid award type';
            }
            
            // Make sure there are no errors
            if (empty($data['name_err']) && empty($data['type_err'])) {
                // Add award
                if ($this->awardModel->addAward($data)) {
                    flash('award_message', 'Award added successfully');
                    $this->redirect('admin/judging/' . $showId);
                    return;
                } else {
                    flash('award_message', 'Something went wrong', 'alert alert-danger');
                }
            }
            
            // Load view with errors
            $data['title'] = 'Add Award for ' . $show->name;
            $data['show'] = $show;
            $data['categories'] = $categories;
            $data['registrations'] = $registrations;
            
            $this->view('admin/add_award', $data);
        } else {
            // Initialize data
            $data = [
                'title' => 'Add Award for ' . $show->name,
                'show' => $show,
                'categories' => $categories,
                'registrations' => $registrations,
                'name' => '',
                'description' => '',
                'type' => 'special',
                'category_id' => null,
                'registration_id' => null,
                'name_err' => '',
                'type_err' => ''
            ];
            
            $this->view('admin/add_award', $data);
        }
    }
    
    /**
     * Age weights
     * 
     * @param int $showId Show ID (optional)
     */
    public function ageWeights($showId = null) {
        // Get all shows if no show ID provided
        if (!$showId) {
            $shows = $this->showModel->getShows();
            
            $data = [
                'title' => 'Age Weights',
                'shows' => $shows
            ];
            
            $this->view('admin/age_weights_shows', $data);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get age weights for this show
        $ageWeights = $this->judgingModel->getAgeWeightsByShowId($showId);
        
        $data = [
            'title' => 'Age Weights for ' . $show->name,
            'show' => $show,
            'age_weights' => $ageWeights
        ];
        
        $this->view('admin/age_weights', $data);
    }
    
    /**
     * Add age weight
     * 
     * @param int $showId Show ID
     */
    public function addAgeWeight($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'min_age' => (int)trim($_POST['year_start']),
                'max_age' => (int)trim($_POST['year_end']),
                'multiplier' => (float)trim($_POST['multiplier']),
                'description' => trim($_POST['description'] ?? ''),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'year_start_err' => '',
                'year_end_err' => '',
                'multiplier_err' => '',
                'description_err' => '',
                'is_active_err' => '',
                'title' => 'Add Age Weight',
                'show' => $show,
                'year_start' => (int)trim($_POST['year_start']),
                'year_end' => (int)trim($_POST['year_end'])
            ];
            
            // Validate year start
            if ($data['year_start'] < 1900 || $data['year_start'] > gmdate('Y')) {
                $data['year_start_err'] = 'Start year must be between 1900 and ' . gmdate('Y');
            }
            
            // Validate year end
            if ($data['year_end'] < 1900 || $data['year_end'] > gmdate('Y')) {
                $data['year_end_err'] = 'End year must be between 1900 and ' . gmdate('Y');
            } elseif ($data['year_end'] < $data['year_start']) {
                $data['year_end_err'] = 'End year must be greater than or equal to start year';
            }
            
            // Validate multiplier
            if ($data['multiplier'] <= 0) {
                $data['multiplier_err'] = 'Multiplier must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['year_start_err']) && empty($data['year_end_err']) && empty($data['multiplier_err'])) {
                // Add age weight
                if ($this->judgingModel->addAgeWeight($data)) {
                    // Set success message
                    $_SESSION['flash_message'] = [
                        'type' => 'success',
                        'message' => 'Age weight has been added successfully.'
                    ];
                    
                    // Check if there's a referrer in the form data
                    if (isset($_POST['referrer']) && !empty($_POST['referrer'])) {
                        $referrer = $_POST['referrer'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from form
                            header('Location: ' . $referrer);
                            exit;
                        }
                    } 
                    // Check if there's a referrer in the HTTP headers
                    else if (isset($_SERVER['HTTP_REFERER']) && !empty($_SERVER['HTTP_REFERER'])) {
                        $referrer = $_SERVER['HTTP_REFERER'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from headers
                            header('Location: ' . $referrer);
                            exit;
                        }
                    }
                    
                    // Fallback to show page with age weights section
                    $this->redirect('admin/editShow/' . $showId . '#age-weights');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/add_age_weight', $data);
            }
        } else {
            // Init data
            $data = [
                'show_id' => $showId,
                'min_age' => gmdate('Y') - 50,
                'max_age' => gmdate('Y') - 30,
                'multiplier' => 1.5,
                'description' => '',
                'is_active' => 1,
                'year_start' => gmdate('Y') - 50,
                'year_end' => gmdate('Y') - 30,
                'year_start_err' => '',
                'year_end_err' => '',
                'multiplier_err' => '',
                'description_err' => '',
                'is_active_err' => '',
                'title' => 'Add Age Weight',
                'show' => $show
            ];
            
            // Load view
            $this->view('admin/add_age_weight', $data);
        }
    }
    
    /**
     * Edit age weight
     * 
     * @param int $id Age weight ID
     */
    public function editAgeWeight($id) {
        // Get age weight
        $ageWeight = $this->judgingModel->getAgeWeightById($id);
        
        if (!$ageWeight) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($ageWeight->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'show_id' => $ageWeight->show_id,
                'min_age' => (int)trim($_POST['min_age']),
                'max_age' => (int)trim($_POST['max_age']),
                'multiplier' => (float)trim($_POST['multiplier']),
                'description' => trim($_POST['description'] ?? ''),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'min_age_err' => '',
                'max_age_err' => '',
                'multiplier_err' => '',
                'description_err' => '',
                'is_active_err' => '',
                'title' => 'Edit Age Weight',
                'show' => $show,
                'ageWeight' => $ageWeight
            ];
            
            // Validate min age
            if ($data['min_age'] < 1900 || $data['min_age'] > gmdate('Y')) {
                $data['min_age_err'] = 'Min age must be between 1900 and ' . gmdate('Y');
            }
            
            // Validate max age
            if ($data['max_age'] < 1900 || $data['max_age'] > gmdate('Y')) {
                $data['max_age_err'] = 'Max age must be between 1900 and ' . gmdate('Y');
            } elseif ($data['max_age'] < $data['min_age']) {
                $data['max_age_err'] = 'Max age must be greater than or equal to min age';
            }
            
            // Validate multiplier
            if ($data['multiplier'] <= 0) {
                $data['multiplier_err'] = 'Multiplier must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['min_age_err']) && empty($data['max_age_err']) && empty($data['multiplier_err'])) {
                // Update age weight
                if ($this->judgingModel->updateAgeWeight($data)) {
                    $this->redirect('admin/ageWeights/' . $ageWeight->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('admin/edit_age_weight', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $ageWeight->id,
                'show_id' => $ageWeight->show_id,
                'min_age' => $ageWeight->min_age,
                'max_age' => $ageWeight->max_age,
                'multiplier' => $ageWeight->multiplier,
                'description' => $ageWeight->description ?? '',
                'is_active' => $ageWeight->is_active ?? 1,
                'min_age_err' => '',
                'max_age_err' => '',
                'multiplier_err' => '',
                'description_err' => '',
                'is_active_err' => '',
                'title' => 'Edit Age Weight',
                'show' => $show,
                'ageWeight' => $ageWeight
            ];
            
            // Load view
            $this->view('admin/edit_age_weight', $data);
        }
    }
    
    /**
     * Delete age weight
     * 
     * @param int $id Age weight ID
     */
    public function deleteAgeWeight($id) {
        // Get age weight
        $ageWeight = $this->judgingModel->getAgeWeightById($id);
        
        if (!$ageWeight) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete age weight
            if ($this->judgingModel->deleteAgeWeight($id)) {
                // Set flash message
                $_SESSION['flash_message'] = [
                    'type' => 'success',
                    'message' => 'Age weight has been deleted successfully.'
                ];
                $this->redirect('admin/editShow/' . $ageWeight->show_id . '#age-weights');
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/editShow/' . $ageWeight->show_id . '#age-weights');
        }
    }
    
    /**
     * Manage registrations
     * 
     * @param int $showId Show ID
     */
    public function registrations($showId = null) {
        // If no show ID provided, show all registrations
        if (!$showId) {
            // Get all shows
            $shows = $this->showModel->getShows();
            
            // Add registration counts to each show
            if (!empty($shows)) {
                foreach ($shows as &$show) {
                    // Get registration count for this show
                    $show->registration_count = $this->registrationModel->getRegistrationCountByShow($show->id);
                    
                    // Try to get coordinator name if available
                    if (isset($show->coordinator_id) && $show->coordinator_id) {
                        $coordinator = $this->userModel->getUserById($show->coordinator_id);
                        if ($coordinator) {
                            $show->coordinator_name = $coordinator->name;
                        }
                    }
                }
            }
            
            $data = [
                'title' => 'Show Registrations',
                'shows' => $shows
            ];
            
            $this->view('admin/registrations_shows', $data);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get registrations
        $registrations = $this->registrationModel->getShowRegistrations($showId);
        
        // Get category statistics
        $category_stats = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Calculate registration statistics
        $total_registrations = count($registrations);
        $paid_registrations = 0;
        $pending_registrations = 0;
        $total_revenue = 0;
        
        foreach ($registrations as $registration) {
            if (isset($registration->payment_status) && $registration->payment_status === 'paid') {
                $paid_registrations++;
                // Add to total revenue if fee is set
                if (isset($registration->fee) && is_numeric($registration->fee)) {
                    $total_revenue += $registration->fee;
                }
            } elseif (isset($registration->payment_status) && $registration->payment_status === 'pending') {
                $pending_registrations++;
            }
        }
        
        $data = [
            'title' => 'Registrations for ' . $show->name,
            'show' => $show,
            'registrations' => $registrations,
            'category_stats' => $category_stats,
            'categories' => $categories,
            'total_registrations' => $total_registrations,
            'paid_registrations' => $paid_registrations,
            'pending_registrations' => $pending_registrations,
            'total_revenue' => $total_revenue
        ];
        
        $this->view('admin/registrations', $data);
    }
    
    /**
     * View registration details (URL-friendly alias for viewRegistration)
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function view_registration($id) {
        $this->viewRegistration($id);
    }
    
    /**
     * View registration details
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function viewRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get vehicle images
        $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($registration->show_id);
        
        $data = [
            'title' => 'Registration Details',
            'registration' => $registration,
            'show' => $show,
            'images' => $images,
            'categories' => $categories
        ];
        
        $this->view('admin/view_registration', $data);
    }
    
    /**
     * Update registration status
     * 
     * @param int $id Registration ID
     */
    public function updateRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'category_id' => intval($_POST['category_id']),
                'status' => trim($_POST['status']),
                'category_id_err' => '',
                'status_err' => ''
            ];
            
            // Validate category
            if (empty($data['category_id'])) {
                $data['category_id_err'] = 'Please select a category';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            if (empty($data['category_id_err']) && empty($data['status_err'])) {
                // Update registration
                if ($this->registrationModel->updateRegistration($data)) {
                    // Redirect to registration details
                    $this->redirect('admin/viewRegistration/' . $id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Get vehicle images
                $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
                
                // Get categories
                $categories = $this->showModel->getShowCategories($registration->show_id);
                
                $data['title'] = 'Registration Details';
                $data['registration'] = $registration;
                $data['show'] = $show;
                $data['images'] = $images;
                $data['categories'] = $categories;
                
                // Load view with errors
                $this->view('admin/view_registration', $data);
            }
        } else {
            $this->redirect('admin/viewRegistration/' . $id);
        }
    }
    
    /**
     * Delete registration
     * 
     * @param int $id Registration ID
     */
    public function deleteRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete registration
            if ($this->registrationModel->deleteRegistration($id)) {
                // Check if a redirect URL was provided
                if (isset($_POST['redirect_url']) && !empty($_POST['redirect_url'])) {
                    // Redirect to the provided URL
                    header('Location: ' . $_POST['redirect_url']);
                    exit;
                } else {
                    // Redirect to registrations page
                    $this->redirect('admin/registrations/' . $registration->show_id);
                }
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/registrations/' . $registration->show_id);
        }
    }
    
    /**
     * Delete registration (URL-friendly alias for deleteRegistration)
     * 
     * @param int $id Registration ID
     */
    public function delete_registration($id) {
        $this->deleteRegistration($id);
    }
    
    /**
     * Cancel a registration
     * 
     * @param int $id Registration ID
     */
    public function cancelRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Update registration status to cancelled
            $updateData = [
                'id' => $id,
                'status' => 'cancelled'
            ];
            
            if ($this->registrationModel->updateRegistration($updateData)) {
                // Check if a redirect URL was provided
                if (isset($_POST['redirect_url']) && !empty($_POST['redirect_url'])) {
                    // Redirect to the provided URL
                    header('Location: ' . $_POST['redirect_url']);
                    exit;
                } else {
                    // Redirect to registrations page
                    $this->redirect('admin/registrations/' . $registration->show_id);
                }
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('admin/registrations/' . $registration->show_id);
        }
    }
    
    /**
     * View all registrations for a specific user
     * 
     * @param int $userId User ID
     */
    public function userRegistrations($userId = null) {
        // Get all shows for reference
        $shows = $this->showModel->getShows();
        $showsById = [];
        foreach ($shows as $show) {
            $showsById[$show->id] = $show;
        }
        
        // Get all categories for reference
        $categories = [];
        foreach ($shows as $show) {
            $showCategories = $this->showModel->getShowCategories($show->id);
            if (is_array($showCategories)) {
                foreach ($showCategories as $category) {
                    $categories[$category->id] = $category;
                }
            }
        }
        
        // Get current user role
        $currentUser = $this->userModel->getUserById($_SESSION['user_id']);
        $isAdmin = ($currentUser && $currentUser->role === 'admin');
        $isCoordinator = ($currentUser && $currentUser->role === 'coordinator');
        
        // Initialize variables
        $user = null;
        $registrations = [];
        $availableShows = [];
        $selectedShowId = isset($_GET['show_id']) ? (int)$_GET['show_id'] : 0;
        
        // If a user ID is provided, show registrations for that specific user
        if ($userId) {
            // Get user
            $user = $this->userModel->getUserById($userId);
            
            if (!$user) {
                $this->redirect('home/not_found');
                return;
            }
            
            // Get registration counts only (for performance)
            $registrationCounts = $this->registrationModel->getUserRegistrationCounts($userId);

            // Get available shows for filtering
            $availableShows = $shows;

            $data = [
                'title' => 'Registrations for ' . $user->name,
                'user' => $user,
                'registration_counts' => $registrationCounts,
                'available_shows' => $availableShows,
                'view_mode' => 'user'
            ];
        } 
        // No user ID provided - show all registrations based on role
        else {
            // For admin, get all shows
            if ($isAdmin) {
                $availableShows = $shows;
                
                // If a show is selected, get registrations for that show
                if ($selectedShowId > 0) {
                    $registrations = $this->registrationModel->getShowRegistrations($selectedShowId);
                } else {
                    // Get all registrations (limited to recent ones to avoid performance issues)
                    $registrations = $this->registrationModel->getAllRegistrations(500); // Limit to 500 most recent
                }
                
                $title = 'All Registrations';
            } 
            // For coordinator, get shows they coordinate
            elseif ($isCoordinator) {
                $availableShows = $this->showModel->getShowsByCoordinator($currentUser->id);
                
                // If a show is selected, get registrations for that show
                if ($selectedShowId > 0) {
                    // Verify this coordinator has access to this show
                    $hasAccess = false;
                    foreach ($availableShows as $show) {
                        if ($show->id == $selectedShowId) {
                            $hasAccess = true;
                            break;
                        }
                    }
                    
                    if ($hasAccess) {
                        $registrations = $this->registrationModel->getShowRegistrations($selectedShowId);
                    } else {
                        // Redirect to dashboard if trying to access unauthorized show
                        $this->redirect('admin/dashboard');
                        return;
                    }
                } 
                // No show selected, get registrations for all shows this coordinator manages
                else if (!empty($availableShows)) {
                    $showIds = array_map(function($show) {
                        return $show->id;
                    }, $availableShows);
                    
                    $registrations = $this->registrationModel->getRegistrationsByShowIds($showIds);
                } else {
                    // No shows available for this coordinator
                    $registrations = [];
                }
                
                $title = 'Registrations for Your Shows';
            } 
            // Other roles shouldn't access this page
            else {
                $this->redirect('admin/dashboard');
                return;
            }
            
            $data = [
                'title' => $title,
                'user' => $currentUser,
                'registrations' => $registrations,
                'shows' => $showsById,
                'categories' => $categories,
                'csrf_token' => $this->generateCsrfToken(),
                'view_mode' => 'all',
                'available_shows' => $availableShows,
                'selected_show_id' => $selectedShowId,
                'is_admin' => $isAdmin,
                'is_coordinator' => $isCoordinator
            ];
        }
        
        $this->view('admin/user_registrations_optimized', $data);
    }

    /**
     * AJAX endpoint for loading paginated user registrations
     *
     * @param int $userId User ID from URL path
     * @return void
     */
    public function loadUserRegistrations($userId = null) {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get user ID from parameter
        $userId = (int)($userId ?? 0);

        // Get other parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $showFilter = $_GET['show_filter'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $orderBy = $_GET['order_by'] ?? 'start_date';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        if (!$userId) {
            http_response_code(400);
            echo json_encode(['error' => 'User ID required']);
            return;
        }

        try {
            $result = $this->registrationModel->getPaginatedUserRegistrations(
                $userId, $page, $perPage, $search, $showFilter, $statusFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'registrations' => $result['registrations'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in AdminController::loadUserRegistrations: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load registrations']);
        }
    }

    /**
     * Edit a registration
     *
     * @param int $id Registration ID
     */
    public function editRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get all vehicles for admin (not just user's vehicles)
        $vehicles = $this->vehicleModel->getAllVehicles();
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($registration->show_id);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $vehicleId = isset($_POST['vehicle_id']) ? intval($_POST['vehicle_id']) : 0;
            $categoryId = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
            $status = isset($_POST['status']) ? $_POST['status'] : 'pending';
            $paymentStatus = isset($_POST['payment_status']) ? $_POST['payment_status'] : 'unpaid';
            
            // Debug log the form data
            error_log('DEBUG: Form data - vehicleId: ' . $vehicleId . ', categoryId: ' . $categoryId . 
                     ', status: ' . $status . ', paymentStatus: ' . $paymentStatus);
            
            // Validate form data
            $errors = [];
            
            if (empty($vehicleId)) {
                $errors['vehicle_id'] = 'Please select a vehicle';
            } else {
                // Check if vehicle is already registered for this show (except current registration)
                if ($vehicleId != $registration->vehicle_id && 
                    $this->registrationModel->isVehicleRegistered($registration->show_id, $vehicleId)) {
                    $errors['vehicle_id'] = 'This vehicle is already registered for this show';
                }
            }
            
            if (empty($categoryId)) {
                $errors['category_id'] = 'Please select a category';
            } else {
                // Verify category belongs to show
                $category = $this->showModel->getCategoryById($categoryId);
                if (!$category || $category->show_id != $registration->show_id) {
                    $errors['category_id'] = 'Invalid category selection';
                }
            }
            
            // If no errors, update registration
            if (empty($errors)) {
                // Get category fee
                $fee = 0;
                foreach ($categories as $cat) {
                    if ($cat->id == $categoryId) {
                        $fee = $cat->registration_fee;
                        break;
                    }
                }
                
                // Create registration data
                $registrationData = [
                    'id' => $id,
                    'category_id' => $categoryId,
                    'vehicle_id' => $vehicleId,
                    'status' => $status,
                    'payment_status' => $paymentStatus
                ];
                
                // Update registration
                $updateResult = $this->registrationModel->updateRegistration($registrationData);
                
                // Verify the update was successful
                if ($updateResult) {
                    // Set success message
                    setFlashMessage('success', 'Registration updated successfully');
                    
                    // Get the referer from the form submission
                    $referer = isset($_POST['referer']) ? $_POST['referer'] : '';
                    
                    // Check if referer is from our site and not the current page
                    if (!empty($referer) && 
                        strpos($referer, BASE_URL) !== false && 
                        strpos($referer, 'editRegistration') === false) {
                        // Redirect back to the referring page
                        header('Location: ' . $referer);
                        exit;
                    } else {
                        // Default fallback if no valid referer
                        $this->redirect('admin/viewRegistration/' . $id);
                    }
                } else {
                    // Log the error for debugging
                    error_log('ERROR: Failed to update registration ID ' . $id . ' with status ' . $status);
                    $this->redirect('home/error/Failed%20to%20update%20registration');
                }
            } else {
                // If there are errors, show the form again with errors
                $data = [
                    'title' => 'Edit Registration',
                    'registration' => $registration,
                    'show' => $show,
                    'vehicles' => $vehicles,
                    'categories' => $categories,
                    'errors' => $errors,
                    'vehicle_id' => $vehicleId,
                    'category_id' => $categoryId,
                    'status' => $status,
                    'payment_status' => $paymentStatus
                ];
                
                $this->view('admin/edit_registration', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Edit Registration',
                'registration' => $registration,
                'show' => $show,
                'vehicles' => $vehicles,
                'categories' => $categories,
                'errors' => [],
                'vehicle_id' => $registration->vehicle_id,
                'category_id' => $registration->category_id,
                'status' => $registration->status,
                'payment_status' => isset($registration->payment_status) ? $registration->payment_status : 'unpaid'
            ];
            
            // Debug log the registration data
            error_log('DEBUG: Registration data - ID: ' . $registration->id . 
                     ', status: ' . $registration->status . 
                     ', payment_status: ' . (isset($registration->payment_status) ? $registration->payment_status : 'not set'));
            
            // Load view
            $this->view('admin/edit_registration', $data);
        }
    }
    
    /**
     * Fan votes monitoring
     * 
     * @param int|null $showId Show ID (optional)
     * @return void
     */
    public function fanVotes($showId = null) {
        // If no show ID is provided, redirect to shows list
        if ($showId === null) {
            $this->redirect('admin/shows');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('admin/shows');
            return;
        }
        
        // Get all votes for this show
        $votes = $this->judgingModel->getShowVotes($showId);
        
        // Get vote rankings
        $rankings = $this->judgingModel->getFanVoteRankings($showId);
        
        // Calculate statistics
        $uniqueIps = [];
        $facebookVotes = 0;
        $vehiclesWithVotes = [];
        
        foreach ($votes as $vote) {
            // Count unique IPs
            if (!in_array($vote->voter_ip, $uniqueIps)) {
                $uniqueIps[] = $vote->voter_ip;
            }
            
            // Count Facebook votes
            if (!empty($vote->fb_user_id)) {
                $facebookVotes++;
            }
            
            // Count vehicles with votes
            if (!in_array($vote->registration_id, $vehiclesWithVotes)) {
                $vehiclesWithVotes[] = $vote->registration_id;
            }
        }
        
        $data = [
            'title' => 'Fan Votes - ' . $show->name,
            'show' => $show,
            'votes' => $votes,
            'rankings' => $rankings,
            'unique_ips' => count($uniqueIps),
            'facebook_votes' => $facebookVotes,
            'vehicles_with_votes' => count($vehiclesWithVotes)
        ];
        
        $this->view('admin/fan_votes', $data);
    }
    
    /**
     * Delete a fan vote
     * 
     * @param int $voteId Vote ID
     * @return void
     */
    public function deleteFanVote($voteId) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get vote details to determine which show to redirect to
            $this->db = new Database();
            $this->db->query('SELECT show_id FROM fan_votes WHERE id = :id');
            $this->db->bind(':id', $voteId);
            $vote = $this->db->single();
            
            if (!$vote) {
                $this->redirect('admin/shows');
                return;
            }
            
            // Delete vote
            $this->db->query('DELETE FROM fan_votes WHERE id = :id');
            $this->db->bind(':id', $voteId);
            
            if ($this->db->execute()) {
                flash('admin_message', 'Vote deleted successfully', 'alert alert-success');
            } else {
                flash('admin_message', 'Failed to delete vote', 'alert alert-danger');
            }
            
            $this->redirect('admin/fanVotes/' . $vote->show_id);
        } else {
            $this->redirect('admin/shows');
        }
    }
    
    /**
     * Generate QR codes for fan voting
     * 
     * @param int|null $showId Show ID (optional)
     * @return void
     */
    public function qrCodes($showId = null) {
        // If no show ID is provided, get shows with fan voting enabled
        if ($showId === null) {
            // Get all shows with fan voting enabled
            $shows = $this->showModel->getShowsWithFanVotingEnabled();
            
            if (empty($shows)) {
                flash('admin_message', 'No shows with fan voting enabled found', 'alert alert-warning');
                $this->redirect('admin/shows');
                return;
            }
            
            // If there's only one show, redirect to it directly
            if (count($shows) === 1) {
                $this->redirect('admin/qrcodes/' . $shows[0]->id);
                return;
            }
            
            // Display list of shows to select from
            $data = [
                'title' => 'Select Show for QR Codes',
                'shows' => $shows
            ];
            
            $this->view('admin/select_show_for_qr', $data);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            flash('admin_message', 'Show not found', 'alert alert-danger');
            $this->redirect('admin/shows');
            return;
        }
        
        // Check if fan voting is enabled for this show
        if (!$show->fan_voting_enabled) {
            flash('admin_message', 'Fan voting is not enabled for this show', 'alert alert-warning');
            $this->redirect('admin/shows');
            return;
        }
        
        // Get all approved vehicles for this show
        $vehicles = $this->vehicleModel->getShowVehiclesWithImages($showId);
        
        $data = [
            'title' => 'QR Codes - ' . $show->name,
            'show' => $show,
            'vehicles' => $vehicles
        ];
        
        $this->view('admin/qr_codes', $data);
    }
    
    /**
     * Print QR codes for fan voting
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function printQrCodes($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('admin/shows');
            return;
        }
        
        // Check if a specific vehicle was requested
        $vehicleId = isset($_GET['vehicle_id']) ? (int)$_GET['vehicle_id'] : null;
        
        // Get vehicles for this show
        if ($vehicleId) {
            // Get specific registration
            $registration = $this->registrationModel->getRegistrationById($vehicleId);
            
            if (!$registration || $registration->show_id != $showId) {
                flash('admin_message', 'Vehicle not found for this show', 'alert alert-warning');
                $this->redirect('admin/manageQrCodes?show_id=' . $showId);
                return;
            }
            
            // Get vehicle details
            $this->db->query('SELECT v.*, r.id as registration_id, r.registration_number, r.status, 
                            sc.name as category_name, sc.id as category_id, 
                            u.name as owner_name
                            FROM vehicles v 
                            JOIN registrations r ON v.id = r.vehicle_id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            JOIN users u ON v.owner_id = u.id 
                            WHERE r.id = :registration_id');
            $this->db->bind(':registration_id', $vehicleId);
            $vehicle = $this->db->single();
            
            if (!$vehicle) {
                flash('admin_message', 'Vehicle not found', 'alert alert-warning');
                $this->redirect('admin/manageQrCodes?show_id=' . $showId);
                return;
            }
            
            // Get images for the vehicle
            $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id ORDER BY is_primary DESC, id');
            $this->db->bind(':entity_type', 'vehicle');
            $this->db->bind(':entity_id', $vehicle->id);
            $vehicle->images = $this->db->resultSet();
            
            $vehicles = [$vehicle];
        } else {
            // Get all approved vehicles for this show
            $vehicles = $this->vehicleModel->getShowVehiclesWithImages($showId);
        }
        
        $data = [
            'title' => 'Print QR Codes - ' . $show->name,
            'show' => $show,
            'vehicles' => $vehicles,
            'single_vehicle' => $vehicleId ? true : false
        ];
        
        $this->view('shared/print_qr_codes', $data);
    }
    
    /**
     * QR Code Management
     * 
     * @return void
     */
    public function manageQrCodes() {
        // Get all active shows (not past end date)
        // Using getShows with parameters to filter for active shows instead of getActiveShows
        // This is to fix the "Call to undefined method ShowModel::getActiveShows()" error
        $shows = $this->showModel->getShows(null, null, null, true);
        
        // Filter shows to only include those that haven't ended yet
        $activeShows = [];
        $currentUserDate = convertUTCToUserDateTime(date('Y-m-d H:i:s'), $_SESSION['user_id'], 'Y-m-d');
        foreach ($shows as $show) {
            $showEndDate = convertUTCToUserDateTime($show->end_date, $_SESSION['user_id'], 'Y-m-d');
            if (strtotime($showEndDate) >= strtotime($currentUserDate)) {
                $activeShows[] = $show;
            }
        }
        
        // Default data
        $data = [
            'title' => 'QR Code Management',
            'shows' => $activeShows,
            'selected_show' => null,
            'vehicles' => [],
            'error' => null
        ];
        
        // Check if a show was selected
        if (isset($_GET['show_id']) && !empty($_GET['show_id'])) {
            $showId = (int)$_GET['show_id'];
            $show = $this->showModel->getShowById($showId);
            
            if ($show) {
                $data['selected_show'] = $show;
                
                // Get all registrations for this show with payment status
                $registrations = $this->registrationModel->getShowRegistrations($showId);
                
                // Filter to only include registrations with completed payment
                $paidRegistrations = array_filter($registrations, function($reg) {
                    return isset($reg->payment_status) && $reg->payment_status === 'completed';
                });
                
                $data['vehicles'] = $paidRegistrations;
            } else {
                $data['error'] = 'Show not found';
            }
        }
        
        $this->view('admin/manage_qr_codes', $data);
    }
    
    /**
     * Download QR code for a vehicle registration
     * 
     * @param int $registrationId Registration ID
     * @return void
     */
    // The downloadQrCode method is already defined at line 4664
    // This duplicate declaration has been removed to fix the error
    
    /**
     * Lookup QR code by registration number
     * 
     * @return void
     */
    public function lookupQrCode() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('admin/manageQrCodes');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        // Get form data
        $registrationNumber = trim($_POST['registration_number']);
        $showId = !empty($_POST['lookup_show_id']) ? (int)$_POST['lookup_show_id'] : null;
        
        // Validate registration number
        if (empty($registrationNumber)) {
            flash('admin_message', 'Please enter a registration number', 'alert alert-danger');
            $this->redirect('admin/manageQrCodes');
            return;
        }
        
        // Find registration
        $registration = null;
        
        if ($showId) {
            // Look for registration in specific show
            $this->db->query('SELECT r.* FROM registrations r 
                             WHERE r.registration_number = :reg_number 
                             AND r.show_id = :show_id');
            $this->db->bind(':reg_number', $registrationNumber);
            $this->db->bind(':show_id', $showId);
            $registration = $this->db->single();
        } else {
            // Look for registration in any show
            $this->db->query('SELECT r.* FROM registrations r 
                             WHERE r.registration_number = :reg_number');
            $this->db->bind(':reg_number', $registrationNumber);
            $registration = $this->db->single();
        }
        
        if (!$registration) {
            flash('admin_message', 'Registration not found', 'alert alert-danger');
            $this->redirect('admin/manageQrCodes');
            return;
        }
        
        // Redirect to print QR code
        $this->redirect('admin/printQrCodes/' . $registration->show_id . '?vehicle_id=' . $registration->id);
    }
    
    /**
     * Export registrations for a specific show to CSV
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function exportRegistrations($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('admin/shows');
            return;
        }
        
        // Get registrations for this show
        $registrations = $this->registrationModel->getShowRegistrations($showId);
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="registrations_' . $show->name . '_' . gmdate('Y-m-d') . '.csv"');
        
        // Create output stream
        $output = fopen('php://output', 'w');
        
        // Add CSV headers
        fputcsv($output, [
            'ID', 'Registration #', 'Vehicle', 'Year', 'Make', 'Model', 'Color', 
            'Owner Name', 'Owner Email', 'Category', 'Payment Status', 'Fee', 
            'Registration Date', 'Check-in Status', 'Notes'
        ]);
        
        // Add data rows
        foreach ($registrations as $registration) {
            // Format vehicle info
            $vehicle = $registration->year . ' ' . $registration->make . ' ' . $registration->model;
            
            // Get owner email if available
            $ownerEmail = '';
            if (isset($registration->owner_id) && $registration->owner_id) {
                $owner = $this->userModel->getUserById($registration->owner_id);
                if ($owner && isset($owner->email)) {
                    $ownerEmail = $owner->email;
                }
            }
            
            // Format check-in status
            $checkedIn = isset($registration->checked_in) && $registration->checked_in ? 'Yes' : 'No';
            
            // Format registration number
            $registrationNumber = isset($registration->registration_number) ? $registration->registration_number : $registration->id;
            
            fputcsv($output, [
                $registration->id,
                $registrationNumber,
                $vehicle,
                $registration->year,
                $registration->make,
                $registration->model,
                $registration->color ?? '',
                $registration->owner_name,
                $ownerEmail,
                $registration->category_name,
                isset($registration->payment_status) ? ucfirst($registration->payment_status) : 'Unknown',
                isset($registration->fee) ? '$' . number_format($registration->fee, 2) : '',
                isset($registration->registration_date) ? convertUTCToUserDateTime($registration->registration_date, $_SESSION['user_id'], 'Y-m-d H:i:s') : convertUTCToUserDateTime($registration->created_at, $_SESSION['user_id'], 'Y-m-d H:i:s'),
                $checkedIn,
                isset($registration->notes) ? $registration->notes : ''
            ]);
        }
        
        // Close the output stream
        fclose($output);
        exit;
    }
    

    
    /**
     * Export fan votes to CSV
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function exportFanVotes($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('admin/shows');
            return;
        }
        
        // Get all votes for this show
        $votes = $this->judgingModel->getShowVotes($showId);
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="fan_votes_' . $showId . '_' . gmdate('Y-m-d') . '.csv"');
        
        // Create output stream
        $output = fopen('php://output', 'w');
        
        // Add CSV headers
        fputcsv($output, ['ID', 'IP Address', 'Facebook User ID', 'Facebook User Name', 'Facebook User Email', 
                         'Registration ID', 'Registration Number', 'Vehicle', 'Owner', 'Category', 'Date/Time']);
        
        // Add data rows
        foreach ($votes as $vote) {
            fputcsv($output, [
                $vote->id,
                $vote->voter_ip,
                $vote->fb_user_id ?? '',
                $vote->fb_user_name ?? '',
                $vote->fb_user_email ?? '',
                $vote->registration_id,
                $vote->registration_number,
                $vote->year . ' ' . $vote->make . ' ' . $vote->model,
                $vote->owner_name,
                $vote->category_name,
                $vote->created_at
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Show list of shows for registration
     * 
     * @return void
     */
    public function registerVehicle() {
        // Get filter status from query parameters
        $status = isset($_GET['status']) ? $_GET['status'] : null;
        $sort = isset($_GET['sort']) ? $_GET['sort'] : null;
        $order = isset($_GET['order']) ? $_GET['order'] : 'asc';
        
        // Get all shows excluding completed and cancelled by default
        $shows = $this->showModel->getShows($status, null, null, true);
        
        // Apply sorting if specified
        if ($sort) {
            usort($shows, function($a, $b) use ($sort, $order) {
                $aVal = $a->$sort;
                $bVal = $b->$sort;
                
                // Handle date fields
                if (in_array($sort, ['start_date', 'end_date', 'registration_start', 'registration_end'])) {
                    $aVal = strtotime($aVal);
                    $bVal = strtotime($bVal);
                }
                
                if ($order === 'asc') {
                    return $aVal <=> $bVal;
                } else {
                    return $bVal <=> $aVal;
                }
            });
        }
        
        $data = [
            'title' => 'Register Vehicle for User',
            'shows' => $shows,
            'current_status' => $status,
            'current_sort' => $sort,
            'current_order' => $order
        ];
        
        $this->view('admin/register_vehicle', $data);
    }
    
    /**
     * Register a vehicle for a user after registration end date
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function registerVehicleForUser($showId = 0) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            flash('admin_message', 'Show not found', 'alert alert-danger');
            $this->redirect('admin/shows');
            return;
        }
        
        // Get all users
        $users = $this->userModel->getUsers();
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get payment methods
        $paymentModel = $this->model('PaymentModel');
        $paymentMethods = $paymentModel->getPaymentMethods();
        
        $data = [
            'title' => 'Register Vehicle for User - ' . $show->name,
            'show' => $show,
            'users' => $users,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'user_id' => '',
            'vehicle_id' => '',
            'category_id' => '',
            'payment_method_id' => '',
            'fee' => $show->registration_fee ?? 0,
            'status' => 'approved', // Default to approved for admin registrations
            'user_id_err' => '',
            'vehicle_id_err' => '',
            'category_id_err' => '',
            'payment_method_id_err' => ''
        ];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Get form data
            $data['user_id'] = $_POST['user_id'] ?? '';
            $data['vehicle_id'] = $_POST['vehicle_id'] ?? '';
            $data['category_id'] = $_POST['category_id'] ?? '';
            $data['payment_method_id'] = $_POST['payment_method_id'] ?? '';
            $data['fee'] = $_POST['fee'] ?? $show->registration_fee ?? 0;
            $data['status'] = $_POST['status'] ?? 'approved';
            
            // Validate user
            if (empty($data['user_id'])) {
                $data['user_id_err'] = 'Please select a user';
            }
            
            // Validate vehicle if provided
            if (empty($data['vehicle_id']) && $data['vehicle_id'] !== 'new') {
                $data['vehicle_id_err'] = 'Please select a vehicle or choose to create a new one';
            }
            
            // Validate category
            if (empty($data['category_id'])) {
                $data['category_id_err'] = 'Please select a category';
            }
            
            // Validate payment method
            if (empty($data['payment_method_id'])) {
                $data['payment_method_id_err'] = 'Please select a payment method';
            }
            
            // Check for errors
            if (empty($data['user_id_err']) && empty($data['vehicle_id_err']) && 
                empty($data['category_id_err']) && empty($data['payment_method_id_err'])) {
                
                // If creating a new vehicle
                if ($data['vehicle_id'] === 'new') {
                    // Create new vehicle
                    $vehicleData = [
                        'owner_id' => $data['user_id'],
                        'make' => $_POST['make'] ?? '',
                        'model' => $_POST['model'] ?? '',
                        'year' => $_POST['year'] ?? '',
                        'color' => $_POST['color'] ?? '',
                        'license_plate' => $_POST['license_plate'] ?? '',
                        'vin' => $_POST['vin'] ?? '',
                        'description' => $_POST['description'] ?? ''
                    ];
                    
                    // Validate vehicle data
                    if (empty($vehicleData['make'])) {
                        $data['vehicle_id_err'] = 'Please enter a make';
                    } elseif (empty($vehicleData['model'])) {
                        $data['vehicle_id_err'] = 'Please enter a model';
                    } elseif (empty($vehicleData['year'])) {
                        $data['vehicle_id_err'] = 'Please enter a year';
                    }
                    
                    if (empty($data['vehicle_id_err'])) {
                        // Create vehicle
                        $vehicleId = $this->vehicleModel->createVehicle($vehicleData);
                        
                        if ($vehicleId) {
                            $data['vehicle_id'] = $vehicleId;
                        } else {
                            $data['vehicle_id_err'] = 'Error creating vehicle';
                        }
                    }
                }
                
                // If no errors, create registration
                if (empty($data['vehicle_id_err'])) {
                    $registrationData = [
                        'show_id' => $showId,
                        'user_id' => $data['user_id'],
                        'vehicle_id' => $data['vehicle_id'],
                        'category_id' => $data['category_id'],
                        'status' => $data['status'],
                        'fee' => $data['fee'],
                        'payment_method_id' => $data['payment_method_id'],
                        'payment_status' => 'completed', // Default to completed for admin registrations
                        'payment_reference' => 'Admin registration'
                    ];
                    
                    // Create registration
                    $registrationId = $this->registrationModel->createRegistration($registrationData);
                    
                    if ($registrationId) {
                        flash('admin_message', 'Vehicle registered successfully', 'alert alert-success');
                        $this->redirect('admin/show/' . $showId);
                    } else {
                        flash('admin_message', 'Error registering vehicle', 'alert alert-danger');
                    }
                }
            }
        }
        
        // Load view
        $this->view('admin/register_vehicle_for_user', $data);
    }
    
    /**
     * Get user vehicles via AJAX
     * 
     * @return void
     */
    public function getUserVehicles() {
        // Check if request is AJAX
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(403);
            echo json_encode(['error' => 'Forbidden']);
            return;
        }
        
        // Get user ID from POST
        $userId = $_POST['user_id'] ?? 0;
        
        if (empty($userId)) {
            http_response_code(400);
            echo json_encode(['error' => 'User ID is required']);
            return;
        }
        
        // Get user vehicles
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Return vehicles as JSON
        header('Content-Type: application/json');
        echo json_encode(['vehicles' => $vehicles]);
    }
    
    /**
     * Geocode Events
     * 
     * This method provides an interface to geocode events that have addresses but no coordinates.
     * 
     * @return void
     */
    public function geocodeEvents() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Make sure CalendarModel is loaded
        if (!isset($this->calendarModel)) {
            $this->calendarModel = $this->model('CalendarModel');
        }
        
        $data = [
            'title' => 'Geocode Events'
        ];
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                flash('admin_message', 'Invalid request', 'alert alert-danger');
                $this->redirect('admin/dashboard');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get limit
            $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
            
            try {
                // Load the geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get all events with addresses but no coordinates
                $events = $this->calendarModel->getEventsWithoutCoordinates();
                
                // Limit the number of events to process
                $events = array_slice($events, 0, $limit);
                
                $successCount = 0;
                $failCount = 0;
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Process each event
                foreach ($events as $event) {
                    try {
                        // Create address array for geocoding
                        $eventData = [
                            'address1' => $event->address1 ?? '',
                            'address2' => $event->address2 ?? '',
                            'city' => $event->city ?? '',
                            'state' => $event->state ?? '',
                            'zipcode' => $event->zipcode ?? ''
                        ];
                        
                        // Use the geocodeEvent function
                        $eventData = geocodeEvent($eventData, $mapSettings, 'adminGeocode', $event->id);
                        
                        // If geocoding was successful, update the event
                        if (isset($eventData['lat']) && $eventData['lat'] && isset($eventData['lng']) && $eventData['lng']) {
                            if ($this->calendarModel->updateEventCoordinates($event->id, $eventData['lat'], $eventData['lng'])) {
                                $successCount++;
                            } else {
                                $failCount++;
                            }
                        } else {
                            $failCount++;
                        }
                    } catch (Exception $e) {
                        error_log('Error geocoding event ' . $event->id . ': ' . $e->getMessage());
                        $failCount++;
                    }
                    
                    // Sleep for a short time to avoid overwhelming the geocoding service
                    usleep(500000); // 0.5 seconds
                }
                
                // Set results
                $data['results'] = [
                    'total' => count($events),
                    'success' => $successCount,
                    'fail' => $failCount
                ];
                
                // Flash message
                flash('admin_message', "Geocoding completed: {$successCount} successful, {$failCount} failed", 'alert alert-info');
            } catch (Exception $e) {
                error_log('Error in geocodeEvents: ' . $e->getMessage());
                flash('admin_message', 'An error occurred while geocoding events: ' . $e->getMessage(), 'alert alert-danger');
            }
        }
        
        $this->view('admin/geocode_events', $data);
    }
    
    /**
     * Get media library contents as HTML
     */
    public function getMediaLibrary() {
        // Check if user is logged in and is admin
        if (!isLoggedIn() || !isAdmin()) {
            echo '<div class="alert alert-danger">Access denied</div>';
            return;
        }
        
        // Get images from the media library
        $mediaDir = 'uploads/media';
        $baseUrl = BASE_URL . '/uploads/media';
        
        if (is_dir($mediaDir)) {
            $images = glob($mediaDir . '/*.{jpg,jpeg,png,gif}', GLOB_BRACE);
            
            if (count($images) > 0) {
                foreach ($images as $index => $image) {
                    $filename = basename($image);
                    $imageUrl = $baseUrl . '/' . $filename;
                    ?>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100 image-card" data-image-url="<?php echo $imageUrl; ?>">
                            <img src="<?php echo $imageUrl; ?>" class="card-img-top" alt="<?php echo $filename; ?>" style="height: 120px; object-fit: contain;">
                            <div class="card-body text-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="selected_image" 
                                           id="image-<?php echo $index; ?>" 
                                           value="<?php echo $imageUrl; ?>">
                                    <label class="form-check-label" for="image-<?php echo $index; ?>">
                                        <?php echo substr($filename, 0, 20) . (strlen($filename) > 20 ? '...' : ''); ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                echo '<div class="col-12"><div class="alert alert-info">No images found in the media library.</div></div>';
            }
        } else {
            echo '<div class="col-12"><div class="alert alert-warning">Media directory not found.</div></div>';
        }
    }

    /**
     * Handle media upload for the template designer
     */
    public function uploadMedia() {
        // Check if user is logged in and is admin
        if (!isLoggedIn() || !isAdmin()) {
            echo json_encode(['error' => 'Access denied']);
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                echo json_encode(['error' => 'Invalid security token']);
                return;
            }
            
            // Check if file was uploaded
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                // Get file info
                $fileName = $_FILES['image']['name'];
                $fileSize = $_FILES['image']['size'];
                $fileTmp = $_FILES['image']['tmp_name'];
                $fileType = $_FILES['image']['type'];
                
                // Get file extension
                $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                
                // Allowed extensions
                $allowedExts = array('jpg', 'jpeg', 'png', 'gif');
                
                // Custom name if provided
                if (!empty($_POST['name'])) {
                    $customName = $_POST['name'];
                    // Remove any special characters
                    $customName = preg_replace('/[^A-Za-z0-9\-_]/', '', $customName);
                    // Add extension
                    $fileName = $customName . '.' . $fileExt;
                }
                
                // Check if extension is allowed
                if (in_array($fileExt, $allowedExts)) {
                    // Check file size (5MB max)
                    $maxSize = 5 * 1024 * 1024;
                    if ($fileSize <= $maxSize) {
                        // Create upload directory if it doesn't exist
                        $uploadDir = 'uploads/media/';
                        if (!file_exists($uploadDir)) {
                            mkdir($uploadDir, 0777, true);
                            
                            // Add index.php to prevent directory listing
                            if (!file_exists($uploadDir . 'index.php')) {
                                file_put_contents($uploadDir . 'index.php', '<?php // Silence is golden');
                            }
                        }
                        
                        // Generate unique filename to prevent overwriting
                        $uniqueFileName = time() . '_' . $fileName;
                        $uploadFile = $uploadDir . $uniqueFileName;
                        
                        // Upload file
                        if (move_uploaded_file($fileTmp, $uploadFile)) {
                            // Return success response for iframe submission
                            echo '<script>
                                if (window.parent && window.parent.document) {
                                    var uploadStatus = window.parent.document.getElementById("upload-status");
                                    if (uploadStatus) {
                                        uploadStatus.innerHTML = "<div class=\"alert alert-success\">Image uploaded successfully!</div>";
                                    }
                                    
                                    // Trigger refresh in parent window
                                    if (window.parent.refreshMediaLibrary) {
                                        window.parent.refreshMediaLibrary();
                                    }
                                }
                            </script>';
                            return;
                        } else {
                            echo '<script>
                                if (window.parent && window.parent.document) {
                                    var uploadStatus = window.parent.document.getElementById("upload-status");
                                    if (uploadStatus) {
                                        uploadStatus.innerHTML = "<div class=\"alert alert-danger\">Error uploading file. Please try again.</div>";
                                    }
                                }
                            </script>';
                        }
                    } else {
                        echo '<script>
                            if (window.parent && window.parent.document) {
                                var uploadStatus = window.parent.document.getElementById("upload-status");
                                if (uploadStatus) {
                                    uploadStatus.innerHTML = "<div class=\"alert alert-danger\">File size too large. Maximum size is 5MB.</div>";
                                }
                            }
                        </script>';
                    }
                } else {
                    echo '<script>
                        if (window.parent && window.parent.document) {
                            var uploadStatus = window.parent.document.getElementById("upload-status");
                            if (uploadStatus) {
                                uploadStatus.innerHTML = "<div class=\"alert alert-danger\">Invalid file type. Allowed types: jpg, jpeg, png, gif.</div>";
                            }
                        }
                    </script>';
                }
            } else {
                echo '<script>
                    if (window.parent && window.parent.document) {
                        var uploadStatus = window.parent.document.getElementById("upload-status");
                        if (uploadStatus) {
                            uploadStatus.innerHTML = "<div class=\"alert alert-danger\">No file selected or error in file upload.</div>";
                        }
                    }
                </script>';
            }
        } else {
            // If not POST request, return error
            echo '<script>
                if (window.parent && window.parent.document) {
                    var uploadStatus = window.parent.document.getElementById("upload-status");
                    if (uploadStatus) {
                        uploadStatus.innerHTML = "<div class=\"alert alert-danger\">Invalid request method.</div>";
                    }
                }
            </script>';
        }
    }
    
    /**
     * Check in a vehicle registration
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function checkIn($id) {
        // Verify CSRF token
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if already checked in
        if ($registration->checked_in) {
            $this->redirect('admin/view_registration/' . $id);
            return;
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 1,
            'check_in_time' => gmdate('Y-m-d H:i:s')
        ];
        
        // Update registration
        if ($this->registrationModel->updateRegistration($id, $data)) {
            // Log the check-in
            error_log('Admin checked in registration #' . $id . ' for ' . $registration->year . ' ' . 
                      $registration->make . ' ' . $registration->model);
            
            // Set success message
            $_SESSION['success_message'] = 'Vehicle successfully checked in';
        } else {
            // Set error message
            $_SESSION['error_message'] = 'Failed to check in vehicle';
        }
        
        // Redirect back to registration details
        $this->redirect('admin/view_registration/' . $id);
    }
    
    /**
     * Undo check-in for a vehicle registration
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function undoCheckIn($id) {
        // Verify CSRF token
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if not checked in
        if (!$registration->checked_in) {
            $this->redirect('admin/view_registration/' . $id);
            return;
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 0,
            'check_in_time' => null
        ];
        
        // Update registration
        if ($this->registrationModel->updateRegistration($id, $data)) {
            // Log the action
            error_log('Admin undid check-in for registration #' . $id . ' for ' . $registration->year . ' ' . 
                      $registration->make . ' ' . $registration->model);
            
            // Set success message
            $_SESSION['success_message'] = 'Check-in has been undone';
        } else {
            // Set error message
            $_SESSION['error_message'] = 'Failed to undo check-in';
        }
        
        // Redirect back to registration details
        $this->redirect('admin/view_registration/' . $id);
    }
    
    /**
     * View scoring system logs
     * 
     * @param string $date Optional date in YYYY-MM-DD format
     * @return void
     */
    public function viewLogs($date = null) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // If no date is provided, use today's date
        if (!$date) {
            $date = gmdate('Y-m-d');
        }
        
        // Check if the date is valid
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            $date = gmdate('Y-m-d');
        }
        
        // Load the view
        $this->view('admin/view_logs', [
            'date' => $date,
            'pageTitle' => 'Scoring System Logs'
        ]);
    }
    
    /**
     * Manage staff for a show
     * 
     * @param int $showId Show ID
     */
    public function manageStaff($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get staff model
        $staffModel = $this->model('StaffModel');
        
        // Get all staff assigned to this show
        $assignedStaff = $staffModel->getShowStaff($showId);
        
        // Get all available staff (not yet assigned to this show)
        $availableStaff = $staffModel->getAvailableStaff($showId);
        
        $data = [
            'title' => $show->name . ' - Manage Staff',
            'show' => $show,
            'assigned_staff' => $assignedStaff,
            'available_staff' => $availableStaff
        ];
        
        $this->view('admin/shows/manage_staff', $data);
    }
    
    /**
     * Assign staff to a show
     * 
     * @param int $showId Show ID
     */
    public function assignStaff($showId) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('admin/manageStaff/' . $showId);
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get staff ID from POST
        $staffId = isset($_POST['staff_id']) ? (int)$_POST['staff_id'] : 0;
        
        if ($staffId <= 0) {
            $this->setFlashMessage('admin_message', 'Invalid staff selection', 'danger');
            $this->redirect('admin/manageStaff/' . $showId);
            return;
        }
        
        // Get staff model
        $staffModel = $this->model('StaffModel');
        
        // Get current user ID (admin who is making the assignment)
        $userId = $this->auth->getCurrentUserId();
        
        // Assign staff to show
        if ($staffModel->assignStaffToShow($staffId, $showId, $userId)) {
            $this->setFlashMessage('admin_message', 'Staff assigned successfully', 'success');
        } else {
            $this->setFlashMessage('admin_message', 'Failed to assign staff', 'danger');
        }
        
        $this->redirect('admin/manageStaff/' . $showId);
    }
    
    /**
     * Remove staff from a show
     * 
     * @param int $showId Show ID
     * @param int $staffId Staff ID
     */
    public function removeStaff($showId, $staffId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get staff model
        $staffModel = $this->model('StaffModel');
        
        // Remove staff from show
        if ($staffModel->removeStaffFromShow($staffId, $showId)) {
            $this->setFlashMessage('admin_message', 'Staff removed successfully', 'success');
        } else {
            $this->setFlashMessage('admin_message', 'Failed to remove staff', 'danger');
        }
        
        $this->redirect('admin/manageStaff/' . $showId);
    }
    
    /**
     * Manage all staff
     */
    public function manageAllStaff() {
        // Get all users with staff role
        $userModel = $this->model('UserModel');
        $staff = $userModel->getUsersByRole('staff');
        
        // Get all users who could be promoted to staff
        $potentialStaff = $userModel->getUsersByRole('user');
        
        $data = [
            'title' => 'Manage Staff',
            'staff' => $staff,
            'potential_staff' => $potentialStaff
        ];
        
        $this->view('admin/users/manage_staff', $data);
    }
    
    /**
     * Promote user to staff
     * 
     * @param int $userId User ID
     */
    public function promoteToStaff($userId) {
        // Get user model
        $userModel = $this->model('UserModel');
        
        // Update user role to staff
        if ($userModel->updateUserRole($userId, 'staff')) {
            $this->setFlashMessage('admin_message', 'User promoted to staff successfully', 'success');
        } else {
            $this->setFlashMessage('admin_message', 'Failed to promote user', 'danger');
        }
        
        $this->redirect('admin/manageAllStaff');
    }
    
    /**
     * Demote staff to user
     * 
     * @param int $userId User ID
     */
    public function demoteStaff($userId) {
        // Get user model
        $userModel = $this->model('UserModel');
        
        // Update user role to user
        if ($userModel->updateUserRole($userId, 'user')) {
            $this->setFlashMessage('admin_message', 'Staff demoted to user successfully', 'success');
        } else {
            $this->setFlashMessage('admin_message', 'Failed to demote staff', 'danger');
        }
        
        $this->redirect('admin/manageAllStaff');
    }
    
    /**
     * Run Scripts
     * 
     * Provides a secure interface for administrators to run maintenance scripts.
     */
    public function runScripts() {
        // Get available scripts using multiple methods to ensure reliability
        $testDir = APPROOT . '/test';
        $scriptsList = [];
        
        // Log the test directory path for debugging
        error_log('Admin Script Runner - Test directory path: ' . $testDir);
        
        // Method 1: Try using glob (most common)
        $testScripts = glob($testDir . '/*.php');
        
        // Method 2: If glob fails, try using scandir
        if (empty($testScripts) && is_dir($testDir) && is_readable($testDir)) {
            error_log('Admin Script Runner - glob() returned no results, trying scandir()');
            $scandirFiles = scandir($testDir);
            $testScripts = [];
            foreach ($scandirFiles as $file) {
                if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'php') {
                    $testScripts[] = $testDir . '/' . $file;
                }
            }
        }
        
        // Method 3: If both fail, try using DirectoryIterator
        if (empty($testScripts) && is_dir($testDir)) {
            error_log('Admin Script Runner - scandir() returned no results, trying DirectoryIterator');
            try {
                $dirIterator = new DirectoryIterator($testDir);
                foreach ($dirIterator as $fileInfo) {
                    if ($fileInfo->isFile() && $fileInfo->getExtension() == 'php') {
                        $testScripts[] = $fileInfo->getPathname();
                    }
                }
            } catch (Exception $e) {
                error_log('Admin Script Runner - DirectoryIterator error: ' . $e->getMessage());
            }
        }
        
        // Log the number of scripts found
        error_log('Admin Script Runner - Found ' . count($testScripts) . ' scripts');
        
        foreach ($testScripts as $script) {
            $scriptName = basename($script);
            $scriptDescription = "No description available";
            
            // Try to extract description from file
            $content = file_get_contents($script);
            if (preg_match('/\/\*\*\s*\n\s*\*\s*(.*?)(?:\s*\n\s*\*|$)/s', $content, $matches)) {
                $scriptDescription = trim($matches[1]);
            }
            
            $scriptsList[] = [
                'name' => $scriptName,
                'path' => $script,
                'description' => $scriptDescription
            ];
        }

        $data = [
            'title' => 'Admin Script Runner',
            'scripts' => $scriptsList
        ];

        // Check if a script is being run
        if (isset($_POST['run_script']) && !empty($_POST['script'])) {
            $selectedScript = $_POST['script'];
            $scriptPath = APPROOT . '/test/' . $selectedScript;
            
            // Validate that the script exists and is in the test directory
            if (file_exists($scriptPath) && is_file($scriptPath) && dirname($scriptPath) === APPROOT . '/test') {
                // Capture script output
                ob_start();
                
                // Run the script in the current context, but with admin privileges guaranteed
                // We'll temporarily override the access control in the script
                $scriptContent = file_get_contents($scriptPath);
                
                // Replace access control code with a bypass for admin
                $modifiedContent = preg_replace(
                    '/\/\/ Only allow running by admin.*?exit;.*?\}/s',
                    '// Access control bypassed by admin script runner',
                    $scriptContent
                );
                
                // Execute the modified script
                try {
                    eval('?>' . $modifiedContent);
                } catch (Exception $e) {
                    echo "Error executing script: " . $e->getMessage();
                }
                
                $output = ob_get_clean();
                
                $data['output'] = $output;
                $data['selected_script'] = $selectedScript;
            } else {
                $data['output'] = "Invalid script selected.";
                $data['selected_script'] = $selectedScript;
            }
        }

        $this->view('admin/run_scripts', $data);
    }
    
    /**
     * Notification Settings
     */
    public function notification_settings() {
        // Load notification model
        $notificationModel = $this->model('NotificationModel');
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Update settings
            $settings = [
                'email_enabled' => isset($_POST['email_enabled']) ? '1' : '0',
                'sms_enabled' => isset($_POST['sms_enabled']) ? '1' : '0',
                'push_enabled' => isset($_POST['push_enabled']) ? '1' : '0',
                'toast_enabled' => isset($_POST['toast_enabled']) ? '1' : '0',
                'default_notification_times' => $_POST['default_notification_times'] ?? '[1440, 60, 15]',
                'max_notification_attempts' => $_POST['max_notification_attempts'] ?? '3',
                'notification_retry_interval' => $_POST['notification_retry_interval'] ?? '30',
                'email_from_address' => $_POST['email_from_address'] ?? '',
                'email_from_name' => $_POST['email_from_name'] ?? ''
            ];
            
            // Update each setting
            $success = true;
            foreach ($settings as $key => $value) {
                if (!$notificationModel->updateSetting($key, $value)) {
                    $success = false;
                }
            }
            
            if ($success) {
                $this->setFlashMessage('success', 'Notification settings updated successfully');
            } else {
                $this->setFlashMessage('error', 'Failed to update some notification settings');
            }
            
            $this->redirect('admin/notification_settings');
            return;
        }
        
        // Get current settings
        $settings = $notificationModel->getSettings();
        
        // Get SMS providers
        try {
            // Check if sms_providers table exists
            $this->db->query("SHOW TABLES LIKE 'sms_providers'");
            $tableExists = $this->db->single();
            
            if ($tableExists) {
                $smsProviders = $notificationModel->getSmsProviders();
            } else {
                $smsProviders = [];
                error_log('SMS providers table does not exist');
            }
        } catch (Exception $e) {
            error_log('Error loading SMS providers: ' . $e->getMessage());
            $smsProviders = [];
        }
        
        // Get users for testing
        $userModel = $this->model('UserModel');
        $users = $userModel->getUsers();
        
        // Get notification statistics
        $stats = [
            'pending' => 0,
            'sent_today' => 0,
            'failed' => 0,
            'subscriptions' => 0
        ];
        
        try {
            $stats = $notificationModel->getQueueStats();
            
            // Get subscription count
            $this->db->query('SELECT COUNT(*) as count FROM event_notification_subscriptions WHERE is_active = 1');
            $subscriptionResult = $this->db->single();
            $stats['subscriptions'] = $subscriptionResult ? $subscriptionResult->count : 0;
            
            // Get sent today count
            $this->db->query('SELECT COUNT(*) as count FROM notification_queue WHERE status = "sent" AND DATE(created_at) = UTC_DATE()');
            $sentTodayResult = $this->db->single();
            $stats['sent_today'] = $sentTodayResult ? $sentTodayResult->count : 0;
        } catch (Exception $e) {
            error_log('Error getting notification stats: ' . $e->getMessage());
        }
        
        $data = [
            'title' => 'Notification Settings',
            'settings' => $settings,
            'smsProviders' => $smsProviders,
            'users' => $users ?? [],
            'stats' => $stats,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/notification_settings', $data);
    }
    
    /**
     * Update SMS Provider Configuration
     */
    public function updateSmsProvider() {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Load notification model
        $notificationModel = $this->model('NotificationModel');
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        $providerId = intval($_POST['provider_id'] ?? 0);
        
        if ($providerId <= 0) {
            $this->setFlashMessage('error', 'Invalid provider ID');
            $this->redirect('admin/notification_settings');
            return;
        }
        
        // Get provider to check if it exists
        $providers = $notificationModel->getSmsProviders();
        $provider = null;
        foreach ($providers as $p) {
            if ($p->id == $providerId) {
                $provider = $p;
                break;
            }
        }
        
        if (!$provider) {
            $this->setFlashMessage('error', 'SMS provider not found');
            $this->redirect('admin/notification_settings');
            return;
        }
        
        // Prepare configuration based on provider type
        $config = [];
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        $isDefault = isset($_POST['is_default']) ? 1 : 0;
        
        // Provider-specific configuration
        switch ($provider->provider_key) {
            case 'twilio':
                $config = [
                    'account_sid' => $_POST['config']['account_sid'] ?? '',
                    'auth_token' => $_POST['config']['auth_token'] ?? '',
                    'from_number' => $_POST['config']['from_number'] ?? ''
                ];
                break;
                
            case 'textmagic':
                $config = [
                    'username' => $_POST['config']['username'] ?? '',
                    'api_key' => $_POST['config']['api_key'] ?? ''
                ];
                break;
                
            case 'nexmo':
                $config = [
                    'api_key' => $_POST['config']['api_key'] ?? '',
                    'api_secret' => $_POST['config']['api_secret'] ?? '',
                    'from' => $_POST['config']['from'] ?? ''
                ];
                break;
                
            case 'clicksend':
                $config = [
                    'username' => $_POST['config']['username'] ?? '',
                    'api_key' => $_POST['config']['api_key'] ?? ''
                ];
                break;
                
            case 'plivo':
                $config = [
                    'auth_id' => $_POST['config']['auth_id'] ?? '',
                    'auth_token' => $_POST['config']['auth_token'] ?? '',
                    'src' => $_POST['config']['src'] ?? ''
                ];
                break;
                
            default:
                $this->setFlashMessage('error', 'Unknown provider type');
                $this->redirect('admin/notification_settings');
                return;
        }
        
        // Update provider
        $updateData = [
            'is_active' => $isActive,
            'is_default' => $isDefault,
            'configuration' => $config
        ];
        
        if ($notificationModel->updateSmsProvider($providerId, $updateData)) {
            $this->setFlashMessage('success', $provider->name . ' configuration updated successfully');
        } else {
            $this->setFlashMessage('error', 'Failed to update ' . $provider->name . ' configuration');
        }
        
        $this->redirect('admin/notification_settings');
    }
    
    /**
     * Install Notification System
     */
    public function installNotifications() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Run the installation
            ob_start();
            
            try {
                // Check if installer file exists
                $installerPath = APPROOT . '/install/install_notifications.php';
                if (file_exists($installerPath)) {
                    // Use external installer if available
                    require_once $installerPath;
                    $installer = new NotificationInstaller();
                    $installer->install();
                } else {
                    // Use built-in installation method
                    $this->runDirectNotificationInstallation();
                }
                
                $output = ob_get_clean();
                
                $data = [
                    'title' => 'Install Notification System',
                    'output' => $output,
                    'success' => true
                ];
                
            } catch (Exception $e) {
                $output = ob_get_clean();
                $data = [
                    'title' => 'Install Notification System',
                    'output' => $output,
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }
            
            $this->view('admin/install_notifications', $data);
            return;
        }
        
        // Show installation form
        $data = [
            'title' => 'Install Notification System',
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/install_notifications', $data);
    }
    
    /**
     * Run Direct Notification Installation
     */
    private function runDirectNotificationInstallation() {
        echo "<h2>Installing Notification System v3.48.6</h2>\n";
        
        $errors = [];
        $success = [];
        
        // Create database tables
        echo "Creating database tables...\n";
        
        $tables = [
            'user_notification_preferences' => "
                CREATE TABLE IF NOT EXISTS `user_notification_preferences` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `email_notifications` tinyint(1) DEFAULT 1,
                  `sms_notifications` tinyint(1) DEFAULT 0,
                  `push_notifications` tinyint(1) DEFAULT 1,
                  `toast_notifications` tinyint(1) DEFAULT 1,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `user_id` (`user_id`),
                  KEY `idx_user_notifications` (`user_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'event_notification_subscriptions' => "
                CREATE TABLE IF NOT EXISTS `event_notification_subscriptions` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `event_id` int(11) NOT NULL,
                  `event_type` enum('calendar_event','car_show') NOT NULL DEFAULT 'calendar_event',
                  `notification_times` text NOT NULL COMMENT 'JSON array of notification times in minutes before event',
                  `notify_registration_end` tinyint(1) DEFAULT 0 COMMENT 'For car shows - notify before registration deadline',
                  `registration_notification_times` text DEFAULT NULL COMMENT 'JSON array for registration deadline notifications',
                  `is_active` tinyint(1) DEFAULT 1,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `user_event_subscription` (`user_id`, `event_id`, `event_type`),
                  KEY `idx_user_subscriptions` (`user_id`),
                  KEY `idx_event_subscriptions` (`event_id`, `event_type`),
                  KEY `idx_active_subscriptions` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'notification_queue' => "
                CREATE TABLE IF NOT EXISTS `notification_queue` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `subscription_id` int(11) NOT NULL,
                  `notification_type` enum('email','sms','push','toast') NOT NULL,
                  `event_id` int(11) NOT NULL,
                  `event_type` enum('calendar_event','car_show') NOT NULL,
                  `notification_category` enum('event_reminder','registration_deadline') NOT NULL DEFAULT 'event_reminder',
                  `scheduled_time` datetime NOT NULL,
                  `subject` varchar(255) NOT NULL,
                  `message` text NOT NULL,
                  `status` enum('pending','sent','failed','cancelled') DEFAULT 'pending',
                  `attempts` int(11) DEFAULT 0,
                  `last_attempt` datetime DEFAULT NULL,
                  `error_message` text DEFAULT NULL,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `idx_user_notifications` (`user_id`),
                  KEY `idx_subscription_notifications` (`subscription_id`),
                  KEY `idx_scheduled_notifications` (`scheduled_time`, `status`),
                  KEY `idx_status_notifications` (`status`),
                  KEY `idx_event_notifications` (`event_id`, `event_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'notification_settings' => "
                CREATE TABLE IF NOT EXISTS `notification_settings` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `setting_key` varchar(100) NOT NULL,
                  `setting_value` text NOT NULL,
                  `setting_type` enum('string','integer','boolean','json') DEFAULT 'string',
                  `description` text DEFAULT NULL,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `setting_key` (`setting_key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'sms_providers' => "
                CREATE TABLE IF NOT EXISTS `sms_providers` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `name` varchar(100) NOT NULL,
                  `provider_key` varchar(50) NOT NULL,
                  `api_endpoint` varchar(255) NOT NULL,
                  `api_key` varchar(255) DEFAULT NULL,
                  `api_secret` varchar(255) DEFAULT NULL,
                  `sender_id` varchar(50) DEFAULT NULL,
                  `is_active` tinyint(1) DEFAULT 0,
                  `is_default` tinyint(1) DEFAULT 0,
                  `configuration` text DEFAULT NULL COMMENT 'JSON configuration for provider-specific settings',
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `provider_key` (`provider_key`),
                  KEY `idx_active_providers` (`is_active`),
                  KEY `idx_default_provider` (`is_default`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'user_push_notifications' => "
                CREATE TABLE IF NOT EXISTS `user_push_notifications` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `title` varchar(255) NOT NULL,
                  `message` text NOT NULL,
                  `event_id` int(11) DEFAULT NULL,
                  `event_type` enum('calendar_event','car_show','test') DEFAULT NULL,
                  `is_read` tinyint(1) DEFAULT 0,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `idx_user_push` (`user_id`),
                  KEY `idx_unread_push` (`user_id`, `is_read`),
                  KEY `idx_event_push` (`event_id`, `event_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            'user_toast_notifications' => "
                CREATE TABLE IF NOT EXISTS `user_toast_notifications` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `title` varchar(255) NOT NULL,
                  `message` text NOT NULL,
                  `event_id` int(11) DEFAULT NULL,
                  `event_type` enum('calendar_event','car_show','test') DEFAULT NULL,
                  `is_read` tinyint(1) DEFAULT 0,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `idx_user_toast` (`user_id`),
                  KEY `idx_unread_toast` (`user_id`, `is_read`),
                  KEY `idx_event_toast` (`event_id`, `event_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];
        
        foreach ($tables as $tableName => $sql) {
            try {
                $this->db->query($sql);
                $this->db->execute();
                $success[] = "Created table: $tableName";
                echo "✓ Created table: $tableName\n";
            } catch (Exception $e) {
                $errorMessage = $e->getMessage();
                if (strpos($errorMessage, 'already exists') !== false) {
                    $success[] = "Table already exists: $tableName";
                    echo "✓ Table already exists: $tableName\n";
                } else {
                    $errors[] = "Failed to create table $tableName: " . $errorMessage;
                    echo "✗ Failed to create table $tableName: " . $errorMessage . "\n";
                }
            }
        }
        
        // Add phone column to users table if it doesn't exist
        try {
            $this->db->query("SHOW COLUMNS FROM users LIKE 'phone'");
            $result = $this->db->single();
            
            if (!$result) {
                $this->db->query("ALTER TABLE users ADD COLUMN phone varchar(20) DEFAULT NULL AFTER email");
                $this->db->execute();
                $success[] = "Added phone column to users table";
                echo "✓ Added phone column to users table\n";
                
                $this->db->query("ALTER TABLE users ADD INDEX idx_user_phone (phone)");
                $this->db->execute();
                $success[] = "Added index for phone column";
                echo "✓ Added index for phone column\n";
            } else {
                $success[] = "Phone column already exists in users table";
                echo "✓ Phone column already exists in users table\n";
            }
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                $success[] = "Phone column already exists in users table";
                echo "✓ Phone column already exists in users table\n";
            } else {
                $errors[] = "Failed to add phone column: " . $e->getMessage();
                echo "✗ Failed to add phone column: " . $e->getMessage() . "\n";
            }
        }
        
        // Insert default settings
        echo "Inserting default settings...\n";
        $settings = [
            ['email_enabled', '1', 'boolean', 'Enable email notifications globally'],
            ['sms_enabled', '1', 'boolean', 'Enable SMS notifications globally'],
            ['push_enabled', '1', 'boolean', 'Enable push notifications globally'],
            ['toast_enabled', '1', 'boolean', 'Enable toast notifications globally'],
            ['default_notification_times', '[1440, 60, 15]', 'json', 'Default notification times in minutes before event (24h, 1h, 15min)'],
            ['max_notification_attempts', '3', 'integer', 'Maximum number of attempts to send a notification'],
            ['notification_retry_interval', '30', 'integer', 'Minutes to wait before retrying failed notifications'],
            ['email_from_address', '<EMAIL>', 'string', 'Default from email address for notifications'],
            ['email_from_name', 'Rowan Elite Rides Events', 'string', 'Default from name for email notifications']
        ];
        
        foreach ($settings as $setting) {
            try {
                $this->db->query("INSERT IGNORE INTO notification_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
                $this->db->bind(1, $setting[0]);
                $this->db->bind(2, $setting[1]);
                $this->db->bind(3, $setting[2]);
                $this->db->bind(4, $setting[3]);
                $this->db->execute();
                
                $success[] = "Inserted setting: {$setting[0]}";
                echo "✓ Inserted setting: {$setting[0]}\n";
            } catch (Exception $e) {
                $errors[] = "Failed to insert setting {$setting[0]}: " . $e->getMessage();
                echo "✗ Failed to insert setting {$setting[0]}: " . $e->getMessage() . "\n";
            }
        }
        
        // Insert SMS providers
        echo "Inserting SMS providers...\n";
        $providers = [
            ['Twilio', 'twilio', 'https://api.twilio.com/2010-04-01/Accounts/{account_sid}/Messages.json', 'Events', '{"account_sid":"","auth_token":"","from_number":""}'],
            ['TextMagic', 'textmagic', 'https://rest.textmagic.com/api/v2/messages', 'Events', '{"username":"","api_key":""}'],
            ['Nexmo/Vonage', 'nexmo', 'https://rest.nexmo.com/sms/json', 'Events', '{"api_key":"","api_secret":"","from":""}'],
            ['ClickSend', 'clicksend', 'https://rest.clicksend.com/v3/sms/send', 'Events', '{"username":"","api_key":""}'],
            ['Plivo', 'plivo', 'https://api.plivo.com/v1/Account/{auth_id}/Message/', 'Events', '{"auth_id":"","auth_token":"","src":""}']
        ];
        
        foreach ($providers as $provider) {
            try {
                $this->db->query("INSERT IGNORE INTO sms_providers (name, provider_key, api_endpoint, sender_id, configuration) VALUES (?, ?, ?, ?, ?)");
                $this->db->bind(1, $provider[0]);
                $this->db->bind(2, $provider[1]);
                $this->db->bind(3, $provider[2]);
                $this->db->bind(4, $provider[3]);
                $this->db->bind(5, $provider[4]);
                $this->db->execute();
                
                $success[] = "Inserted SMS provider: {$provider[0]}";
                echo "✓ Inserted SMS provider: {$provider[0]}\n";
            } catch (Exception $e) {
                $errors[] = "Failed to insert SMS provider {$provider[0]}: " . $e->getMessage();
                echo "✗ Failed to insert SMS provider {$provider[0]}: " . $e->getMessage() . "\n";
            }
        }
        
        // Create default notification preferences for existing users
        echo "Creating default notification preferences for existing users...\n";
        try {
            $this->db->query("INSERT INTO user_notification_preferences (user_id, email_notifications, sms_notifications, push_notifications, toast_notifications)
                              SELECT id, 1, 0, 1, 1 FROM users 
                              WHERE id NOT IN (SELECT user_id FROM user_notification_preferences)");
            $this->db->execute();
            $success[] = "Created default notification preferences for existing users";
            echo "✓ Created default notification preferences for existing users\n";
        } catch (Exception $e) {
            $errors[] = "Failed to create default notification preferences: " . $e->getMessage();
            echo "✗ Failed to create default notification preferences: " . $e->getMessage() . "\n";
        }
        
        echo "\n=== Installation Summary ===\n";
        echo "Success: " . count($success) . " operations\n";
        echo "Errors: " . count($errors) . " operations\n";
        
        if (!empty($errors)) {
            echo "\nErrors encountered:\n";
            foreach ($errors as $error) {
                echo "- " . $error . "\n";
            }
        }
        
        echo "\nInstallation completed!\n";
    }
    
    /**
     * Check and create SMS providers if missing
     */
    public function checkSmsProviders() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        try {
            // Check if sms_providers table exists
            $this->db->query("SHOW TABLES LIKE 'sms_providers'");
            $tableExists = $this->db->single();
            
            if (!$tableExists) {
                echo "SMS providers table does not exist. Please run the notification system installation first.";
                return;
            }
            
            // Check if providers exist
            $this->db->query("SELECT COUNT(*) as count FROM sms_providers");
            $result = $this->db->single();
            $count = $result ? $result->count : 0;
            
            echo "Current SMS providers count: " . $count . "<br>";
            
            if ($count == 0) {
                echo "No SMS providers found. Creating default providers...<br>";
                
                // Insert SMS providers
                $providers = [
                    ['Twilio', 'twilio', 'https://api.twilio.com/2010-04-01/Accounts/{account_sid}/Messages.json', 'Events', '{"account_sid":"","auth_token":"","from_number":""}'],
                    ['TextMagic', 'textmagic', 'https://rest.textmagic.com/api/v2/messages', 'Events', '{"username":"","api_key":""}'],
                    ['Nexmo/Vonage', 'nexmo', 'https://rest.nexmo.com/sms/json', 'Events', '{"api_key":"","api_secret":"","from":""}'],
                    ['ClickSend', 'clicksend', 'https://rest.clicksend.com/v3/sms/send', 'Events', '{"username":"","api_key":""}'],
                    ['Plivo', 'plivo', 'https://api.plivo.com/v1/Account/{auth_id}/Message/', 'Events', '{"auth_id":"","auth_token":"","src":""}']
                ];
                
                foreach ($providers as $provider) {
                    try {
                        $this->db->query("INSERT INTO sms_providers (name, provider_key, api_endpoint, sender_id, configuration) VALUES (?, ?, ?, ?, ?)");
                        $this->db->bind(1, $provider[0]);
                        $this->db->bind(2, $provider[1]);
                        $this->db->bind(3, $provider[2]);
                        $this->db->bind(4, $provider[3]);
                        $this->db->bind(5, $provider[4]);
                        $this->db->execute();
                        
                        echo "✓ Created SMS provider: {$provider[0]}<br>";
                    } catch (Exception $e) {
                        echo "✗ Failed to create SMS provider {$provider[0]}: " . $e->getMessage() . "<br>";
                    }
                }
                
                echo "<br>SMS providers created successfully!<br>";
            } else {
                echo "SMS providers already exist:<br>";
                
                $this->db->query("SELECT name, provider_key, is_active, is_default FROM sms_providers ORDER BY name");
                $providers = $this->db->resultSet();
                
                foreach ($providers as $provider) {
                    $status = [];
                    if ($provider->is_active) $status[] = 'Active';
                    if ($provider->is_default) $status[] = 'Default';
                    $statusText = !empty($status) ? ' (' . implode(', ', $status) . ')' : ' (Inactive)';
                    
                    echo "- " . $provider->name . " (" . $provider->provider_key . ")" . $statusText . "<br>";
                }
            }
            
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage();
        }
    }
    
    /**
     * Notification Queue
     */
    public function notification_queue() {
        // Load notification model
        $notificationModel = $this->model('NotificationModel');
        
        // Get filter parameters from GET request
        $current_status = $_GET['status'] ?? 'all';
        $current_limit = (int)($_GET['limit'] ?? 50);
        
        // Validate status filter
        $validStatuses = ['all', 'pending', 'sent', 'failed', 'cancelled'];
        if (!in_array($current_status, $validStatuses)) {
            $current_status = 'all';
        }
        
        // Validate limit
        $validLimits = [25, 50, 100, 200];
        if (!in_array($current_limit, $validLimits)) {
            $current_limit = 50;
        }
        
        // Auto-fix NULL last_attempt values for sent notifications
        $fixResult = $notificationModel->fixNullLastAttemptValues();
        if ($fixResult['success'] && $fixResult['fixed_count'] > 0) {
            $this->setFlashMessage('info', "Auto-fixed {$fixResult['fixed_count']} notification records with missing Last Attempt times.");
            
            if (DEBUG_MODE) {
                error_log("AdminController: Auto-fixed {$fixResult['fixed_count']} notification last_attempt values");
            }
        }
        
        // Get queue items with filters
        $status_filter = ($current_status === 'all') ? null : $current_status;
        $notifications = $notificationModel->getQueueItems($status_filter, $current_limit);
        
        // Get queue statistics
        $stats = $notificationModel->getQueueStats();
        
        $data = [
            'title' => 'Notification Queue',
            'notifications' => $notifications ?? [], // Ensure it's never null
            'stats' => $stats,
            'current_status' => $current_status,
            'current_limit' => $current_limit,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/notification_queue', $data);
    }
    
    /**
     * Clear Failed Notifications
     */
    public function clearFailed() {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Load notification model
        $notificationModel = $this->model('NotificationModel');
        
        // Clear failed notifications
        $result = $notificationModel->clearFailedNotifications();
        
        if ($result) {
            $this->setFlashMessage('success', 'Failed notifications cleared successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to clear failed notifications.');
        }
        
        $this->redirect('admin/notification_queue');
    }
    
    /**
     * Test Notifications
     */
    public function test_notifications() {
        // Load notification models
        $notificationModel = $this->model('NotificationModel');
        $notificationService = $this->model('NotificationService');
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Verify CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            $testType = $_POST['test_type'] ?? '';
            $testEmail = $_POST['test_email'] ?? '';
            $testPhone = $_POST['test_phone'] ?? '';
            $testMessage = $_POST['test_message'] ?? 'This is a test notification from the Events & Shows system.';
            
            $results = [];
            
            switch ($testType) {
                case 'email':
                    if (!empty($testEmail)) {
                        $result = $notificationService->sendTestEmail($testEmail, $testMessage);
                        $results[] = [
                            'type' => 'Email',
                            'recipient' => $testEmail,
                            'success' => $result,
                            'message' => $result ? 'Email sent successfully' : 'Failed to send email'
                        ];
                    }
                    break;
                    
                case 'sms':
                    if (!empty($testPhone)) {
                        $result = $notificationService->sendTestSms($testPhone, $testMessage);
                        $results[] = [
                            'type' => 'SMS',
                            'recipient' => $testPhone,
                            'success' => $result,
                            'message' => $result ? 'SMS sent successfully' : 'Failed to send SMS'
                        ];
                    }
                    break;
                    
                case 'push':
                    $userId = $this->auth->getCurrentUserId();
                    $result = $notificationService->sendTestPushNotification($userId, 'Test Notification', $testMessage);
                    $results[] = [
                        'type' => 'Push',
                        'recipient' => 'Current User',
                        'success' => $result,
                        'message' => $result ? 'Push notification sent successfully' : 'Failed to send push notification'
                    ];
                    break;
                    
                case 'toast':
                    $userId = $this->auth->getCurrentUserId();
                    $result = $notificationService->sendTestToastNotification($userId, 'Test Notification', $testMessage);
                    $results[] = [
                        'type' => 'Toast',
                        'recipient' => 'Current User',
                        'success' => $result,
                        'message' => $result ? 'Toast notification sent successfully' : 'Failed to send toast notification'
                    ];
                    break;
                    
                case 'all':
                    // Test all notification types
                    if (!empty($testEmail)) {
                        $result = $notificationService->sendTestEmail($testEmail, $testMessage);
                        $results[] = [
                            'type' => 'Email',
                            'recipient' => $testEmail,
                            'success' => $result,
                            'message' => $result ? 'Email sent successfully' : 'Failed to send email'
                        ];
                    }
                    
                    if (!empty($testPhone)) {
                        $result = $notificationService->sendTestSms($testPhone, $testMessage);
                        $results[] = [
                            'type' => 'SMS',
                            'recipient' => $testPhone,
                            'success' => $result,
                            'message' => $result ? 'SMS sent successfully' : 'Failed to send SMS'
                        ];
                    }
                    
                    $userId = $this->auth->getCurrentUserId();
                    $result = $notificationService->sendTestPushNotification($userId, 'Test Notification', $testMessage);
                    $results[] = [
                        'type' => 'Push',
                        'recipient' => 'Current User',
                        'success' => $result,
                        'message' => $result ? 'Push notification sent successfully' : 'Failed to send push notification'
                    ];
                    
                    $result = $notificationService->sendTestToastNotification($userId, 'Test Notification', $testMessage);
                    $results[] = [
                        'type' => 'Toast',
                        'recipient' => 'Current User',
                        'success' => $result,
                        'message' => $result ? 'Toast notification sent successfully' : 'Failed to send toast notification'
                    ];
                    break;
            }
            
            // Store results in session for display
            $_SESSION['test_results'] = $results;
            $this->redirect('admin/test_notifications');
            return;
        }
        
        // Get test results from session
        $testResults = $_SESSION['test_results'] ?? [];
        unset($_SESSION['test_results']);
        
        // Get notification settings
        $settings = $notificationModel->getSettings();
        
        // Get users for testing
        $userModel = $this->model('UserModel');
        $users = $userModel->getUsers();
        
        $data = [
            'title' => 'Test Notifications',
            'testResults' => $testResults,
            'settings' => $settings,
            'users' => $users ?? [], // Ensure it's never null
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/test_notifications', $data);
    }
    
    /**
     * Clear Toast Notifications
     */
    public function clearToastNotifications() {
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        $userId = $_POST['user_id'] ?? $_SESSION['user_id'];
        
        // Load notification model
        $notificationModel = $this->model('NotificationModel');
        
        // Clear toast notifications for the user
        $success = $notificationModel->clearUserToastNotifications($userId);
        
        if ($success) {
            $this->setFlashMessage('success', 'Toast notifications cleared successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to clear toast notifications.');
        }
        
        $this->redirect('admin/test_notifications');
    }
    
    /**
     * Update notification settings
     */
    public function updateNotificationSettings() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/notification_settings');
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->setFlashMessage('error', 'Invalid request. Please try again.');
            $this->redirect('admin/notification_settings');
        }
        
        // Load notification model
        $notificationModel = $this->model('NotificationModel');
        
        $settings = [
            'email_enabled' => isset($_POST['email_enabled']),
            'sms_enabled' => isset($_POST['sms_enabled']),
            'push_enabled' => isset($_POST['push_enabled']),
            'toast_enabled' => isset($_POST['toast_enabled']),
            'email_from_address' => $_POST['email_from_address'] ?? '',
            'email_from_name' => $_POST['email_from_name'] ?? '',
            'max_notification_attempts' => (int) ($_POST['max_notification_attempts'] ?? 3),
            'notification_retry_interval' => (int) ($_POST['notification_retry_interval'] ?? 30)
        ];
        
        // Validate email settings
        if (!filter_var($settings['email_from_address'], FILTER_VALIDATE_EMAIL)) {
            $this->setFlashMessage('error', 'Please enter a valid email address.');
            $this->redirect('admin/notification_settings');
        }
        
        if (empty($settings['email_from_name'])) {
            $this->setFlashMessage('error', 'Please enter a from name for emails.');
            $this->redirect('admin/notification_settings');
        }
        
        // Validate numeric settings
        if ($settings['max_notification_attempts'] < 1 || $settings['max_notification_attempts'] > 10) {
            $this->setFlashMessage('error', 'Max retry attempts must be between 1 and 10.');
            $this->redirect('admin/notification_settings');
        }
        
        if ($settings['notification_retry_interval'] < 5 || $settings['notification_retry_interval'] > 1440) {
            $this->setFlashMessage('error', 'Retry interval must be between 5 and 1440 minutes.');
            $this->redirect('admin/notification_settings');
        }
        
        // Update settings
        $success = true;
        $updatedCount = 0;
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("AdminController::updateNotificationSettings - Updating settings: " . print_r($settings, true));
        }
        
        foreach ($settings as $key => $value) {
            $type = is_bool($value) ? 'boolean' : (is_int($value) ? 'integer' : 'string');
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("AdminController::updateNotificationSettings - Updating $key = " . var_export($value, true) . " (type: $type)");
            }
            
            if ($notificationModel->updateNotificationSetting($key, $value, $type)) {
                $updatedCount++;
            } else {
                $success = false;
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("AdminController::updateNotificationSettings - Failed to update $key");
                }
                break;
            }
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("AdminController::updateNotificationSettings - Updated $updatedCount/" . count($settings) . " settings successfully");
        }
        
        if ($success) {
            $this->setFlashMessage('success', 'Notification settings updated successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update notification settings.');
        }
        
        $this->redirect('admin/notification_settings');
    }
    
    /**
     * Club ownership verifications management
     * 
     * @return void
     */
    public function clubOwnershipVerifications() {
        // Get status filter
        $status = isset($_GET['status']) && in_array($_GET['status'], ['pending', 'approved', 'denied']) ? $_GET['status'] : 'all';
        
        // Get verifications
        $verifications = $this->calendarModel->getClubOwnershipVerifications($status);
        
        $data = [
            'title' => 'Club Ownership Verifications',
            'verifications' => $verifications,
            'current_status' => $status
        ];
        
        $this->view('admin/club_ownership_verifications', $data);
    }
    
    /**
     * Approve club ownership verification
     * 
     * @param int $id Verification ID
     * @return void
     */
    public function approveClubOwnership($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                flash('verification_message', 'Invalid request', 'alert alert-danger');
                redirect('admin/clubOwnershipVerifications');
            }
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            $adminNotes = trim($_POST['admin_notes'] ?? '');
            
            // Get verification request
            $verification = $this->calendarModel->getClubOwnershipVerificationById($id);
            
            if (!$verification) {
                flash('verification_message', 'Verification request not found', 'alert alert-danger');
                redirect('admin/clubOwnershipVerifications');
            }
            
            if ($verification->status !== 'pending') {
                flash('verification_message', 'This verification request has already been processed', 'alert alert-warning');
                redirect('admin/clubOwnershipVerifications');
            }
            
            // Approve the verification
            if ($this->calendarModel->approveClubOwnershipVerification($id, $_SESSION['user_id'], $adminNotes)) {
                // Send approval notification email
                $this->sendOwnershipApprovalNotification($verification, $adminNotes);
                
                flash('verification_message', 'Club ownership verification approved successfully', 'alert alert-success');
            } else {
                flash('verification_message', 'Failed to approve verification request', 'alert alert-danger');
            }
        }
        
        redirect('admin/clubOwnershipVerifications');
    }
    
    /**
     * Deny club ownership verification
     * 
     * @param int $id Verification ID
     * @return void
     */
    public function denyClubOwnership($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                flash('verification_message', 'Invalid request', 'alert alert-danger');
                redirect('admin/clubOwnershipVerifications');
            }
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            $adminNotes = trim($_POST['admin_notes'] ?? '');
            
            if (empty($adminNotes)) {
                flash('verification_message', 'Please provide a reason for denial', 'alert alert-danger');
                redirect('admin/clubOwnershipVerifications');
            }
            
            // Get verification request
            $verification = $this->calendarModel->getClubOwnershipVerificationById($id);
            
            if (!$verification) {
                flash('verification_message', 'Verification request not found', 'alert alert-danger');
                redirect('admin/clubOwnershipVerifications');
            }
            
            if ($verification->status !== 'pending') {
                flash('verification_message', 'This verification request has already been processed', 'alert alert-warning');
                redirect('admin/clubOwnershipVerifications');
            }
            
            // Deny the verification
            if ($this->calendarModel->denyClubOwnershipVerification($id, $_SESSION['user_id'], $adminNotes)) {
                // Send denial notification email
                $this->sendOwnershipDenialNotification($verification, $adminNotes);
                
                flash('verification_message', 'Club ownership verification denied', 'alert alert-success');
            } else {
                flash('verification_message', 'Failed to deny verification request', 'alert alert-danger');
            }
        }
        
        redirect('admin/clubOwnershipVerifications');
    }
    
    /**
     * Send ownership approval notification email
     * 
     * @param object $verification Verification object
     * @param string $adminNotes Admin notes
     * @return void
     */
    private function sendOwnershipApprovalNotification($verification, $adminNotes = '') {
        $subject = 'Club Ownership Verification Approved - ' . SITENAME;
        
        $message = "Dear " . $verification->user_name . ",\n\n";
        $message .= "Congratulations! Your club ownership verification request has been approved.\n\n";
        $message .= "Club: " . $verification->club_name . "\n";
        $message .= "Request ID: #" . $verification->id . "\n";
        $message .= "Approved on: " . formatDateTimeForUser(gmdate('Y-m-d H:i:s'), $_SESSION['user_id'] ?? null, 'F j, Y g:i A') . "\n\n";
        
        if (!empty($adminNotes)) {
            $message .= "Admin Notes:\n" . $adminNotes . "\n\n";
        }
        
        $message .= "You now have full ownership privileges for this club, including:\n";
        $message .= "- Adding and removing club members\n";
        $message .= "- Editing club information\n";
        $message .= "- Managing club events\n\n";
        
        $message .= "You can manage your club at: " . URLROOT . "/calendar/manageClubs\n\n";
        $message .= "Thank you for being part of " . SITENAME . "!\n\n";
        $message .= "Best regards,\n";
        $message .= SITENAME . " Administration Team";
        
        // Send email
        mail($verification->user_email, $subject, $message);
    }
    
    /**
     * Send ownership denial notification email
     * 
     * @param object $verification Verification object
     * @param string $adminNotes Admin notes (reason for denial)
     * @return void
     */
    private function sendOwnershipDenialNotification($verification, $adminNotes) {
        $subject = 'Club Ownership Verification Denied - ' . SITENAME;
        
        $message = "Dear " . $verification->user_name . ",\n\n";
        $message .= "We regret to inform you that your club ownership verification request has been denied.\n\n";
        $message .= "Club: " . $verification->club_name . "\n";
        $message .= "Request ID: #" . $verification->id . "\n";
        $message .= "Reviewed on: " . formatDateTimeForUser(gmdate('Y-m-d H:i:s'), $_SESSION['user_id'] ?? null, 'F j, Y g:i A') . "\n\n";
        
        $message .= "Reason for denial:\n" . $adminNotes . "\n\n";
        
        $message .= "If you believe this decision was made in error or if you have additional information that supports your ownership claim, please contact us directly.\n\n";
        
        $message .= "You may submit a new verification request in the future if circumstances change.\n\n";
        
        $message .= "Thank you for your understanding.\n\n";
        $message .= "Best regards,\n";
        $message .= SITENAME . " Administration Team";
        
        // Send email
        mail($verification->user_email, $subject, $message);
    }
    

    
    /**
     * Get show statistics for dashboard
     */
    private function getShowStatistics($isAdmin, $userId) {
        $stats = new stdClass();
        
        // Base query conditions
        $whereCondition = '';
        $params = [];
        
        if (!$isAdmin) {
            $whereCondition = 'WHERE s.coordinator_id = :coordinator_id';
            $params[':coordinator_id'] = $userId;
        }
        
        // Total shows
        $this->db->query("SELECT COUNT(*) as count FROM shows s $whereCondition");
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        $stats->total_shows = $this->db->single()->count;
        
        // Active shows
        $activeWhere = $whereCondition ? $whereCondition . " AND s.status = 'active'" : "WHERE s.status = 'active'";
        $this->db->query("SELECT COUNT(*) as count FROM shows s $activeWhere");
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        $stats->active_shows = $this->db->single()->count;
        
        // Upcoming shows
        $upcomingWhere = $whereCondition ? $whereCondition . " AND s.status = 'upcoming'" : "WHERE s.status = 'upcoming'";
        $this->db->query("SELECT COUNT(*) as count FROM shows s $upcomingWhere");
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        $stats->upcoming_shows = $this->db->single()->count;
        
        // Total registrations
        $this->db->query("SELECT COUNT(DISTINCT r.id) as count 
                         FROM shows s 
                         LEFT JOIN registrations r ON s.id = r.show_id 
                         $whereCondition");
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        $stats->total_registrations = $this->db->single()->count;
        
        // Total revenue
        $this->db->query("SELECT SUM(CASE WHEN r.payment_status = 'completed' THEN COALESCE(r.fee, 0) ELSE 0 END) as revenue
                         FROM shows s 
                         LEFT JOIN registrations r ON s.id = r.show_id 
                         $whereCondition");
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        $result = $this->db->single();
        $stats->total_revenue = $result->revenue ?? 0;
        
        // Pending payments
        $this->db->query("SELECT COUNT(DISTINCT r.id) as count 
                         FROM shows s 
                         LEFT JOIN registrations r ON s.id = r.show_id 
                         $whereCondition AND r.payment_status = 'pending'");
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        $stats->pending_payments = $this->db->single()->count;
        
        return $stats;
    }
    
    /**
     * Get recent show activities
     */
    private function getRecentShowActivities($isAdmin, $userId) {
        $whereCondition = '';
        $params = [];
        
        if (!$isAdmin) {
            $whereCondition = 'WHERE s.coordinator_id = :coordinator_id';
            $params[':coordinator_id'] = $userId;
        }
        
        // Get recent registrations
        $this->db->query("SELECT r.id, r.created_at, s.name as show_name, s.id as show_id,
                                 CONCAT(v.year, ' ', v.make, ' ', v.model) as vehicle_info,
                                 u.name as owner_name,
                                 'registration' as activity_type
                         FROM registrations r
                         JOIN shows s ON r.show_id = s.id
                         JOIN vehicles v ON r.vehicle_id = v.id
                         JOIN users u ON v.owner_id = u.id
                         $whereCondition
                         ORDER BY r.created_at DESC
                         LIMIT 10");
        
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Get upcoming show deadlines
     */
    private function getUpcomingShowDeadlines($isAdmin, $userId) {
        $whereCondition = '';
        $params = [];
        
        if (!$isAdmin) {
            $whereCondition = 'WHERE s.coordinator_id = :coordinator_id';
            $params[':coordinator_id'] = $userId;
        }
        
        $additionalWhere = $whereCondition ? ' AND ' : ' WHERE ';
        
        $this->db->query("SELECT s.id, s.name, s.registration_end, s.start_date, s.end_date,
                                 DATEDIFF(s.registration_end, NOW()) as days_to_reg_deadline,
                                 DATEDIFF(s.start_date, NOW()) as days_to_show
                         FROM shows s
                         $whereCondition
                         $additionalWhere (s.registration_end >= NOW() OR s.start_date >= NOW())
                         AND s.status IN ('upcoming', 'active')
                         ORDER BY s.registration_end ASC, s.start_date ASC
                         LIMIT 10");
        
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Quick actions for show management
     */
    public function quickAction() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/shows');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        $action = $_POST['action'] ?? '';
        $showIds = $_POST['show_ids'] ?? [];
        
        if (empty($action) || empty($showIds)) {
            setFlashMessage('admin_error', 'Invalid action or no shows selected', 'danger');
            $this->redirect('admin/shows');
            return;
        }
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($showIds as $showId) {
            $showId = (int)$showId;
            
            // Verify user has permission to modify this show
            $show = $this->showModel->getShowById($showId);
            if (!$show) {
                $errorCount++;
                continue;
            }
            
            // Check permissions
            $currentUser = $this->userModel->getUserById($_SESSION['user_id']);
            $isAdmin = ($currentUser && $currentUser->role === 'admin');
            $isCoordinator = ($currentUser && $currentUser->role === 'coordinator' && $show->coordinator_id == $currentUser->id);
            
            if (!$isAdmin && !$isCoordinator) {
                $errorCount++;
                continue;
            }
            
            switch ($action) {
                case 'activate':
                    if ($this->showModel->updateShowStatus($showId, 'active')) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                    break;
                    
                case 'complete':
                    if ($this->showModel->updateShowStatus($showId, 'completed')) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                    break;
                    
                case 'cancel':
                    if ($this->showModel->updateShowStatus($showId, 'cancelled')) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                    break;
                    
                case 'delete':
                    // Only admins can delete shows
                    if ($isAdmin && $this->showModel->deleteShow($showId)) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                    break;
                    
                default:
                    $errorCount++;
                    break;
            }
        }
        
        // Set flash messages
        if ($successCount > 0) {
            setFlashMessage('admin_success', "Successfully processed $successCount show(s)", 'success');
        }
        
        if ($errorCount > 0) {
            setFlashMessage('admin_error', "Failed to process $errorCount show(s)", 'danger');
        }
        
        $this->redirect('admin/shows');
    }
    
    /**
     * Fix pending show payments
     * 
     * This method finds shows stuck in payment_pending status and creates
     * missing payment records so admins can manage them
     */
    public function fixPendingShowPayments() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Find shows with payment_pending status
        $this->db->query('SELECT s.*, u.name as coordinator_name, u.email as coordinator_email
                          FROM shows s
                          LEFT JOIN users u ON s.coordinator_id = u.id
                          WHERE s.status = "payment_pending"
                          ORDER BY s.created_at DESC');
        
        $pendingShows = $this->db->resultSet();
        
        // Get default payment method
        $this->db->query('SELECT * FROM payment_methods WHERE is_active = 1 ORDER BY id LIMIT 1');
        $defaultPaymentMethod = $this->db->single();
        
        // Get listing fee from settings
        $listingFee = floatval($this->settingsModel->getSetting('listing_fee', 25.00));
        
        $fixedShows = [];
        $existingPayments = [];
        
        foreach ($pendingShows as $show) {
            // Check if payment record already exists
            $this->db->query('SELECT COUNT(*) as count FROM payments 
                              WHERE payment_type = "show_listing" AND related_id = :show_id');
            $this->db->bind(':show_id', $show->id);
            $existingPayment = $this->db->single();
            
            if ($existingPayment->count > 0) {
                $existingPayments[] = $show;
            } else {
                // Create missing payment record
                $paymentData = [
                    'user_id' => $show->coordinator_id,
                    'amount' => $listingFee,
                    'payment_method_id' => $defaultPaymentMethod->id,
                    'payment_status' => 'pending',
                    'payment_reference' => 'ADMIN-FIX-' . $show->id . '-' . time(),
                    'payment_type' => 'show_listing',
                    'related_id' => $show->id,
                    'notes' => 'Payment record created by admin for show: ' . $show->name,
                    'created_at' => $show->created_at
                ];
                
                if ($this->paymentModel->createPayment($paymentData)) {
                    $fixedShows[] = $show;
                }
            }
        }
        
        $data = [
            'title' => 'Fix Pending Show Payments',
            'pending_shows' => $pendingShows,
            'fixed_shows' => $fixedShows,
            'existing_payments' => $existingPayments,
            'listing_fee' => $listingFee
        ];
        
        $this->view('admin/fix_pending_payments', $data);
    }
}