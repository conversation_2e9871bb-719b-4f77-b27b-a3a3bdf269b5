<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Club Member Management</h1>
            <p class="text-muted mb-0"><?php echo $data['club']->name; ?> - Optimized for thousands of members</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo URLROOT; ?>/calendar/manageClubs" class="btn btn-info me-2 d-none d-sm-inline">
                <i class="fas fa-users me-2"></i> Clubs
            </a>
            <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addMemberModal">
                <i class="fas fa-user-plus me-2 d-none d-sm-inline"></i> Add Member
            </button>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2 d-none d-sm-inline"></i> Back
            </a>
        </div>
    </div>

    <?php flash('calendar_message'); ?>

    <!-- Member Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Member Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm member-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">All Members</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['member_counts']['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Members</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm member-overview-card" 
                                 data-filter="active" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Active</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['member_counts']['active'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Active Members</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm member-overview-card" 
                                 data-filter="admin" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Admins</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['member_counts']['admin'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Admin Members</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-secondary shadow-sm member-overview-card" 
                                 data-filter="pending" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-secondary mb-2">Pending</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['member_counts']['pending'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Pending Approval</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Member Details Section (Lazy Loaded) -->
    <div class="row mb-4 member-section" id="member-section" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning bg-opacity-25">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <span class="badge bg-warning text-dark me-2">Member Details</span>
                            <span class="badge bg-secondary" id="member-count-display">0</span>
                        </h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="closeMemberSection()">
                                <i class="fas fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filter Controls -->
                <div class="card-body border-bottom">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="search-members" class="form-label">Search Members</label>
                            <input type="text" class="form-control" id="search-members" 
                                   placeholder="Search by name or email...">
                        </div>
                        <div class="col-md-2">
                            <label for="role-filter" class="form-label">Role</label>
                            <select class="form-select" id="role-filter">
                                <option value="all">All Roles</option>
                                <option value="admin">Admin</option>
                                <option value="member">Member</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status-filter" class="form-label">Status</label>
                            <select class="form-select" id="status-filter">
                                <option value="all">All Status</option>
                                <option value="active">Active</option>
                                <option value="pending">Pending</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="per-page-members" class="form-label">Per Page</label>
                            <select class="form-select" id="per-page-members">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-warning" onclick="searchMembers()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearMemberSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div class="card-body text-center" id="loading-members">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading members...</p>
                </div>
                
                <!-- Members Content (Will be populated via AJAX) -->
                <div id="members-content" style="display: none;">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Member Modal -->
<div class="modal fade" id="addMemberModal" tabindex="-1" aria-labelledby="addMemberModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMemberModalLabel">Add Club Member</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="user-search" class="form-label">Search Users</label>
                    <input type="text" class="form-control" id="user-search" placeholder="Search by name or email...">
                </div>
                <div id="user-search-results" style="display: none;">
                    <!-- Search results will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
const clubId = <?php echo $data['club']->id; ?>;

document.addEventListener('DOMContentLoaded', function() {
    // Member overview card click handlers
    document.querySelectorAll('.member-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadMemberSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-members').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchMembers();
        }
    });

    // Filter change handlers
    document.getElementById('role-filter').addEventListener('change', searchMembers);
    document.getElementById('status-filter').addEventListener('change', searchMembers);
    document.getElementById('per-page-members').addEventListener('change', searchMembers);

    // User search for adding members
    document.getElementById('user-search').addEventListener('input', debounce(searchUsers, 300));
});

function loadMemberSection(filter = 'all') {
    // Show the member section
    const section = document.getElementById('member-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        if (filter === 'admin' || filter === 'member') {
            document.getElementById('role-filter').value = filter;
        } else if (filter === 'active' || filter === 'pending' || filter === 'inactive') {
            document.getElementById('status-filter').value = filter;
        }
    }

    // Load members
    loadMembers(1);
}

function closeMemberSection() {
    const section = document.getElementById('member-section');
    section.style.display = 'none';
}

function loadMembers(page = 1) {
    const loadingDiv = document.getElementById('loading-members');
    const contentDiv = document.getElementById('members-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-members').value;
    const roleFilter = document.getElementById('role-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    const perPage = document.getElementById('per-page-members').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        role_filter: roleFilter,
        status_filter: statusFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/calendar/loadClubMembers/${clubId}?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderMembers(data);
        } else {
            showMemberError(data.error || 'Failed to load members');
        }
    })
    .catch(error => {
        console.error('Error loading members:', error);
        showMemberError('Network error occurred');
    });
}

function searchMembers() {
    loadMembers(1);
}

function clearMemberSearch() {
    document.getElementById('search-members').value = '';
    document.getElementById('role-filter').value = 'all';
    document.getElementById('status-filter').value = 'all';
    loadMembers(1);
}

function renderMembers(data) {
    const loadingDiv = document.getElementById('loading-members');
    const contentDiv = document.getElementById('members-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render members table and pagination
    let html = '';

    if (data.members.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No members found.</p></div>';
    } else {
        html = renderMembersTable(data.members, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update member count display
    document.getElementById('member-count-display').textContent = data.pagination.total_members.toLocaleString();
}

function renderMembersTable(members, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Member</th><th>Email</th><th>Role</th><th>Status</th><th>Joined</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    members.forEach(member => {
        html += '<tr>';
        html += '<td><strong>' + member.name + '</strong></td>';
        html += '<td>' + (member.email || 'N/A') + '</td>';
        html += '<td>';
        if (member.role === 'admin') {
            html += '<span class="badge bg-info">Admin</span>';
        } else {
            html += '<span class="badge bg-secondary">Member</span>';
        }
        html += '</td>';
        html += '<td>';
        if (member.status === 'active') {
            html += '<span class="badge bg-success">Active</span>';
        } else if (member.status === 'pending') {
            html += '<span class="badge bg-warning text-dark">Pending</span>';
        } else {
            html += '<span class="badge bg-danger">Inactive</span>';
        }
        html += '</td>';
        html += '<td>' + (member.joined_date || 'N/A') + '</td>';
        html += '<td>' + getMemberActions(member.id, member.role) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderMemberPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_members.toLocaleString()} members`;
    html += '</div>';

    return html;
}

function getMemberActions(memberId, role) {
    return `
        <div class="btn-group btn-group-sm">
            <button class="btn btn-primary" onclick="editMemberRole(${memberId}, '${role}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-danger" onclick="removeMember(${memberId})">
                <i class="fas fa-user-minus"></i>
            </button>
        </div>
    `;
}

function renderMemberPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadMembers(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadMembers(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadMembers(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showMemberError(message) {
    const loadingDiv = document.getElementById('loading-members');
    const contentDiv = document.getElementById('members-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function searchUsers() {
    const query = document.getElementById('user-search').value;
    if (query.length < 2) {
        document.getElementById('user-search-results').style.display = 'none';
        return;
    }

    fetch(`<?php echo BASE_URL; ?>/calendar/searchUsersForClub/${clubId}?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderUserSearchResults(data.users);
        }
    })
    .catch(error => {
        console.error('Error searching users:', error);
    });
}

function renderUserSearchResults(users) {
    const resultsDiv = document.getElementById('user-search-results');

    if (users.length === 0) {
        resultsDiv.innerHTML = '<div class="alert alert-info">No users found.</div>';
    } else {
        let html = '<div class="list-group">';
        users.forEach(user => {
            html += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${user.name}</strong><br>
                        <small class="text-muted">${user.email}</small>
                    </div>
                    <button class="btn btn-sm btn-primary" onclick="addUserToClub(${user.id}, '${user.name}')">
                        Add
                    </button>
                </div>
            `;
        });
        html += '</div>';
        resultsDiv.innerHTML = html;
    }

    resultsDiv.style.display = 'block';
}

function addUserToClub(userId, userName) {
    if (confirm(`Add ${userName} to the club?`)) {
        fetch(`<?php echo BASE_URL; ?>/calendar/addClubMember/${clubId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ user_id: userId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Member added successfully!');
                document.getElementById('addMemberModal').querySelector('.btn-close').click();
                location.reload(); // Refresh to update counts
            } else {
                alert(data.error || 'Failed to add member');
            }
        })
        .catch(error => {
            console.error('Error adding member:', error);
            alert('Network error occurred');
        });
    }
}

function editMemberRole(memberId, currentRole) {
    const newRole = currentRole === 'admin' ? 'member' : 'admin';
    if (confirm(`Change member role to ${newRole}?`)) {
        // Implementation for role change
        alert('Role change functionality would be implemented here');
    }
}

function removeMember(memberId) {
    if (confirm('Remove this member from the club?')) {
        // Implementation for member removal
        alert('Member removal functionality would be implemented here');
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
