<?php
/**
 * Calendar Model
 * 
 * This model handles all database operations related to the calendar system.
 * 
 * Version 1.0.2 - Fixed last day of month event filtering
 * - Fixed date filtering to properly include events on the last day of the month
 * - Modified SQL queries to use DATE() function for accurate date comparisons
 * - Added debug logging for date filter operations
 * 
 * Version 1.0.1 - Fixed transaction method names
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 */
class CalendarModel {
    private $db;
    private $showModel;
    private $eventTrigger;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Initialize ShowModel
        require_once APPROOT . '/models/ShowModel.php';
        $this->showModel = new ShowModel();
        
        // Initialize EventTrigger if it exists
        if (file_exists(APPROOT . '/core/EventTrigger.php')) {
            require_once APPROOT . '/core/EventTrigger.php';
            $this->eventTrigger = new EventTrigger();
        }
        
        // Ensure the calendars table exists
        $this->ensureCalendarsTableExists();
    }
    
    /**
     * Check if the calendars table exists and create it if it doesn't
     */
    private function ensureCalendarsTableExists() {
        try {
            // Check if the calendars table exists
            $this->db->query("SHOW TABLES LIKE 'calendars'");
            $this->db->execute();
            
            if ($this->db->rowCount() === 0) {
                error_log("CalendarModel::ensureCalendarsTableExists - Calendars table does not exist, creating it");
                
                // Create the calendars table
                $sql = "CREATE TABLE IF NOT EXISTS `calendars` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `description` text,
                    `color` varchar(20) DEFAULT '#3788d8',
                    `is_visible` tinyint(1) NOT NULL DEFAULT '1',
                    `is_public` tinyint(1) NOT NULL DEFAULT '1',
                    `owner_id` int(11) DEFAULT NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `owner_id` (`owner_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
                
                $this->db->query($sql);
                if ($this->db->execute()) {
                    error_log("CalendarModel::ensureCalendarsTableExists - Successfully created calendars table");
                } else {
                    error_log("CalendarModel::ensureCalendarsTableExists - Failed to create calendars table");
                }
            } else {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CalendarModel::ensureCalendarsTableExists - Calendars table already exists");
                }
            }
            
            // Check if the calendar_events table exists
            $this->db->query("SHOW TABLES LIKE 'calendar_events'");
            $this->db->execute();
            
            if ($this->db->rowCount() === 0) {
                error_log("CalendarModel::ensureCalendarsTableExists - Calendar_events table does not exist, creating it");
                
                // Create the calendar_events table
                $sql = "CREATE TABLE IF NOT EXISTS `calendar_events` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `calendar_id` int(11) NOT NULL,
                    `title` varchar(255) NOT NULL,
                    `description` text,
                    `start_date` datetime NOT NULL,
                    `end_date` datetime NOT NULL,
                    `all_day` tinyint(1) NOT NULL DEFAULT '0',
                    `location` varchar(255) DEFAULT NULL,
                    `address1` varchar(255) DEFAULT NULL,
                    `address2` varchar(255) DEFAULT NULL,
                    `city` varchar(100) DEFAULT NULL,
                    `state` varchar(50) DEFAULT NULL,
                    `zipcode` varchar(20) DEFAULT NULL,
                    `venue_id` int(11) DEFAULT NULL,
                    `url` varchar(255) DEFAULT NULL,
                    `color` varchar(20) DEFAULT NULL,
                    `is_recurring` tinyint(1) NOT NULL DEFAULT '0',
                    `recurrence_pattern` varchar(50) DEFAULT NULL,
                    `recurrence_end_date` date DEFAULT NULL,
                    `privacy` enum('public','private','club') NOT NULL DEFAULT 'public',
                    `show_id` int(11) DEFAULT NULL,
                    `created_by` int(11) DEFAULT NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `calendar_id` (`calendar_id`),
                    KEY `venue_id` (`venue_id`),
                    KEY `show_id` (`show_id`),
                    KEY `created_by` (`created_by`),
                    CONSTRAINT `calendar_events_ibfk_1` FOREIGN KEY (`calendar_id`) REFERENCES `calendars` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
                
                $this->db->query($sql);
                if ($this->db->execute()) {
                    error_log("CalendarModel::ensureCalendarsTableExists - Successfully created calendar_events table");
                } else {
                    error_log("CalendarModel::ensureCalendarsTableExists - Failed to create calendar_events table");
                }
            } else {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists");
                }
            }
        } catch (Exception $e) {
            error_log("CalendarModel::ensureCalendarsTableExists - Error: " . $e->getMessage());
        }
    }
    
    /**
     * Check if calendar tables exist
     * 
     * @return bool True if tables exist, false otherwise
     */
    public function tablesExist() {
        $tables = [
            'calendars',
            'calendar_events',
            'calendar_venues',
            'calendar_clubs',
            'calendar_club_members',
            'calendar_event_clubs',
            'calendar_permissions',
            'calendar_notifications',
            'calendar_imports',
            'calendar_settings'
        ];
        
        foreach ($tables as $table) {
            $this->db->query("SHOW TABLES LIKE '{$table}'");
            $this->db->execute();
            if ($this->db->rowCount() == 0) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Create calendar tables
     * 
     * @return bool True if successful, false otherwise
     */
    public function createTables() {
        try {
            $sqlFile = APPROOT . '/database/calendar.sql';
            if (!file_exists($sqlFile)) {
                error_log('Calendar SQL file not found: ' . $sqlFile);
                return false;
            }
            
            $sql = file_get_contents($sqlFile);
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $this->db->query($statement);
                    $this->db->execute();
                }
            }
            
            return true;
        } catch (Exception $e) {
            error_log('Error creating calendar tables: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get calendar counts for overview dashboard
     *
     * @param int $userId User ID to filter by
     * @return array Array with calendar counts by category
     */
    public function getCalendarCounts($userId = null) {
        try {
            $counts = [
                'total' => 0,
                'public' => 0,
                'private' => 0,
                'visible' => 0,
                'hidden' => 0
            ];

            // Base WHERE clause for user access
            $whereClause = '';
            $bindParams = [];

            if ($userId !== null) {
                $whereClause = 'WHERE (owner_id = :user_id OR is_public = 1)';
                $bindParams['user_id'] = $userId;
            } else {
                $whereClause = 'WHERE is_public = 1';
            }

            // Get total count
            $this->db->query("SELECT COUNT(*) as total FROM calendars $whereClause");
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $result = $this->db->single();
            $counts['total'] = $result ? $result->total : 0;

            // Get public count
            $publicWhere = $whereClause . ' AND is_public = 1';
            $this->db->query("SELECT COUNT(*) as public FROM calendars $publicWhere");
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $result = $this->db->single();
            $counts['public'] = $result ? $result->public : 0;

            // Get private count
            $privateWhere = $whereClause . ' AND is_public = 0';
            $this->db->query("SELECT COUNT(*) as private FROM calendars $privateWhere");
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $result = $this->db->single();
            $counts['private'] = $result ? $result->private : 0;

            // Get visible count
            $visibleWhere = $whereClause . ' AND is_visible = 1';
            $this->db->query("SELECT COUNT(*) as visible FROM calendars $visibleWhere");
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $result = $this->db->single();
            $counts['visible'] = $result ? $result->visible : 0;

            // Get hidden count
            $hiddenWhere = $whereClause . ' AND is_visible = 0';
            $this->db->query("SELECT COUNT(*) as hidden FROM calendars $hiddenWhere");
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $result = $this->db->single();
            $counts['hidden'] = $result ? $result->hidden : 0;

            return $counts;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCalendarCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'public' => 0,
                'private' => 0,
                'visible' => 0,
                'hidden' => 0
            ];
        }
    }

    /**
     * Get all calendars
     *
     * @param int $userId Optional user ID to filter by owner
     * @return array Calendars
     */
    public function getCalendars($userId = null) {
        try {
            $sql = 'SELECT c.*, u.name as owner_name 
                    FROM calendars c 
                    LEFT JOIN users u ON c.owner_id = u.id';
            
            if ($userId !== null) {
                $sql .= ' WHERE c.owner_id = :user_id OR c.is_public = 1';
                $this->db->query($sql);
                $this->db->bind(':user_id', $userId);
            } else {
                $this->db->query($sql);
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCalendars: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get paginated calendars with search and filtering
     *
     * @param int $userId User ID to filter by (null for all public calendars)
     * @param int $page Page number (1-based)
     * @param int $perPage Number of calendars per page
     * @param string $search Search term for name/description
     * @param string $visibilityFilter Filter by visibility (all, visible, hidden)
     * @param string $publicFilter Filter by public status (all, public, private)
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC, DESC)
     * @return array Array with calendars, pagination info
     */
    public function getPaginatedCalendars($userId = null, $page = 1, $perPage = 20, $search = '', $visibilityFilter = 'all', $publicFilter = 'all', $orderBy = 'name', $orderDir = 'ASC') {
        $startTime = microtime(true);

        // Validate inputs
        $page = max(1, (int)$page);
        $perPage = max(1, min(100, (int)$perPage));
        $offset = ($page - 1) * $perPage;

        // Validate order by field
        $allowedOrderFields = ['name', 'created_at', 'updated_at', 'owner_name'];
        if (!in_array($orderBy, $allowedOrderFields)) {
            $orderBy = 'name';
        }

        // Validate order direction
        $orderDir = strtoupper($orderDir) === 'DESC' ? 'DESC' : 'ASC';

        // Build WHERE conditions
        $whereConditions = [];
        $bindParams = [];

        // User access filter
        if ($userId !== null) {
            $whereConditions[] = '(c.owner_id = :user_id OR c.is_public = 1)';
            $bindParams['user_id'] = $userId;
        } else {
            // If no user specified, only show public calendars
            $whereConditions[] = 'c.is_public = 1';
        }

        if (!empty($search)) {
            $whereConditions[] = '(c.name LIKE :search_name OR c.description LIKE :search_desc)';
            $bindParams['search_name'] = '%' . $search . '%';
            $bindParams['search_desc'] = '%' . $search . '%';
        }

        if ($visibilityFilter !== 'all') {
            if ($visibilityFilter === 'visible') {
                $whereConditions[] = 'c.is_visible = 1';
            } elseif ($visibilityFilter === 'hidden') {
                $whereConditions[] = 'c.is_visible = 0';
            }
        }

        if ($publicFilter !== 'all') {
            if ($publicFilter === 'public') {
                $whereConditions[] = 'c.is_public = 1';
            } elseif ($publicFilter === 'private') {
                $whereConditions[] = 'c.is_public = 0';
            }
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total
                     FROM calendars c
                     LEFT JOIN users u ON c.owner_id = u.id
                     $whereClause";
        $this->db->query($countSql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $countResult = $this->db->single();
        $totalCalendars = $countResult ? $countResult->total : 0;
        $totalPages = ceil($totalCalendars / $perPage);

        // Get calendars for current page with event counts
        $orderField = $orderBy === 'owner_name' ? 'u.name' : 'c.' . $orderBy;
        $sql = "SELECT c.*, u.name as owner_name,
                       (SELECT COUNT(*) FROM calendar_events WHERE calendar_id = c.id) as event_count
                FROM calendars c
                LEFT JOIN users u ON c.owner_id = u.id
                $whereClause
                ORDER BY $orderField $orderDir
                LIMIT :limit OFFSET :offset";

        $this->db->query($sql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
        $this->db->bind(':offset', $offset, PDO::PARAM_INT);

        $calendars = $this->db->resultSet();

        // Performance monitoring
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);

        error_log("CalendarModel::getPaginatedCalendars - Page: {$page}, Calendars: {$totalCalendars}, Time: {$executionTime}ms");

        if ($executionTime > 100) {
            error_log("WARNING: Slow calendars query detected! Time: {$executionTime}ms");
        }

        return [
            'calendars' => $calendars,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_calendars' => $totalCalendars,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'start_record' => $totalCalendars > 0 ? $offset + 1 : 0,
                'end_record' => min($offset + $perPage, $totalCalendars)
            ],
            'filters' => [
                'search' => $search,
                'visibility_filter' => $visibilityFilter,
                'public_filter' => $publicFilter,
                'order_by' => $orderBy,
                'order_dir' => $orderDir
            ],
            'performance' => [
                'execution_time_ms' => $executionTime
            ]
        ];
    }

    /**
     * Get public calendars (for guest users)
     *
     * @return array Array of public calendar objects
     */
    public function getPublicCalendars() {
        try {
            $sql = 'SELECT c.*, u.name as owner_name 
                    FROM calendars c 
                    LEFT JOIN users u ON c.owner_id = u.id
                    WHERE c.is_public = 1 AND c.is_visible = 1
                    ORDER BY c.name';
            
            $this->db->query($sql);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getPublicCalendars: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get calendar by ID
     * 
     * @param int $id Calendar ID
     * @return object|bool Calendar object or false if not found
     */
    public function getCalendarById($id) {
        try {
            $this->db->query('SELECT c.*, u.name as owner_name 
                             FROM calendars c 
                             LEFT JOIN users u ON c.owner_id = u.id 
                             WHERE c.id = :id');
            $this->db->bind(':id', $id);
            
            $calendar = $this->db->single();
            if ($calendar) {
                return $calendar;
            }
            
            return false;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCalendarById: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new calendar
     * 
     * @param array $data Calendar data
     * @return int|bool New calendar ID or false if failed
     */
    public function createCalendar($data) {
        try {
            error_log("CalendarModel::createCalendar - Creating calendar with name: {$data['name']}");
            
            $this->db->query('INSERT INTO calendars (name, description, color, is_visible, is_public, owner_id) 
                             VALUES (:name, :description, :color, :is_visible, :is_public, :owner_id)');
            
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':color', $data['color'] ?? '#3788d8');
            $this->db->bind(':is_visible', $data['is_visible'] ?? 1);
            $this->db->bind(':is_public', $data['is_public'] ?? 1);
            $this->db->bind(':owner_id', $data['owner_id'] ?? null);
            
            if ($this->db->execute()) {
                $calendarId = $this->db->lastInsertId();
                error_log("CalendarModel::createCalendar - Successfully created calendar with ID: {$calendarId}");
                return $calendarId;
            }
            
            error_log("CalendarModel::createCalendar - Failed to create calendar");
            return false;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::createCalendar: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a calendar
     * 
     * @param array $data Calendar data
     * @return bool True if successful, false otherwise
     */
    public function updateCalendar($data) {
        try {
            $this->db->query('UPDATE calendars 
                             SET name = :name, 
                                 description = :description, 
                                 color = :color, 
                                 is_visible = :is_visible, 
                                 is_public = :is_public 
                             WHERE id = :id');
            
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':color', $data['color'] ?? '#3788d8');
            $this->db->bind(':is_visible', $data['is_visible'] ?? 1);
            $this->db->bind(':is_public', $data['is_public'] ?? 1);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateCalendar: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a calendar
     * 
     * @param int $id Calendar ID
     * @return bool True if successful, false otherwise
     */
    public function deleteCalendar($id) {
        try {
            $this->db->query('DELETE FROM calendars WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::deleteCalendar: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all events
     * 
     * @param array $filters Optional filters (calendar_id, start_date, end_date, privacy)
     * @param int $userId Optional user ID for permission checking
     * @return array Events
     */
    public function getEvents($filters = [], $userId = null) {
        try {
            // Check if we need to join with category or tag tables
            $needCategoryJoin = !empty($filters['category']) || !empty($filters['categories']);
            $needTagJoin = !empty($filters['tag']) || !empty($filters['tags']);
            
            // Base query
            $sql = 'SELECT DISTINCT e.*, c.name as calendar_name, c.color as calendar_color, 
                           v.name as venue_name, v.address as venue_address, 
                           v.city as venue_city, v.state as venue_state, 
                           v.latitude as venue_latitude, v.longitude as venue_longitude,
                           s.name as show_name
                    FROM calendar_events e 
                    LEFT JOIN calendars c ON e.calendar_id = c.id 
                    LEFT JOIN calendar_venues v ON e.venue_id = v.id
                    LEFT JOIN shows s ON e.show_id = s.id';
            
            // Add category join if needed
            if ($needCategoryJoin) {
                // First check if the table exists
                $this->db->query("SHOW TABLES LIKE 'event_category_mapping'");
                $this->db->execute();
                
                if ($this->db->rowCount() > 0) {
                    $sql .= ' LEFT JOIN event_category_mapping ecm ON e.id = ecm.event_id
                              LEFT JOIN event_categories ec ON ecm.category_id = ec.id';
                } else {
                    // If table doesn't exist, we'll ignore category filtering
                    $needCategoryJoin = false;
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('Warning: event_category_mapping table does not exist, ignoring category filter');
                    }
                }
            }
            
            // Add tag join if needed
            if ($needTagJoin) {
                // First check if the table exists
                $this->db->query("SHOW TABLES LIKE 'event_tag_mapping'");
                $this->db->execute();
                
                if ($this->db->rowCount() > 0) {
                    $sql .= ' LEFT JOIN event_tag_mapping etm ON e.id = etm.event_id
                              LEFT JOIN event_tags et ON etm.tag_id = et.id';
                } else {
                    // If table doesn't exist, we'll ignore tag filtering
                    $needTagJoin = false;
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('Warning: event_tag_mapping table does not exist, ignoring tag filter');
                    }
                }
            }
            
            $sql .= ' WHERE 1=1';
            
            $params = [];
            
            // Location-based filtering
            if (!empty($filters['center_lat']) && !empty($filters['center_lng']) && !empty($filters['radius'])) {
                // Use Haversine formula to calculate distance
                // Check both event coordinates (e.lat, e.lng) AND venue coordinates (v.latitude, v.longitude)
                $sql .= ' AND (
                    (e.lat IS NOT NULL AND e.lng IS NOT NULL AND
                    (3959 * acos(cos(radians(:center_lat1)) * cos(radians(e.lat)) * 
                    cos(radians(e.lng) - radians(:center_lng1)) + 
                    sin(radians(:center_lat2)) * sin(radians(e.lat)))) <= :radius1)
                    OR
                    (v.latitude IS NOT NULL AND v.longitude IS NOT NULL AND
                    (3959 * acos(cos(radians(:center_lat3)) * cos(radians(v.latitude)) * 
                    cos(radians(v.longitude) - radians(:center_lng2)) + 
                    sin(radians(:center_lat4)) * sin(radians(v.latitude)))) <= :radius2)
                )';
                
                $params[':center_lat1'] = $filters['center_lat'];
                $params[':center_lng1'] = $filters['center_lng'];
                $params[':center_lat2'] = $filters['center_lat'];
                $params[':center_lat3'] = $filters['center_lat'];
                $params[':center_lng2'] = $filters['center_lng'];
                $params[':center_lat4'] = $filters['center_lat'];
                $params[':radius1'] = $filters['radius'];
                $params[':radius2'] = $filters['radius'];
            }
            
            // Filter by state (case-insensitive)
            if (!empty($filters['state'])) {
                $sql .= ' AND (LOWER(e.state) = LOWER(:state_e) OR LOWER(v.state) = LOWER(:state_v))';
                $params[':state_e'] = $filters['state'];
                $params[':state_v'] = $filters['state'];
            }
            
            // Filter by city (case-insensitive)
            if (!empty($filters['city'])) {
                $sql .= ' AND (LOWER(e.city) = LOWER(:city_e) OR LOWER(v.city) = LOWER(:city_v))';
                $params[':city_e'] = $filters['city'];
                $params[':city_v'] = $filters['city'];
            }
            
            // Filter by venue ID
            if (!empty($filters['venue_id'])) {
                $sql .= ' AND e.venue_id = :venue_id';
                $params[':venue_id'] = $filters['venue_id'];
            }
            
            // Filter by multiple venue IDs
            if (!empty($filters['venue_ids']) && is_array($filters['venue_ids'])) {
                $placeholders = [];
                foreach ($filters['venue_ids'] as $index => $id) {
                    $paramName = ":venue_id_$index";
                    $placeholders[] = $paramName;
                    $params[$paramName] = $id;
                }
                
                if (!empty($placeholders)) {
                    $sql .= " AND e.venue_id IN (" . implode(', ', $placeholders) . ")";
                }
            }
            
            // Filter by club ID
            if (!empty($filters['club_id'])) {
                $sql .= ' AND e.club_id = :club_id';
                $params[':club_id'] = $filters['club_id'];
            }
            
            // Filter by multiple club IDs
            if (!empty($filters['club_ids']) && is_array($filters['club_ids'])) {
                $placeholders = [];
                foreach ($filters['club_ids'] as $index => $id) {
                    $paramName = ":club_id_$index";
                    $placeholders[] = $paramName;
                    $params[$paramName] = $id;
                }
                
                if (!empty($placeholders)) {
                    $sql .= " AND e.club_id IN (" . implode(', ', $placeholders) . ")";
                }
            }
            
            // Filter by keyword (search in title and description)
            if (!empty($filters['keyword'])) {
                $sql .= ' AND (e.title LIKE :keyword_title OR e.description LIKE :keyword_desc)';
                $keywordParam = '%' . $filters['keyword'] . '%';
                $params[':keyword_title'] = $keywordParam;
                $params[':keyword_desc'] = $keywordParam;
                
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('CalendarModel::getEvents - Keyword filter applied: ' . $filters['keyword']);
                    error_log('CalendarModel::getEvents - Keyword parameter: ' . $keywordParam);
                }
            }
            
            // Apply filters
            if (!empty($filters['calendar_id'])) {
                $sql .= ' AND e.calendar_id = :calendar_id';
                $params[':calendar_id'] = $filters['calendar_id'];
            }
            
            // Support for multiple calendar IDs
            if (!empty($filters['calendar_ids']) && is_array($filters['calendar_ids'])) {
                // Use FIND_IN_SET for simpler parameter binding
                $calendarIds = array_map('intval', $filters['calendar_ids']); // Ensure integers for security
                $sql .= " AND FIND_IN_SET(e.calendar_id, :calendar_ids)";
                $params[':calendar_ids'] = implode(',', $calendarIds);
            }
            
            if (!empty($filters['start_date'])) {
                $sql .= ' AND e.end_date >= :start_date';
                $params[':start_date'] = $filters['start_date'];
            }
            
            if (!empty($filters['end_date'])) {
                // Standard date comparison - events that start on or before the end date
                $sql .= ' AND e.start_date <= :end_date';
                $params[':end_date'] = $filters['end_date'];
                
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('CalendarModel::getEvents - End date filter applied: ' . $filters['end_date']);
                }
            }
            
            // Filter by category
            if ($needCategoryJoin) {
                if (!empty($filters['category'])) {
                    $sql .= ' AND ec.id = :category_id';
                    $params[':category_id'] = $filters['category'];
                } else if (!empty($filters['categories']) && is_array($filters['categories'])) {
                    $placeholders = [];
                    foreach ($filters['categories'] as $index => $id) {
                        $paramName = ":category_id_$index";
                        $placeholders[] = $paramName;
                        $params[$paramName] = $id;
                    }
                    
                    if (!empty($placeholders)) {
                        $sql .= " AND ec.id IN (" . implode(', ', $placeholders) . ")";
                    }
                }
            }
            
            // Filter by tag
            if ($needTagJoin) {
                if (!empty($filters['tag'])) {
                    $sql .= ' AND et.id = :tag_id';
                    $params[':tag_id'] = $filters['tag'];
                } else if (!empty($filters['tags']) && is_array($filters['tags'])) {
                    $placeholders = [];
                    foreach ($filters['tags'] as $index => $id) {
                        $paramName = ":tag_id_$index";
                        $placeholders[] = $paramName;
                        $params[$paramName] = $id;
                    }
                    
                    if (!empty($placeholders)) {
                        $sql .= " AND et.id IN (" . implode(', ', $placeholders) . ")";
                    }
                }
            }
            
            // Filter by show ID
            if (!empty($filters['show_id'])) {
                $sql .= ' AND e.show_id = :show_id';
                $params[':show_id'] = $filters['show_id'];
            }
            
            // Filter by price range
            if (!empty($filters['price_min']) || !empty($filters['price_max'])) {
                if (!empty($filters['price_min'])) {
                    $sql .= ' AND e.price >= :price_min';
                    $params[':price_min'] = $filters['price_min'];
                }
                if (!empty($filters['price_max'])) {
                    $sql .= ' AND e.price <= :price_max';
                    $params[':price_max'] = $filters['price_max'];
                }
            }
            
            // Privacy filter
            if ($userId === null) {
                // Public events only for non-logged in users (exclude draft events)
                $sql .= " AND e.privacy = 'public'";
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('CalendarModel::getEvents - Privacy filter: public only (no user)');
                }
            } else {
                // Public events for logged in users, plus draft events if user is the creator
                $sql .= " AND (e.privacy = 'public' OR (e.privacy = 'draft' AND e.created_by = :user_id))";
                $params[':user_id'] = $userId;
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('CalendarModel::getEvents - Privacy filter: public + draft for user ' . $userId);
                }
            }
            
            // Order by start date or custom order
            if (!empty($filters['order_by'])) {
                $sql .= " ORDER BY " . $filters['order_by'];
            } else {
                $sql .= " ORDER BY e.start_date ASC";
            }
            
            // Limit results
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = $filters['limit'];
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('CalendarModel::getEvents - SQL Query: ' . $sql);
                error_log('CalendarModel::getEvents - Params: ' . json_encode($params));
                error_log('CalendarModel::getEvents - Filters received: ' . json_encode($filters));
                error_log('CalendarModel::getEvents - Parameter count: ' . count($params));
            }
            
            $this->db->query($sql);
            
            // Bind parameters
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            $results = $this->db->resultSet();
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('CalendarModel::getEvents - Found ' . count($results) . ' events');
                if (!empty($results)) {
                    error_log('CalendarModel::getEvents - First event title: ' . $results[0]->title);
                    error_log('CalendarModel::getEvents - First event description: ' . ($results[0]->description ?? 'null'));
                }
            }
            
            return $results;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEvents: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update event dates
     * 
     * @param array $data Event data
     * @return bool True on success, false on failure
     */
    public function updateEventDates($data) {
        try {
            $this->db->query("UPDATE calendar_events SET start_date = :start_date, end_date = :end_date WHERE id = :id");
            
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':start_date', $data['start_date']);
            $this->db->bind(':end_date', $data['end_date']);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateEventDates: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get event by ID
     * 
     * @param int $id Event ID
     * @return object|bool Event object or false if not found
     */
    public function getEventById($id) {
        try {
            $this->db->query('SELECT e.*, c.name as calendar_name, c.color as calendar_color, 
                                    v.name as venue_name, v.address as venue_address, 
                                    v.city as venue_city, v.state as venue_state, 
                                    v.latitude as venue_latitude, v.longitude as venue_longitude,
                                    s.name as show_name
                             FROM calendar_events e 
                             LEFT JOIN calendars c ON e.calendar_id = c.id 
                             LEFT JOIN calendar_venues v ON e.venue_id = v.id
                             LEFT JOIN shows s ON e.show_id = s.id
                             WHERE e.id = :id');
            $this->db->bind(':id', $id);
            
            $event = $this->db->single();
            if ($event) {
                // Get associated clubs
                $event->clubs = $this->getEventClubs($id);
                return $event;
            }
            
            return false;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventById: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new event
     * 
     * @param array $data Event data
     * @return int|bool New event ID or false if failed
     */
    public function createEvent($data) {
        try {
            $this->db->beginTransaction();
            
            $this->db->query('INSERT INTO calendar_events (
                                calendar_id, title, description, start_date, end_date, 
                                all_day, location, address1, address2, city, state, zipcode,
                                lat, lng, venue_id, url, color, is_recurring, 
                                recurrence_pattern, recurrence_end_date, privacy, 
                                show_id, created_by
                             ) VALUES (
                                :calendar_id, :title, :description, :start_date, :end_date, 
                                :all_day, :location, :address1, :address2, :city, :state, :zipcode,
                                :lat, :lng, :venue_id, :url, :color, :is_recurring, 
                                :recurrence_pattern, :recurrence_end_date, :privacy, 
                                :show_id, :created_by
                             )');
            
            $this->db->bind(':calendar_id', $data['calendar_id']);
            $this->db->bind(':title', $data['title']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':start_date', $data['start_date']);
            $this->db->bind(':end_date', $data['end_date']);
            $this->db->bind(':all_day', $data['all_day'] ?? 0);
            $this->db->bind(':location', $data['location'] ?? null); // Keep for backward compatibility
            $this->db->bind(':address1', $data['address1'] ?? null);
            $this->db->bind(':address2', $data['address2'] ?? null);
            $this->db->bind(':city', $data['city'] ?? null);
            $this->db->bind(':state', $data['state'] ?? null);
            $this->db->bind(':zipcode', $data['zipcode'] ?? null);
            $this->db->bind(':lat', $data['lat'] ?? null);
            $this->db->bind(':lng', $data['lng'] ?? null);
            $this->db->bind(':venue_id', $data['venue_id'] ?? null);
            $this->db->bind(':url', $data['url'] ?? null);
            $this->db->bind(':color', $data['color'] ?? null);
            $this->db->bind(':is_recurring', $data['is_recurring'] ?? 0);
            $this->db->bind(':recurrence_pattern', $data['recurrence_pattern'] ?? null);
            $this->db->bind(':recurrence_end_date', $data['recurrence_end_date'] ?? null);
            $this->db->bind(':privacy', $data['privacy'] ?? 'public');
            $this->db->bind(':show_id', $data['show_id'] ?? null);
            $this->db->bind(':created_by', $data['created_by'] ?? null);
            
            if ($this->db->execute()) {
                $eventId = $this->db->lastInsertId();
                
                // Add club associations if provided
                if (!empty($data['clubs']) && is_array($data['clubs'])) {
                    foreach ($data['clubs'] as $clubId) {
                        $this->addEventClub($eventId, $clubId);
                    }
                }
                
                // Add notifications if provided
                if (!empty($data['notifications']) && is_array($data['notifications'])) {
                    foreach ($data['notifications'] as $notification) {
                        $this->addNotification($eventId, $notification['user_id'], $notification['notification_time'], $notification['notification_type'] ?? 'email');
                    }
                }
                
                $this->db->commit();
                return $eventId;
            }
            
            $this->db->rollBack();
            return false;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error in CalendarModel::createEvent: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update an event
     * 
     * @param array $data Event data
     * @return bool True if successful, false otherwise
     */
    public function updateEvent($data) {
        try {
            $this->db->beginTransaction();
            
            $this->db->query('UPDATE calendar_events 
                             SET calendar_id = :calendar_id, 
                                 title = :title, 
                                 description = :description, 
                                 start_date = :start_date, 
                                 end_date = :end_date, 
                                 all_day = :all_day, 
                                 location = :location, 
                                 address1 = :address1,
                                 address2 = :address2,
                                 city = :city,
                                 state = :state,
                                 zipcode = :zipcode,
                                 lat = :lat,
                                 lng = :lng,
                                 venue_id = :venue_id, 
                                 url = :url, 
                                 color = :color, 
                                 is_recurring = :is_recurring, 
                                 recurrence_pattern = :recurrence_pattern, 
                                 recurrence_end_date = :recurrence_end_date, 
                                 privacy = :privacy, 
                                 show_id = :show_id
                             WHERE id = :id');
            
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':calendar_id', $data['calendar_id']);
            $this->db->bind(':title', $data['title']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':start_date', $data['start_date']);
            $this->db->bind(':end_date', $data['end_date']);
            $this->db->bind(':all_day', $data['all_day'] ?? 0);
            $this->db->bind(':location', $data['location'] ?? null); // Keep for backward compatibility
            $this->db->bind(':address1', $data['address1'] ?? null);
            $this->db->bind(':address2', $data['address2'] ?? null);
            $this->db->bind(':city', $data['city'] ?? null);
            $this->db->bind(':state', $data['state'] ?? null);
            $this->db->bind(':zipcode', $data['zipcode'] ?? null);
            $this->db->bind(':lat', $data['lat'] ?? null);
            $this->db->bind(':lng', $data['lng'] ?? null);
            $this->db->bind(':venue_id', $data['venue_id'] ?? null);
            $this->db->bind(':url', $data['url'] ?? null);
            $this->db->bind(':color', $data['color'] ?? null);
            $this->db->bind(':is_recurring', $data['is_recurring'] ?? 0);
            $this->db->bind(':recurrence_pattern', $data['recurrence_pattern'] ?? null);
            $this->db->bind(':recurrence_end_date', $data['recurrence_end_date'] ?? null);
            $this->db->bind(':privacy', $data['privacy'] ?? 'public');
            $this->db->bind(':show_id', $data['show_id'] ?? null);
            
            if ($this->db->execute()) {
                // Update club associations if provided
                if (isset($data['clubs']) && is_array($data['clubs'])) {
                    // Remove existing associations
                    $this->db->query('DELETE FROM calendar_event_clubs WHERE event_id = :event_id');
                    $this->db->bind(':event_id', $data['id']);
                    $this->db->execute();
                    
                    // Add new associations
                    foreach ($data['clubs'] as $clubId) {
                        $this->addEventClub($data['id'], $clubId);
                    }
                }
                
                $this->db->commit();
                return true;
            }
            
            $this->db->rollBack();
            return false;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error in CalendarModel::updateEvent: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete an event
     * 
     * @param int $id Event ID
     * @return bool True if successful, false otherwise
     */
    public function deleteEvent($id) {
        try {
            $this->db->query('DELETE FROM calendar_events WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::deleteEvent: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create an event from a show
     * 
     * @param int $showId Show ID
     * @param int $calendarId Calendar ID
     * @param int $createdBy User ID who created the event
     * @return int|bool New event ID or false if failed
     */
    /**
     * Get or create a state-specific calendar
     * 
     * @param string $state State name or abbreviation
     * @param int $ownerId User ID of the calendar owner (usually admin)
     * @return int|bool Calendar ID or false if failed
     */
    public function getOrCreateStateCalendar($state, $ownerId = null) {
        try {
            if (empty($state)) {
                error_log('CalendarModel::getOrCreateStateCalendar - State parameter is empty');
                return false;
            }
            
            error_log("CalendarModel::getOrCreateStateCalendar - Checking for calendar for state: {$state}");
            
            // Check if a calendar for this state already exists
            $this->db->query("SELECT id FROM calendars WHERE name = :name");
            $this->db->bind(':name', "Shows - {$state}");
            $existingCalendar = $this->db->single();
            
            if ($existingCalendar) {
                error_log("CalendarModel::getOrCreateStateCalendar - Found existing calendar for state {$state} with ID: {$existingCalendar->id}");
                return $existingCalendar->id;
            }
            
            error_log("CalendarModel::getOrCreateStateCalendar - No existing calendar found for state {$state}, creating new one");
            
            // Create a new calendar for this state
            $calendarData = [
                'name' => "Shows - {$state}",
                'description' => "Calendar for car shows in {$state}",
                'color' => '#3788d8', // Default blue color
                'is_visible' => 1,
                'is_public' => 1,
                'owner_id' => $ownerId
            ];
            
            $calendarId = $this->createCalendar($calendarData);
            
            if ($calendarId) {
                error_log("CalendarModel::getOrCreateStateCalendar - Successfully created calendar for state {$state} with ID: {$calendarId}");
            } else {
                error_log("CalendarModel::getOrCreateStateCalendar - Failed to create calendar for state {$state}");
            }
            
            return $calendarId;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getOrCreateStateCalendar: ' . $e->getMessage());
            return false;
        }
    }
    
    public function createEventFromShow($showId, $calendarId = null, $createdBy = null) {
        try {
            // Get show details
            $show = $this->showModel->getShowById($showId);
            if (!$show) {
                error_log("CalendarModel::createEventFromShow - Show with ID {$showId} not found");
                return false;
            }
            
            // Get state from custom field values if available
            $state = null;
            $address1 = '';
            $address2 = '';
            $city = '';
            $zipcode = '';
            
            // Try to get address components from custom field values
            require_once APPROOT . '/models/CustomFieldValuesModel.php';
            $customFieldValuesModel = new CustomFieldValuesModel();
            
            // Get state
            $stateValue = $customFieldValuesModel->getCustomFieldValue($showId, 'state');
            if ($stateValue) {
                $state = $stateValue;
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CalendarModel::createEventFromShow - Using state from custom field values: {$state}");
                }
            } else if (!empty($show->location)) {
                // Fallback to location parsing
                if (!isset($this->showModel)) {
                    require_once APPROOT . '/models/ShowModel.php';
                    $this->showModel = new ShowModel();
                }
                $locationData = $this->showModel->parseLocation($show->location);
                $state = $locationData['state'];
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CalendarModel::createEventFromShow - Extracted state from location: {$state}");
                }
            }
            
            // Get address1
            $address1Value = $customFieldValuesModel->getCustomFieldValue($showId, 'address1');
            if ($address1Value) {
                $address1 = $address1Value;
            } else {
                $address1 = $show->location ?? '';
            }
            
            // Get address2
            $address2Value = $customFieldValuesModel->getCustomFieldValue($showId, 'address2');
            if ($address2Value) {
                $address2 = $address2Value;
            }
            
            // Get city
            $cityValue = $customFieldValuesModel->getCustomFieldValue($showId, 'city');
            if ($cityValue) {
                $city = $cityValue;
            } else if (!empty($show->location)) {
                // Fallback to location parsing
                if (!isset($this->showModel)) {
                    require_once APPROOT . '/models/ShowModel.php';
                    $this->showModel = new ShowModel();
                }
                $locationData = $this->showModel->parseLocation($show->location);
                $city = $locationData['city'];
            }
            
            // Get zipcode
            $zipcodeValue = $customFieldValuesModel->getCustomFieldValue($showId, 'zipcode');
            if ($zipcodeValue) {
                $zipcode = $zipcodeValue;
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarModel::createEventFromShow - Address components: address1={$address1}, city={$city}, state={$state}, zipcode={$zipcode}");
            }
            
            // If no calendar ID is provided, try to get or create a state-specific calendar
            if ($calendarId === null && !empty($state)) {
                error_log("CalendarModel::createEventFromShow - Attempting to get/create state calendar for state: {$state}");
                $calendarId = $this->getOrCreateStateCalendar($state, $createdBy);
                
                if ($calendarId) {
                    error_log("CalendarModel::createEventFromShow - Using state calendar with ID: {$calendarId}");
                } else {
                    error_log("CalendarModel::createEventFromShow - Failed to get/create state calendar, falling back to default");
                    // If we couldn't get/create a state calendar, fall back to default
                    $settingsModel = new SettingsModel();
                    $calendarId = $settingsModel->getSetting('default_show_calendar_id', null);
                    
                    if ($calendarId) {
                        error_log("CalendarModel::createEventFromShow - Using default calendar with ID: {$calendarId}");
                    } else {
                        error_log("CalendarModel::createEventFromShow - No calendar ID provided and couldn't determine default");
                        return false;
                    }
                }
            } else if ($calendarId === null) {
                error_log("CalendarModel::createEventFromShow - No calendar ID provided and state is empty, falling back to default");
                $settingsModel = new SettingsModel();
                $calendarId = $settingsModel->getSetting('default_show_calendar_id', null);
                
                if ($calendarId) {
                    error_log("CalendarModel::createEventFromShow - Using default calendar with ID: {$calendarId}");
                } else {
                    error_log("CalendarModel::createEventFromShow - No default calendar ID found in settings");
                    return false;
                }
            } else {
                error_log("CalendarModel::createEventFromShow - Using provided calendar ID: {$calendarId}");
            }
            
            // Create event data
            $eventData = [
                'calendar_id' => $calendarId,
                'title' => $show->name,
                'description' => $show->description,
                'start_date' => $show->start_date,
                'end_date' => $show->end_date,
                'all_day' => 0,
                'location' => $show->location,
                'address1' => $address1,
                'address2' => $address2,
                'city' => $city,
                'state' => $state,
                'zipcode' => $zipcode,
                'lat' => $show->lat ?? null, // Include lat from the show
                'lng' => $show->lng ?? null, // Include lng from the show
                'privacy' => 'public',
                'show_id' => $showId,
                'created_by' => $createdBy ?? $show->coordinator_id // Use coordinator ID if no creator specified
            ];
            
            // Log the lat/lng values being sent to the calendar event
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarModel::createEventFromShow - Using coordinates: lat=" . 
                         ($show->lat ?? 'null') . ", lng=" . ($show->lng ?? 'null'));
            }
            
            return $this->createEvent($eventData);
        } catch (Exception $e) {
            error_log('Error in CalendarModel::createEventFromShow: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Sync events with shows
     * 
     * @param int $calendarId Calendar ID to sync with
     * @return bool True if successful, false otherwise
     */
    public function syncEventsWithShows($calendarId) {
        try {
            // Get all published shows
            $this->db->query('SELECT * FROM shows WHERE status = "published"');
            $shows = $this->db->resultSet();
            
            if (empty($shows)) {
                return true; // No shows to sync
            }
            
            // Get existing events linked to shows
            $this->db->query('SELECT * FROM calendar_events WHERE calendar_id = :calendar_id AND show_id IS NOT NULL');
            $this->db->bind(':calendar_id', $calendarId);
            $existingEvents = $this->db->resultSet();
            
            // Create a map of show IDs to event IDs
            $showEventMap = [];
            foreach ($existingEvents as $event) {
                $showEventMap[$event->show_id] = $event->id;
            }
            
            // Process each show
            foreach ($shows as $show) {
                if (isset($showEventMap[$show->id])) {
                    // Update existing event
                    $eventId = $showEventMap[$show->id];
                    $eventData = [
                        'id' => $eventId,
                        'calendar_id' => $calendarId,
                        'title' => $show->name,
                        'description' => $show->description,
                        'start_date' => $show->start_date,
                        'end_date' => $show->end_date,
                        'all_day' => 0,
                        'location' => $show->location,
                        'address1' => $show->address1 ?? null,
                        'address2' => $show->address2 ?? null,
                        'city' => $show->city ?? null,
                        'state' => $show->state ?? null,
                        'zipcode' => $show->zipcode ?? null,
                        'lat' => $show->lat ?? null,
                        'lng' => $show->lng ?? null,
                        'privacy' => 'public',
                        'show_id' => $show->id
                    ];
                    
                    // Log the lat/lng values being sent to the calendar event
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CalendarModel::syncEventsWithShows - Updating event ID {$eventId} with coordinates: lat=" . 
                                 ($show->lat ?? 'null') . ", lng=" . ($show->lng ?? 'null'));
                    }
                    $this->updateEvent($eventData);
                } else {
                    // Create new event
                    $this->createEventFromShow($show->id, $calendarId);
                }
            }
            
            return true;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::syncEventsWithShows: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get venue counts for overview dashboard
     *
     * @return array Array with venue counts by category
     */
    public function getVenueCounts() {
        try {
            $counts = [
                'total' => 0,
                'by_state' => []
            ];

            // Get total count
            $this->db->query('SELECT COUNT(*) as total FROM calendar_venues');
            $result = $this->db->single();
            $counts['total'] = $result ? $result->total : 0;

            // Get count by state
            $this->db->query('SELECT state, COUNT(*) as count FROM calendar_venues WHERE state IS NOT NULL AND state != "" GROUP BY state ORDER BY count DESC LIMIT 10');
            $stateResults = $this->db->resultSet();
            foreach ($stateResults as $stateResult) {
                $counts['by_state'][$stateResult->state] = $stateResult->count;
            }

            return $counts;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getVenueCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'by_state' => []
            ];
        }
    }

    /**
     * Get all venues
     *
     * @return array Venues
     */
    public function getVenues() {
        try {
            $this->db->query('SELECT * FROM calendar_venues ORDER BY name');
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getVenues: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get paginated venues with search and filtering
     *
     * @param int $page Page number (1-based)
     * @param int $perPage Number of venues per page
     * @param string $search Search term for name/address/city
     * @param string $state State filter
     * @param string $city City filter
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC, DESC)
     * @return array Array with venues, pagination info
     */
    public function getPaginatedVenues($page = 1, $perPage = 20, $search = '', $state = '', $city = '', $orderBy = 'name', $orderDir = 'ASC') {
        $startTime = microtime(true);

        // Validate inputs
        $page = max(1, (int)$page);
        $perPage = max(1, min(100, (int)$perPage));
        $offset = ($page - 1) * $perPage;

        // Validate order by field
        $allowedOrderFields = ['name', 'city', 'state', 'zipcode', 'created_at', 'updated_at'];
        if (!in_array($orderBy, $allowedOrderFields)) {
            $orderBy = 'name';
        }

        // Validate order direction
        $orderDir = strtoupper($orderDir) === 'DESC' ? 'DESC' : 'ASC';

        // Build WHERE conditions
        $whereConditions = [];
        $bindParams = [];

        if (!empty($search)) {
            $whereConditions[] = '(name LIKE :search_name OR address1 LIKE :search_addr OR city LIKE :search_city)';
            $bindParams['search_name'] = '%' . $search . '%';
            $bindParams['search_addr'] = '%' . $search . '%';
            $bindParams['search_city'] = '%' . $search . '%';
        }

        if (!empty($state)) {
            $whereConditions[] = 'state = :state';
            $bindParams['state'] = $state;
        }

        if (!empty($city)) {
            $whereConditions[] = 'city LIKE :city';
            $bindParams['city'] = '%' . $city . '%';
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM calendar_venues $whereClause";
        $this->db->query($countSql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $countResult = $this->db->single();
        $totalVenues = $countResult ? $countResult->total : 0;
        $totalPages = ceil($totalVenues / $perPage);

        // Get venues for current page with event counts
        $sql = "SELECT v.*,
                       (SELECT COUNT(*) FROM calendar_events WHERE venue_id = v.id) as event_count
                FROM calendar_venues v
                $whereClause
                ORDER BY v.$orderBy $orderDir
                LIMIT :limit OFFSET :offset";

        $this->db->query($sql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
        $this->db->bind(':offset', $offset, PDO::PARAM_INT);

        $venues = $this->db->resultSet();

        // Performance monitoring
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);

        error_log("CalendarModel::getPaginatedVenues - Page: {$page}, Venues: {$totalVenues}, Time: {$executionTime}ms");

        if ($executionTime > 100) {
            error_log("WARNING: Slow venues query detected! Time: {$executionTime}ms");
        }

        return [
            'venues' => $venues,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_venues' => $totalVenues,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'start_record' => $totalVenues > 0 ? $offset + 1 : 0,
                'end_record' => min($offset + $perPage, $totalVenues)
            ],
            'filters' => [
                'search' => $search,
                'state' => $state,
                'city' => $city,
                'order_by' => $orderBy,
                'order_dir' => $orderDir
            ],
            'performance' => [
                'execution_time_ms' => $executionTime
            ]
        ];
    }

    /**
     * Search venues by name
     *
     * @param string $search Search term
     * @return array Matching venues
     */
    public function searchVenues($search) {
        try {
            // First try to find exact matches in name (higher priority)
            $this->db->query('SELECT * FROM calendar_venues 
                             WHERE name LIKE :exact_name
                             ORDER BY name 
                             LIMIT 5');
            $this->db->bind(':exact_name', $search . '%');
            $exactMatches = $this->db->resultSet();
            
            // Then find partial matches in name, address, city, or state
            $this->db->query('SELECT * FROM calendar_venues 
                             WHERE (name LIKE :partial_search 
                             OR address LIKE :partial_search
                             OR city LIKE :partial_search 
                             OR state LIKE :partial_search)
                             AND name NOT LIKE :exclude_exact
                             ORDER BY 
                                CASE 
                                    WHEN name LIKE :partial_search THEN 1
                                    WHEN address LIKE :partial_search THEN 2
                                    WHEN city LIKE :partial_search THEN 3
                                    WHEN state LIKE :partial_search THEN 4
                                    ELSE 5
                                END,
                                name
                             LIMIT 15');
            $this->db->bind(':partial_search', '%' . $search . '%');
            $this->db->bind(':exclude_exact', $search . '%'); // Exclude exact matches we already have
            $partialMatches = $this->db->resultSet();
            
            // Combine results, with exact matches first
            return array_merge($exactMatches, $partialMatches);
        } catch (Exception $e) {
            error_log('Error in CalendarModel::searchVenues: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get venue by ID
     * 
     * @param int $id Venue ID
     * @return object|bool Venue object or false if not found
     */
    public function getVenueById($id) {
        try {
            $this->db->query('SELECT * FROM calendar_venues WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->single();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getVenueById: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new venue
     * 
     * @param array $data Venue data
     * @return int|bool New venue ID or false if failed
     */
    public function createVenue($data) {
        try {
            $this->db->query('INSERT INTO calendar_venues (
                                name, address, city, state, zip, country, 
                                latitude, longitude, website, phone, email, 
                                capacity, notes
                             ) VALUES (
                                :name, :address, :city, :state, :zip, :country, 
                                :latitude, :longitude, :website, :phone, :email, 
                                :capacity, :notes
                             )');
            
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':address', $data['address'] ?? null);
            $this->db->bind(':city', $data['city'] ?? null);
            $this->db->bind(':state', $data['state'] ?? null);
            $this->db->bind(':zip', $data['zip'] ?? null);
            $this->db->bind(':country', $data['country'] ?? null);
            $this->db->bind(':latitude', $data['latitude'] ?? null);
            $this->db->bind(':longitude', $data['longitude'] ?? null);
            $this->db->bind(':website', $data['website'] ?? null);
            $this->db->bind(':phone', $data['phone'] ?? null);
            $this->db->bind(':email', $data['email'] ?? null);
            $this->db->bind(':capacity', $data['capacity'] ?? null);
            $this->db->bind(':notes', $data['notes'] ?? null);
            
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            }
            
            return false;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::createVenue: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a venue
     * 
     * @param array $data Venue data
     * @return bool True if successful, false otherwise
     */
    public function updateVenue($data) {
        try {
            $this->db->query('UPDATE calendar_venues 
                             SET name = :name, 
                                 address = :address, 
                                 city = :city, 
                                 state = :state, 
                                 zip = :zip, 
                                 country = :country, 
                                 latitude = :latitude, 
                                 longitude = :longitude, 
                                 website = :website, 
                                 phone = :phone, 
                                 email = :email, 
                                 capacity = :capacity, 
                                 notes = :notes
                             WHERE id = :id');
            
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':address', $data['address'] ?? null);
            $this->db->bind(':city', $data['city'] ?? null);
            $this->db->bind(':state', $data['state'] ?? null);
            $this->db->bind(':zip', $data['zip'] ?? null);
            $this->db->bind(':country', $data['country'] ?? null);
            $this->db->bind(':latitude', $data['latitude'] ?? null);
            $this->db->bind(':longitude', $data['longitude'] ?? null);
            $this->db->bind(':website', $data['website'] ?? null);
            $this->db->bind(':phone', $data['phone'] ?? null);
            $this->db->bind(':email', $data['email'] ?? null);
            $this->db->bind(':capacity', $data['capacity'] ?? null);
            $this->db->bind(':notes', $data['notes'] ?? null);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateVenue: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a venue
     * 
     * @param int $id Venue ID
     * @return bool True if successful, false otherwise
     */
    public function deleteVenue($id) {
        try {
            $this->db->query('DELETE FROM calendar_venues WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::deleteVenue: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get club counts for overview dashboard
     *
     * @return array Array with club counts by category
     */
    public function getClubCounts() {
        try {
            $counts = [
                'total' => 0,
                'verified' => 0,
                'unverified' => 0,
                'with_owner' => 0,
                'without_owner' => 0
            ];

            // Get total count
            $this->db->query('SELECT COUNT(*) as total FROM calendar_clubs');
            $result = $this->db->single();
            $counts['total'] = $result ? $result->total : 0;

            // Get verified count
            $this->db->query('SELECT COUNT(*) as verified FROM calendar_clubs WHERE is_verified = 1');
            $result = $this->db->single();
            $counts['verified'] = $result ? $result->verified : 0;

            // Get unverified count
            $this->db->query('SELECT COUNT(*) as unverified FROM calendar_clubs WHERE is_verified = 0');
            $result = $this->db->single();
            $counts['unverified'] = $result ? $result->unverified : 0;

            // Get clubs with owner count
            $this->db->query('SELECT COUNT(*) as with_owner FROM calendar_clubs WHERE owner_id IS NOT NULL');
            $result = $this->db->single();
            $counts['with_owner'] = $result ? $result->with_owner : 0;

            // Get clubs without owner count
            $this->db->query('SELECT COUNT(*) as without_owner FROM calendar_clubs WHERE owner_id IS NULL');
            $result = $this->db->single();
            $counts['without_owner'] = $result ? $result->without_owner : 0;

            return $counts;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'verified' => 0,
                'unverified' => 0,
                'with_owner' => 0,
                'without_owner' => 0
            ];
        }
    }

    /**
     * Get all clubs
     *
     * @return array Clubs
     */
    public function getClubs() {
        try {
            $this->db->query('SELECT c.*, u.name as owner_name 
                             FROM calendar_clubs c 
                             LEFT JOIN users u ON c.owner_id = u.id 
                             ORDER BY c.name');
            $clubs = $this->db->resultSet();
            
            // Get member count for each club
            foreach ($clubs as &$club) {
                $this->db->query('SELECT COUNT(*) as count FROM calendar_club_members WHERE club_id = :club_id');
                $this->db->bind(':club_id', $club->id);
                $result = $this->db->single();
                $club->member_count = $result->count ?? 0;
                
                // Find owner from members if not set in club table
                if (!$club->owner_id) {
                    $this->db->query('SELECT u.id, u.name FROM calendar_club_members cm 
                                     JOIN users u ON cm.user_id = u.id 
                                     WHERE cm.club_id = :club_id AND cm.role = "owner" LIMIT 1');
                    $this->db->bind(':club_id', $club->id);
                    $owner = $this->db->single();
                    if ($owner) {
                        $club->owner_id = $owner->id;
                        $club->owner_name = $owner->name;
                    }
                }
            }
            
            return $clubs;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubs: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get paginated clubs with search and filtering
     *
     * @param int $page Page number (1-based)
     * @param int $perPage Number of clubs per page
     * @param string $search Search term for name/description/email
     * @param string $ownerFilter Filter by ownership (owned, not_owned, all)
     * @param string $verificationFilter Filter by verification status
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC, DESC)
     * @return array Array with clubs, pagination info
     */
    public function getPaginatedClubs($page = 1, $perPage = 20, $search = '', $ownerFilter = 'all', $verificationFilter = 'all', $orderBy = 'name', $orderDir = 'ASC') {
        $startTime = microtime(true);

        // Validate inputs
        $page = max(1, (int)$page);
        $perPage = max(1, min(100, (int)$perPage));
        $offset = ($page - 1) * $perPage;

        // Validate order by field
        $allowedOrderFields = ['name', 'email', 'created_at', 'updated_at', 'verification_status'];
        if (!in_array($orderBy, $allowedOrderFields)) {
            $orderBy = 'name';
        }

        // Validate order direction
        $orderDir = strtoupper($orderDir) === 'DESC' ? 'DESC' : 'ASC';

        // Build WHERE conditions
        $whereConditions = [];
        $bindParams = [];

        if (!empty($search)) {
            $whereConditions[] = '(c.name LIKE :search_name OR c.description LIKE :search_desc OR c.email LIKE :search_email)';
            $bindParams['search_name'] = '%' . $search . '%';
            $bindParams['search_desc'] = '%' . $search . '%';
            $bindParams['search_email'] = '%' . $search . '%';
        }

        // Owner filter
        if ($ownerFilter !== 'all' && isset($_SESSION['user_id'])) {
            if ($ownerFilter === 'owned') {
                $whereConditions[] = 'c.owner_id = :user_id';
                $bindParams['user_id'] = $_SESSION['user_id'];
            } elseif ($ownerFilter === 'not_owned') {
                $whereConditions[] = '(c.owner_id IS NULL OR c.owner_id != :user_id)';
                $bindParams['user_id'] = $_SESSION['user_id'];
            }
        }

        // Verification filter
        if ($verificationFilter !== 'all') {
            if ($verificationFilter === 'verified') {
                $whereConditions[] = 'c.is_verified = 1';
            } elseif ($verificationFilter === 'unverified') {
                $whereConditions[] = 'c.is_verified = 0';
            } elseif (in_array($verificationFilter, ['pending', 'approved', 'denied'])) {
                $whereConditions[] = 'c.verification_status = :verification_status';
                $bindParams['verification_status'] = $verificationFilter;
            }
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM calendar_clubs c $whereClause";
        $this->db->query($countSql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $countResult = $this->db->single();
        $totalClubs = $countResult ? $countResult->total : 0;
        $totalPages = ceil($totalClubs / $perPage);

        // Get clubs for current page with owner info and member counts
        $sql = "SELECT c.id, c.name, c.description, c.email, c.phone, c.website, c.logo,
                       c.owner_id, c.is_verified, c.verification_status, c.created_at, c.updated_at,
                       u.name as owner_name,
                       (SELECT COUNT(*) FROM calendar_club_members WHERE club_id = c.id) as member_count
                FROM calendar_clubs c
                LEFT JOIN users u ON c.owner_id = u.id
                $whereClause
                ORDER BY c.$orderBy $orderDir
                LIMIT :limit OFFSET :offset";

        $this->db->query($sql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
        $this->db->bind(':offset', $offset, PDO::PARAM_INT);

        $clubs = $this->db->resultSet();

        // Performance monitoring
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);

        error_log("CalendarModel::getPaginatedClubs - Page: {$page}, Clubs: {$totalClubs}, Time: {$executionTime}ms");

        if ($executionTime > 100) {
            error_log("WARNING: Slow clubs query detected! Time: {$executionTime}ms");
        }

        return [
            'clubs' => $clubs,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_clubs' => $totalClubs,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'start_record' => $totalClubs > 0 ? $offset + 1 : 0,
                'end_record' => min($offset + $perPage, $totalClubs)
            ],
            'filters' => [
                'search' => $search,
                'owner_filter' => $ownerFilter,
                'verification_filter' => $verificationFilter,
                'order_by' => $orderBy,
                'order_dir' => $orderDir
            ],
            'performance' => [
                'execution_time_ms' => $executionTime
            ]
        ];
    }

    /**
     * Get club by ID
     *
     * @param int $id Club ID
     * @return object|bool Club object or false if not found
     */
    public function getClubById($id) {
        try {
            $this->db->query('SELECT c.*, u.name as owner_name 
                             FROM calendar_clubs c 
                             LEFT JOIN users u ON c.owner_id = u.id 
                             WHERE c.id = :id');
            $this->db->bind(':id', $id);
            
            $club = $this->db->single();
            if ($club) {
                // Get club members
                $club->members = $this->getClubMembers($id);
                
                // Get member count
                $club->member_count = count($club->members);
                
                // Find owner from members if not set in club table
                if (!$club->owner_id && $club->members) {
                    foreach ($club->members as $member) {
                        if ($member->role === 'owner') {
                            $club->owner_id = $member->id;
                            $club->owner_name = $member->name;
                            break;
                        }
                    }
                }
                
                return $club;
            }
            
            return false;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubById: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new club
     * 
     * @param array $data Club data
     * @return int|bool New club ID or false if failed
     */
    public function createClub($data) {
        try {
            $this->db->beginTransaction();
            
            $this->db->query('INSERT INTO calendar_clubs (
                                name, description, logo, website, email, phone
                             ) VALUES (
                                :name, :description, :logo, :website, :email, :phone
                             )');
            
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':logo', $data['logo'] ?? null);
            $this->db->bind(':website', $data['website'] ?? null);
            $this->db->bind(':email', $data['email'] ?? null);
            $this->db->bind(':phone', $data['phone'] ?? null);
            
            if ($this->db->execute()) {
                $clubId = $this->db->lastInsertId();
                
                // Add owner as member with owner role
                if (!empty($data['owner_id'])) {
                    $this->addClubMember($clubId, $data['owner_id'], 'owner');
                }
                
                $this->db->commit();
                return $clubId;
            }
            
            $this->db->rollBack();
            return false;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error in CalendarModel::createClub: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a club
     * 
     * @param array $data Club data
     * @return bool True if successful, false otherwise
     */
    public function updateClub($data) {
        try {
            $this->db->query('UPDATE calendar_clubs 
                             SET name = :name, 
                                 description = :description, 
                                 logo = :logo, 
                                 website = :website, 
                                 email = :email, 
                                 phone = :phone
                             WHERE id = :id');
            
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':logo', $data['logo'] ?? null);
            $this->db->bind(':website', $data['website'] ?? null);
            $this->db->bind(':email', $data['email'] ?? null);
            $this->db->bind(':phone', $data['phone'] ?? null);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateClub: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a club
     * 
     * @param int $id Club ID
     * @return bool True if successful, false otherwise
     */
    public function deleteClub($id) {
        try {
            $this->db->query('DELETE FROM calendar_clubs WHERE id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::deleteClub: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get club member counts for overview dashboard
     *
     * @param int $clubId Club ID
     * @return array Array with member counts by role
     */
    public function getClubMemberCounts($clubId) {
        try {
            $counts = [
                'total' => 0,
                'owners' => 0,
                'admins' => 0,
                'members' => 0
            ];

            // Get total count
            $this->db->query('SELECT COUNT(*) as total FROM calendar_club_members WHERE club_id = :club_id');
            $this->db->bind(':club_id', $clubId);
            $result = $this->db->single();
            $counts['total'] = $result ? $result->total : 0;

            // Get count by role
            $this->db->query('SELECT role, COUNT(*) as count FROM calendar_club_members WHERE club_id = :club_id GROUP BY role');
            $this->db->bind(':club_id', $clubId);
            $roleResults = $this->db->resultSet();

            foreach ($roleResults as $roleResult) {
                switch ($roleResult->role) {
                    case 'owner':
                        $counts['owners'] = $roleResult->count;
                        break;
                    case 'admin':
                        $counts['admins'] = $roleResult->count;
                        break;
                    case 'member':
                    default:
                        $counts['members'] = $roleResult->count;
                        break;
                }
            }

            return $counts;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubMemberCounts: ' . $e->getMessage());
            return [
                'total' => 0,
                'owners' => 0,
                'admins' => 0,
                'members' => 0
            ];
        }
    }

    /**
     * Get club members
     *
     * @param int $clubId Club ID
     * @return array Club members
     */
    public function getClubMembers($clubId) {
        try {
            $this->db->query('SELECT cm.*, u.name, u.email, cm.joined_at 
                             FROM calendar_club_members cm 
                             LEFT JOIN users u ON cm.user_id = u.id 
                             WHERE cm.club_id = :club_id 
                             ORDER BY cm.role, u.name');
            $this->db->bind(':club_id', $clubId);
            
            $members = $this->db->resultSet();
            
            // Add id field for compatibility with view
            foreach ($members as $member) {
                $member->id = $member->user_id;
            }
            
            return $members;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubMembers: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get paginated club members with search and filtering
     *
     * @param int $clubId Club ID
     * @param int $page Page number (1-based)
     * @param int $perPage Number of members per page
     * @param string $search Search term for name/email
     * @param string $roleFilter Filter by role (all, owner, admin, member)
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC, DESC)
     * @return array Array with members, pagination info
     */
    public function getPaginatedClubMembers($clubId, $page = 1, $perPage = 20, $search = '', $roleFilter = 'all', $orderBy = 'name', $orderDir = 'ASC') {
        $startTime = microtime(true);

        // Validate inputs
        $page = max(1, (int)$page);
        $perPage = max(1, min(100, (int)$perPage));
        $offset = ($page - 1) * $perPage;

        // Validate order by field
        $allowedOrderFields = ['name', 'email', 'role', 'joined_at'];
        if (!in_array($orderBy, $allowedOrderFields)) {
            $orderBy = 'name';
        }

        // Validate order direction
        $orderDir = strtoupper($orderDir) === 'DESC' ? 'DESC' : 'ASC';

        // Build WHERE conditions
        $whereConditions = ['cm.club_id = :club_id'];
        $bindParams = ['club_id' => $clubId];

        if (!empty($search)) {
            $whereConditions[] = '(u.name LIKE :search_name OR u.email LIKE :search_email)';
            $bindParams['search_name'] = '%' . $search . '%';
            $bindParams['search_email'] = '%' . $search . '%';
        }

        if ($roleFilter !== 'all' && in_array($roleFilter, ['owner', 'admin', 'member'])) {
            $whereConditions[] = 'cm.role = :role';
            $bindParams['role'] = $roleFilter;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total
                     FROM calendar_club_members cm
                     LEFT JOIN users u ON cm.user_id = u.id
                     $whereClause";
        $this->db->query($countSql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $countResult = $this->db->single();
        $totalMembers = $countResult ? $countResult->total : 0;
        $totalPages = ceil($totalMembers / $perPage);

        // Get members for current page
        $orderField = $orderBy === 'name' ? 'u.name' : ($orderBy === 'email' ? 'u.email' : 'cm.' . $orderBy);
        $sql = "SELECT cm.*, u.name, u.email, u.profile_image, cm.joined_at, cm.user_id as id
                FROM calendar_club_members cm
                LEFT JOIN users u ON cm.user_id = u.id
                $whereClause
                ORDER BY $orderField $orderDir
                LIMIT :limit OFFSET :offset";

        $this->db->query($sql);
        foreach ($bindParams as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
        $this->db->bind(':offset', $offset, PDO::PARAM_INT);

        $members = $this->db->resultSet();

        // Performance monitoring
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);

        error_log("CalendarModel::getPaginatedClubMembers - Club: {$clubId}, Page: {$page}, Members: {$totalMembers}, Time: {$executionTime}ms");

        if ($executionTime > 100) {
            error_log("WARNING: Slow club members query detected! Time: {$executionTime}ms");
        }

        return [
            'members' => $members,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_members' => $totalMembers,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'start_record' => $totalMembers > 0 ? $offset + 1 : 0,
                'end_record' => min($offset + $perPage, $totalMembers)
            ],
            'filters' => [
                'search' => $search,
                'role_filter' => $roleFilter,
                'order_by' => $orderBy,
                'order_dir' => $orderDir
            ],
            'performance' => [
                'execution_time_ms' => $executionTime
            ]
        ];
    }

    /**
     * Add a member to a club
     *
     * @param int $clubId Club ID
     * @param int $userId User ID
     * @param string $role Member role (member, admin, owner)
     * @return bool True if successful, false otherwise
     */
    public function addClubMember($clubId, $userId, $role = 'member') {
        try {
            // Check if member already exists
            $this->db->query('SELECT * FROM calendar_club_members 
                             WHERE club_id = :club_id AND user_id = :user_id');
            $this->db->bind(':club_id', $clubId);
            $this->db->bind(':user_id', $userId);
            
            $existingMember = $this->db->single();
            if ($existingMember) {
                // Update role if different
                if ($existingMember->role != $role) {
                    $this->db->query('UPDATE calendar_club_members 
                                     SET role = :role 
                                     WHERE club_id = :club_id AND user_id = :user_id');
                    $this->db->bind(':club_id', $clubId);
                    $this->db->bind(':user_id', $userId);
                    $this->db->bind(':role', $role);
                    
                    return $this->db->execute();
                }
                
                return true; // Member already exists with the same role
            }
            
            // Add new member
            $this->db->query('INSERT INTO calendar_club_members (club_id, user_id, role) 
                             VALUES (:club_id, :user_id, :role)');
            $this->db->bind(':club_id', $clubId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::addClubMember: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove a member from a club
     * 
     * @param int $clubId Club ID
     * @param int $userId User ID
     * @return bool True if successful, false otherwise
     */
    public function removeClubMember($clubId, $userId) {
        try {
            $this->db->query('DELETE FROM calendar_club_members 
                             WHERE club_id = :club_id AND user_id = :user_id');
            $this->db->bind(':club_id', $clubId);
            $this->db->bind(':user_id', $userId);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::removeClubMember: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get event clubs
     * 
     * @param int $eventId Event ID
     * @return array Event clubs
     */
    public function getEventClubs($eventId) {
        try {
            $this->db->query('SELECT c.* 
                             FROM calendar_clubs c 
                             JOIN calendar_event_clubs ec ON c.id = ec.club_id 
                             WHERE ec.event_id = :event_id 
                             ORDER BY c.name');
            $this->db->bind(':event_id', $eventId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventClubs: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Add a club to an event
     * 
     * @param int $eventId Event ID
     * @param int $clubId Club ID
     * @return bool True if successful, false otherwise
     */
    public function addEventClub($eventId, $clubId) {
        try {
            // Check if association already exists
            $this->db->query('SELECT * FROM calendar_event_clubs 
                             WHERE event_id = :event_id AND club_id = :club_id');
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':club_id', $clubId);
            
            if ($this->db->single()) {
                return true; // Association already exists
            }
            
            // Add new association
            $this->db->query('INSERT INTO calendar_event_clubs (event_id, club_id) 
                             VALUES (:event_id, :club_id)');
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':club_id', $clubId);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::addEventClub: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove a club from an event
     * 
     * @param int $eventId Event ID
     * @param int $clubId Club ID
     * @return bool True if successful, false otherwise
     */
    public function removeEventClub($eventId, $clubId) {
        try {
            $this->db->query('DELETE FROM calendar_event_clubs 
                             WHERE event_id = :event_id AND club_id = :club_id');
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':club_id', $clubId);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::removeEventClub: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Search clubs by name
     * 
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Matching clubs
     */
    public function searchClubsByName($query, $limit = 10) {
        try {
            $this->db->query('SELECT * FROM calendar_clubs 
                             WHERE name LIKE :query 
                             ORDER BY name 
                             LIMIT :limit');
            $this->db->bind(':query', '%' . $query . '%');
            $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::searchClubsByName: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Save event image with Base64 data
     * 
     * @param int $eventId Event ID
     * @param int $userId User ID
     * @param string $imageData Base64 encoded image data
     * @param string $fileName Original file name
     * @param string $fileType MIME type
     * @param int $fileSize Original file size in bytes
     * @param int $width Image width
     * @param int $height Image height
     * @param string $altText Alt text for accessibility
     * @param string $caption Image caption
     * @param int $displayOrder Display order
     * @param bool $isFeatured Whether this is the featured image
     * @return int|false Image ID if successful, false otherwise
     */
    public function saveEventImage($eventId, $userId, $imageData, $fileName, $fileType, $fileSize, $width = null, $height = null, $altText = '', $caption = '', $displayOrder = 0, $isFeatured = false) {
        try {
            $this->db->query('INSERT INTO calendar_event_images 
                             (event_id, user_id, image_data, file_name, file_type, file_size, width, height, alt_text, caption, display_order, is_featured) 
                             VALUES (:event_id, :user_id, :image_data, :file_name, :file_type, :file_size, :width, :height, :alt_text, :caption, :display_order, :is_featured)');
            
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':image_data', $imageData);
            $this->db->bind(':file_name', $fileName);
            $this->db->bind(':file_type', $fileType);
            $this->db->bind(':file_size', $fileSize);
            $this->db->bind(':width', $width);
            $this->db->bind(':height', $height);
            $this->db->bind(':alt_text', $altText);
            $this->db->bind(':caption', $caption);
            $this->db->bind(':display_order', $displayOrder);
            $this->db->bind(':is_featured', $isFeatured ? 1 : 0);
            
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            }
            return false;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::saveEventImage: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get event images
     * 
     * @param int $eventId Event ID
     * @param bool $featuredOnly Get only featured image
     * @return array Event images
     */
    public function getEventImages($eventId, $featuredOnly = false) {
        try {
            $sql = 'SELECT * FROM calendar_event_images WHERE event_id = :event_id';
            if ($featuredOnly) {
                $sql .= ' AND is_featured = 1';
            }
            $sql .= ' ORDER BY display_order, created_at';
            
            $this->db->query($sql);
            $this->db->bind(':event_id', $eventId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventImages: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get event image by ID
     * 
     * @param int $imageId Image ID
     * @return object|false Image data or false if not found
     */
    public function getEventImageById($imageId) {
        try {
            $this->db->query('SELECT * FROM calendar_event_images WHERE id = :id');
            $this->db->bind(':id', $imageId);
            
            return $this->db->single();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventImageById: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete event image
     * 
     * @param int $imageId Image ID
     * @param int $userId User ID (for permission check)
     * @return bool True if successful, false otherwise
     */
    public function deleteEventImage($imageId, $userId = null) {
        try {
            $sql = 'DELETE FROM calendar_event_images WHERE id = :id';
            if ($userId !== null) {
                $sql .= ' AND user_id = :user_id';
            }
            
            $this->db->query($sql);
            $this->db->bind(':id', $imageId);
            if ($userId !== null) {
                $this->db->bind(':user_id', $userId);
            }
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::deleteEventImage: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update event image
     * 
     * @param int $imageId Image ID
     * @param array $data Update data
     * @return bool True if successful, false otherwise
     */
    public function updateEventImage($imageId, $data) {
        try {
            $fields = [];
            $allowedFields = ['alt_text', 'caption', 'display_order', 'is_featured'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $fields[] = "$field = :$field";
                }
            }
            
            if (empty($fields)) {
                return false;
            }
            
            $sql = 'UPDATE calendar_event_images SET ' . implode(', ', $fields) . ' WHERE id = :id';
            $this->db->query($sql);
            $this->db->bind(':id', $imageId);
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $this->db->bind(":$field", $data[$field]);
                }
            }
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateEventImage: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get event image settings
     * 
     * @return array Image settings
     */
    public function getEventImageSettings() {
        try {
            // First try with setting_group column
            $this->db->query("SELECT setting_key, setting_value FROM calendar_settings 
                             WHERE setting_key LIKE 'event_%'");
            $results = $this->db->resultSet();
            
            $settings = [
                'maxImages' => 2,
                'maxImageSize' => 2,
                'allowedTypes' => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
                'enableWysiwyg' => true,
                'socialSharingImages' => true
            ];
            
            foreach ($results as $setting) {
                switch ($setting->setting_key) {
                    case 'event_max_images':
                        $settings['maxImages'] = intval($setting->setting_value);
                        break;
                    case 'event_max_image_size':
                        $settings['maxImageSize'] = floatval($setting->setting_value);
                        break;
                    case 'event_allowed_image_types':
                        $settings['allowedTypes'] = explode(',', $setting->setting_value);
                        break;
                    case 'event_enable_wysiwyg':
                        $settings['enableWysiwyg'] = (bool)$setting->setting_value;
                        break;
                    case 'event_social_sharing_images':
                        $settings['socialSharingImages'] = (bool)$setting->setting_value;
                        break;
                }
            }
            
            return $settings;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventImageSettings: ' . $e->getMessage());
            return [
                'maxImages' => 2,
                'maxImageSize' => 2,
                'allowedTypes' => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
                'enableWysiwyg' => true,
                'socialSharingImages' => true
            ];
        }
    }
    
    /**
     * Count event images for an event
     * 
     * @param int $eventId Event ID
     * @return int Number of images
     */
    public function countEventImages($eventId) {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM calendar_event_images WHERE event_id = :event_id');
            $this->db->bind(':event_id', $eventId);
            $result = $this->db->single();
            
            return $result ? intval($result->count) : 0;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::countEventImages: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Update a single calendar setting
     * 
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool True if successful, false otherwise
     */
    public function updateSetting($key, $value) {
        try {
            $this->db->query('INSERT INTO calendar_settings (setting_key, setting_value) 
                             VALUES (:key, :value) 
                             ON DUPLICATE KEY UPDATE setting_value = :value2');
            $this->db->bind(':key', $key);
            $this->db->bind(':value', $value);
            $this->db->bind(':value2', $value);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateSetting: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get calendar permissions
     * 
     * @param int $calendarId Calendar ID
     * @return array Calendar permissions
     */
    public function getCalendarPermissions($calendarId) {
        try {
            $this->db->query('SELECT cp.*, u.name as user_name, u.email as user_email 
                             FROM calendar_permissions cp 
                             LEFT JOIN users u ON cp.user_id = u.id 
                             WHERE cp.calendar_id = :calendar_id');
            $this->db->bind(':calendar_id', $calendarId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCalendarPermissions: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Add a permission to a calendar
     * 
     * @param int $calendarId Calendar ID
     * @param int $userId User ID
     * @param string $permission Permission type (view, edit, manage)
     * @return bool True if successful, false otherwise
     */
    public function addCalendarPermission($calendarId, $userId, $permission = 'view') {
        try {
            // Check if permission already exists
            $this->db->query('SELECT * FROM calendar_permissions 
                             WHERE calendar_id = :calendar_id AND user_id = :user_id');
            $this->db->bind(':calendar_id', $calendarId);
            $this->db->bind(':user_id', $userId);
            
            $existingPermission = $this->db->single();
            if ($existingPermission) {
                // Update permission if different
                if ($existingPermission->permission != $permission) {
                    $this->db->query('UPDATE calendar_permissions 
                                     SET permission = :permission 
                                     WHERE calendar_id = :calendar_id AND user_id = :user_id');
                    $this->db->bind(':calendar_id', $calendarId);
                    $this->db->bind(':user_id', $userId);
                    $this->db->bind(':permission', $permission);
                    
                    return $this->db->execute();
                }
                
                return true; // Permission already exists with the same type
            }
            
            // Add new permission
            $this->db->query('INSERT INTO calendar_permissions (calendar_id, user_id, permission) 
                             VALUES (:calendar_id, :user_id, :permission)');
            $this->db->bind(':calendar_id', $calendarId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':permission', $permission);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::addCalendarPermission: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove a permission from a calendar
     * 
     * @param int $calendarId Calendar ID
     * @param int $userId User ID
     * @return bool True if successful, false otherwise
     */
    public function removeCalendarPermission($calendarId, $userId) {
        try {
            $this->db->query('DELETE FROM calendar_permissions 
                             WHERE calendar_id = :calendar_id AND user_id = :user_id');
            $this->db->bind(':calendar_id', $calendarId);
            $this->db->bind(':user_id', $userId);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::removeCalendarPermission: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if a user has permission for a calendar
     * 
     * @param int $calendarId Calendar ID
     * @param int $userId User ID
     * @param string $requiredPermission Required permission level (view, edit, manage)
     * @return bool True if user has permission, false otherwise
     */
    public function userHasCalendarPermission($calendarId, $userId, $requiredPermission = 'view') {
        try {
            // Get calendar to check if user is the owner
            $calendar = $this->getCalendarById($calendarId);
            if ($calendar && $calendar->owner_id == $userId) {
                return true; // Owner has all permissions
            }
            
            // Check if calendar is public and required permission is view
            if ($calendar && $calendar->is_public && $requiredPermission == 'view') {
                return true; // Public calendars can be viewed by anyone
            }
            
            // Check specific permissions
            $this->db->query('SELECT * FROM calendar_permissions 
                             WHERE calendar_id = :calendar_id AND user_id = :user_id');
            $this->db->bind(':calendar_id', $calendarId);
            $this->db->bind(':user_id', $userId);
            
            $permission = $this->db->single();
            if (!$permission) {
                return false; // No permission found
            }
            
            // Check permission level
            switch ($requiredPermission) {
                case 'view':
                    return true; // Any permission level allows viewing
                case 'edit':
                    return in_array($permission->permission, ['edit', 'manage']);
                case 'manage':
                    return $permission->permission == 'manage';
                default:
                    return false;
            }
        } catch (Exception $e) {
            error_log('Error in CalendarModel::userHasCalendarPermission: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add a notification for an event
     * 
     * @param int $eventId Event ID
     * @param int $userId User ID
     * @param string $notificationTime Notification time (datetime)
     * @param string $notificationType Notification type (email, system)
     * @return bool True if successful, false otherwise
     */
    public function addNotification($eventId, $userId, $notificationTime, $notificationType = 'email') {
        try {
            $this->db->query('INSERT INTO calendar_notifications (
                                event_id, user_id, notification_time, notification_type, status
                             ) VALUES (
                                :event_id, :user_id, :notification_time, :notification_type, "pending"
                             )');
            
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':notification_time', $notificationTime);
            $this->db->bind(':notification_type', $notificationType);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::addNotification: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get pending notifications
     * 
     * @return array Pending notifications
     */
    public function getPendingNotifications() {
        try {
            $this->db->query('SELECT n.*, e.title as event_title, e.start_date as event_start_date, 
                                    u.name as user_name, u.email as user_email 
                             FROM calendar_notifications n 
                             JOIN calendar_events e ON n.event_id = e.id 
                             JOIN users u ON n.user_id = u.id 
                             WHERE n.status = "pending" AND n.notification_time <= NOW()');
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getPendingNotifications: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update notification status
     * 
     * @param int $notificationId Notification ID
     * @param string $status New status (pending, sent, failed)
     * @return bool True if successful, false otherwise
     */
    public function updateNotificationStatus($notificationId, $status) {
        try {
            $this->db->query('UPDATE calendar_notifications 
                             SET status = :status 
                             WHERE id = :id');
            $this->db->bind(':id', $notificationId);
            $this->db->bind(':status', $status);
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateNotificationStatus: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Import events from iCal
     * 
     * @param int $calendarId Calendar ID
     * @param string $icalUrl iCal URL
     * @param int $createdBy User ID who created the import
     * @return bool True if successful, false otherwise
     */
    public function importFromIcal($calendarId, $icalUrl, $createdBy = null) {
        try {
            // Record the import
            $this->db->query('INSERT INTO calendar_imports (
                                calendar_id, source_type, source_url, last_import, created_by
                             ) VALUES (
                                :calendar_id, "ical", :source_url, NOW(), :created_by
                             )');
            
            $this->db->bind(':calendar_id', $calendarId);
            $this->db->bind(':source_url', $icalUrl);
            $this->db->bind(':created_by', $createdBy);
            
            if (!$this->db->execute()) {
                return false;
            }
            
            $importId = $this->db->lastInsertId();
            
            // Process the iCal file (this would be implemented in a separate function)
            // For now, we'll just return true
            return true;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::importFromIcal: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Import events from Facebook
     * 
     * @param int $calendarId Calendar ID
     * @param string $facebookUrl Facebook event URL
     * @param int $createdBy User ID who created the import
     * @return bool True if successful, false otherwise
     */
    public function importFromFacebook($calendarId, $facebookUrl, $createdBy = null) {
        try {
            // Record the import
            $this->db->query('INSERT INTO calendar_imports (
                                calendar_id, source_type, source_url, last_import, created_by
                             ) VALUES (
                                :calendar_id, "facebook", :source_url, NOW(), :created_by
                             )');
            
            $this->db->bind(':calendar_id', $calendarId);
            $this->db->bind(':source_url', $facebookUrl);
            $this->db->bind(':created_by', $createdBy);
            
            if (!$this->db->execute()) {
                return false;
            }
            
            $importId = $this->db->lastInsertId();
            
            // Process the Facebook event (this would be implemented in a separate function)
            // For now, we'll just return true
            return true;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::importFromFacebook: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Export calendar to iCal
     * 
     * @param int $calendarId Calendar ID
     * @return string|bool iCal content or false if failed
     */
    public function exportToIcal($calendarId) {
        try {
            // Get calendar details
            $calendar = $this->getCalendarById($calendarId);
            if (!$calendar) {
                return false;
            }
            
            // Get events for this calendar
            $events = $this->getEvents(['calendar_id' => $calendarId]);
            
            // Generate iCal content
            $ical = "BEGIN:VCALENDAR\r\n";
            $ical .= "VERSION:2.0\r\n";
            $ical .= "PRODID:-//Events and Shows//Calendar//EN\r\n";
            $ical .= "CALSCALE:GREGORIAN\r\n";
            $ical .= "METHOD:PUBLISH\r\n";
            $ical .= "X-WR-CALNAME:" . $calendar->name . "\r\n";
            $ical .= "X-WR-CALDESC:" . ($calendar->description ?? '') . "\r\n";
            
            foreach ($events as $event) {
                $ical .= "BEGIN:VEVENT\r\n";
                $ical .= "UID:" . $event->id . "@events.rowaneliterides.com\r\n";
                $ical .= "DTSTAMP:" . gmdate('Ymd\THis\Z') . "\r\n";
                
                // Format dates
                $startDate = new DateTime($event->start_date);
                $endDate = new DateTime($event->end_date);
                
                if ($event->all_day) {
                    // All-day event
                    $ical .= "DTSTART;VALUE=DATE:" . $startDate->format('Ymd') . "\r\n";
                    $ical .= "DTEND;VALUE=DATE:" . $endDate->format('Ymd') . "\r\n";
                } else {
                    // Timed event
                    $ical .= "DTSTART:" . $startDate->format('Ymd\THis\Z') . "\r\n";
                    $ical .= "DTEND:" . $endDate->format('Ymd\THis\Z') . "\r\n";
                }
                
                $ical .= "SUMMARY:" . $event->title . "\r\n";
                
                if (!empty($event->description)) {
                    $ical .= "DESCRIPTION:" . str_replace("\n", "\\n", $event->description) . "\r\n";
                }
                
                if (!empty($event->location)) {
                    $ical .= "LOCATION:" . $event->location . "\r\n";
                }
                
                if (!empty($event->url)) {
                    $ical .= "URL:" . $event->url . "\r\n";
                }
                
                $ical .= "END:VEVENT\r\n";
            }
            
            $ical .= "END:VCALENDAR\r\n";
            
            return $ical;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::exportToIcal: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get events by show ID
     * 
     * @param int $showId Show ID
     * @return array Events associated with the show
     */
    public function getEventsByShowId($showId) {
        try {
            $this->db->query('SELECT e.*, c.name as calendar_name, c.color as calendar_color, 
                             v.name as venue_name, v.address as venue_address, 
                             v.city as venue_city, v.state as venue_state, 
                             v.zip as venue_zip, v.country as venue_country 
                             FROM calendar_events e 
                             LEFT JOIN calendars c ON e.calendar_id = c.id 
                             LEFT JOIN calendar_venues v ON e.venue_id = v.id 
                             WHERE e.show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventsByShowId: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get calendar settings
     * 
     * @param string $key Setting key (optional)
     * @return mixed Setting value or all settings
     */
    public function getCalendarSettings($key = null) {
        try {
            if ($key !== null) {
                $this->db->query('SELECT setting_value FROM calendar_settings WHERE setting_key = :key');
                $this->db->bind(':key', $key);
                
                $result = $this->db->single();
                return $result ? $result->setting_value : null;
            } else {
                $this->db->query('SELECT * FROM calendar_settings');
                
                $settings = [];
                $results = $this->db->resultSet();
                
                foreach ($results as $result) {
                    $settings[$result->setting_key] = $result->setting_value;
                }
                
                return $settings;
            }
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCalendarSettings: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Update calendar setting
     * 
     * @param string $key Setting key
     * @param string $value Setting value
     * @return bool True if successful, false otherwise
     */
    public function updateCalendarSetting($key, $value) {
        try {
            // Check if setting exists
            $this->db->query('SELECT * FROM calendar_settings WHERE setting_key = :key');
            $this->db->bind(':key', $key);
            
            if ($this->db->single()) {
                // Update existing setting
                $this->db->query('UPDATE calendar_settings SET setting_value = :value WHERE setting_key = :key');
                $this->db->bind(':key', $key);
                $this->db->bind(':value', $value);
            } else {
                // Insert new setting
                $this->db->query('INSERT INTO calendar_settings (setting_key, setting_value) VALUES (:key, :value)');
                $this->db->bind(':key', $key);
                $this->db->bind(':value', $value);
            }
            
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateCalendarSetting: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get US states for dropdown
     * 
     * @return array States
     */
    public function getUSStates() {
        return [
            'AL' => 'Alabama',
            'AK' => 'Alaska',
            'AZ' => 'Arizona',
            'AR' => 'Arkansas',
            'CA' => 'California',
            'CO' => 'Colorado',
            'CT' => 'Connecticut',
            'DE' => 'Delaware',
            'DC' => 'District of Columbia',
            'FL' => 'Florida',
            'GA' => 'Georgia',
            'HI' => 'Hawaii',
            'ID' => 'Idaho',
            'IL' => 'Illinois',
            'IN' => 'Indiana',
            'IA' => 'Iowa',
            'KS' => 'Kansas',
            'KY' => 'Kentucky',
            'LA' => 'Louisiana',
            'ME' => 'Maine',
            'MD' => 'Maryland',
            'MA' => 'Massachusetts',
            'MI' => 'Michigan',
            'MN' => 'Minnesota',
            'MS' => 'Mississippi',
            'MO' => 'Missouri',
            'MT' => 'Montana',
            'NE' => 'Nebraska',
            'NV' => 'Nevada',
            'NH' => 'New Hampshire',
            'NJ' => 'New Jersey',
            'NM' => 'New Mexico',
            'NY' => 'New York',
            'NC' => 'North Carolina',
            'ND' => 'North Dakota',
            'OH' => 'Ohio',
            'OK' => 'Oklahoma',
            'OR' => 'Oregon',
            'PA' => 'Pennsylvania',
            'RI' => 'Rhode Island',
            'SC' => 'South Carolina',
            'SD' => 'South Dakota',
            'TN' => 'Tennessee',
            'TX' => 'Texas',
            'UT' => 'Utah',
            'VT' => 'Vermont',
            'VA' => 'Virginia',
            'WA' => 'Washington',
            'WV' => 'West Virginia',
            'WI' => 'Wisconsin',
            'WY' => 'Wyoming'
        ];
    }
    
    /**
     * Get events within a radius of a point
     * 
     * @param float $lat Latitude
     * @param float $lng Longitude
     * @param int $radius Radius in miles
     * @param array $additionalFilters Additional filters
     * @param int $userId Optional user ID for permission checking
     * @return array Events
     */
    public function getEventsWithinRadius($lat, $lng, $radius = 100, $additionalFilters = [], $userId = null) {
        $filters = array_merge($additionalFilters, [
            'center_lat' => $lat,
            'center_lng' => $lng,
            'radius' => $radius
        ]);
        
        return $this->getEvents($filters, $userId);
    }
    
    /**
     * Get events with location data for map view
     *
     * @param array $filters Filter parameters
     * @return array
     */
    public function getEventsWithLocation($filters = [])
    {
        try {
            // Let's use a completely different approach to avoid parameter numbering issues
            // We'll use a direct SQL query with concatenated values instead of prepared statements
            // for the IN clause, and named parameters for the rest
            
            $sql = "SELECT e.*, c.name as calendar_name, s.name as show_name
                    FROM calendar_events e
                    LEFT JOIN calendars c ON e.calendar_id = c.id
                    LEFT JOIN shows s ON e.show_id = s.id";
            
            // Add join for club filtering if needed
            if (isset($filters['club_id']) || isset($filters['club_ids'])) {
                $sql .= " LEFT JOIN calendar_event_clubs ec ON e.id = ec.event_id";
            }
            
            $sql .= " WHERE 1=1";
            
            $params = [];
            
            // Filter by start date
            if (isset($filters['start'])) {
                $sql .= " AND e.end_date >= :start_date";
                $params[':start_date'] = $filters['start'];
            }
            
            // Filter by end date
            if (isset($filters['end'])) {
                // Standard date comparison - events that start on or before the end date
                $sql .= " AND e.start_date <= :end_date";
                $params[':end_date'] = $filters['end'];
                
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('CalendarModel::getEventsWithLocation - End date filter applied: ' . $filters['end']);
                }
            }
            
            // Filter by calendar ID
            if (isset($filters['calendar_id'])) {
                $sql .= " AND e.calendar_id = :single_calendar_id";
                $params[':single_calendar_id'] = $filters['calendar_id'];
            }
            
            // Filter by multiple calendar IDs - use direct SQL concatenation for the IN clause
            if (isset($filters['calendar_ids']) && is_array($filters['calendar_ids']) && !empty($filters['calendar_ids'])) {
                // Sanitize the calendar IDs (ensure they're all integers)
                $calendarIds = array_map(function($id) {
                    return (int)$id;
                }, $filters['calendar_ids']);
                
                // Join them with commas for the IN clause
                $calendarIdsStr = implode(',', $calendarIds);
                
                // Add directly to the SQL (safe because we've sanitized the values)
                $sql .= " AND e.calendar_id IN ($calendarIdsStr)";
            }
            
            // Filter by state
            if (isset($filters['state'])) {
                $sql .= " AND e.state = :state";
                $params[':state'] = $filters['state'];
            }
            
            // Filter by city
            if (isset($filters['city'])) {
                $sql .= " AND e.city = :city";
                $params[':city'] = $filters['city'];
            }
            
            // Filter by venue ID
            if (isset($filters['venue_id'])) {
                $sql .= " AND e.venue_id = :venue_id";
                $params[':venue_id'] = $filters['venue_id'];
            }
            
            // Filter by multiple venue IDs
            if (isset($filters['venue_ids']) && is_array($filters['venue_ids']) && !empty($filters['venue_ids'])) {
                // Sanitize the venue IDs (ensure they're all integers)
                $venueIds = array_map(function($id) {
                    return (int)$id;
                }, $filters['venue_ids']);
                
                // Join them with commas for the IN clause
                $venueIdsStr = implode(',', $venueIds);
                
                // Add directly to the SQL (safe because we've sanitized the values)
                $sql .= " AND e.venue_id IN ($venueIdsStr)";
            }
            
            // Filter by club ID
            if (isset($filters['club_id'])) {
                $sql .= " AND ec.club_id = :club_id";
                $params[':club_id'] = $filters['club_id'];
            }
            
            // Filter by multiple club IDs
            if (isset($filters['club_ids']) && is_array($filters['club_ids']) && !empty($filters['club_ids'])) {
                // Sanitize the club IDs (ensure they're all integers)
                $clubIds = array_map(function($id) {
                    return (int)$id;
                }, $filters['club_ids']);
                
                // Join them with commas for the IN clause
                $clubIdsStr = implode(',', $clubIds);
                
                // Add directly to the SQL (safe because we've sanitized the values)
                $sql .= " AND ec.club_id IN ($clubIdsStr)";
            }
            
            // Filter by event type/category (using title or description)
            if (isset($filters['keyword'])) {
                $sql .= " AND (e.title LIKE :keyword OR e.description LIKE :keyword)";
                $params[':keyword'] = '%' . $filters['keyword'] . '%';
            }
            
            // Location-based filtering using Haversine formula
            if (isset($filters['radius']) && isset($filters['lat']) && isset($filters['lng'])) {
                // Earth's radius in miles
                $earthRadius = 3959;
                
                // Use different parameter names to avoid any confusion
                $sql .= " AND (
                    $earthRadius * acos(
                        cos(radians(:lat_param)) * 
                        cos(radians(e.lat)) * 
                        cos(radians(e.lng) - radians(:lng_param)) + 
                        sin(radians(:lat_param2)) * 
                        sin(radians(e.lat))
                    ) <= :radius_param
                )";
                
                $params[':lat_param'] = $filters['lat'];
                $params[':lng_param'] = $filters['lng'];
                $params[':lat_param2'] = $filters['lat'];
                $params[':radius_param'] = $filters['radius'];
            }
            
            // Filter by show ID
            if (isset($filters['show_id'])) {
                $sql .= " AND e.show_id = :show_id";
                $params[':show_id'] = $filters['show_id'];
            }
            
            // Filter by privacy level
            if (isset($filters['privacy'])) {
                $sql .= " AND e.privacy = :privacy";
                $params[':privacy'] = $filters['privacy'];
            }
            
            // Only include events with location data if map view
            if (isset($filters['map_view']) && $filters['map_view']) {
                $sql .= " AND (e.address1 IS NOT NULL OR e.city IS NOT NULL OR e.state IS NOT NULL)";
                $sql .= " AND e.lat IS NOT NULL AND e.lng IS NOT NULL";
            }
            
            // Group by to avoid duplicates when joining with clubs
            if (isset($filters['club_id']) || isset($filters['club_ids'])) {
                $sql .= " GROUP BY e.id";
            }
            
            // Order by start date
            $sql .= " ORDER BY e.start_date ASC";
            
            // Add limit if specified
            if (isset($filters['limit']) && is_numeric($filters['limit'])) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = (int)$filters['limit'];
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarModel::getEventsWithLocation - SQL: " . $sql);
                error_log("CalendarModel::getEventsWithLocation - Params: " . json_encode($params));
            }
            
            $this->db->query($sql);
            
            // Bind all parameters
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            $results = $this->db->resultSet();
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarModel::getEventsWithLocation - Found " . count($results) . " events");
            }
            
            return $results;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventsWithLocation: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get states with event counts
     *
     * @return array
     */
    public function getStatesWithEventCounts()
    {
        try {
            $sql = "SELECT state, COUNT(*) as event_count
                    FROM calendar_events
                    WHERE state IS NOT NULL AND state != ''
                    GROUP BY state
                    ORDER BY state ASC";
            
            $this->db->query($sql);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getStatesWithEventCounts: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get cities with event counts
     *
     * @param string $state Optional state to filter by
     * @return array
     */
    public function getCitiesWithEventCounts($state = null)
    {
        try {
            $sql = "SELECT city, COUNT(*) as event_count
                    FROM calendar_events
                    WHERE city IS NOT NULL AND city != ''";
            
            $params = [];
            
            if ($state) {
                $sql .= " AND state = :state";
                $params[':state'] = $state;
            }
            
            $sql .= " GROUP BY city
                      ORDER BY city ASC";
            
            $this->db->query($sql);
            
            // Bind parameters if any
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCitiesWithEventCounts: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get venues with event counts
     *
     * @param string $state Optional state to filter by
     * @param string $city Optional city to filter by
     * @return array
     */
    public function getVenuesWithEventCounts($state = null, $city = null)
    {
        try {
            $sql = "SELECT v.id, v.name, COUNT(e.id) as event_count
                    FROM calendar_venues v
                    LEFT JOIN calendar_events e ON v.id = e.venue_id
                    WHERE 1=1";
            
            $params = [];
            
            if ($state) {
                $sql .= " AND v.state = :state";
                $params[':state'] = $state;
            }
            
            if ($city) {
                $sql .= " AND v.city = :city";
                $params[':city'] = $city;
            }
            
            $sql .= " GROUP BY v.id
                      ORDER BY v.name ASC";
            
            $this->db->query($sql);
            
            // Bind parameters if any
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getVenuesWithEventCounts: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get clubs with event counts
     *
     * @return array
     */
    public function getClubsWithEventCounts()
    {
        try {
            $sql = "SELECT c.id, c.name, COUNT(ec.event_id) as event_count
                    FROM calendar_clubs c
                    LEFT JOIN calendar_event_clubs ec ON c.id = ec.club_id
                    GROUP BY c.id
                    ORDER BY c.name ASC";
            
            $this->db->query($sql);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubsWithEventCounts: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get calendar display settings
     *
     * @return array
     */
    public function getCalendarDisplaySettings()
    {
        try {
            $settings = [
                'default_view' => 'month',
                'start_day' => '0',
                'time_format' => '12',
                'date_format' => 'MM/DD/YYYY',
                'events_per_page' => '10',
                // Event Chart Settings defaults
                'event_show_weekends' => '1',
                'event_enable_drag_drop' => '0',
                'event_show_today_line' => '0',
                'event_show_event_hover' => '1',
                'event_mobile_breakpoint' => '992'
            ];
            
            // Get settings from database
            $this->db->query("SELECT setting_key, setting_value FROM calendar_settings 
                             WHERE setting_key IN ('calendar_default_view', 'calendar_start_day', 
                                                 'calendar_time_format', 'calendar_date_format', 
                                                 'calendar_events_per_page', 'event_show_weekends',
                                                 'event_enable_drag_drop', 'event_show_today_line',
                                                 'event_show_event_hover', 'event_mobile_breakpoint')");
            $results = $this->db->resultSet();
            
            // Map database settings to array
            foreach ($results as $result) {
                if (strpos($result->setting_key, 'calendar_') === 0) {
                    $key = str_replace('calendar_', '', $result->setting_key);
                    $settings[$key] = $result->setting_value;
                } else {
                    // Event settings keep their full key name
                    $settings[$result->setting_key] = $result->setting_value;
                }
            }
            
            return $settings;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCalendarDisplaySettings: ' . $e->getMessage());
            return [
                'default_view' => 'month',
                'start_day' => '0',
                'time_format' => '12',
                'date_format' => 'MM/DD/YYYY',
                'events_per_page' => '10',
                // Event Chart Settings defaults
                'event_show_weekends' => '1',
                'event_enable_drag_drop' => '0',
                'event_show_today_line' => '0',
                'event_show_event_hover' => '1',
                'event_mobile_breakpoint' => '992'
            ];
        }
    }
    
    /**
     * Update calendar settings
     *
     * @param array $settings Calendar settings
     * @return bool
     */
    public function updateCalendarSettings($settings)
    {
        try {
            // Map settings to database keys
            $dbSettings = [
                'calendar_default_view' => $settings['default_view'],
                'calendar_start_day' => $settings['start_day'],
                'calendar_time_format' => $settings['time_format'],
                'calendar_date_format' => $settings['date_format'],
                'calendar_events_per_page' => $settings['events_per_page']
            ];
            
            // Add Event chart settings if they exist
            if (isset($settings['event_show_weekends'])) {
                $dbSettings['event_show_weekends'] = $settings['event_show_weekends'];
            }
            if (isset($settings['event_enable_drag_drop'])) {
                $dbSettings['event_enable_drag_drop'] = $settings['event_enable_drag_drop'];
            }
            if (isset($settings['event_show_today_line'])) {
                $dbSettings['event_show_today_line'] = $settings['event_show_today_line'];
            }
            if (isset($settings['event_show_event_hover'])) {
                $dbSettings['event_show_event_hover'] = $settings['event_show_event_hover'];
            }
            if (isset($settings['event_mobile_breakpoint'])) {
                $dbSettings['event_mobile_breakpoint'] = $settings['event_mobile_breakpoint'];
            }
            
            // Update each setting
            foreach ($dbSettings as $key => $value) {
                // Check if setting exists
                $this->db->query("SELECT COUNT(*) as count FROM calendar_settings WHERE setting_key = :key");
                $this->db->bind(':key', $key);
                $result = $this->db->single();
                
                if ($result->count > 0) {
                    // Update existing setting
                    $this->db->query("UPDATE calendar_settings SET setting_value = :value WHERE setting_key = :key");
                } else {
                    // Insert new setting
                    $this->db->query("INSERT INTO calendar_settings (setting_key, setting_value) VALUES (:key, :value)");
                }
                
                $this->db->bind(':key', $key);
                $this->db->bind(':value', $value);
                $this->db->execute();
            }
            
            return true;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateCalendarSettings: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get map provider settings
     *
     * @return array
     */
    public function getMapProviderSettings()
    {
        try {
            $settings = $this->getCalendarSettings();
            
            // Default map provider settings
            $mapProviderSettings = [
                'provider' => 'google', // Default provider
                'api_key' => '',        // API key for the provider (client-side)
                'server_api_key' => '', // Server-side API key for Google Places/Geocoding
                'default_zoom' => 4,
                'default_lat' => 39.8283,
                'default_lng' => -98.5795,
                'filter_radius' => 100,
                'tile_url' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', // For OpenStreetMap
                'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            ];
            
            // Override with database settings if available
            foreach ($mapProviderSettings as $key => $value) {
                $settingKey = 'map_' . $key;
                if (isset($settings[$settingKey])) {
                    $mapProviderSettings[$key] = $settings[$settingKey];
                }
            }
            
            return $mapProviderSettings;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getMapProviderSettings: ' . $e->getMessage());
            return [
                'provider' => 'openstreetmap', // Fallback to free provider
                'api_key' => '',
                'server_api_key' => '',
                'default_zoom' => 4,
                'default_lat' => 39.8283,
                'default_lng' => -98.5795,
                'filter_radius' => 100,
                'tile_url' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            ];
        }
    }
    
    /**
     * Get venues without coordinates
     *
     * @return array
     */
    public function getVenuesWithoutCoordinates()
    {
        try {
            $this->db->query("SELECT * FROM calendar_venues WHERE latitude IS NULL OR longitude IS NULL OR latitude = '' OR longitude = ''");
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getVenuesWithoutCoordinates: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get events without coordinates
     *
     * @return array
     */
    public function getEventsWithoutCoordinates()
    {
        try {
            $this->db->query("SELECT id, title, location, address1, city, state, zipcode FROM calendar_events 
                              WHERE (lat IS NULL OR lng IS NULL OR lat = '' OR lng = '') 
                              AND (address1 IS NOT NULL OR city IS NOT NULL OR state IS NOT NULL OR location IS NOT NULL)");
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventsWithoutCoordinates: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update venue coordinates
     *
     * @param int $id Venue ID
     * @param float $latitude Latitude
     * @param float $longitude Longitude
     * @return bool
     */
    public function updateVenueCoordinates($id, $latitude, $longitude)
    {
        try {
            $this->db->query("UPDATE calendar_venues SET latitude = :latitude, longitude = :longitude WHERE id = :id");
            $this->db->bind(':id', $id);
            $this->db->bind(':latitude', $latitude);
            $this->db->bind(':longitude', $longitude);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateVenueCoordinates: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update event coordinates
     *
     * @param int $id Event ID
     * @param float $lat Latitude
     * @param float $lng Longitude
     * @return bool
     */
    public function updateEventCoordinates($id, $lat, $lng)
    {
        try {
            $this->db->query("UPDATE calendar_events SET lat = :lat, lng = :lng WHERE id = :id");
            $this->db->bind(':id', $id);
            $this->db->bind(':lat', $lat);
            $this->db->bind(':lng', $lng);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateEventCoordinates: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update map provider settings
     *
     * @param array $settings Map provider settings
     * @return bool
     */
    public function updateMapProviderSettings($settings)
    {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Map provider settings to update
            $mapSettings = [
                'provider', 'api_key', 'server_api_key', 'default_zoom', 'default_lat', 
                'default_lng', 'filter_radius', 'tile_url', 'attribution'
            ];
            
            foreach ($mapSettings as $key) {
                if (isset($settings[$key])) {
                    $settingKey = 'map_' . $key;
                    $value = $settings[$key];
                    
                    // Check if setting exists
                    $this->db->query("SELECT * FROM calendar_settings WHERE setting_key = :key");
                    $this->db->bind(':key', $settingKey);
                    $this->db->execute();
                    $exists = $this->db->rowCount() > 0;
                    
                    if ($exists) {
                        // Update existing setting
                        $this->db->query("UPDATE calendar_settings SET setting_value = :value WHERE setting_key = :key");
                        $this->db->bind(':key', $settingKey);
                        $this->db->bind(':value', $value);
                        $this->db->execute();
                    } else {
                        // Insert new setting
                        $this->db->query("INSERT INTO calendar_settings (setting_key, setting_value) VALUES (:key, :value)");
                        $this->db->bind(':key', $settingKey);
                        $this->db->bind(':value', $value);
                        $this->db->execute();
                    }
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollBack();
            error_log('Error in CalendarModel::updateMapProviderSettings: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get marker customization settings
     * 
     * @return array
     */
    public function getMarkerCustomizationSettings()
    {
        try {
            $settings = $this->getCalendarSettings();
            
            // Default marker customization settings
            $markerSettings = [
                'marker_type' => 'default',       // default, pin, custom
                'marker_size' => 16,              // size in pixels
                'marker_border' => 'true',        // true/false
                'marker_border_color' => '#FFFFFF', // border color
                'marker_border_width' => 2,       // border width in pixels
                'custom_marker_url' => '',        // URL for custom marker image
                'use_calendar_colors' => 'true',  // true/false
                'default_marker_color' => '#3788d8' // default color if not using calendar colors
            ];
            
            // Override with database settings if available
            foreach ($markerSettings as $key => $value) {
                if (isset($settings[$key])) {
                    $markerSettings[$key] = $settings[$key];
                }
            }
            
            return $markerSettings;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getMarkerCustomizationSettings: ' . $e->getMessage());
            return [
                'marker_type' => 'default',
                'marker_size' => 16,
                'marker_border' => 'true',
                'marker_border_color' => '#FFFFFF',
                'marker_border_width' => 2,
                'custom_marker_url' => '',
                'use_calendar_colors' => 'true',
                'default_marker_color' => '#3788d8'
            ];
        }
    }
    
    /**
     * Update marker customization settings
     * 
     * @param array $settings
     * @return bool
     */
    public function updateMarkerCustomizationSettings($settings)
    {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Debug log
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Updating marker settings: ' . json_encode($settings));
            }
            
            // Marker settings to update
            $markerSettings = [
                'marker_type', 'marker_size', 'marker_border', 'marker_border_color',
                'marker_border_width', 'custom_marker_url', 'use_calendar_colors', 'default_marker_color'
            ];
            
            foreach ($markerSettings as $key) {
                if (isset($settings[$key])) {
                    $value = $settings[$key];
                    
                    // Debug log
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("Setting $key = $value");
                    }
                    
                    // Check if setting exists
                    $this->db->query("SELECT * FROM calendar_settings WHERE setting_key = :key");
                    $this->db->bind(':key', $key);
                    $this->db->execute();
                    $exists = $this->db->rowCount() > 0;
                    
                    if ($exists) {
                        // Update existing setting
                        $this->db->query("UPDATE calendar_settings SET setting_value = :value WHERE setting_key = :key");
                        $this->db->bind(':key', $key);
                        $this->db->bind(':value', $value);
                        $this->db->execute();
                    } else {
                        // Insert new setting
                        $this->db->query("INSERT INTO calendar_settings (setting_key, setting_value) VALUES (:key, :value)");
                        $this->db->bind(':key', $key);
                        $this->db->bind(':value', $value);
                        $this->db->execute();
                    }
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            // Debug log
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Marker settings updated successfully');
            }
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollBack();
            error_log('Error in CalendarModel::updateMarkerCustomizationSettings: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * Update event location data
     *
     * @param int $eventId Event ID
     * @param array $locationData Location data
     * @return bool
     */
    public function updateEventLocation($eventId, $locationData)
    {
        try {
            $sql = "UPDATE calendar_events
                    SET address1 = :address1,
                        address2 = :address2,
                        city = :city,
                        state = :state,
                        zipcode = :zipcode,
                        lat = :lat,
                        lng = :lng
                    WHERE id = :id";
            
            $this->db->query($sql, [
                ':id' => $eventId,
                ':address1' => $locationData['address1'],
                ':address2' => $locationData['address2'],
                ':city' => $locationData['city'],
                ':state' => $locationData['state'],
                ':zipcode' => $locationData['zipcode'],
                ':lat' => $locationData['lat'],
                ':lng' => $locationData['lng']
            ]);
            
            return $this->db->rowCount() > 0;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::updateEventLocation: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all event categories with event counts
     * 
     * @return array
     */
    public function getCategories() {
        try {
            // Check if the event_categories table exists
            $this->db->query("SHOW TABLES LIKE 'event_categories'");
            $this->db->execute();
            
            if ($this->db->rowCount() === 0) {
                // Table doesn't exist, create it
                $this->db->query("
                    CREATE TABLE event_categories (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ");
                $this->db->execute();
                
                // Create mapping table if it doesn't exist
                $this->db->query("SHOW TABLES LIKE 'event_category_mapping'");
                $this->db->execute();
                
                if ($this->db->rowCount() === 0) {
                    $this->db->query("
                        CREATE TABLE event_category_mapping (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            event_id INT NOT NULL,
                            category_id INT NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE KEY unique_event_category (event_id, category_id),
                            FOREIGN KEY (category_id) REFERENCES event_categories(id) ON DELETE CASCADE
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");
                    $this->db->execute();
                }
                
                // Return empty array since we just created the tables
                return [];
            }
            
            // Get categories with event counts
            $this->db->query("
                SELECT c.id, c.name, COUNT(DISTINCT ec.event_id) as event_count
                FROM event_categories c
                LEFT JOIN event_category_mapping ec ON c.id = ec.category_id
                GROUP BY c.id
                ORDER BY c.name ASC
            ");
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getCategories: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all event tags with event counts
     * 
     * @return array
     */
    public function getTags() {
        try {
            // Check if the event_tags table exists
            $this->db->query("SHOW TABLES LIKE 'event_tags'");
            $this->db->execute();
            
            if ($this->db->rowCount() === 0) {
                // Table doesn't exist, create it
                $this->db->query("
                    CREATE TABLE event_tags (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ");
                $this->db->execute();
                
                // Create mapping table if it doesn't exist
                $this->db->query("SHOW TABLES LIKE 'event_tag_mapping'");
                $this->db->execute();
                
                if ($this->db->rowCount() === 0) {
                    $this->db->query("
                        CREATE TABLE event_tag_mapping (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            event_id INT NOT NULL,
                            tag_id INT NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE KEY unique_event_tag (event_id, tag_id),
                            FOREIGN KEY (tag_id) REFERENCES event_tags(id) ON DELETE CASCADE
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");
                    $this->db->execute();
                }
                
                // Return empty array since we just created the tables
                return [];
            }
            
            // Get tags with event counts
            $this->db->query("
                SELECT t.id, t.name, COUNT(DISTINCT et.event_id) as event_count
                FROM event_tags t
                LEFT JOIN event_tag_mapping et ON t.id = et.tag_id
                GROUP BY t.id
                ORDER BY t.name ASC
            ");
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getTags: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Submit club ownership verification request
     * 
     * @param array $data Verification request data
     * @return int|bool Verification request ID or false if failed
     */
    public function submitClubOwnershipVerification($data) {
        try {
            $this->db->query('INSERT INTO club_ownership_verifications (
                                club_id, user_id, request_type, user_name, user_email, user_phone,
                                club_information, verification_documents, additional_info
                             ) VALUES (
                                :club_id, :user_id, :request_type, :user_name, :user_email, :user_phone,
                                :club_information, :verification_documents, :additional_info
                             )');
            
            $this->db->bind(':club_id', $data['club_id']);
            $this->db->bind(':user_id', $data['user_id']);
            $this->db->bind(':request_type', $data['request_type'] ?? 'new_ownership');
            $this->db->bind(':user_name', $data['user_name']);
            $this->db->bind(':user_email', $data['user_email']);
            $this->db->bind(':user_phone', $data['user_phone'] ?? null);
            $this->db->bind(':club_information', $data['club_information']);
            $this->db->bind(':verification_documents', $data['verification_documents'] ?? null);
            $this->db->bind(':additional_info', $data['additional_info'] ?? null);
            
            if ($this->db->execute()) {
                $verificationId = $this->db->lastInsertId();
                
                // Update club verification status to pending
                $this->db->query('UPDATE calendar_clubs 
                                 SET verification_status = "pending", 
                                     verification_requested_at = NOW() 
                                 WHERE id = :club_id');
                $this->db->bind(':club_id', $data['club_id']);
                $this->db->execute();
                
                return $verificationId;
            }
            
            return false;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::submitClubOwnershipVerification: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all club ownership verification requests
     * 
     * @param string $status Filter by status (pending, approved, denied)
     * @return array Verification requests
     */
    public function getClubOwnershipVerifications($status = null) {
        try {
            $sql = 'SELECT v.*, c.name as club_name, c.logo as club_logo, u.name as reviewer_name
                    FROM club_ownership_verifications v
                    LEFT JOIN calendar_clubs c ON v.club_id = c.id
                    LEFT JOIN users u ON v.reviewed_by = u.id';
            
            if ($status) {
                $sql .= ' WHERE v.status = :status';
            }
            
            $sql .= ' ORDER BY v.requested_at DESC';
            
            $this->db->query($sql);
            
            if ($status) {
                $this->db->bind(':status', $status);
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubOwnershipVerifications: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get club ownership verification request by ID
     * 
     * @param int $id Verification request ID
     * @return object|bool Verification request or false if not found
     */
    public function getClubOwnershipVerificationById($id) {
        try {
            $this->db->query('SELECT v.*, c.name as club_name, c.logo as club_logo, 
                                    c.description as club_description, c.website as club_website,
                                    c.email as club_email, c.phone as club_phone,
                                    u.name as reviewer_name, req_user.name as requester_name
                             FROM club_ownership_verifications v
                             LEFT JOIN calendar_clubs c ON v.club_id = c.id
                             LEFT JOIN users u ON v.reviewed_by = u.id
                             LEFT JOIN users req_user ON v.user_id = req_user.id
                             WHERE v.id = :id');
            $this->db->bind(':id', $id);
            
            return $this->db->single();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubOwnershipVerificationById: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get existing club ownership verification request for user and club
     * 
     * @param int $clubId Club ID
     * @param int $userId User ID
     * @return object|false Verification object or false if not found
     */
    public function getClubOwnershipRequest($clubId, $userId) {
        try {
            $this->db->query('SELECT * FROM club_ownership_verifications 
                             WHERE club_id = :club_id AND user_id = :user_id 
                             ORDER BY requested_at DESC LIMIT 1');
            $this->db->bind(':club_id', $clubId);
            $this->db->bind(':user_id', $userId);
            
            return $this->db->single();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getClubOwnershipRequest: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Approve club ownership verification
     * 
     * @param int $verificationId Verification request ID
     * @param int $reviewerId Admin user ID who approved
     * @param string $notes Admin notes
     * @return bool True if successful, false otherwise
     */
    public function approveClubOwnershipVerification($verificationId, $reviewerId, $notes = '') {
        try {
            $this->db->beginTransaction();
            
            // Get verification request details
            $verification = $this->getClubOwnershipVerificationById($verificationId);
            if (!$verification) {
                $this->db->rollBack();
                return false;
            }
            
            // Update verification request
            $this->db->query('UPDATE club_ownership_verifications 
                             SET status = "approved", 
                                 reviewed_at = NOW(), 
                                 reviewed_by = :reviewer_id,
                                 admin_notes = :notes
                             WHERE id = :id');
            $this->db->bind(':id', $verificationId);
            $this->db->bind(':reviewer_id', $reviewerId);
            $this->db->bind(':notes', $notes);
            $this->db->execute();
            
            // Update club with new owner
            $this->db->query('UPDATE calendar_clubs 
                             SET owner_id = :owner_id, 
                                 is_verified = 1, 
                                 verification_status = "approved",
                                 verification_notes = :notes
                             WHERE id = :club_id');
            $this->db->bind(':owner_id', $verification->user_id);
            $this->db->bind(':club_id', $verification->club_id);
            $this->db->bind(':notes', $notes);
            $this->db->execute();
            
            // Add user as owner member if not already a member
            $this->addClubMember($verification->club_id, $verification->user_id, 'owner');
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error in CalendarModel::approveClubOwnershipVerification: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Deny club ownership verification
     * 
     * @param int $verificationId Verification request ID
     * @param int $reviewerId Admin user ID who denied
     * @param string $notes Admin notes explaining denial
     * @return bool True if successful, false otherwise
     */
    public function denyClubOwnershipVerification($verificationId, $reviewerId, $notes = '') {
        try {
            $this->db->beginTransaction();
            
            // Get verification request details
            $verification = $this->getClubOwnershipVerificationById($verificationId);
            if (!$verification) {
                $this->db->rollBack();
                return false;
            }
            
            // Update verification request
            $this->db->query('UPDATE club_ownership_verifications 
                             SET status = "denied", 
                                 reviewed_at = NOW(), 
                                 reviewed_by = :reviewer_id,
                                 admin_notes = :notes
                             WHERE id = :id');
            $this->db->bind(':id', $verificationId);
            $this->db->bind(':reviewer_id', $reviewerId);
            $this->db->bind(':notes', $notes);
            $this->db->execute();
            
            // Update club verification status
            $this->db->query('UPDATE calendar_clubs 
                             SET verification_status = "denied",
                                 verification_notes = :notes
                             WHERE id = :club_id');
            $this->db->bind(':club_id', $verification->club_id);
            $this->db->bind(':notes', $notes);
            $this->db->execute();
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error in CalendarModel::denyClubOwnershipVerification: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if user has pending verification request for a club
     * 
     * @param int $clubId Club ID
     * @param int $userId User ID
     * @return bool True if has pending request, false otherwise
     */
    public function hasPendingClubVerificationRequest($clubId, $userId) {
        try {
            $this->db->query('SELECT COUNT(*) as count 
                             FROM club_ownership_verifications 
                             WHERE club_id = :club_id AND user_id = :user_id AND status = "pending"');
            $this->db->bind(':club_id', $clubId);
            $this->db->bind(':user_id', $userId);
            
            $result = $this->db->single();
            return $result && $result->count > 0;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::hasPendingClubVerificationRequest: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get verification statistics
     * 
     * @return array Verification statistics
     */
    public function getVerificationStatistics() {
        try {
            $stats = [
                'pending' => 0,
                'approved' => 0,
                'denied' => 0,
                'total' => 0
            ];
            
            $this->db->query('SELECT status, COUNT(*) as count 
                             FROM club_ownership_verifications 
                             GROUP BY status');
            $results = $this->db->resultSet();
            
            foreach ($results as $result) {
                $stats[$result->status] = $result->count;
                $stats['total'] += $result->count;
            }
            
            return $stats;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getVerificationStatistics: ' . $e->getMessage());
            return [
                'pending' => 0,
                'approved' => 0,
                'denied' => 0,
                'total' => 0
            ];
        }
    }
    
}