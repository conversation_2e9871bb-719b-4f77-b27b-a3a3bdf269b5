<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Manage Vehicle Images</h1>
            <p class="text-muted">
                <?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?>
            </p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/user/vehicles" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to My Vehicles
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Vehicle Details</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <?php if (isset($primary_image)): ?>
                        <img src="<?php 
                            if (isset($primary_image->file_path)) {
                                echo BASE_URL . '/' . $primary_image->file_path;
                            } elseif (isset($primary_image->image_path)) {
                                echo BASE_URL . '/' . $primary_image->image_path;
                            } elseif (isset($primary_image->filename)) {
                                echo BASE_URL . '/uploads/vehicles/' . $primary_image->filename;
                            } elseif (isset($primary_image->file_name)) {
                                echo BASE_URL . '/uploads/vehicles/' . $primary_image->file_name;
                            } elseif (!empty($vehicle->primary_image)) {
                                echo BASE_URL . '/uploads/vehicles/' . $vehicle->primary_image;
                            } else {
                                echo BASE_URL . '/uploads/vehicles/default.png';
                            }
                        ?>" class="img-fluid rounded" alt="<?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?>">
                        <?php else: ?>
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-car fa-4x text-secondary"></i>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <h4><?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?></h4>
                    
                    <div class="mb-3">
                        <strong>Color:</strong> <?php echo $vehicle->color; ?>
                    </div>
                    
                    <div class="mb-3">
                        <strong>License Plate:</strong> <?php echo $vehicle->license_plate; ?>
                    </div>
                    
                    <div class="mb-3">
                        <strong>VIN:</strong> 
                        <?php if (!empty($vehicle->vin)): ?>
                        <?php echo $vehicle->vin; ?>
                        <?php else: ?>
                        <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (!empty($vehicle->modifications)): ?>
                    <div class="mb-3">
                        <strong>Modifications:</strong>
                        <p><?php echo nl2br($vehicle->modifications); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <a href="<?php echo BASE_URL; ?>/user/vehicles/edit/<?php echo $vehicle->id; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i> Edit Vehicle Details
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Image Tips</h5>
                </div>
                <div class="card-body">
                    <h6>For Best Results:</h6>
                    <ul>
                        <li>Use high-resolution images (at least 1200x800 pixels)</li>
                        <li>Take photos in good lighting conditions</li>
                        <li>Include exterior, interior, and engine bay photos</li>
                        <li>Capture unique features and modifications</li>
                        <li>Keep file size under 5MB per image</li>
                        <li>Use JPG or PNG format</li>
                    </ul>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> Good quality images can significantly improve your chances of winning awards!
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Vehicle Images</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <a href="<?php echo BASE_URL; ?>/image_editor/upload/vehicle/<?php echo $vehicle->id; ?>" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-cloud-upload-alt me-2"></i> Upload Images
                            </a>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info btn-lg w-100" data-camera-capture="camera-upload" data-entity-type="vehicle" data-entity-id="<?php echo $vehicle->id; ?>" data-csrf-token="<?php echo generateCsrfToken(); ?>">
                                <i class="fas fa-camera me-2"></i> Take Photo
                            </button>
                        </div>
                        <div class="col-md-4">
                            <a href="<?php echo BASE_URL; ?>/image_editor/vehicle/<?php echo $vehicle->id; ?>" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-edit me-2"></i> Use Image Editor
                            </a>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> <strong>New!</strong> Try our advanced Image Editor to enhance your vehicle photos with professional editing tools.
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Vehicle Images</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <a href="<?php echo BASE_URL; ?>/image_editor/vehicle/<?php echo $vehicle->id; ?>" class="btn btn-success">
                            <i class="fas fa-magic me-2"></i> Advanced Image Editor
                        </a>
                        <div class="form-text">Try our new advanced image editor with professional editing tools!</div>
                    </div>
                    
                    <?php if (empty($images)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No images have been uploaded for this vehicle yet.
                    </div>
                    <?php else: ?>
                    
                    <?php 
                    // Find primary image
                    $primaryImage = null;
                    $otherImages = [];
                    
                    foreach ($images as $image) {
                        if (isset($image->is_primary) && $image->is_primary) {
                            $primaryImage = $image;
                        } else {
                            $otherImages[] = $image;
                        }
                    }
                    
                    // If no primary image is set, use the first image
                    if (!$primaryImage && count($images) > 0) {
                        $primaryImage = $images[0];
                    }
                    ?>
                    
                    <?php if ($primaryImage): ?>
                    <!-- Primary Image -->
                    <div class="mb-4">
                        <h5 class="text-muted mb-3">Primary Image</h5>
                        <div class="text-center">
                            <a href="<?php 
                               if (isset($primaryImage->file_path)) {
                                   echo BASE_URL . '/' . $primaryImage->file_path;
                               } elseif (isset($primaryImage->image_path)) {
                                   echo BASE_URL . '/' . $primaryImage->image_path;
                               } elseif (isset($primaryImage->filename)) {
                                   echo BASE_URL . '/uploads/vehicles/' . $primaryImage->filename;
                               } elseif (isset($primaryImage->file_name)) {
                                   echo BASE_URL . '/uploads/vehicles/' . $primaryImage->file_name;
                               } else {
                                   echo BASE_URL . '/uploads/vehicles/default.png';
                               }
                               ?>" 
                               data-image-viewer 
                               data-gallery-id="vehicle-gallery-<?php echo $vehicle->id; ?>"
                               data-title="<?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?> - Primary Image">
                                <img src="<?php 
                                     if (isset($primaryImage->file_path)) {
                                         echo BASE_URL . '/' . $primaryImage->file_path;
                                     } elseif (isset($primaryImage->image_path)) {
                                         echo BASE_URL . '/' . $primaryImage->image_path;
                                     } elseif (isset($primaryImage->filename)) {
                                         echo BASE_URL . '/uploads/vehicles/' . $primaryImage->filename;
                                     } elseif (isset($primaryImage->file_name)) {
                                         echo BASE_URL . '/uploads/vehicles/' . $primaryImage->file_name;
                                     } else {
                                         echo BASE_URL . '/uploads/vehicles/default.png';
                                     }
                                     ?>" 
                                     class="img-fluid rounded shadow" 
                                     alt="<?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?> - Primary Image"
                                     style="max-height: 400px; object-fit: contain;">
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <h5 class="text-muted mb-3">All Images</h5>
                    <div class="row" id="image-gallery">
                        <?php foreach ($images as $image): ?>
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <a href="<?php 
                                   if (isset($image->file_path)) {
                                       echo BASE_URL . '/' . $image->file_path;
                                   } elseif (isset($image->image_path)) {
                                       echo BASE_URL . '/' . $image->image_path;
                                   } elseif (isset($image->filename)) {
                                       echo BASE_URL . '/uploads/vehicles/' . $image->filename;
                                   } elseif (isset($image->file_name)) {
                                       echo BASE_URL . '/uploads/vehicles/' . $image->file_name;
                                   } else {
                                       echo BASE_URL . '/uploads/vehicles/default.png';
                                   }
                                   ?>" 
                                   data-image-viewer
                                   data-gallery-id="vehicle-gallery-<?php echo $vehicle->id; ?>"
                                   data-title="<?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?> - <?php echo isset($image->description) ? $image->description : ''; ?>">
                                    <img src="<?php 
                                         // For thumbnails, check if there's a thumbnail version or use the original
                                         if (isset($image->file_path)) {
                                             // For new system, use the original image since thumbnails might not exist
                                             echo BASE_URL . '/' . $image->file_path;
                                         } elseif (isset($image->image_path)) {
                                             // For old system, check if thumbnail exists
                                             $thumbPath = str_replace('/uploads/vehicles/', '/uploads/vehicles/thumbnails/', $image->image_path);
                                             if (file_exists($thumbPath)) {
                                                 echo BASE_URL . '/' . $thumbPath;
                                             } else {
                                                 echo BASE_URL . '/' . $image->image_path;
                                             }
                                         } elseif (isset($image->filename)) {
                                             echo BASE_URL . '/uploads/vehicles/thumbnails/' . $image->filename;
                                         } elseif (isset($image->file_name)) {
                                             echo BASE_URL . '/uploads/vehicles/thumbnails/' . $image->file_name;
                                         } else {
                                             echo BASE_URL . '/uploads/vehicles/default.png';
                                         }
                                         ?>" 
                                         class="card-img-top" 
                                         alt="Vehicle Image" 
                                         style="height: 150px; object-fit: cover;">
                                </a>
                                <div class="card-body">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input primary-image-radio" type="radio" name="primary_image" id="primary_image_<?php echo $image->id; ?>" value="<?php echo $image->id; ?>" <?php echo ($image->is_primary) ? 'checked' : ''; ?> data-image-id="<?php echo $image->id; ?>">
                                        <label class="form-check-label" for="primary_image_<?php echo $image->id; ?>">
                                            Set as primary image
                                        </label>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">Uploaded: <?php echo formatDateTimeForUser($image->created_at, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></small>
                                        <button type="button" class="btn btn-sm btn-danger delete-image-btn" data-bs-toggle="modal" data-bs-target="#deleteImageModal" data-image-id="<?php echo $image->id; ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Image Modal -->
<div class="modal fade" id="deleteImageModal" tabindex="-1" aria-labelledby="deleteImageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteImageModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this image? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> If you delete the primary image, you'll need to select a new one.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteImageForm" action="<?php echo BASE_URL; ?>/user/vehicles/deleteImage" method="post">
                    <input type="hidden" name="image_id" id="delete_image_id" value="">
                    <input type="hidden" name="vehicle_id" value="<?php echo $vehicle->id; ?>">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete Image</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        
        // Handle delete image button
        const deleteButtons = document.querySelectorAll('.delete-image-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const imageId = this.getAttribute('data-image-id');
                document.getElementById('delete_image_id').value = imageId;
            });
        });
        
        // Handle primary image selection
        const primaryImageRadios = document.querySelectorAll('.primary-image-radio');
        primaryImageRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    const imageId = this.getAttribute('data-image-id');
                    const vehicleId = <?php echo $vehicle->id; ?>;
                    
                    // Send AJAX request to update primary image
                    fetch('<?php echo BASE_URL; ?>/user/vehicles/setPrimaryImage', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `image_id=${imageId}&vehicle_id=${vehicleId}&<?php echo CSRF_TOKEN_NAME; ?>=${encodeURIComponent('<?php echo $csrf_token; ?>')}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            const alertDiv = document.createElement('div');
                            alertDiv.className = 'alert alert-success alert-dismissible fade show';
                            alertDiv.innerHTML = `
                                <i class="fas fa-check-circle me-2"></i> Primary image updated successfully.
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            `;
                            document.querySelector('.container').prepend(alertDiv);
                            
                            // Auto-dismiss after 3 seconds
                            setTimeout(() => {
                                const bsAlert = new bootstrap.Alert(alertDiv);
                                bsAlert.close();
                            }, 3000);
                        } else {
                            alert('Error updating primary image. Please try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                    });
                }
            });
        });
    });
</script>

<script>
    // Initialize image viewer when the page is fully loaded
    window.addEventListener('load', function() {
        console.log('Vehicle images: Page fully loaded, initializing image viewer');
        if (typeof initImageViewer === 'function') {
            initImageViewer();
        } else {
            console.error('Vehicle images: initImageViewer function not found');
        }
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>