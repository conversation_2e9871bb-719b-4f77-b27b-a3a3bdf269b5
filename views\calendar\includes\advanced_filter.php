<?php
/**
 * Advanced Filter Component for Calendar Views
 * 
 * This component provides a consistent filtering interface across all calendar views
 * (month, week, day, list, and map).
 */
?>
<div class="card mb-4">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i> Advanced Filters
        </h5>
        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
            <i class="fas fa-chevron-down"></i>
        </button>
    </div>
    <div class="collapse" id="filterCollapse">
        <div class="card-body">
            <form id="advanced-filter-form">
                <!-- Date Range Filter -->
                <div class="mb-3">
                    <label for="date-range" class="form-label">Date Range</label>
                    <div class="input-group">
                        <input type="date" id="start-date" class="form-control" placeholder="Start Date">
                        <span class="input-group-text">to</span>
                        <input type="date" id="end-date" class="form-control" placeholder="End Date">
                    </div>
                </div>

                <div class="row">
                    <!-- Calendar Filter -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Calendars</label>
                        <div id="calendar-filters" class="filter-scroll-container">
                            <?php if (isset($data['calendars']) && !empty($data['calendars'])) : ?>
                                <?php foreach ($data['calendars'] as $calendar) : ?>
                                    <div class="form-check">
                                        <input class="form-check-input calendar-checkbox" type="checkbox" 
                                               value="<?php echo $calendar->id; ?>" 
                                               id="calendar-<?php echo $calendar->id; ?>" checked>
                                        <label class="form-check-label" for="calendar-<?php echo $calendar->id; ?>">
                                            <span class="color-dot" style="background-color: <?php echo $calendar->color; ?>"></span>
                                            <?php echo $calendar->name; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <div class="text-muted">
                                    <small>No calendars available</small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Club Filter -->
                    <div class="col-md-6 mb-3">
                        <label for="club-filter" class="form-label">Car Clubs</label>
                        <select id="club-filter" class="form-select" multiple>
                            <option value="">All Clubs</option>
                            <!-- Clubs will be populated via JavaScript -->
                        </select>
                    </div>
                </div>

                <div class="row">
                    <!-- State Filter -->
                    <div class="col-md-4 mb-3">
                        <label for="state-filter" class="form-label">State <small class="text-info">(for precise filtering)</small></label>
                        <select id="state-filter" class="form-select">
                            <option value="">All States</option>
                            <!-- States will be populated via JavaScript -->
                        </select>
                        <small class="form-text text-muted">Select state first for better city filtering</small>
                    </div>

                    <!-- City Filter -->
                    <div class="col-md-4 mb-3">
                        <label for="city-filter" class="form-label">City <small class="text-info">(select state first)</small></label>
                        <select id="city-filter" class="form-select">
                            <option value="">All Cities</option>
                            <!-- Cities will be populated via JavaScript -->
                        </select>
                        <small class="form-text text-muted">For cities with common names (e.g., China Grove), select state first</small>
                    </div>

                    <!-- Venue Filter -->
                    <div class="col-md-4 mb-3">
                        <label for="venue-filter" class="form-label">Venue</label>
                        <select id="venue-filter" class="form-select">
                            <option value="">All Venues</option>
                            <!-- Venues will be populated via JavaScript -->
                        </select>
                    </div>
                </div>

                <!-- Location Search and Radius -->
                <div class="row">
                    <!-- Location Search -->
                    <div class="col-md-8 mb-3">
                        <label for="location-search" class="form-label">Search Location <small class="text-info">(include state for best results)</small></label>
                        <input type="text" id="location-search" class="form-control" placeholder="Enter city name (include state for best results, e.g., China Grove, NC)">
                        <small class="form-text text-muted">Enter a location to filter events within the specified radius. Include state for precise results (e.g., "Austin, TX" vs just "Austin")</small>
                    </div>

                    <!-- Radius Filter -->
                    <div class="col-md-4 mb-3">
                        <label for="radius-filter" class="form-label">Radius (miles): <span id="radius-value"><?php echo isset($data['mapSettings']['filter_radius']) ? $data['mapSettings']['filter_radius'] : 50; ?></span></label>
                        <input type="range" class="form-range" id="radius-filter" min="10" max="500" step="10" 
                               value="<?php echo isset($data['mapSettings']['filter_radius']) ? $data['mapSettings']['filter_radius'] : 50; ?>">
                    </div>
                </div>

                <!-- Keyword Search -->
                <div class="mb-3">
                    <label for="keyword-filter" class="form-label">Keyword Search</label>
                    <input type="text" id="keyword-filter" class="form-control" placeholder="Search event titles and descriptions">
                </div>
                
                <div class="row">
                    <!-- Category Filter -->
                    <div class="col-md-6 mb-3">
                        <label for="category-filter" class="form-label">Event Category</label>
                        <select id="category-filter" class="form-select" multiple>
                            <option value="">All Categories</option>
                            <!-- Categories will be populated via JavaScript -->
                        </select>
                    </div>
                    
                    <!-- Tag Filter -->
                    <div class="col-md-6 mb-3">
                        <label for="tag-filter" class="form-label">Event Tags</label>
                        <select id="tag-filter" class="form-select" multiple>
                            <option value="">All Tags</option>
                            <!-- Tags will be populated via JavaScript -->
                        </select>
                    </div>
                </div>
                
                <!-- Price Range Filter -->
                <div class="mb-3">
                    <label for="price-range-filter" class="form-label">Price Range: <span id="price-range-value">$0 - $500</span></label>
                    <div id="price-range-slider"></div>
                    <input type="hidden" id="price-min" value="0">
                    <input type="hidden" id="price-max" value="500">
                </div>

                <!-- Apply Filters Button -->
                <div class="d-flex justify-content-between">
                    <button id="apply-filters" type="button" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i> Apply Filters
                    </button>
                    
                    <!-- Reset Filters Button -->
                    <button id="reset-filters" type="button" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-2"></i> Reset Filters
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.filter-scroll-container {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
}

.color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}
</style>