<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Reports Dashboard</h1>
            <p class="text-muted mb-0">Generate comprehensive reports for your shows</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt me-2 d-none d-sm-inline"></i> Dashboard
            </a>
        </div>
    </div>

    <?php flash('report_message'); ?>

    <!-- Reports Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Reports Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm report-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">Total Shows</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($report_counts['total_shows'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Available for Reports</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm report-overview-card" 
                                 data-filter="completed" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Completed Shows</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($report_counts['completed_shows'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">With Full Data</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm report-overview-card" 
                                 data-filter="registrations" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Total Registrations</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($report_stats['total_registrations'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Across All Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm report-overview-card" 
                                 data-filter="revenue" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Total Revenue</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        $<?php echo number_format($report_stats['total_revenue'] ?? 0, 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Registration Fees</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Report Generation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Report Generation
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/registrationReport" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <span>Registration Summary</span>
                                <small class="text-muted mt-1">View registration statistics</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/financialReport" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                <span>Financial Summary</span>
                                <small class="text-muted mt-1">Revenue and payment data</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/judgingReport" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-trophy fa-2x mb-2"></i>
                                <span>Judging Results</span>
                                <small class="text-muted mt-1">Awards and winners</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/attendanceReport" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                                <span>Attendance Report</span>
                                <small class="text-muted mt-1">Check-in and attendance</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Summary -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary"><?php echo number_format($report_stats['this_month_registrations'] ?? 0); ?></h4>
                            <small class="text-muted">Registrations This Month</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">$<?php echo number_format($report_stats['this_month_revenue'] ?? 0, 0); ?></h4>
                            <small class="text-muted">Revenue This Month</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Upcoming Shows
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-warning"><?php echo number_format($report_counts['upcoming_shows'] ?? 0); ?></h4>
                            <small class="text-muted">Shows This Month</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info"><?php echo number_format($report_stats['avg_registrations_per_show'] ?? 0, 1); ?></h4>
                            <small class="text-muted">Avg Registrations</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Show Details Section (Lazy Loaded) -->
    <div class="show-section" id="show-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">Show Details</span>
                        <span class="badge bg-secondary" id="show-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closeShowSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-shows" class="form-label">Search Shows</label>
                        <input type="text" class="form-control" id="search-shows" 
                               placeholder="Search by name, location, or date...">
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="all">All Status</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="completed">Completed</option>
                            <option value="draft">Draft</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date-filter" class="form-label">Date Range</label>
                        <select class="form-select" id="date-filter">
                            <option value="">All Dates</option>
                            <option value="this_month">This Month</option>
                            <option value="last_month">Last Month</option>
                            <option value="this_year">This Year</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-shows" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-shows">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchShows()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearShowSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportShows()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-shows">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading shows...</p>
            </div>
            
            <!-- Shows Content (Will be populated via AJAX) -->
            <div id="shows-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Report overview card click handlers
    document.querySelectorAll('.report-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, '').replace('$', ''));

            if (count > 0) {
                loadShowSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-shows').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchShows();
        }
    });

    // Filter change handlers
    document.getElementById('status-filter').addEventListener('change', searchShows);
    document.getElementById('date-filter').addEventListener('change', searchShows);
    document.getElementById('per-page-shows').addEventListener('change', searchShows);
});

function loadShowSection(filter = 'all') {
    // Show the show section
    const section = document.getElementById('show-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter === 'completed') {
        document.getElementById('status-filter').value = 'completed';
    } else if (filter === 'upcoming') {
        document.getElementById('status-filter').value = 'upcoming';
    }

    // Load shows
    loadShows(1);
}

function closeShowSection() {
    const section = document.getElementById('show-section');
    section.style.display = 'none';
}

function loadShows(page = 1) {
    const loadingDiv = document.getElementById('loading-shows');
    const contentDiv = document.getElementById('shows-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-shows').value;
    const statusFilter = document.getElementById('status-filter').value;
    const dateFilter = document.getElementById('date-filter').value;
    const perPage = document.getElementById('per-page-shows').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        status_filter: statusFilter,
        date_filter: dateFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/coordinator/loadReportShows?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderShows(data);
        } else {
            showShowError(data.error || 'Failed to load shows');
        }
    })
    .catch(error => {
        console.error('Error loading shows:', error);
        showShowError('Network error occurred');
    });
}

function searchShows() {
    loadShows(1);
}

function clearShowSearch() {
    document.getElementById('search-shows').value = '';
    document.getElementById('status-filter').value = 'all';
    document.getElementById('date-filter').value = '';
    loadShows(1);
}

function renderShows(data) {
    const loadingDiv = document.getElementById('loading-shows');
    const contentDiv = document.getElementById('shows-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render shows table and pagination
    let html = '';

    if (data.shows.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No shows found.</p></div>';
    } else {
        html = renderShowsTable(data.shows, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update show count display
    document.getElementById('show-count-display').textContent = data.pagination.total_shows.toLocaleString();
}

function renderShowsTable(shows, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Show Name</th><th>Date</th><th>Location</th><th>Registrations</th><th>Status</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    shows.forEach(show => {
        html += '<tr>';
        html += '<td><strong>' + show.name + '</strong></td>';
        html += '<td>' + formatDate(show.start_date) + '</td>';
        html += '<td>' + (show.location || 'TBD') + '</td>';
        html += '<td><span class="badge bg-info">' + (show.registration_count || 0) + '</span></td>';
        html += '<td>' + getStatusBadge(show.status) + '</td>';
        html += '<td>' + getShowActions(show.id, show.status) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderShowPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_shows.toLocaleString()} shows`;
    html += '</div>';

    return html;
}

function getStatusBadge(status) {
    switch (status) {
        case 'published':
            return '<span class="badge bg-success">Published</span>';
        case 'draft':
            return '<span class="badge bg-warning text-dark">Draft</span>';
        case 'completed':
            return '<span class="badge bg-primary">Completed</span>';
        case 'cancelled':
            return '<span class="badge bg-danger">Cancelled</span>';
        default:
            return '<span class="badge bg-secondary">' + status + '</span>';
    }
}

function getShowActions(showId, status) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/coordinator/showReport/${showId}" class="btn btn-primary">
                <i class="fas fa-chart-bar"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/coordinator/registrationReport?show_id=${showId}" class="btn btn-info">
                <i class="fas fa-users"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/coordinator/exportRegistrations/${showId}" class="btn btn-success">
                <i class="fas fa-download"></i>
            </a>
        </div>
    `;
}

function renderShowPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadShows(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadShows(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadShows(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showShowError(message) {
    const loadingDiv = document.getElementById('loading-shows');
    const contentDiv = document.getElementById('shows-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function exportShows() {
    // Get current filter values
    const search = document.getElementById('search-shows').value;
    const statusFilter = document.getElementById('status-filter').value;
    const dateFilter = document.getElementById('date-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        status_filter: statusFilter,
        date_filter: dateFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/coordinator/exportReportShows?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
