<?php
/**
 * Judge Controller
 * 
 * This controller handles all judge-related functionality.
 */
class JudgeController extends Controller {
    private $showModel;
    private $registrationModel;
    private $vehicleModel;
    private $judgingModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is a judge
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole(['judge', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->showModel = $this->model('ShowModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('judge/dashboard');
    }
    
    /**
     * Direct link to score page
     */
    public function scoreLink() {
        $data = [
            'title' => 'Vehicle Score Link'
        ];
        
        $this->view('judge/score_link', $data);
    }
    
    /**
     * Judge dashboard
     */
    public function dashboard() {
        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get judge assignment counts and statistics only (for performance)
        $judgeCounts = $this->showModel->getJudgeAssignmentCounts($userId);
        $judgeStats = $this->showModel->getJudgeStats($userId);

        $data = [
            'title' => 'Judge Dashboard',
            'judge_counts' => $judgeCounts,
            'judge_stats' => $judgeStats
        ];

        $this->view('judge/dashboard_optimized', $data);
    }

    /**
     * AJAX endpoint for loading paginated judge assignments
     *
     * @return void
     */
    public function loadAssignments() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $dateFilter = $_GET['date_filter'] ?? '';
        $orderBy = $_GET['order_by'] ?? 'start_date';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            $result = $this->showModel->getPaginatedJudgeAssignments(
                $userId, $page, $perPage, $search, $statusFilter, $dateFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'assignments' => $result['assignments'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in JudgeController::loadAssignments: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load assignments']);
        }
    }
    
    /**
     * Show details
     * 
     * @param int $id Show ID
     */
    public function show($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is assigned to this show
        $userId = $this->auth->getCurrentUserId();
        
        // If admin, allow access, otherwise check assignment
        if (!$this->auth->hasRole('admin')) {
            $this->db->query('SELECT id FROM judge_assignments WHERE show_id = :show_id AND judge_id = :judge_id');
            $this->db->bind(':show_id', $id);
            $this->db->bind(':judge_id', $userId);
            $assignment = $this->db->single();
            
            if (!$assignment) {
                $this->redirect('home/access_denied');
                return;
            }
        }
        
        // Get judging assignments
        $assignments = $this->registrationModel->getJudgingAssignments($id, $userId);
        
        // Get categories assigned to this judge
        $categories = $this->showModel->getJudgeCategories($id, $userId);
        
        // Get all categories for the show with their prefixes
        $allCategories = $this->showModel->getShowCategories($id);
        $categoryPrefixes = [];
        
        if (!empty($allCategories)) {
            foreach ($allCategories as $category) {
                $prefix = $this->registrationModel->getCategoryPrefixPublic($category->id);
                $categoryPrefixes[$category->id] = [
                    'name' => $category->name,
                    'prefix' => $prefix
                ];
            }
        }
        
        $data = [
            'title' => $show->name,
            'show' => $show,
            'assignments' => $assignments,
            'categories' => $categories,
            'allCategories' => $allCategories,
            'categoryPrefixes' => $categoryPrefixes
        ];
        
        $this->view('judge/show', $data);
    }
    
    /**
     * Judge a vehicle
     * 
     * @param int $registrationId Registration ID
     */
    public function judge($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is assigned to this show
        $userId = $this->auth->getCurrentUserId();
        
        // If admin, allow access, otherwise check assignment
        if (!$this->auth->hasRole('admin')) {
            $this->db->query('SELECT id FROM judge_assignments WHERE show_id = :show_id AND judge_id = :judge_id 
                              AND (category_id = :category_id OR category_id IS NULL)');
            $this->db->bind(':show_id', $registration->show_id);
            $this->db->bind(':judge_id', $userId);
            $this->db->bind(':category_id', $registration->category_id);
            $assignment = $this->db->single();
            
            if (!$assignment) {
                $this->redirect('home/access_denied');
                return;
            }
        }
        
        // Get vehicle images
        $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        
        // Try to get primary image if no images were found
        if (empty($images)) {
            // First try using the getPrimaryImage method
            $primaryImage = $this->vehicleModel->getPrimaryImage($registration->vehicle_id);
            if ($primaryImage) {
                // If we found a primary image, add it to the images array
                $images = [$primaryImage];
            } else {
                // Try to get the primary image directly from the vehicles table
                $this->db->query('SELECT primary_image FROM vehicles WHERE id = :vehicle_id AND primary_image IS NOT NULL AND primary_image != ""');
                $this->db->bind(':vehicle_id', $registration->vehicle_id);
                $vehicle = $this->db->single();
                
                if ($vehicle && isset($vehicle->primary_image) && !empty($vehicle->primary_image)) {
                    // Create a simple object to mimic an image record
                    $imageObj = new stdClass();
                    $imageObj->filename = $vehicle->primary_image;
                    $imageObj->is_primary = 1;
                    $images = [$imageObj];
                }
            }
        }
        
        // Get judging metrics
        $metrics = $this->showModel->getJudgingMetrics($registration->show_id);
        
        // Get existing scores
        $scores = $this->judgingModel->getJudgeScores($registrationId, $userId);
        
        // Check for auto-saved scores if no regular scores exist
        $hasAutoSavedScores = false;
        if (empty($scores)) {
            $autoSavedScores = $this->judgingModel->getAutoSavedScores($registrationId, $userId);
            if (!empty($autoSavedScores)) {
                $scores = $autoSavedScores;
                $hasAutoSavedScores = true;
            }
        }
        
        // Handle AJAX auto-save request
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['auto_save'])) {
            // This is an AJAX request for auto-saving
            header('Content-Type: application/json');
            
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                echo json_encode(['success' => false, 'message' => 'Invalid request']);
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $scoreData = [];
            
            foreach ($metrics as $metric) {
                $scoreValue = isset($_POST['score_' . $metric->id]) ? floatval($_POST['score_' . $metric->id]) : 0;
                $comments = isset($_POST['comments_' . $metric->id]) ? trim($_POST['comments_' . $metric->id]) : '';
                
                $scoreData[] = [
                    'registration_id' => $registrationId,
                    'judge_id' => $userId,
                    'metric_id' => $metric->id,
                    'score' => $scoreValue,
                    'comments' => $comments,
                    'is_draft' => 1,
                    'auto_saved' => 1
                ];
            }
            
            // Save scores
            $result = $this->judgingModel->autoSaveScores($scoreData);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? 'Scores auto-saved successfully' : 'Failed to auto-save scores',
                'timestamp' => gmdate('Y-m-d H:i:s')
            ]);
            return;
        }
        
        // Check if form was submitted (regular POST, not auto-save)
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && !isset($_POST['auto_save'])) {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $scoreData = [];
            $errors = [];
            $isDraft = isset($_POST['save_draft']);
            
            foreach ($metrics as $metric) {
                $scoreValue = isset($_POST['score_' . $metric->id]) ? floatval($_POST['score_' . $metric->id]) : 0;
                $comments = isset($_POST['comments_' . $metric->id]) ? trim($_POST['comments_' . $metric->id]) : '';
                
                // Validate score
                if (!$isDraft && ($scoreValue < 0 || $scoreValue > $metric->max_score)) {
                    $errors['score_' . $metric->id] = 'Score must be between 0 and ' . $metric->max_score;
                }
                
                $scoreData[] = [
                    'registration_id' => $registrationId,
                    'judge_id' => $userId,
                    'metric_id' => $metric->id,
                    'score' => $scoreValue,
                    'comments' => $comments,
                    'is_draft' => $isDraft
                ];
            }
            
            // Check for errors
            if (empty($errors)) {
                // Save scores
                if ($this->judgingModel->saveScores($scoreData)) {
                    // If not a draft, finalize scores
                    if (!$isDraft) {
                        $this->judgingModel->finalizeScores($registrationId, $userId);
                    }
                    
                    // Redirect to show page
                    $this->redirect('judge/show/' . $registration->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $data = [
                    'title' => 'Judge Vehicle',
                    'registration' => $registration,
                    'show' => $show,
                    'images' => $images,
                    'metrics' => $metrics,
                    'scores' => $scores,
                    'errors' => $errors
                ];
                
                $this->view('judge/judge', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Judge Vehicle',
                'registration' => $registration,
                'show' => $show,
                'images' => $images,
                'metrics' => $metrics,
                'scores' => $scores,
                'errors' => [],
                'has_auto_saved' => $hasAutoSavedScores
            ];
            
            // Load view
            $this->view('judge/judge', $data);
        }
    }
    
    /**
     * View scores for a vehicle
     * 
     * @param int $registrationId Registration ID
     */
    public function viewScores($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is assigned to this show
        $userId = $this->auth->getCurrentUserId();
        
        // If admin, allow access, otherwise check assignment
        if (!$this->auth->hasRole('admin')) {
            $this->db->query('SELECT id FROM judge_assignments WHERE show_id = :show_id AND judge_id = :judge_id');
            $this->db->bind(':show_id', $registration->show_id);
            $this->db->bind(':judge_id', $userId);
            $assignment = $this->db->single();
            
            if (!$assignment) {
                $this->redirect('home/access_denied');
                return;
            }
        }
        
        // Get vehicle images
        $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        
        // Try to get primary image if no images were found
        if (empty($images)) {
            // First try using the getPrimaryImage method
            $primaryImage = $this->vehicleModel->getPrimaryImage($registration->vehicle_id);
            if ($primaryImage) {
                // If we found a primary image, add it to the images array
                $images = [$primaryImage];
            } else {
                // Try to get the primary image directly from the vehicles table
                $this->db->query('SELECT primary_image FROM vehicles WHERE id = :vehicle_id AND primary_image IS NOT NULL AND primary_image != ""');
                $this->db->bind(':vehicle_id', $registration->vehicle_id);
                $vehicle = $this->db->single();
                
                if ($vehicle && isset($vehicle->primary_image) && !empty($vehicle->primary_image)) {
                    // Create a simple object to mimic an image record
                    $imageObj = new stdClass();
                    $imageObj->filename = $vehicle->primary_image;
                    $imageObj->is_primary = 1;
                    $images = [$imageObj];
                }
            }
        }
        
        // Get scores for the "Detailed Scores by Judge" section
        $scores = $this->judgingModel->getScores($registrationId);
        
        // Get judging metrics for this show
        $this->db->query("SELECT id, name, max_score, weight FROM judging_metrics 
                         WHERE show_id = :show_id ORDER BY display_order");
        $this->db->bind(':show_id', $registration->show_id);
        $metrics = $this->db->resultSet();
        
        // Get age weight for this vehicle
        $this->db->query("SELECT multiplier FROM age_weights 
                         WHERE show_id = :show_id 
                         AND :vehicle_year BETWEEN min_age AND max_age");
        $this->db->bind(':show_id', $registration->show_id);
        $this->db->bind(':vehicle_year', $registration->year);
        $ageWeightResult = $this->db->single();
        $ageWeight = $ageWeightResult ? $ageWeightResult->multiplier : 1.0;
        
        // Get the scoring settings to display the formula
        $this->db->query("SELECT ss.*, sf.name as formula_name, sf.formula, sf.id as formula_id
                         FROM show_scoring_settings ss
                         LEFT JOIN scoring_formulas sf ON ss.formula_id = sf.id
                         WHERE ss.show_id = :show_id");
        $this->db->bind(':show_id', $registration->show_id);
        $scoringSettings = $this->db->single();
        
        // If no settings found, create default settings
        if (!$scoringSettings) {
            $scoringSettings = new stdClass();
            $scoringSettings->formula_name = 'Default';
            $scoringSettings->formula = 'rawScore * (metricWeight * 100) * (useAgeWeight ? ageWeight : 1)';
            $scoringSettings->custom_formula = '';
            $scoringSettings->weight_multiplier = 100;
            $scoringSettings->use_age_weight = 1;
            $scoringSettings->age_weight_multiplier = 1;
            $scoringSettings->normalize_scores = 0;
        }
        
        // If formula_id is set but formula is null, try to get the formula directly
        if (isset($scoringSettings->formula_id) && $scoringSettings->formula_id && 
            (!isset($scoringSettings->formula) || empty($scoringSettings->formula))) {
            $this->db->query("SELECT formula, name FROM scoring_formulas WHERE id = :id");
            $this->db->bind(':id', $scoringSettings->formula_id);
            $formulaData = $this->db->single();
            
            if ($formulaData) {
                $scoringSettings->formula = $formulaData->formula;
                $scoringSettings->formula_name = $formulaData->name;
            }
        }
        
        // Determine which formula is being used
        $activeFormula = !empty($scoringSettings->custom_formula) ? 
            $scoringSettings->custom_formula : 
            (isset($scoringSettings->formula) && !empty($scoringSettings->formula) ? 
                $scoringSettings->formula : 
                'rawScore * (metricWeight * 100) * (useAgeWeight ? ageWeight : 1)');
        
        // Get only the current judge's scores
        $judgeScores = $this->judgingModel->getJudgeScores($registrationId, $userId);
        
        // Calculate the weighted total score for the current judge only
        $weightedTotalScore = 0;
        $totalMaxPossible = 0;
        
        foreach ($judgeScores as $score) {
            $metricWeight = $score->weight;
            $rawScore = $score->score;
            $maxScore = $score->max_score;
            
            // Apply the formula to calculate the weighted score
            $useAgeWeight = $scoringSettings->use_age_weight ?? 1;
            $weightMultiplier = $scoringSettings->weight_multiplier ?? 100;
            
            // Simple formula calculation (this should match the formula logic in the system)
            $weightedScore = $rawScore * ($metricWeight * $weightMultiplier) * ($useAgeWeight ? $ageWeight : 1);
            $weightedTotalScore += $weightedScore;
            
            // Calculate max possible score for normalization if needed
            $maxWeightedScore = $maxScore * ($metricWeight * $weightMultiplier) * ($useAgeWeight ? $ageWeight : 1);
            $totalMaxPossible += $maxWeightedScore;
        }
        
        // Normalize to 100-point scale if setting is enabled
        if (isset($scoringSettings->normalize_scores) && $scoringSettings->normalize_scores && $totalMaxPossible > 0) {
            $weightedTotalScore = ($weightedTotalScore / $totalMaxPossible) * 100;
        }
        
        // Get metric scores from vehicle_metric_scores table (for reference only)
        $this->db->query("SELECT vms.* FROM vehicle_metric_scores vms
                         JOIN vehicle_total_scores vts ON vms.vehicle_total_score_id = vts.id
                         WHERE vts.registration_id = :registration_id");
        $this->db->bind(':registration_id', $registrationId);
        $metricScores = $this->db->resultSet();
        
        // If no metric scores found, create empty array
        if (empty($metricScores)) {
            $metricScores = [];
        }
        
        $data = [
            'title' => 'View Scores',
            'registration' => $registration,
            'show' => $show,
            'images' => $images,
            'scores' => $scores,
            'metric_scores' => $metricScores,
            'weighted_total_score' => $weightedTotalScore,
            'age_weight' => $ageWeight,
            'metrics' => $metrics,
            'scoring_settings' => $scoringSettings,
            'active_formula' => $activeFormula,
            'formula_name' => $scoringSettings->formula_name ?? 'Custom'
        ];
        
        $this->view('judge/view_scores', $data);
    }
    
    /**
     * Handle QR code scanning for vehicle judging
     * 
     * @param string $registrationCode Registration code or ID
     * @return void
     */
    public function vehicle($registrationCode) {
        // Check if registration code is valid
        $registrationId = intval($registrationCode);
        
        if ($registrationId <= 0) {
            // Try to find registration by code
            $this->db->query('SELECT id FROM registrations WHERE registration_number = :code');
            $this->db->bind(':code', $registrationCode);
            $result = $this->db->single();
            
            if ($result) {
                $registrationId = $result->id;
            } else {
                $this->redirect('home/error/Invalid%20QR%20code');
                return;
            }
        }
        
        // Redirect to judge page
        $this->redirect('judge/judge/' . $registrationId);
    }
    
    /**
     * Check in a vehicle registration
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function checkIn($id) {
        // Verify CSRF token
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if already checked in
        if ($registration->checked_in) {
            $this->redirect('judge/judge/' . $id);
            return;
        }
        
        // Verify judge has access to this registration (skip for admins)
        if (!$this->auth->hasRole('admin')) {
            $this->db->query('SELECT id FROM judge_assignments 
                             WHERE show_id = :show_id AND judge_id = :judge_id 
                             AND (category_id = :category_id OR category_id IS NULL)');
            $this->db->bind(':show_id', $registration->show_id);
            $this->db->bind(':judge_id', $_SESSION['user_id']);
            $this->db->bind(':category_id', $registration->category_id);
            $assignment = $this->db->single();
            
            if (!$assignment) {
                $this->redirect('home/error/You%20do%20not%20have%20permission%20to%20check%20in%20this%20vehicle');
                return;
            }
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 1,
            'check_in_time' => gmdate('Y-m-d H:i:s')
        ];
        
        // Update registration
        if ($this->registrationModel->updateRegistration($id, $data)) {
            // Log the check-in
            error_log('Judge checked in registration #' . $id . ' for ' . $registration->year . ' ' . 
                      $registration->make . ' ' . $registration->model);
            
            // Set success message
            $_SESSION['success_message'] = 'Vehicle successfully checked in';
        } else {
            // Set error message
            $_SESSION['error_message'] = 'Failed to check in vehicle';
        }
        
        // Redirect back to judging page
        $this->redirect('judge/judge/' . $id);
    }
    
    /**
     * Undo check-in for a vehicle registration
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function undoCheckIn($id) {
        // Verify CSRF token
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if not checked in
        if (!$registration->checked_in) {
            $this->redirect('judge/judge/' . $id);
            return;
        }
        
        // Verify judge has access to this registration (skip for admins)
        if (!$this->auth->hasRole('admin')) {
            $this->db->query('SELECT id FROM judge_assignments 
                             WHERE show_id = :show_id AND judge_id = :judge_id 
                             AND (category_id = :category_id OR category_id IS NULL)');
            $this->db->bind(':show_id', $registration->show_id);
            $this->db->bind(':judge_id', $_SESSION['user_id']);
            $this->db->bind(':category_id', $registration->category_id);
            $assignment = $this->db->single();
            
            if (!$assignment) {
                $this->redirect('home/error/You%20do%20not%20have%20permission%20to%20undo%20check-in%20for%20this%20vehicle');
                return;
            }
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 0,
            'check_in_time' => null
        ];
        
        // Update registration
        if ($this->registrationModel->updateRegistration($id, $data)) {
            // Log the action
            error_log('Judge undid check-in for registration #' . $id . ' for ' . $registration->year . ' ' . 
                      $registration->make . ' ' . $registration->model);
            
            // Set success message
            $_SESSION['success_message'] = 'Check-in has been undone';
        } else {
            // Set error message
            $_SESSION['error_message'] = 'Failed to undo check-in';
        }
        
        // Redirect back to judging page
        $this->redirect('judge/judge/' . $id);
    }
}